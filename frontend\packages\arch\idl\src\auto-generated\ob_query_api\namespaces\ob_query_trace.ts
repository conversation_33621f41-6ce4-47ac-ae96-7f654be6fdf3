/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_ob_query_telemetry_span from './flow_devops_ob_query_telemetry_span';
import * as flow_devops_ob_query_telemetry from './flow_devops_ob_query_telemetry';

export type Int64 = string | number;

export enum CozeChannel {
  /** 默认为Coze, 未来扩展到其他渠道 */
  Coze = 0,
}

export enum DebugScene {
  /** 默认play ground Debug场景 */
  Debug = 0,
}

export enum FrontedTagType {
  /** 文本 */
  TEXT = 0,
  /** 时间，用时间戳，单位是毫秒 */
  TIME = 1,
  /** 时间间隔，单位是毫秒 */
  TIME_DURATION = 2,
}

export enum InputOutputType {
  /** 文本类型 */
  TEXT = 0,
}

export enum QueryScene {
  /** doubao cici 全链路调试台 */
  ALICE_OP = 0,
  /** doubao cici debug 功能 */
  DOUBAO_CICI_DEBUG = 1,
  /** workflow debug 功能 */
  WORKFLOW_DEBUG = 2,
  /** Bots 运营后台 */
  COZE_OP = 3,
  /** fornax platform */
  FORNAX = 4,
}

export enum QueryTypeEnum {
  Undefined = 0,
  Match = 1,
  Term = 2,
  Range = 3,
  Exist = 4,
  NotExist = 5,
  NotIn = 6,
}

export enum TagType {
  STRING = 0,
  DOUBLE = 1,
  BOOL = 2,
  LONG = 3,
  BYTES = 4,
}

export enum TenantLevel {
  Ordinary = 0,
  AdvancedWhitelist = 1,
  DoubaoTransfer = 2,
  DoubaoUnencrypted = 3,
}

export interface FilterTag {
  data_type?: string;
  tag_key?: string;
  multi_tag_keys?: Array<string>;
  values?: Array<string>;
  query_type?: QueryTypeEnum;
}

export interface FrontendTag {
  key: string;
  /** 多语，如无配置时值沿用 key */
  key_alias?: string;
  tag_type: TagType;
  value?: Value;
  /** 前端类型，用于前端处理 */
  frontend_tag_type?: FrontedTagType;
  /** 是否可复制 */
  can_copy?: boolean;
}

export interface GetTraceFrontendRequest {
  trace_id?: string;
  log_id?: string;
  /** 调用场景 */
  scene?: QueryScene;
  start_at?: Int64;
  end_at?: Int64;
  bot_id?: string;
  space_id?: string;
  /** 租户: flow, flow_vip */
  tenant?: string;
  Base?: base.Base;
}

export interface GetTraceFrontendResponse {
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
  data?: TraceFrontend;
  BaseResp?: base.BaseResp;
}

export interface KeyScene {
  /** 场景，如"拆分搜索词"\"搜索" */
  scene?: string;
  /** 状态信息 */
  status_message?: string;
  system?: string;
  /** 历史消息 */
  history_messages?: Array<MessageItem>;
  /** 输入 */
  input?: KeySceneInput;
  /** 输出 */
  output?: KeySceneOutput;
  /** 单位是毫秒 */
  duration?: Int64;
  /** 开始时间，用于排序，单位是毫秒 */
  start_time?: Int64;
  /** 子场景 */
  sub_key_scenes?: Array<KeyScene>;
  /** 键值对列表 */
  key_values?: Array<KeyValue>;
  /** 场景代码 */
  scene_code?: string;
}

export interface KeySceneInput {
  role?: string;
  content_list?: Array<TraceSummaryContent>;
}

export interface KeySceneOutput {
  role?: string;
  content_list?: Array<TraceSummaryContent>;
}

export interface KeyValue {
  key?: string;
  value?: string;
}

export interface ListDebugQueriesRequest {
  startAtMS: Int64;
  endAtMS: Int64;
  spaceID: Int64;
  botID: Int64;
  status?: Array<flow_devops_ob_query_telemetry_span.SpanStatus>;
  inputSearch?: string;
  limit?: number;
  pageToken?: string;
  Base?: base.Base;
}

export interface ListDebugQueriesResponse {
  data: flow_devops_ob_query_telemetry.ListTracesData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface MessageItem {
  /** 角色 */
  role?: string;
  /** 内容 */
  content?: string;
}

export interface SearchTraceRequest {
  /** 租户: doubao/cici/fornax/coze */
  tenant?: string;
  trace_id?: string;
  log_id?: string;
  message_id?: string;
  start_at?: Int64;
  end_at?: Int64;
  tenant_level?: TenantLevel;
  token?: string;
  Base?: base.Base;
}

export interface SearchTraceResponse {
  code: number;
  msg: string;
  data: TraceData;
  BaseResp?: base.BaseResp;
}

export interface Span {
  trace_id?: string;
  log_id?: string;
  psm?: string;
  dc?: string;
  pod_name?: string;
  span_id?: string;
  type?: string;
  name?: string;
  parent_id?: string;
  /** 单位是毫秒 */
  duration?: Int64;
  /** 单位是毫秒 */
  start_time?: Int64;
  status_code?: number;
  tags?: Array<Tag>;
}

export interface SpanInputOutput {
  /** TEXT */
  type?: InputOutputType;
  content?: string;
  /** 是否是加密状态 */
  is_encrypted?: boolean;
}

export interface SpanSummary {
  tags?: Array<FrontendTag>;
}

/** Tag */
export interface Tag {
  key?: string;
  tag_type?: TagType;
  value?: Value;
}

export interface TraceData {
  spans?: Array<Span>;
}

export interface TraceFrontend {
  spans?: Array<TraceFrontendSpan>;
  header?: TraceHeader;
}

export interface TraceFrontendDoubaoCiciDebug {
  spans?: Array<TraceFrontendSpan>;
  header?: TraceHeader;
  summary?: TraceSummary;
}

export interface TraceFrontendSpan {
  trace_id?: string;
  log_id?: string;
  span_id?: string;
  type?: string;
  name?: string;
  alias_name?: string;
  parent_id?: string;
  /** 单位是毫秒 */
  duration?: Int64;
  /** 单位是毫秒 */
  start_time?: Int64;
  status_code?: number;
  tags?: Array<Tag>;
  /** 节点详情 */
  summary?: SpanSummary;
  input?: SpanInputOutput;
  output?: SpanInputOutput;
  /** 是否是入口节点 */
  is_entry?: boolean;
  /** 产品线 */
  product_line?: string;
  /** 是否是关键节点 */
  is_key_span?: boolean;
  /** 节点负责人列表, 邮箱前缀 */
  owner_list?: Array<string>;
  /** 节点详情文档 */
  rundown_doc_url?: string;
  /** 是否是加密状态 */
  is_encrypted?: boolean;
}

export interface TraceHeader {
  /** 单位是毫秒 */
  duration?: Int64;
  /** 输入消耗token数 */
  tokens?: number;
  status_code?: number;
  tags?: Array<FrontendTag>;
  /** 消息ID */
  message_id?: string;
  /** 单位是毫秒 */
  start_time?: Int64;
}

export interface TraceSummary {
  /** 一级 System 的文本 */
  system?: string;
  /** 一级历史消息 */
  history_messages?: Array<MessageItem>;
  key_scenes?: Array<KeyScene>;
  /** 输入 */
  input?: string;
  /** 输出 */
  output?: string;
  /** 一级当前对话的耗时, 单位是毫秒 */
  duration?: Int64;
  /** 用户ID */
  user_id?: string;
  /** 一级KeyScene列表 */
  top_level_key_scenes?: Array<KeyScene>;
  /** 是否是加密状态 */
  is_encrypted?: boolean;
}

export interface TraceSummaryContent {
  /** 键 */
  key?: string;
  /** 内容 */
  content?: string;
}

export interface Value {
  v_str?: string;
  v_double?: number;
  v_bool?: boolean;
  v_long?: Int64;
  v_bytes?: Blob;
}
/* eslint-enable */
