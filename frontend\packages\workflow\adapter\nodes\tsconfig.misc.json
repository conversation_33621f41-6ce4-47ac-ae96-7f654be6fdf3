{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"rootDir": "./", "outDir": "./dist", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "target": "ES2020", "moduleResolution": "bundler"}, "include": ["__tests__", "vitest.config.ts", "stories"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}]}