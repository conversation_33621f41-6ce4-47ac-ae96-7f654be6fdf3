/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as table from './table';
import * as base from './base';
import * as project_memory from './project_memory';

export type Int64 = string | number;

export interface DelProfileMemoryRequest {
  user_id?: Int64;
  bot_id?: string;
  keywords?: Array<string>;
  connector_id?: Int64;
  /** 引用信息 */
  ref_info?: table.RefInfo;
  project_id?: string;
  Base?: base.Base;
}

export interface DelProfileMemoryResponse {
  BaseResp: base.BaseResp;
}

export interface GetMemoryVariableMetaListData {
  items?: Array<VariableNew>;
}

export interface GetProfileMemoryRequest {
  user_id?: Int64;
  bot_id?: string;
  keywords?: Array<string>;
  connector_id?: Int64;
  version?: string;
  /** 引用信息 */
  ref_info?: table.RefInfo;
  ext?: string;
  project_id?: string;
  ProjectVersion?: Int64;
  VariableChannel?: project_memory.VariableChannel;
  Base?: base.Base;
}

export interface GetProfileMemoryResponse {
  memories?: Array<KVItem>;
  BaseResp: base.BaseResp;
}

export interface GetSysVariableConfRequest {
  Base?: base.Base;
}

export interface GetSysVariableConfResponse {
  conf?: Array<VariableInfo>;
  group_conf?: Array<GroupVariableInfo>;
  BaseResp: base.BaseResp;
}

export interface GetVariableData {
  items?: Array<KVItem>;
}

export interface GroupVariableInfo {
  group_name?: string;
  group_desc?: string;
  group_ext_desc?: string;
  var_info_list?: Array<VariableInfo>;
  sub_group_info?: Array<GroupVariableInfo>;
}

export interface KVItem {
  keyword?: string;
  value?: string;
  create_time?: Int64;
  update_time?: Int64;
  is_system?: boolean;
  prompt_disabled?: boolean;
  schema?: string;
}

export interface OpenGetPlaygroundVariableReq {
  /** Project的用户变量传project */
  app_id?: string;
  /** bot id */
  bot_id?: string;
  connector_id?: string;
  /** 渠道uid */
  connector_uid?: string;
  /** 变量名称 */
  keywords?: Array<string>;
  Base?: base.Base;
}

export interface OpenGetPlaygroundVariableResp {
  code?: number;
  msg?: string;
  data?: GetVariableData;
  BaseResp: base.BaseResp;
}

export interface OpenSetPlaygroundVariableReq {
  /** Project的用户变量传project */
  app_id?: string;
  /** bot id */
  bot_id?: string;
  /** 渠道id */
  connector_id?: string;
  /** 渠道uid */
  connector_uid?: string;
  /** 要设置的值 */
  data?: Array<KVItem>;
  Base?: base.Base;
}

export interface OpenSetPlaygroundVariableResp {
  code?: number;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface SetKvMemoryReq {
  bot_id: string;
  user_id?: Int64;
  data: Array<KVItem>;
  connector_id?: Int64;
  /** 引用信息 */
  ref_info?: table.RefInfo;
  project_id?: string;
  ProjectVersion?: Int64;
  Base?: base.Base;
}

export interface SetKvMemoryResp {
  BaseResp?: base.BaseResp;
}

export interface VariableInfo {
  key?: string;
  default_value?: string;
  description?: string;
  sensitive?: string;
  must_not_use_in_prompt?: string;
  can_write?: string;
  example?: string;
  ext_desc?: string;
  group_name?: string;
  group_desc?: string;
  group_ext_desc?: string;
  EffectiveChannelList?: Array<string>;
  is_allow_modify?: string;
}

export interface VariableNew {
  /** 变量名 */
  keyword?: string;
  /** 默认值 */
  default_value?: string;
  /** 变量类型 */
  variable_type?: string;
  /** 变量来源 */
  channel?: string;
  /** 变量描述 */
  description?: string;
  /** 是否启用 */
  enable?: boolean;
  /** 变量默认支持在Prompt中访问，取消勾选后将不支持在Prompt中访问（仅能在Workflow中访问 */
  prompt_enable?: boolean;
}
/* eslint-enable */
