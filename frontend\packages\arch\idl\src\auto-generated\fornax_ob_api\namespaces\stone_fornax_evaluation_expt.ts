/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 聚合器类型 */
export enum AggregatorType {
  Average = 1,
  Sum = 2,
  Max = 3,
  Min = 4,
  /** 得分的分布情况 */
  Distribution = 5,
}

export enum DataType {
  /** 默认，有小数的浮点数值类型 */
  Double = 0,
  /** 得分分布 */
  ScoreDistribution = 1,
}

export enum ExptAggregateCalculateStatus {
  Unknown = 0,
  Idle = 1,
  Calculating = 2,
}

export enum ExptRetryMode {
  Unknown = 0,
  RetryAll = 1,
  RetryFailure = 2,
  RetryTargetItems = 3,
}

export enum ExptStatus {
  Unknown = 0,
  /** 待执行 */
  Pending = 2,
  /** 执行中 */
  Processing = 3,
  /** 执行成功 */
  Success = 11,
  /** 执行失败 */
  Failed = 12,
  /** 用户终止 */
  Terminated = 13,
  /** 系统内部异常终止 */
  SystemTerminated = 14,
}

export enum FieldType {
  Unknown = 0,
  /** 评估器得分, FieldKey为evaluatorVersionID,value为score */
  EvaluatorScore = 1,
  CreatorBy = 2,
  ExptStatus = 3,
  TurnRunState = 4,
  TargetID = 5,
  EvalSetID = 6,
  EvaluatorID = 7,
  TargetType = 8,
  SourceTarget = 9,
  EvaluatorVersionID = 20,
  TargetVersionID = 21,
  EvalSetVersionID = 22,
}

export enum FilterLogicOp {
  Unknown = 0,
  And = 1,
  Or = 2,
}

export enum FilterOperatorType {
  Unknown = 0,
  /** 等于 */
  Equal = 1,
  /** 不等于 */
  NotEqual = 2,
  /** 大于 */
  Greater = 3,
  /** 大于等于 */
  GreaterOrEqual = 4,
  /** 小于 */
  Less = 5,
  /** 小于等于 */
  LessOrEqual = 6,
  /** 包含 */
  In = 7,
  /** 不包含 */
  NotIn = 8,
}

export enum ItemRunState {
  Unknown = -1,
  /** 排队中 */
  Queueing = 0,
  /** 执行中 */
  Processing = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 终止执行 */
  Terminal = 5,
}

export enum TurnRunState {
  /** 未开始执行 */
  Queueing = 0,
  /** 执行成功 */
  Success = 1,
  /** 执行失败 */
  Fail = 2,
  Processing = 3,
  Terminal = 4,
}
/* eslint-enable */
