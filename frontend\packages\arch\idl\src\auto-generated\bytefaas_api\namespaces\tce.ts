/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as cluster from './cluster';
import * as service from './service';

export type Int64 = string | number;

export interface GetTCEClusterListItem {
  /** Unique identifier for the TCE cluster */
  cluster_id: Int64;
  /** Name of the TCE cluster */
  name: string;
  /** Virtual region where the cluster is located */
  vregion: string;
  /** List of IDC (Internet Data Center) names associated with the cluster */
  idcs: Array<string>;
  /** Indicates if the cluster supports FaaS in the virtual region */
  is_faas_vregion_support: boolean;
  /** Link to the cluster details or management page */
  link: string;
  /** Total number of replicas in the cluster */
  replica_total: number;
  /** Mapping of IDC name to replica count in that IDC */
  replica: Record<string, number>;
}

export interface GetTCEClusterListRequest {
  /** TCE PSM (Product Service Module) identifier for which to list clusters */
  tce_psm: string;
}

export interface GetTCEClusterListResponse {
  code?: number;
  error?: string;
  /** List of TCE cluster items returned in the response */
  data?: Array<GetTCEClusterListItem>;
}

export interface GetTCEClusterMigrateMQAppParamsRequest {
  /** TCE PSM identifier for which to get cluster migration parameters */
  tce_psm: string;
  /** Cluster ID in TCE for which to get cluster migration parameters */
  tce_cluster_id: Int64;
}

export interface GetTCEClusterMigrateMQAppParamsResponse {
  code?: number;
  error?: string;
  /** Migration parameters for the cluster */
  data?: cluster.CreateClusterRequest;
}

export interface GetTCEMigrateMQAppParamsData {
  /** Service creation request parameters for migration */
  service: service.CreateServiceRequest;
  /** Cluster creation request parameters for migration */
  cluster: cluster.CreateClusterRequest;
}

export interface GetTCEMigrateMQAppParamsRequest {
  /** TCE PSM identifier for which to get migration parameters */
  tce_psm: string;
  /** Cluster ID in TCE for which to get migration parameters */
  tce_cluster_id: Int64;
}

export interface GetTCEMigrateMQAppParamsResponse {
  code?: number;
  error?: string;
  /** Migration parameters data for both service and cluster */
  data?: GetTCEMigrateMQAppParamsData;
}

export interface GetTCEServiceMigrateMQAppParamsRequest {
  /** TCE PSM identifier for which to get service migration parameters */
  tce_psm: string;
}

export interface GetTCEServiceMigrateMQAppParamsResponse {
  code?: number;
  error?: string;
  /** Migration parameters for the service */
  data?: service.CreateServiceRequest;
}
/* eslint-enable */
