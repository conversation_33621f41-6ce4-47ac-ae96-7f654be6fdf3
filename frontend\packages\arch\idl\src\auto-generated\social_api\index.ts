/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as society from './namespaces/society';

export { base, bot_common, society };
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/society';

export type Int64 = string | number;

export default class SocialApiService<T> {
  private request: any = () => {
    throw new Error('SocialApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/society/meta/create_society_meta */
  CreateSocietyMeta(
    req: society.CreateSocietyMetaRequest,
    options?: T,
  ): Promise<society.CreateSocietyMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/create_society_meta');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      space_id: _req['space_id'],
      icon_uri: _req['icon_uri'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/delete_society_meta */
  DeleteSocietyMeta(
    req: society.DeleteSocietyMetaRequest,
    options?: T,
  ): Promise<society.DeleteSocietyMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/delete_society_meta');
    const method = 'POST';
    const data = { meta_id: _req['meta_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/update_society_host */
  UpdateSocietyHost(
    req: society.UpdateSocietyHostRequest,
    options?: T,
  ): Promise<society.UpdateSocietyHostResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/update_society_host');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      name: _req['name'],
      persona: _req['persona'],
      icon_uri: _req['icon_uri'],
      role_visibility_config: _req['role_visibility_config'],
      host_config: _req['host_config'],
      work_info: _req['work_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/process/create_process */
  CreateProcess(
    req: society.CreateProcessRequest,
    options?: T,
  ): Promise<society.CreateProcessResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/process/create_process');
    const method = 'POST';
    const data = {
      process: _req['process'],
      agent_list: _req['agent_list'],
      device_id: _req['device_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/society/message/get_message_list
   *
   * ---message---//
   */
  GetSocietyMessageList(
    req: society.GetSocietyMessageListRequest,
    options?: T,
  ): Promise<society.GetSocietyMessageListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/message/get_message_list');
    const method = 'POST';
    const data = {
      process_id: _req['process_id'],
      agent_id: _req['agent_id'],
      before_task_id: _req['before_task_id'],
      after_task_id: _req['after_task_id'],
      count: _req['count'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/message/delete */
  DeleteSocietyMessage(
    req: society.DeleteSocietyMessageRequest,
    options?: T,
  ): Promise<society.DeleteSocietyMessageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/message/delete');
    const method = 'POST';
    const data = {
      process_id: _req['process_id'],
      round: _req['round'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/user_agent/reply */
  UserAgentReply(
    req: society.UserAgentReplyRequest,
    options?: T,
  ): Promise<society.UserAgentReplyResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/user_agent/reply');
    const method = 'POST';
    const data = {
      process_id: _req['process_id'],
      content: _req['content'],
      task_id: _req['task_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/society/meta/get_society_meta_list
   *
   * ---meta---//
   */
  GetSocietyMetaList(
    req: society.GetSocietyMetaListRequest,
    options?: T,
  ): Promise<society.GetSocietyMetaListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/get_society_meta_list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      creator_id: _req['creator_id'],
      query: _req['query'],
      page: _req['page'],
      size: _req['size'],
      publish_filter: _req['publish_filter'],
      query_source: _req['query_source'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/get_society_meta_detail */
  GetSocietyMetaDetail(
    req: society.GetSocietyMetaDetailRequest,
    options?: T,
  ): Promise<society.GetSocietyMetaDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/get_society_meta_detail');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      process_mode: _req['process_mode'],
      meta_version: _req['meta_version'],
      need_process_info: _req['need_process_info'],
      query_source: _req['query_source'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/update_society_meta */
  UpdateSocietyMeta(
    req: society.UpdateSocietyMetaRequest,
    options?: T,
  ): Promise<society.UpdateSocietyMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/update_society_meta');
    const method = 'POST';
    const data = { meta: _req['meta'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/society/process/get_process_detail
   *
   * ---process---//
   */
  GetProcessDetail(
    req: society.GetProcessDetailRequest,
    options?: T,
  ): Promise<society.GetProcessDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/process/get_process_detail');
    const method = 'POST';
    const data = { process_id: _req['process_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/society/user_agent/update
   *
   * ---user agent---//
   */
  UpdateUserAgent(
    req: society.UpdateUserAgentRequest,
    options?: T,
  ): Promise<society.UpdateUserAgentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/user_agent/update');
    const method = 'POST';
    const data = {
      process_id: _req['process_id'],
      agent_id: _req['agent_id'],
      device_id: _req['device_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/batch_delete_society_role */
  BatchDeleteSocietyRole(
    req: society.BatchDeleteSocietyRoleRequest,
    options?: T,
  ): Promise<society.BatchDeleteSocietyRoleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/batch_delete_society_role');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      role_id_list: _req['role_id_list'],
      role_visibility_config: _req['role_visibility_config'],
      host_work_info: _req['host_work_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/batch_create_society_role */
  BatchCreateSocietyRole(
    req: society.BatchCreateSocietyRoleRequest,
    options?: T,
  ): Promise<society.BatchCreateSocietyRoleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/batch_create_society_role');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      role_list: _req['role_list'],
      role_visibility_config: _req['role_visibility_config'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/batch_update_society_role */
  BatchUpdateSocietyRole(
    req: society.BatchUpdateSocietyRoleRequest,
    options?: T,
  ): Promise<society.BatchUpdateSocietyRoleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/batch_update_society_role');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      role_list: _req['role_list'],
      role_visibility_config: _req['role_visibility_config'],
      host_work_info: _req['host_work_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/process/Operate_process */
  OperateProcess(
    req: society.OperateProcessRequest,
    options?: T,
  ): Promise<society.OperateProcessResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/process/Operate_process');
    const method = 'POST';
    const data = {
      process_id: _req['process_id'],
      operate_process_type: _req['operate_process_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/duplicate_society_meta */
  DuplicateSocietyMeta(
    req: society.DuplicateSocietyMetaRequest,
    options?: T,
  ): Promise<society.DuplicateSocietyMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/duplicate_society_meta');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      meta_version: _req['meta_version'],
      target_space_id: _req['target_space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/publish_meta_pre_create */
  PublishMetaPreCreate(
    req: society.PublishMetaPreCreateRequest,
    options?: T,
  ): Promise<society.PublishMetaPreCreateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/publish_meta_pre_create');
    const method = 'POST';
    const data = { meta_id: _req['meta_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/publishing_meta */
  PublishingMeta(
    req: society.PublishingMetaRequest,
    options?: T,
  ): Promise<society.PublishingMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/publishing_meta');
    const method = 'POST';
    const data = {
      order_id: _req['order_id'],
      change_log_list: _req['change_log_list'],
      connector_list: _req['connector_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/get_publish_meta_result */
  GetPublishMetaResult(
    req: society.GetPublishMetaResultRequest,
    options?: T,
  ): Promise<society.GetPublishMetaResultResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/get_publish_meta_result');
    const method = 'POST';
    const data = { order_id: _req['order_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/generate_meta_store_category */
  GenerateMetaStoreCategory(
    req: society.GenerateMetaStoreCategoryRequest,
    options?: T,
  ): Promise<society.GenerateMetaStoreCategoryResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/society/meta/generate_meta_store_category',
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      prompt: _req['prompt'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/get_publish_order_entity_list */
  GetPublishOrderEntityList(
    req: society.GetPublishEntityListRequest,
    options?: T,
  ): Promise<society.GetPublishEntityListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/society/meta/get_publish_order_entity_list',
    );
    const method = 'POST';
    const data = { order_id: _req['order_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/get_meta_role_list */
  GetMetaRoleList(
    req: society.GetMetaRoleListRequest,
    options?: T,
  ): Promise<society.GetMetaRoleListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/get_meta_role_list');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      meta_version: _req['meta_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/meta/get_meta_variables */
  GetMetaVariables(
    req: society.GetMetaVariablesRequest,
    options?: T,
  ): Promise<society.GetMetaVariablesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/meta/get_meta_variables');
    const method = 'POST';
    const data = {
      meta_id: _req['meta_id'],
      meta_version: _req['meta_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/society/process/get_memory */
  GetProcessMemory(
    req: society.GetProcessMemoryRequest,
    options?: T,
  ): Promise<society.GetProcessMemoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/society/process/get_memory');
    const method = 'POST';
    const data = {
      process_id: _req['process_id'],
      agent_id: _req['agent_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
