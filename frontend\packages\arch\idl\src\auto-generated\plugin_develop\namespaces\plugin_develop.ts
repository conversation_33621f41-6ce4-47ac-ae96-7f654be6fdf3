/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as plugin_develop_common from './plugin_develop_common';
import * as base from './base';
import * as card from './card';

export type Int64 = string | number;

export enum ConnectStatus {
  /** 未连接/断开链接 */
  Disconnected = 0,
  /** 正在连接（需要用户火山接受连接） */
  Connecting = 1,
  /** 已连接 */
  Connected = 2,
  /** 用户拒绝连接/连接失败 */
  ConnectFailed = 3,
}

export enum OAuthPluginSource {
  Plugin = 1,
  Workflow = 2,
  ShortcutCommand = 3,
  Task = 4,
}

export enum OperateLinkType {
  /** 删除连接 */
  Delete = 1,
}

export interface BatchCreateAPIRequest {
  plugin_id?: string;
  ai_plugin?: string;
  /** tools信息存在这里，OpenAPI yaml格式 */
  openapi?: string;
  space_id?: string;
  dev_id?: string;
  /** false: 只创建不重复的 path
true : 只替换已存在的 path */
  replace_same_paths?: boolean;
  /** 要替换的path列表 */
  paths_to_replace?: Array<plugin_develop_common.PluginAPIInfo>;
  edit_version?: number;
  Base?: base.Base;
}

export interface BatchCreateAPIResponse {
  code?: Int64;
  msg?: string;
  /** PathsToReplace表示要覆盖的tools，
如果BaseResp.StatusCode = DuplicateAPIPath，那么PathsToReplace不为空 */
  paths_duplicated?: Array<plugin_develop_common.PluginAPIInfo>;
  paths_created?: Array<plugin_develop_common.PluginAPIInfo>;
  edit_version?: number;
  /** BaseResp.StatusCode
DuplicateAPIPath: 有重复的API Path，且 request.ReplaceDupPath = false
InvalidParam: 其他错误 */
  BaseResp: base.BaseResp;
}

export interface BatchGetPluginPricingRulesRequest {
  plugin_apis?: Array<plugin_develop_common.PluginApi>;
  Base?: base.Base;
}

export interface BatchGetPluginPricingRulesResponse {
  pricing_rules?: Array<plugin_develop_common.PluginPricingRule>;
  BaseResp?: base.BaseResp;
}

export interface BindCardRequest {
  plugin_id?: string;
  api_name?: string;
  card_id?: string;
  bot_id?: string;
  mapping_rule?: string;
  max_display_rows?: string;
  card_version_num?: string;
  agent_id?: string;
  llm_text_card?: boolean;
  biz_type?: plugin_develop_common.CardBusinessType;
  business_id?: string;
  unique_id?: string;
  /** 是否选择了预置卡片 */
  plugin_preset_card_selected?: boolean;
  Base?: base.Base;
}

export interface BindCardResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface BindPluginPresetCardRequest {
  plugin_id?: string;
  /** 2: string api_name */
  card_id?: string;
  api_id?: string;
  mapping_rule?: string;
  max_display_rows?: Int64;
  card_version_num?: string;
  llm_text_card?: boolean;
  /** 编辑态版本, for 插件预置卡片 */
  edit_version?: number;
  Base?: base.Base;
}

export interface BindPluginPresetCardResponse {
  code?: Int64;
  msg?: string;
  edit_version?: number;
  BaseResp?: base.BaseResp;
}

export interface BuildCardBodyByRespRequest {
  card_id: string;
  mapping_rule: string;
  max_display_rows: string;
  card_version: string;
  connector_id: string;
  example_resp?: string;
  Base?: base.Base;
}

export interface BuildCardBodyByRespResponse {
  code?: Int64;
  msg?: string;
  plugin_card_resp?: plugin_develop_common.PluginCardResp;
  BaseResp?: base.BaseResp;
}

export interface CheckAndLockPluginEditRequest {
  plugin_id: string;
  Base?: base.Base;
}

export interface CheckAndLockPluginEditResponse {
  code: number;
  msg: string;
  data?: plugin_develop_common.CheckAndLockPluginEditData;
  BaseResp?: base.BaseResp;
}

export interface ConfigPluginAsyncRequest {
  /** bot id */
  bot_id: string;
  /** 渠道id，当前只支持coze渠道，为"10000010" */
  connector_id: string;
  /** 插件id，如果为workflow则为workflow id */
  plugin_id: string;
  /** 开关状态 是否打开异步开关 */
  switch_status: boolean;
  /** 异步插件运行时的提示信息，最大1000个字符 */
  message?: string;
  /** 插件tool名称 */
  api_name?: string;
  /** 是否为用户手动配置，添加和移除插件传入false */
  by_user: boolean;
  /** 当前用户所在team id */
  space_id: string;
  Base?: base.Base;
}

export interface ConfigPluginAsyncResponse {
  BaseResp?: base.BaseResp;
}

export interface Convert2OpenAPIRequest {
  plugin_name?: string;
  plugin_url?: string;
  data: string;
  merge_same_paths?: boolean;
  space_id?: string;
  plugin_description?: string;
  Base?: base.Base;
}

export interface Convert2OpenAPIResponse {
  code?: Int64;
  msg?: string;
  openapi?: string;
  ai_plugin?: string;
  plugin_data_format?: plugin_develop_common.PluginDataFormat;
  duplicate_api_infos?: Array<plugin_develop_common.DuplicateAPIInfo>;
  /** BaseResp.StatusCode
DuplicateAPIPath: 导入的文件中有重复的API Path，且 request.MergeSamePaths = false
InvalidParam: 其他错误 */
  BaseResp?: base.BaseResp;
}

export interface CreateAPIRequest {
  /** 第一次调用保存并继续的时候使用这个接口 */
  plugin_id: string;
  name: string;
  desc: string;
  path?: string;
  method?: plugin_develop_common.APIMethod;
  api_extend?: plugin_develop_common.APIExtend;
  request_params?: Array<plugin_develop_common.APIParameter>;
  response_params?: Array<plugin_develop_common.APIParameter>;
  disabled?: boolean;
  /** 编辑态版本 */
  edit_version?: number;
  function_name?: string;
  Base?: base.Base;
}

export interface CreateAPIResponse {
  code?: Int64;
  msg?: string;
  api_id?: string;
  edit_version?: number;
  BaseResp?: base.BaseResp;
}

export interface CreateCardRequest {
  space_id?: string;
  card_id?: string;
  version_num?: string;
  Base?: base.Base;
}

export interface CreateCardResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.CreateCardData;
  BaseResp?: base.BaseResp;
}

export interface CreatePluginFeedbackRequest {
  plugin_id?: string;
  feedback_type?: plugin_develop_common.FeedbackType;
  feedback?: string;
  /** referer */
  Referer?: string;
  Base?: base.Base;
}

export interface CreatePluginFeedbackResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DebugAPIRequest {
  plugin_id: string;
  api_id: string;
  parameters: string;
  /** json */
  operation: plugin_develop_common.DebugOperation;
  edit_version?: number;
  Base?: base.Base;
}

export interface DebugAPIResponse {
  code?: Int64;
  msg?: string;
  response_params?: Array<plugin_develop_common.APIParameter>;
  /** parse时会返回这个字段 */
  success?: boolean;
  resp?: string;
  reason?: string;
  raw_resp?: string;
  raw_req?: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteAPIRequest {
  plugin_id: string;
  api_id: string;
  edit_version?: number;
  Base?: base.Base;
}

export interface DeleteAPIResponse {
  code?: Int64;
  msg?: string;
  edit_version?: number;
  BaseResp?: base.BaseResp;
}

export interface DeleteBotDefaultParamsRequest {
  bot_id?: string;
  dev_id?: string;
  plugin_id?: string;
  api_name?: string;
  /** bot删除工具时: DeleteBot = false , APIName要设置
删除bot时   : DeleteBot = true  , APIName为空 */
  delete_bot?: boolean;
  space_id?: string;
  plugin_referrer_id?: string;
  plugin_referrer_scene?: plugin_develop_common.PluginReferrerScene;
  workflow_id?: string;
  Base?: base.Base;
}

export interface DeleteBotDefaultParamsResponse {
  BaseResp?: base.BaseResp;
}

export interface DeleteCardRequest {
  card_id?: string;
  Base?: base.Base;
}

export interface DeleteCardResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DelPluginRequest {
  plugin_id?: string;
  Base?: base.Base;
}

export interface DelPluginResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetAgentTaskExecListRequest {
  /** agent id */
  bot_id: string;
  /** team id */
  space_id: string;
  /** 任务id */
  trigger_id: string;
  /** 渠道 ，不填写则为全部渠道 */
  connector_ids?: Array<string>;
  /** 触发器用户id */
  trigger_user_id?: string;
  /** 页码，从1开始，不填默认为1 */
  page?: number;
  /** 分页大小，不填默认20 */
  size?: number;
  Base?: base.Base;
}

export interface GetAgentTaskExecListResponse {
  code?: number;
  msg?: string;
  /** 触发器列表 */
  task_exec_list?: Array<plugin_develop_common.AgentTaskExecInfo>;
  /** 页码 */
  page?: number;
  /** 分页大小 */
  size?: number;
  /** 总条数 */
  total?: number;
  BaseResp?: base.BaseResp;
}

export interface GetAgentTaskListRequest {
  /** agent id */
  bot_id: string;
  /** team id */
  space_id: string;
  /** 触发器事件类型，不填代表全部类型 */
  trigger_event_type?: plugin_develop_common.TriggerEventType;
  /** 渠道，不填写则为全部渠道 */
  connector_id?: string;
  /** 任务类型，不填写则为全部类型 */
  trigger_type?: plugin_develop_common.TriggerTaskType;
  /** 触发器名称，不填写则为不按照名称筛选 */
  trigger_name?: string;
  /** 触发器id */
  trigger_id?: string;
  /** 用户触发器用户id */
  trigger_user_id?: string;
  /** 页码，从1开始，不填默认为1 */
  page?: number;
  /** 分页大小，不填默认20 */
  size?: number;
  Base?: base.Base;
}

export interface GetAgentTaskListResponse {
  code?: number;
  msg?: string;
  /** 触发器列表 */
  task_list?: Array<plugin_develop_common.AgentTaskInfo>;
  /** 页码 */
  page?: number;
  /** 分页大小 */
  size?: number;
  /** 总条数 */
  total?: number;
  BaseResp?: base.BaseResp;
}

export interface GetAPIRespStructRequest {
  plugin_id?: string;
  api_name?: string;
  space_id?: string;
  Base?: base.Base;
}

export interface GetAsyncPluginConfigRequest {
  /** bot id */
  bot_id: string;
  /** 渠道id，当前只支持coze渠道，为"10000010" */
  connector_id: string;
  /** 插件id，如果为workflow则为workflow id */
  plugin_id?: string;
  /** 插件tool名称 */
  api_name?: string;
  /** 当前用户所在team id */
  space_id: string;
  Base?: base.Base;
}

export interface GetAsyncPluginConfigResponse {
  data?: plugin_develop_common.AsyncConfig;
  /** bot配置的异步插件总数量 */
  async_config_count?: number;
  BaseResp?: base.BaseResp;
}

export interface GetBindCardsStatusRequest {
  space_id?: string;
  bot_id?: string;
  business_list?: Array<plugin_develop_common.CardBusinessInfo>;
  agent_id?: string;
  using_master?: boolean;
  Base?: base.Base;
}

export interface GetBindCardsStatusResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.BindCardsStatusData;
  BaseResp?: base.BaseResp;
}

export interface GetBotDefaultParamsRequest {
  space_id?: string;
  bot_id?: string;
  dev_id?: string;
  plugin_id?: string;
  api_name?: string;
  plugin_referrer_id?: string;
  plugin_referrer_scene?: plugin_develop_common.PluginReferrerScene;
  plugin_is_debug?: boolean;
  workflow_id?: string;
  plugin_publish_version_ts?: string;
  Base?: base.Base;
}

export interface GetBotDefaultParamsResponse {
  code?: Int64;
  msg?: string;
  request_params?: Array<plugin_develop_common.APIParameter>;
  response_params?: Array<plugin_develop_common.APIParameter>;
  response_style?: plugin_develop_common.ResponseStyle;
  BaseResp?: base.BaseResp;
}

export interface GetCardBindRequest {
  plugin_id?: string;
  api_name?: string;
  bot_id?: string;
  agent_id?: string;
  biz_type?: plugin_develop_common.CardBusinessType;
  business_id?: string;
  unique_id?: string;
  Base?: base.Base;
}

export interface GetCardBindResponse {
  code?: Int64;
  msg?: string;
  mapping_rule?: string;
  card_id?: string;
  max_display_rows?: Int64;
  card_version_num?: string;
  category?: plugin_develop_common.CardCategory;
  llm_text_card?: boolean;
  /** 是否选中了插件预置卡片 */
  plugin_preset_card_selected?: boolean;
  BaseResp?: base.BaseResp;
}

export interface GetCardRespStructRequest {
  biz_type?: plugin_develop_common.CardBizType;
  plugin_id?: string;
  unique_id?: string;
  space_id?: string;
  from_plugin_preset?: boolean;
  Base?: base.Base;
}

export interface GetCardRespStructResponse {
  code?: Int64;
  msg?: string;
  api_struct?: Array<plugin_develop_common.APIStruct>;
  BaseResp?: base.BaseResp;
}

export interface GetDevPluginListRequest {
  status?: Array<plugin_develop_common.PluginStatus>;
  page?: number;
  size?: number;
  dev_id: string;
  space_id?: string;
  scope_type?: plugin_develop_common.ScopeType;
  order_by?: plugin_develop_common.OrderBy;
  /** 发布状态筛选：true:已发布, false:未发布 */
  publish_status?: boolean;
  /** 插件名或工具名 */
  name?: string;
  /** 插件种类筛选 端/云 */
  plugin_type_for_filter?: plugin_develop_common.PluginTypeForFilter;
  project_id?: string;
  /** 插件id列表 */
  plugin_ids?: Array<string>;
  Base?: base.Base;
}

export interface GetDevPluginListResponse {
  code?: number;
  msg?: string;
  plugin_list?: Array<plugin_develop_common.PluginInfoForPlayground>;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface GetNL2APPConfigRequest {
  Base?: base.Base;
}

export interface GetNL2APPConfigResponse {
  /** json格式 */
  config?: string;
  BaseResp?: base.BaseResp;
}

export interface GetOAuthPluginListRequest {
  entity_id: string;
  /** '授权上下文, 0-bot, 1-project,2-workflow' */
  context_type?: number;
  /** 授权实体的版本，根据context_type，可能是 bot_version、project_version、workflow_version */
  entity_version?: string;
  Base?: base.Base;
}

export interface GetOAuthPluginListResponse {
  oauth_plugin_list?: Array<OAuthPluginInfo>;
  BaseResp: base.BaseResp;
}

export interface GetOAuthSchemaRequest {
  Base?: base.Base;
}

export interface GetOAuthSchemaResponse {
  code?: Int64;
  msg?: string;
  oauth_schema?: string;
  ide_conf?: string;
  /** 约定的json */
  BaseResp?: base.BaseResp;
}

export interface GetOAuthStatusRequest {
  plugin_id: string;
  /** '授权上下文, 0-bot, 1-project,2-workflow' */
  context_type?: number;
  /** bot_id, project_id or workflow_id */
  entity_id?: string;
  /** '授权类型，0-单独授权，1-共享授权' */
  auth_mode?: number;
  /** 是否草稿版本，仅共享授权会有草稿版本 */
  is_draft?: boolean;
  /** 是否强制获取授权使用的 client_url，目前仅共享授权场景需要设置为true */
  force_get_client_url?: boolean;
  Base?: base.Base;
}

export interface GetOAuthStatusResponse {
  /** 是否为授权插件 */
  is_oauth?: boolean;
  /** 用户授权状态 */
  status?: plugin_develop_common.OAuthStatus;
  /** 未授权，返回授权url */
  content?: string;
  BaseResp: base.BaseResp;
}

export interface GetPlaygroundPluginListRequest {
  /** 页码 */
  page?: number;
  /** 每页大小 */
  size?: number;
  /** 按照api名称搜索 */
  name?: string;
  /** team id */
  space_id?: string;
  /** 插件id列表 */
  plugin_ids?: Array<string>;
  /** 插件类型筛选 */
  plugin_types?: Array<number>;
  /** 插件渠道 默认获取全部渠道 */
  channel_id?: number;
  /** 是否是自己创建的插件 */
  self_created?: boolean;
  /** 排序 */
  order_by?: number;
  /** 是否获取在渠道下架的插件 临时字段，给wk引用页使用 */
  is_get_offline?: boolean;
  /** referer */
  Referer?: string;
  Base?: base.Base;
}

export interface GetPlaygroundPluginListResponse {
  code: number;
  msg: string;
  data?: plugin_develop_common.GetPlaygroundPluginListData;
  BaseResp?: base.BaseResp;
}

export interface GetPluginAPIsRequest {
  plugin_id: string;
  api_ids?: Array<string>;
  page?: number;
  size?: number;
  order?: plugin_develop_common.APIListOrder;
  preview_version_ts?: string;
  Base?: base.Base;
}

export interface GetPluginAPIsResponse {
  code?: Int64;
  msg?: string;
  api_info?: Array<plugin_develop_common.PluginAPIInfo>;
  total?: number;
  edit_version?: number;
  BaseResp?: base.BaseResp;
}

export interface GetPluginAsyncTaskListRequest {
  /** bot id */
  bot_id: string;
  /** 渠道id，当前只支持coze渠道，为"10000010" */
  connector_id: string;
  /** 当前页 从1开始 */
  page: number;
  /** 页码 */
  size: number;
  /** 当前用户所在team id */
  space_id: string;
  Base?: base.Base;
}

export interface GetPluginAsyncTaskListResponse {
  /** 任务列表 */
  tasks?: Array<plugin_develop_common.AsyncPluginTask>;
  page?: number;
  size?: number;
  total?: number;
  BaseResp?: base.BaseResp;
}

export interface GetPluginCardsRequest {
  space_id?: string;
  bot_id?: string;
  plugins?: Array<plugin_develop_common.APIInfo>;
  agent_id?: string;
  using_master?: boolean;
  Base?: base.Base;
}

export interface GetPluginCardsResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.GetPluginCardsData;
  BaseResp?: base.BaseResp;
}

export interface GetPluginChangelogRequest {
  plugin_id: string;
  page: number;
  size: number;
  Base?: base.Base;
}

export interface GetPluginChangelogResponse {
  code: number;
  msg: string;
  data?: plugin_develop_common.GetPluginChangelogData;
  BaseResp?: base.BaseResp;
}

export interface GetPluginExportIPConfigRequest {
  /** space id */
  space_id: string;
  Base?: base.Base;
}

export interface GetPluginExportIPConfigResponse {
  /** 该space是否支持配置 */
  is_support?: boolean;
  BaseResp?: base.BaseResp;
}

export interface GetPluginInfoRequest {
  /** 目前只支持插件openapi插件的信息 */
  plugin_id: string;
  preview_version_ts?: string;
  Base?: base.Base;
}

export interface GetPluginInfoResponse {
  code?: Int64;
  msg?: string;
  meta_info?: plugin_develop_common.PluginMetaInfo;
  code_info?: plugin_develop_common.CodeInfo;
  status?: boolean;
  /** 0 无更新 1 有更新未发布 */
  published?: boolean;
  /** 是否已发布 */
  creator?: plugin_develop_common.Creator;
  /** 创建人信息 */
  statistic_data?: plugin_develop_common.PluginStatisticData;
  plugin_product_status?: plugin_develop_common.ProductStatus;
  /** 隐私声明状态 */
  privacy_status?: boolean;
  /** 隐私声明内容 */
  privacy_info?: string;
  creation_method?: plugin_develop_common.CreationMethod;
  ide_code_runtime?: string;
  /** 编辑态版本 */
  edit_version?: number;
  plugin_type?: plugin_develop_common.PluginType;
  /** plugin的商品状态 */
  BaseResp?: base.BaseResp;
}

export interface GetPluginListRequest {
  status?: Array<plugin_develop_common.PluginStatus>;
  page?: number;
  size?: number;
  space_id?: string;
  scope_type?: plugin_develop_common.ScopeType;
  order_by?: plugin_develop_common.OrderBy;
  /** 发布状态筛选：true:已发布, false:未发布 */
  publish_status?: boolean;
  /** 插件名或工具名 */
  name?: string;
  plugin_type_for_filter?: plugin_develop_common.PluginTypeForFilter;
  Base?: base.Base;
}

export interface GetPluginListResponse {
  code?: Int64;
  msg?: string;
  data: plugin_develop_common.GetPluginListData;
  BaseResp?: base.BaseResp;
}

export interface GetPluginNextVersionRequest {
  plugin_id: string;
  space_id: string;
  Base?: base.Base;
}

export interface GetPluginNextVersionResponse {
  code?: Int64;
  msg?: string;
  next_version_name?: string;
  BaseResp?: base.BaseResp;
}

export interface GetPluginPresetCardBindRequest {
  plugin_id?: string;
  /** 2: string api_name */
  api_id?: string;
  Base?: base.Base;
}

export interface GetPluginPresetCardBindResponse {
  code?: Int64;
  msg?: string;
  mapping_rule?: string;
  card_id?: string;
  max_display_rows?: Int64;
  card_version_num?: string;
  category?: plugin_develop_common.CardCategory;
  llm_text_card?: boolean;
  BaseResp?: base.BaseResp;
}

export interface GetPluginPresetCardRequest {
  plugin_id?: string;
  api_name?: string;
  Base?: base.Base;
}

export interface GetPluginPresetCardResponse {
  code?: Int64;
  msg?: string;
  /** 插件预置绑定卡片的信息 */
  card_bind_info?: plugin_develop_common.CardBindInfo;
  /** 卡片本身的信息 */
  card_info?: plugin_develop_common.CozeCardInfo;
  card_meta_info?: Array<card.CardMetaInfo>;
  BaseResp?: base.BaseResp;
}

export interface GetPluginPricingRulesByWorkflowIDRequest {
  workflow_id: string;
  space_id: string;
  Base?: base.Base;
}

export interface GetPluginPricingRulesByWorkflowIDResponse {
  pricing_rules?: Array<plugin_develop_common.PluginPricingRule>;
  BaseResp?: base.BaseResp;
}

export interface GetPluginPublishHistoryRequest {
  plugin_id: string;
  space_id: string;
  /** 翻页，第几页 */
  page?: number;
  /** 翻页，每页几条 */
  size?: number;
  Base?: base.Base;
}

export interface GetPluginPublishHistoryResponse {
  code?: Int64;
  msg?: string;
  /** 时间倒序 */
  plugin_publish_info_list?: Array<plugin_develop_common.PluginPublishInfo>;
  /** 总共多少条，大于 page x size 说明还有下一页 */
  total?: number;
  BaseResp?: base.BaseResp;
}

export interface GetPrivateLinkInfoData {
  private_link?: PrivateLink;
}

export interface GetPrivateLinkInfoRequest {
  private_link_id?: string;
  Base?: base.Base;
}

export interface GetPrivateLinkInfoResponse {
  data?: GetPrivateLinkInfoData;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetPublishedPluginListRequest {
  space_id?: string;
  page?: number;
  size?: number;
  user_space_ids?: Array<string>;
  plugin_ids?: Array<string>;
  Base?: base.Base;
}

export interface GetPublishedPluginListResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.PublishedPluginListData;
  BaseResp?: base.BaseResp;
}

export interface GetQueriedOAuthPluginListRequest {
  bot_id: string;
  Base?: base.Base;
}

export interface GetQueriedOAuthPluginListResponse {
  oauth_plugin_list?: Array<OAuthPluginInfo>;
  BaseResp: base.BaseResp;
}

export interface GetUpdatedAPIsRequest {
  plugin_id: string;
  Base?: base.Base;
}

export interface GetUpdatedAPIsResponse {
  code?: Int64;
  msg?: string;
  created_api_names?: Array<string>;
  deleted_api_names?: Array<string>;
  updated_api_names?: Array<string>;
  BaseResp?: base.BaseResp;
}

export interface GetUserAuthorityRequest {
  plugin_id: string;
  creation_method: plugin_develop_common.CreationMethod;
  project_id?: string;
  Base?: base.Base;
}

export interface GetUserAuthorityResponse {
  code: number;
  msg: string;
  data?: plugin_develop_common.GetUserAuthorityData;
  BaseResp?: base.BaseResp;
}

export interface GetWorkflowMessageNodesRequest {
  /** 空间id */
  space_id?: string;
  plugin_id?: string;
  Base?: base.Base;
}

export interface GetWorkflowMessageNodesResponse {
  /** 返回码 */
  code?: Int64;
  /** 返回信息 */
  msg?: string;
  /** 结果 */
  data?: plugin_develop_common.GetWorkflowMessageNodesData;
  BaseResp?: base.BaseResp;
}

export interface NoUpdatedPromptRequest {
  plugin_id: string;
  Base?: base.Base;
}

export interface NoUpdatedPromptResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface OAuthPluginInfo {
  plugin_id?: string;
  /** 用户授权状态 */
  status?: plugin_develop_common.OAuthStatus;
  /** 插件name */
  name?: string;
  /** 插件头像 */
  plugin_icon?: string;
  /** workflow, or bot */
  source_type?: Array<OAuthPluginSource>;
}

export interface OperatePrivateLinkRequest {
  private_link_id?: string;
  /** 操作类型 */
  operate_type?: OperateLinkType;
  Base?: base.Base;
}

export interface OperatePrivateLinkResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface OptimizeParamPromptRequest {
  request_params?: Array<plugin_develop_common.APIParameter>;
  response_params?: Array<plugin_develop_common.APIParameter>;
  api_desc?: string;
  space_id?: string;
  plugin_id: string;
  Base?: base.Base;
}

export interface OptimizeParamPromptResponse {
  /** 优化了参数 Desc 字段 */
  request_params?: Array<plugin_develop_common.APIParameter>;
  /** 优化了参数 Desc 字段 */
  response_params?: Array<plugin_develop_common.APIParameter>;
  BaseResp?: base.BaseResp;
}

export interface OptimizeToolPromptRequest {
  api_desc?: string;
  space_id?: string;
  plugin_id: string;
  Base?: base.Base;
}

export interface OptimizeToolPromptResponse {
  /** 优化后的描述 */
  api_desc?: string;
  BaseResp?: base.BaseResp;
}

export interface PluginAPI2CodeRequest {
  plugin_id: string;
  api_id: string;
  space_id: string;
  dev_id: string;
  program_lang: plugin_develop_common.ProgramLang;
  Base?: base.Base;
}

export interface PluginAPI2CodeResponse {
  code?: Int64;
  msg?: string;
  program_lang?: plugin_develop_common.ProgramLang;
  program_code?: string;
  BaseResp?: base.BaseResp;
}

export interface PrivateLink {
  /** 更新时必传 */
  id?: string;
  /** 名称，只允许更新名称 */
  name?: string;
  /** 终端节点服务id */
  endpoint_service_id?: string;
  /** 终端节点服务域名 */
  domain?: string;
  /** 私网插件访问URL */
  plugin_access_url?: string;
  enterprise_id?: string;
  /** 状态 */
  status?: ConnectStatus;
}

export interface PrivateLinkListData {
  private_links?: Array<PrivateLink>;
}

export interface PrivateLinkListRequest {
  /** 企业ID */
  enterprise_id?: string;
  Base?: base.Base;
}

export interface PrivateLinkListResponse {
  data?: PrivateLinkListData;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface PublishPluginRequest {
  plugin_id: string;
  /** 隐私声明状态 */
  privacy_status?: boolean;
  /** 隐私声明内容 */
  privacy_info?: string;
  version_name?: string;
  version_desc?: string;
  Base?: base.Base;
}

export interface PublishPluginResponse {
  code?: Int64;
  msg?: string;
  version_ts?: string;
  BaseResp?: base.BaseResp;
}

export interface QueryCardDetailRequest {
  space_id?: string;
  card_id?: string;
  card_version?: string;
  Base?: base.Base;
}

export interface QueryCardDetailResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.QueryCardDetailData;
  BaseResp?: base.BaseResp;
}

export interface QueryCardListRequest {
  page?: number;
  size?: number;
  space_id?: string;
  category?: plugin_develop_common.CardCategory;
  bind_card_id?: string;
  status?: plugin_develop_common.QueryCardStatus;
  Base?: base.Base;
}

export interface QueryCardListResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.QueryCardListData;
  BaseResp?: base.BaseResp;
}

export interface QuickBindPluginPresetCardRequest {
  plugin_id?: string;
  api_name?: string;
  bot_id?: string;
  agent_id?: string;
  /** 当前选择的team的ID */
  space_id?: string;
  Base?: base.Base;
}

export interface QuickBindPluginPresetCardResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface RegisterPluginMetaRequest {
  name: string;
  desc: string;
  url?: string;
  icon: plugin_develop_common.PluginIcon;
  auth_type?: plugin_develop_common.AuthorizationType;
  location?: plugin_develop_common.AuthorizationServiceLocation;
  /** service */
  key?: string;
  /** service   Authorization: xxxxxx */
  service_token?: string;
  /** service */
  oauth_info?: string;
  /** json序列化 */
  space_id: string;
  common_params?: Partial<
    Record<
      plugin_develop_common.ParameterLocation,
      Array<plugin_develop_common.commonParamSchema>
    >
  >;
  /** 默认0 默认原来表单创建方式，1 cloud ide创建方式 */
  creation_method?: plugin_develop_common.CreationMethod;
  /** ide创建下的代码编程语言 "1" Node.js "2" Python3 */
  ide_code_runtime?: string;
  plugin_type?: plugin_develop_common.PluginType;
  project_id?: string;
  /** 二级授权类型 */
  sub_auth_type?: number;
  auth_payload?: string;
  /** 设置固定出口ip */
  fixed_export_ip?: boolean;
  /** 私网连接id */
  private_link_id?: string;
  /** 公共参数列表 */
  Base?: base.Base;
}

export interface RegisterPluginMetaResponse {
  code?: Int64;
  msg?: string;
  plugin_id?: string;
  BaseResp?: base.BaseResp;
}

export interface RegisterPluginRequest {
  /** ap_json */
  ai_plugin?: string;
  /** openapi.yaml */
  openapi?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  /** plugin 类型，1 plugin 2=app 3= func */
  plugin_type?: plugin_develop_common.PluginType;
  space_id?: string;
  import_from_file?: boolean;
  project_id?: string;
  Base?: base.Base;
}

export interface RegisterPluginResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.RegisterPluginData;
  BaseResp?: base.BaseResp;
}

export interface RevokeAuthTokenRequest {
  plugin_id: string;
  /** 如果不传使用uid赋值 bot_id = connector_uid */
  bot_id?: string;
  context_type?: number;
  Base?: base.Base;
}

export interface RevokeAuthTokenResponse {
  BaseResp: base.BaseResp;
}

export interface SavePluginRequest {
  plugin_id?: string;
  item_infos?: Array<plugin_develop_common.PluginCurrentInfoItemInfo>;
  Base?: base.Base;
}

export interface SavePluginResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SetAgentTaskStatusRequest {
  /** agent id */
  bot_id: string;
  /** team id */
  space_id: string;
  /** 任务id */
  trigger_id: string;
  /** 渠道id */
  connector_id: string;
  /** 是否置为失效，默认否 */
  set_invalid?: boolean;
  Base?: base.Base;
}

export interface SetAgentTaskStatusResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UnlockPluginEditRequest {
  plugin_id: string;
  Base?: base.Base;
}

export interface UnlockPluginEditResponse {
  code: number;
  msg: string;
  released: boolean;
  BaseResp?: base.BaseResp;
}

export interface UpdateAPIRequest {
  plugin_id: string;
  api_id: string;
  name?: string;
  desc?: string;
  path?: string;
  method?: plugin_develop_common.APIMethod;
  request_params?: Array<plugin_develop_common.APIParameter>;
  response_params?: Array<plugin_develop_common.APIParameter>;
  disabled?: boolean;
  api_extend?: plugin_develop_common.APIExtend;
  /** 编辑态版本 */
  edit_version?: number;
  /** 是否保存调试结果 */
  save_example?: boolean;
  /** 调试结果 */
  debug_example?: plugin_develop_common.DebugExample;
  function_name?: string;
  /** 启用/禁用 */
  Base?: base.Base;
}

export interface UpdateAPIResponse {
  code?: Int64;
  msg?: string;
  edit_version?: number;
  BaseResp?: base.BaseResp;
}

export interface UpdateBotDefaultParamsRequest {
  space_id?: string;
  bot_id?: string;
  dev_id?: string;
  plugin_id?: string;
  api_name?: string;
  request_params?: Array<plugin_develop_common.APIParameter>;
  response_params?: Array<plugin_develop_common.APIParameter>;
  plugin_referrer_id?: string;
  plugin_referrer_scene?: plugin_develop_common.PluginReferrerScene;
  response_style?: plugin_develop_common.ResponseStyle;
  workflow_id?: string;
  Base?: base.Base;
}

export interface UpdateBotDefaultParamsResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UpdatePluginMetaRequest {
  plugin_id: string;
  name?: string;
  desc?: string;
  url?: string;
  icon?: plugin_develop_common.PluginIcon;
  /** uri */
  auth_type?: plugin_develop_common.AuthorizationType;
  location?: plugin_develop_common.AuthorizationServiceLocation;
  /** service */
  key?: string;
  /** service   Authorization: xxxxxx */
  service_token?: string;
  /** service */
  oauth_info?: string;
  /** json序列化 */
  common_params?: Partial<
    Record<
      plugin_develop_common.ParameterLocation,
      Array<plugin_develop_common.commonParamSchema>
    >
  >;
  /** //默认0 默认原来表单创建方式，1 cloud ide创建方式 */
  creation_method?: plugin_develop_common.CreationMethod;
  /** 编辑态版本 */
  edit_version?: number;
  plugin_type?: plugin_develop_common.PluginType;
  /** 二级授权类型 */
  sub_auth_type?: number;
  auth_payload?: string;
  /** 是否配置固定出口ip */
  fixed_export_ip?: boolean;
  /** 私网连接id */
  private_link_id?: string;
  Base?: base.Base;
}

export interface UpdatePluginMetaResponse {
  code?: Int64;
  msg?: string;
  edit_version?: number;
  BaseResp?: base.BaseResp;
}

export interface UpdatePluginRequest {
  plugin_id?: string;
  ai_plugin?: string;
  openapi?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  source_code?: string;
  /** 编辑态版本 */
  edit_version?: number;
  /** 函数代码 */
  Base?: base.Base;
}

export interface UpdatePluginResponse {
  code?: Int64;
  msg?: string;
  data: plugin_develop_common.UpdatePluginData;
  BaseResp?: base.BaseResp;
}

export interface UpsertPluginRequest {
  plugin_id: string;
  space_id: string;
  plugin_desc: string;
  api_desc: string;
  project_id?: string;
  Base?: base.Base;
}

export interface UpsertPluginResponse {
  code?: Int64;
  msg?: string;
  data?: plugin_develop_common.UpsertPluginData;
  BaseResp?: base.BaseResp;
}

export interface UpsertPrivateLinkData {
  /** 创建场景会返回创建成功的ID */
  private_link_id?: string;
}

export interface UpsertPrivateLinkRequest {
  private_link?: PrivateLink;
  Base?: base.Base;
}

export interface UpsertPrivateLinkResponse {
  data?: UpsertPrivateLinkData;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface WakeupIdePluginRequest {
  project_id?: string;
  dev_id?: string;
  space_id?: string;
  Base?: base.Base;
}

export interface WakeupIdePluginResponse {
  code?: number;
  message?: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
