{"name": "@coze-workflow/fabric-canvas", "version": "0.0.1", "description": "fabric-canvas", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.tsx", "./share": "./src/share/index.ts"}, "main": "src/index.tsx", "scripts": {"build": "exit 0", "dev": "storybook dev -p 6006", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/base-adapter": "workspace:*", "@coze-workflow/components": "workspace:*", "@tanstack/react-query": "~5.13.4", "@use-gesture/react": "10.3.1", "ahooks": "^3.7.8", "classnames": "^2.3.2", "fabric": "6.0.0-rc2", "lodash-es": "^4.17.21", "nanoid": "^4.0.2"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tailwind-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@storybook/addon-essentials": "^7.6.7", "@storybook/addon-interactions": "^7.6.7", "@storybook/addon-links": "^7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.7", "@storybook/react": "^7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/test": "^7.6.7", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "storybook": "^7.6.7", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}