/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as op from './namespaces/op';
import * as op_bots from './namespaces/op_bots';
import * as plugin_common from './namespaces/plugin_common';

export { base, bot_common, op, op_bots, plugin_common };
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/op';
export * from './namespaces/op_bots';
export * from './namespaces/plugin_common';

export type Int64 = string | number;

export default class PromptEvaluateApiService<T> {
  private request: any = () => {
    throw new Error('PromptEvaluateApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/op_bots/get_op_explore_bot */
  GetOpExploreBotList(
    req?: op_bots.GetOpExploreBotListRequest,
    options?: T,
  ): Promise<op_bots.GetOpExploreBotListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_op_explore_bot');
    const method = 'POST';
    const data = {
      bot_status: _req['bot_status'],
      key_word: _req['key_word'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      category_id: _req['category_id'],
      uncategorized: _req['uncategorized'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/get_op_all_bot */
  GetOpAllBotList(
    req?: op_bots.GetOpAllBotListRequest,
    options?: T,
  ): Promise<op_bots.GetOpAllBotListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_op_all_bot');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_name: _req['bot_name'],
      publish_platform: _req['publish_platform'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      is_publish: _req['is_publish'],
      bot_id: _req['bot_id'],
      batch_bot_id: _req['batch_bot_id'],
      connector_id: _req['connector_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/get_space_list */
  GetSpaceList(
    req?: op_bots.GetOpSpaceListRequest,
    options?: T,
  ): Promise<op_bots.GetOpSpaceListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_space_list');
    const method = 'POST';
    const data = { name: _req['name'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/logout */
  Logout(
    req?: op_bots.LogoutRequest,
    options?: T,
  ): Promise<op_bots.LogoutResponse> {
    const url = this.genBaseURL('/api/op_bots/logout');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/op_bots/get_sso_user_info */
  GetSSOUserInfo(
    req?: op_bots.GetSSOUserInfoRequest,
    options?: T,
  ): Promise<op_bots.GetSSOUserInfoResponse> {
    const url = this.genBaseURL('/api/op_bots/get_sso_user_info');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/op_bots/get_icon */
  GetIcon(
    req?: op_bots.GetIconRequest,
    options?: T,
  ): Promise<op_bots.GetIconResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_icon');
    const method = 'POST';
    const data = { icon_type: _req['icon_type'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/duplicate_explore_bot */
  DuplicateExploreBot(
    req?: op_bots.DuplicateExploreBotRequest,
    options?: T,
  ): Promise<op_bots.DuplicateExploreBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/duplicate_explore_bot');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/update_explore_bot */
  UpdateExploreBot(
    req: op_bots.UpdateExploreBotRequest,
    options?: T,
  ): Promise<op_bots.UpdateExploreBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/op_bots/update_explore_bot');
    const method = 'POST';
    const data = {
      explore_bot_id: _req['explore_bot_id'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
      index: _req['index'],
      explore_status: _req['explore_status'],
      del_status: _req['del_status'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/grant_bot_qualification
   *
   * 授权
   */
  GrantBotQualification(
    req?: op_bots.GrantBotQualificationRequest,
    options?: T,
  ): Promise<op_bots.GrantBotQualificationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/grant_bot_qualification');
    const method = 'POST';
    const data = {
      total: _req['total'],
      timestamp: _req['timestamp'],
      user_ids: _req['user_ids'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/list_waiting_queue
   *
   * -----------wait list-----------
   *
   * 获取当前列表
   */
  ListWaitingQueue(
    req: op_bots.ListWaitingQueueRequest,
    options?: T,
  ): Promise<op_bots.ListWaitingQueueResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/op_bots/list_waiting_queue');
    const method = 'POST';
    const data = {
      page_size: _req['page_size'],
      page_no: _req['page_no'],
      wait_status: _req['wait_status'],
      email: _req['email'],
      user_id: _req['user_id'],
      mobile: _req['mobile'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/get_wait_list_statistical
   *
   * 获取统计信息
   */
  GetWaitListStatistical(
    req?: op_bots.GetWaitListStatisticalRequest,
    options?: T,
  ): Promise<op_bots.GetWaitListStatisticalResponse> {
    const url = this.genBaseURL('/api/op_bots/get_wait_list_statistical');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/op_bots/fetch_message_ids
   *
   * ---------prompt-----------
   */
  FetchMsgList(
    req?: op_bots.FetchMsgListRequest,
    options?: T,
  ): Promise<op_bots.FetchMsgListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/fetch_message_ids');
    const method = 'POST';
    const data = {
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      content: _req['content'],
      message_id: _req['message_id'],
      device_id: _req['device_id'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      user_id: _req['user_id'],
      log_id: _req['log_id'],
      conn_id: _req['conn_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/bot_info_compare */
  BotInfoCompare(
    req?: op_bots.BotInfoCompareRequest,
    options?: T,
  ): Promise<op_bots.BotInfoCompareResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/bot_info_compare');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], conn_id: _req['conn_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/batch_update_explore_bot */
  BatchUpdateExploreBot(
    req: op_bots.BatchUpdateExploreBotRequest,
    options?: T,
  ): Promise<op_bots.BatchUpdateExploreBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/op_bots/batch_update_explore_bot');
    const method = 'POST';
    const data = {
      explore_status: _req['explore_status'],
      explore_bot: _req['explore_bot'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/op_bots/get_category_list
   *
   * 获取分类列表
   */
  GetCategoryList(
    req?: op_bots.GetCategoryListRequest,
    options?: T,
  ): Promise<op_bots.GetCategoryListResponse> {
    const url = this.genBaseURL('/api/op_bots/get_category_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/op_bots/save_category
   *
   * 保存分类
   */
  SaveCategory(
    req?: op_bots.SaveCategoryRequest,
    options?: T,
  ): Promise<op_bots.SaveCategoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/save_category');
    const method = 'POST';
    const data = { categorys: _req['categorys'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/fetch_chain_events */
  GetChainEventList(
    req?: op_bots.FetchChainEventRequest,
    options?: T,
  ): Promise<op_bots.FetchChainEventListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/fetch_chain_events');
    const method = 'POST';
    const data = {
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      offset: _req['offset'],
      limit: _req['limit'],
      root_id: _req['root_id'],
      log_id: _req['log_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/migrate_bot */
  MigrateBot(
    req?: op_bots.MigrateBotRequest,
    options?: T,
  ): Promise<op_bots.MigrateBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/draftbot/migrate_bot');
    const method = 'POST';
    const data = {
      bot_ids: _req['bot_ids'],
      target_space_id: _req['target_space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/get_bot_migrate_progress */
  GetBotMigrateProgress(
    req?: op_bots.GetBotMigrateProgressRequest,
    options?: T,
  ): Promise<op_bots.GetBotMigrateProgressResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/draftbot/get_bot_migrate_progress');
    const method = 'POST';
    const data = {
      target_space_id: _req['target_space_id'],
      bot_id: _req['bot_id'],
      page: _req['page'],
      size: _req['size'],
      final_status: _req['final_status'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/retry_mig_task */
  RetryMigTask(
    req?: op_bots.RetryMigTaskRequest,
    options?: T,
  ): Promise<op_bots.RetryMigTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/draftbot/retry_mig_task');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/plugin/update_status */
  UpdatePluginStatus(
    req: op_bots.UpdatePluginStatusRequest,
    options?: T,
  ): Promise<op_bots.UpdatePluginStatusResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/op_bots/plugin/update_status');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/plugin/transfer_space */
  MSetPluginSpaceByIDs(
    req: op_bots.MSetPluginSpaceByIDsRequest,
    options?: T,
  ): Promise<op_bots.MSetPluginSpaceByIDsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/op_bots/plugin/transfer_space');
    const method = 'POST';
    const data = {
      plugin_ids: _req['plugin_ids'],
      target_space_id: _req['target_space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/plugin/list */
  GetPluginList(
    req?: op_bots.GetPluginListRequest,
    options?: T,
  ): Promise<op_bots.GetPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/plugin/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      status: _req['status'],
      plugin_ids: _req['plugin_ids'],
      space_id: _req['space_id'],
      plugin_name: _req['plugin_name'],
      order_by: _req['order_by'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/update_api_key */
  UpdateApiKey(
    req?: op_bots.UpdateApiKeyRequest,
    options?: T,
  ): Promise<op_bots.UpdateApiKeyResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/update_api_key');
    const method = 'POST';
    const data = {
      api_key_id: _req['api_key_id'],
      operate_type: _req['operate_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/get_api_key
   *
   * -------api_key-------
   */
  GetApiKeyList(
    req?: op_bots.GetApiKeyListRequest,
    options?: T,
  ): Promise<op_bots.GetApiKeyListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_api_key');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/official-plugin/publish */
  PublishOfficialPlugin(
    req: op_bots.PublishOfficialPluginRequest,
    options?: T,
  ): Promise<op_bots.PublishOfficialPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/official-plugin/publish');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/official-plugin/delete */
  DeleteOfficialPlugin(
    req: op_bots.DeleteOfficialPluginRequest,
    options?: T,
  ): Promise<op_bots.DeleteOfficialPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/op_bots/official-plugin/delete');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/official-plugin/offline */
  OfflineOfficialPlugin(
    req: op_bots.OfflineOfficialPluginRequest,
    options?: T,
  ): Promise<op_bots.OfflineOfficialPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/official-plugin/offline');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/official-plugin/online */
  OnlineOfficialPlugin(
    req: op_bots.OnlineOfficialPluginRequest,
    options?: T,
  ): Promise<op_bots.OnlineOfficialPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/official-plugin/online');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/official-plugin/cancel-publish */
  CancelPublishOfficialPlugin(
    req: op_bots.CancelPublishOfficialPluginRequest,
    options?: T,
  ): Promise<op_bots.CancelPublishOfficialPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/official-plugin/cancel-publish');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/op_bots/get_conn_id */
  GetConnId(
    req?: op_bots.GetConnIdRequest,
    options?: T,
  ): Promise<op_bots.GetConnIdResponse> {
    const url = this.genBaseURL('/api/op_bots/get_conn_id');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/op_bots/update_allow_domain
   *
   * bots plugin
   */
  UpdateAllowDomain(
    req?: op_bots.UpdateAllowDomainRequest,
    options?: T,
  ): Promise<op_bots.UpdateAllowDomainResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/update_allow_domain');
    const method = 'POST';
    const data = { domain: _req['domain'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/op_bots/mget_user_uid
   *
   * ---------op_bot----------------------
   */
  MGetUserUID(
    req?: op_bots.MGetUserUIDRequest,
    options?: T,
  ): Promise<op_bots.MGetUserUIDResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/mget_user_uid');
    const method = 'GET';
    const params = { uid_list: _req['uid_list'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/op_bots/search_user_uid */
  SearchUserUID(
    req?: op_bots.SearchUserUIDRequest,
    options?: T,
  ): Promise<op_bots.SearchUserUIDResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/search_user_uid');
    const method = 'GET';
    const params = { username: _req['username'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/op_bots/get_all_user_label
   *
   * 标签管理和用户标签配置
   *
   * 显示所有标签
   */
  GetAllUserLabel(
    req?: op_bots.GetAllUserLabelRequest,
    options?: T,
  ): Promise<op_bots.GetAllUserLabelResponse> {
    const url = this.genBaseURL('/api/op_bots/get_all_user_label');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/op_bots/save_user_label
   *
   * 创建或更新标签
   */
  SaveUserLabel(
    req?: op_bots.SaveUserLabelRequest,
    options?: T,
  ): Promise<op_bots.SaveUserLabelResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/save_user_label');
    const method = 'POST';
    const data = { user_label: _req['user_label'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/update_user_label
   *
   * 更新用户标签
   */
  UpdateUserLabel(
    req?: op_bots.UpdateUserLabelRequest,
    options?: T,
  ): Promise<op_bots.UpdateUserLabelResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/update_user_label');
    const method = 'POST';
    const data = { user_ids: _req['user_ids'], label_id: _req['label_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/delete_user_label
   *
   * 删除标签
   */
  DeleteUserLabel(
    req?: op_bots.DeleteUserLabelRequest,
    options?: T,
  ): Promise<op_bots.DeleteUserLabelResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/delete_user_label');
    const method = 'POST';
    const data = { label_id: _req['label_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/get_labelled_user
   *
   * 显示已配置标签的用户
   */
  GetLabelledUser(
    req?: op_bots.GetLabelledUserRequest,
    options?: T,
  ): Promise<op_bots.GetLabelledUserResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_labelled_user');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      user_id: _req['user_id'],
      user_unique_name: _req['user_unique_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/mget_user_label_info
   *
   * 根据ID或者Name查找用户信息
   */
  MGetUserLabelInfo(
    req?: op_bots.MGetUserLabelInfoRequest,
    options?: T,
  ): Promise<op_bots.MGetUserLabelInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/mget_user_label_info');
    const method = 'POST';
    const data = {
      query_condition: _req['query_condition'],
      query_type: _req['query_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/upload_file
   *
   * 上传图片
   */
  UploadFile(
    req?: op_bots.UploadFileRequest,
    options?: T,
  ): Promise<op_bots.UploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/upload_file');
    const method = 'POST';
    const data = { file_head: _req['file_head'], data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/intent_case/detail
   *
   * 意图识别获取case详情
   */
  GetIntentCaseDetail(
    req?: op_bots.GetIntentCaseDetailRequest,
    options?: T,
  ): Promise<op_bots.GetIntentCaseDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/intent_case/detail');
    const method = 'POST';
    const data = {
      from_date: _req['from_date'],
      to_date: _req['to_date'],
      agent_id: _req['agent_id'],
      case_types: _req['case_types'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/intent_case/stats
   *
   * 意图识别case统计
   */
  GetIntentCaseStats(
    req?: op_bots.GetIntentCaseStatsRequest,
    options?: T,
  ): Promise<op_bots.GetIntentCaseStatsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/intent_case/stats');
    const method = 'POST';
    const data = {
      from_date: _req['from_date'],
      to_date: _req['to_date'],
      agent_id: _req['agent_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_bots/intent_case/export
   *
   * 意图识别抽样case导出
   */
  ExportIntentCase(
    req?: op_bots.ExportIntentCaseRequest,
    options?: T,
  ): Promise<op_bots.ExportIntentCaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/intent_case/export');
    const method = 'POST';
    const data = {
      from_date: _req['from_date'],
      to_date: _req['to_date'],
      is_dora: _req['is_dora'],
      data_source: _req['data_source'],
      session_num: _req['session_num'],
      plugin_name: _req['plugin_name'],
      intent_case_type: _req['intent_case_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/op_bots/get_user_info
   *
   * 获取用户信息
   */
  GetUserInfo(
    req?: op.OpGetUserInfoRequest,
    options?: T,
  ): Promise<op.OpGetUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_bots/get_user_info');
    const method = 'GET';
    const params = { user_id: _req['user_id'] };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
