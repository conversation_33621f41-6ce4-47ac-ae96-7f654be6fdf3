/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateVoicePrintGroupData {
  id?: string;
}

export interface CreateVoicePrintGroupFeatureData {
  id?: string;
}

export interface CreateVoicePrintGroupFeatureRequest {
  group_id?: Int64;
  /** 文件类型 */
  'Content-Type': string;
  /** 二进制数据 */
  body: Blob;
}

export interface CreateVoicePrintGroupFeatureResponse {
  code?: number;
  msg?: string;
  data?: CreateVoicePrintGroupFeatureData;
}

export interface CreateVoicePrintGroupRequest {
  name?: string;
  desc?: string;
  coze_account_id?: string;
}

export interface CreateVoicePrintGroupResponse {
  code?: number;
  msg?: string;
  data?: CreateVoicePrintGroupData;
}

export interface DeleteVoicePrintGroupFeatureRequest {
  group_id?: Int64;
  feature_id?: Int64;
}

export interface DeleteVoicePrintGroupFeatureResponse {
  code?: number;
  msg?: string;
}

export interface DeleteVoicePrintGroupRequest {
  group_id?: Int64;
}

export interface DeleteVoicePrintGroupResponse {
  code?: number;
  msg?: string;
}

export interface FeatureScore {
  feature_id?: string;
  feature_name?: string;
  feature_desc?: string;
  score?: number;
}

export interface GetVoicePrintGroupFeatureListData {
  items?: Array<VoicePrintGroupFeature>;
  total?: Int64;
}

export interface GetVoicePrintGroupFeatureListRequest {
  group_id?: Int64;
  page_num?: Int64;
  page_size?: Int64;
}

export interface GetVoicePrintGroupFeatureListResponse {
  code?: number;
  msg?: string;
  data?: GetVoicePrintGroupFeatureListData;
}

export interface GetVoicePrintGroupListData {
  items?: Array<VoicePrintGroup>;
  total?: Int64;
}

export interface GetVoicePrintGroupListRequest {
  page_num?: Int64;
  page_size?: Int64;
  /** 模糊前缀匹配 */
  name?: string;
  /** 匹配用户ID */
  user_id?: string;
  /** 声纹组ID */
  group_id?: string;
  /** 账号ID */
  coze_account_id?: string;
}

export interface GetVoicePrintGroupListResponse {
  code?: number;
  msg?: string;
  data?: GetVoicePrintGroupListData;
}

export interface SpeakerIdentifyData {
  feature_score_list?: Array<FeatureScore>;
}

export interface SpeakerIdentifyRequest {
  group_id?: Int64;
  /** 文件类型 */
  'Content-Type': string;
  /** 二进制数据 */
  body: Blob;
}

export interface SpeakerIdentifyResponse {
  code?: number;
  msg?: string;
  data?: SpeakerIdentifyData;
}

export interface UpdateVoicePrintGroupFeatureRequest {
  group_id?: Int64;
  feature_id?: Int64;
  /** 文件类型 */
  'Content-Type': string;
  /** 二进制数据 */
  body: Blob;
}

export interface UpdateVoicePrintGroupFeatureResponse {
  code?: number;
  msg?: string;
}

export interface UpdateVoicePrintGroupRequest {
  group_id?: Int64;
  name?: string;
  desc?: string;
}

export interface UpdateVoicePrintGroupResponse {
  code?: number;
  msg?: string;
}

/** -------------------- VoicePrint Group Manage ------------------ */
export interface VoicePrintGroup {
  id?: string;
  name?: string;
  desc?: string;
  created_at?: Int64;
  updated_at?: Int64;
  icon_url?: string;
  user_info?: common.UserInfo;
  feature_count?: number;
}

export interface VoicePrintGroupFeature {
  id?: string;
  group_id?: string;
  name?: string;
  audio_url?: string;
  created_at?: Int64;
  updated_at?: Int64;
  desc?: string;
  icon_url?: string;
  user_info?: common.UserInfo;
}
/* eslint-enable */
