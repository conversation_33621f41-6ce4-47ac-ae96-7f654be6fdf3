/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum IntelligenceTaskActionEventMsgEventType {
  TaskSuccess = 1,
  TaskFailed = 2,
  TaskCanceled = 3,
}

export enum IntelligenceTaskActionType {
  Copy = 1,
  Move = 2,
  Publish = 3,
}

export enum IntelligenceTaskEntityLocationType {
  Project = 1,
  Space = 2,
  Online = 3,
  Template = 4,
}

export enum IntelligenceTaskEntityType {
  Plugin = 1,
  Workflow = 2,
  Imageflow = 3,
  Knowledge = 4,
  UI = 5,
  Project = 6,
  Database = 7,
  Variable = 8,
}

export enum IntelligenceTaskStatus {
  Success = 1,
  Processing = 2,
  Failed = 3,
  Canceled = 4,
}

export enum IntelligenceTaskType {
  /** 复制项目内的资源到同项目 */
  CopyResourceInProject = 1,
  /** 复制项目资源到Library */
  CopyProjectResourceToLibrary = 2,
  /** 移动项目资源到Library */
  MoveProjectResourceToLibrary = 3,
  /** 复制Library资源到项目 */
  CopyLibraryResourceToProject = 4,
  /** 复制项目 */
  CopyProject = 5,
  /** 项目发布到渠道 */
  PublishProject = 6,
  /** 复制项目模板 */
  CopyTemplateToProject = 7,
  /** 项目发布到模板 */
  PublishProjectTemplate = 8,
  /** 上架模板 */
  LaunchTemplate = 9,
  /** 草稿版本存档 */
  ArchiveProject = 10,
  /** 线上版本加载到草稿，草稿版本加载到草稿 */
  RollbackProject = 11,
  /** 单个资源跨空间复制 */
  CrossSpaceCopy = 12,
  /** 项目跨空间复制 */
  CrossSpaceCopyProject = 13,
}

export interface FailedReasonDetail {
  /** 失败原因 */
  FailedReason?: string;
  /** 操作实体id */
  EntityId?: Int64;
  /** 操作实体类型 */
  EntityType?: IntelligenceTaskEntityType;
  /** 实体名称 */
  EntityName?: string;
}

export interface IntelligenceTaskEntityLocationInfo {
  /** '位置类型' */
  LocationType?: IntelligenceTaskEntityLocationType;
  /** '位置空间id' */
  SpaceId?: Int64;
  /** '位置project id' */
  ProjectId?: Int64;
  /** '位置实体version' */
  Version?: Int64;
}

/** task资源方实现接口定义,外部可引用 */
export interface IntelligenceTaskInfo {
  /** 任务id */
  TaskId?: Int64;
  /** 任务创建者id */
  UserId?: Int64;
  /** 操作实体类型 */
  EntityType?: IntelligenceTaskEntityType;
  /** 操作实体id */
  EntityId?: Int64;
  /** '操作类型' */
  ActionType?: IntelligenceTaskActionType;
  /** '源位置信息 */
  SourceLocationInfo?: IntelligenceTaskEntityLocationInfo;
  /** '目标位置信息' */
  TargetLocationInfo?: IntelligenceTaskEntityLocationInfo;
  /** '业务透传字段 json string' */
  Extra?: string;
  /** 任务状态 */
  Status?: IntelligenceTaskStatus;
  /** 重试次数 */
  RetryNum?: number;
  /** 失败原因汇总 */
  FailedReasons?: Array<FailedReasonDetail>;
  /** 项目类型 */
  TaskType?: IntelligenceTaskType;
  /** 创建时间 */
  CreateTime?: Int64;
  /** 更新时间 */
  UpdateTime?: Int64;
}
/* eslint-enable */
