/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as card from './namespaces/card';
import * as card_resource_common from './namespaces/card_resource_common';
import * as copilot_common from './namespaces/copilot_common';
import * as permission from './namespaces/permission';
import * as plugin_develop from './namespaces/plugin_develop';
import * as plugin_develop_common from './namespaces/plugin_develop_common';
import * as plugin_openapi from './namespaces/plugin_openapi';
import * as resource from './namespaces/resource';
import * as resource_resource_common from './namespaces/resource_resource_common';
import * as retriever from './namespaces/retriever';
import * as shortcut_command from './namespaces/shortcut_command';
import * as task_common from './namespaces/task_common';

export {
  base,
  bot_common,
  card,
  card_resource_common,
  copilot_common,
  permission,
  plugin_develop,
  plugin_develop_common,
  plugin_openapi,
  resource,
  resource_resource_common,
  retriever,
  shortcut_command,
  task_common,
};
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/card';
export * from './namespaces/card_resource_common';
export * from './namespaces/copilot_common';
export * from './namespaces/permission';
export * from './namespaces/plugin_develop';
export * from './namespaces/plugin_develop_common';
export * from './namespaces/plugin_openapi';
export * from './namespaces/resource';
export * from './namespaces/resource_resource_common';
export * from './namespaces/retriever';
export * from './namespaces/shortcut_command';
export * from './namespaces/task_common';

export type Int64 = string | number;

export default class PluginDevelopService<T> {
  private request: any = () => {
    throw new Error('PluginDevelopService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/plugin_api/register */
  RegisterPlugin(
    req?: plugin_develop.RegisterPluginRequest,
    options?: T,
  ): Promise<plugin_develop.RegisterPluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/register');
    const method = 'POST';
    const data = {
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      client_id: _req['client_id'],
      client_secret: _req['client_secret'],
      service_token: _req['service_token'],
      plugin_type: _req['plugin_type'],
      space_id: _req['space_id'],
      import_from_file: _req['import_from_file'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/update */
  UpdatePlugin(
    req?: plugin_develop.UpdatePluginRequest,
    options?: T,
  ): Promise<plugin_develop.UpdatePluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/update');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      client_id: _req['client_id'],
      client_secret: _req['client_secret'],
      service_token: _req['service_token'],
      source_code: _req['source_code'],
      edit_version: _req['edit_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/del_plugin */
  DelPlugin(
    req?: plugin_develop.DelPluginRequest,
    options?: T,
  ): Promise<plugin_develop.DelPluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/del_plugin');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/register_plugin_meta */
  RegisterPluginMeta(
    req: plugin_develop.RegisterPluginMetaRequest,
    options?: T,
  ): Promise<plugin_develop.RegisterPluginMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/register_plugin_meta');
    const method = 'POST';
    const data = {
      name: _req['name'],
      desc: _req['desc'],
      url: _req['url'],
      icon: _req['icon'],
      auth_type: _req['auth_type'],
      location: _req['location'],
      key: _req['key'],
      service_token: _req['service_token'],
      oauth_info: _req['oauth_info'],
      space_id: _req['space_id'],
      common_params: _req['common_params'],
      creation_method: _req['creation_method'],
      ide_code_runtime: _req['ide_code_runtime'],
      plugin_type: _req['plugin_type'],
      project_id: _req['project_id'],
      sub_auth_type: _req['sub_auth_type'],
      auth_payload: _req['auth_payload'],
      fixed_export_ip: _req['fixed_export_ip'],
      private_link_id: _req['private_link_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/update_api */
  UpdateAPI(
    req: plugin_develop.UpdateAPIRequest,
    options?: T,
  ): Promise<plugin_develop.UpdateAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/update_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      name: _req['name'],
      desc: _req['desc'],
      path: _req['path'],
      method: _req['method'],
      request_params: _req['request_params'],
      response_params: _req['response_params'],
      disabled: _req['disabled'],
      api_extend: _req['api_extend'],
      edit_version: _req['edit_version'],
      save_example: _req['save_example'],
      debug_example: _req['debug_example'],
      function_name: _req['function_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/delete_api */
  DeleteAPI(
    req: plugin_develop.DeleteAPIRequest,
    options?: T,
  ): Promise<plugin_develop.DeleteAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/delete_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      edit_version: _req['edit_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/debug_api */
  DebugAPI(
    req: plugin_develop.DebugAPIRequest,
    options?: T,
  ): Promise<plugin_develop.DebugAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/debug_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      parameters: _req['parameters'],
      operation: _req['operation'],
      edit_version: _req['edit_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/create_api */
  CreateAPI(
    req: plugin_develop.CreateAPIRequest,
    options?: T,
  ): Promise<plugin_develop.CreateAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/create_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      name: _req['name'],
      desc: _req['desc'],
      path: _req['path'],
      method: _req['method'],
      api_extend: _req['api_extend'],
      request_params: _req['request_params'],
      response_params: _req['response_params'],
      disabled: _req['disabled'],
      edit_version: _req['edit_version'],
      function_name: _req['function_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_plugin_list */
  GetPluginList(
    req?: plugin_develop.GetPluginListRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_plugin_list');
    const method = 'POST';
    const data = {
      status: _req['status'],
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      scope_type: _req['scope_type'],
      order_by: _req['order_by'],
      publish_status: _req['publish_status'],
      name: _req['name'],
      plugin_type_for_filter: _req['plugin_type_for_filter'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/update_plugin_meta */
  UpdatePluginMeta(
    req: plugin_develop.UpdatePluginMetaRequest,
    options?: T,
  ): Promise<plugin_develop.UpdatePluginMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/update_plugin_meta');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      name: _req['name'],
      desc: _req['desc'],
      url: _req['url'],
      icon: _req['icon'],
      auth_type: _req['auth_type'],
      location: _req['location'],
      key: _req['key'],
      service_token: _req['service_token'],
      oauth_info: _req['oauth_info'],
      common_params: _req['common_params'],
      creation_method: _req['creation_method'],
      edit_version: _req['edit_version'],
      plugin_type: _req['plugin_type'],
      sub_auth_type: _req['sub_auth_type'],
      auth_payload: _req['auth_payload'],
      fixed_export_ip: _req['fixed_export_ip'],
      private_link_id: _req['private_link_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/publish_plugin */
  PublishPlugin(
    req: plugin_develop.PublishPluginRequest,
    options?: T,
  ): Promise<plugin_develop.PublishPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/publish_plugin');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      privacy_status: _req['privacy_status'],
      privacy_info: _req['privacy_info'],
      version_name: _req['version_name'],
      version_desc: _req['version_desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_plugin_info */
  GetPluginInfo(
    req: plugin_develop.GetPluginInfoRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_plugin_info');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      preview_version_ts: _req['preview_version_ts'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_published_list */
  GetPublishedPluginList(
    req?: plugin_develop.GetPublishedPluginListRequest,
    options?: T,
  ): Promise<plugin_develop.GetPublishedPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_published_list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      size: _req['size'],
      user_space_ids: _req['user_space_ids'],
      plugin_ids: _req['plugin_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_plugin_apis */
  GetPluginAPIs(
    req: plugin_develop.GetPluginAPIsRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginAPIsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_plugin_apis');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_ids: _req['api_ids'],
      page: _req['page'],
      size: _req['size'],
      order: _req['order'],
      preview_version_ts: _req['preview_version_ts'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_updated_apis */
  GetUpdatedAPIs(
    req: plugin_develop.GetUpdatedAPIsRequest,
    options?: T,
  ): Promise<plugin_develop.GetUpdatedAPIsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_updated_apis');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/no_updated_prompt */
  NoUpdatedPrompt(
    req: plugin_develop.NoUpdatedPromptRequest,
    options?: T,
  ): Promise<plugin_develop.NoUpdatedPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/no_updated_prompt');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_oauth_schema */
  GetOAuthSchema(
    req?: plugin_develop.GetOAuthSchemaRequest,
    options?: T,
  ): Promise<plugin_develop.GetOAuthSchemaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_oauth_schema');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/save_plugin */
  SavePlugin(
    req?: plugin_develop.SavePluginRequest,
    options?: T,
  ): Promise<plugin_develop.SavePluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/save_plugin');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      item_infos: _req['item_infos'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/plugin_api_to_code */
  PluginAPI2Code(
    req: plugin_develop.PluginAPI2CodeRequest,
    options?: T,
  ): Promise<plugin_develop.PluginAPI2CodeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/plugin_api_to_code');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      space_id: _req['space_id'],
      dev_id: _req['dev_id'],
      program_lang: _req['program_lang'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/convert_to_openapi */
  Convert2OpenAPI(
    req: plugin_develop.Convert2OpenAPIRequest,
    options?: T,
  ): Promise<plugin_develop.Convert2OpenAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/convert_to_openapi');
    const method = 'POST';
    const data = {
      plugin_name: _req['plugin_name'],
      plugin_url: _req['plugin_url'],
      data: _req['data'],
      merge_same_paths: _req['merge_same_paths'],
      space_id: _req['space_id'],
      plugin_description: _req['plugin_description'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/batch_create_api */
  BatchCreateAPI(
    req?: plugin_develop.BatchCreateAPIRequest,
    options?: T,
  ): Promise<plugin_develop.BatchCreateAPIResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/batch_create_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      space_id: _req['space_id'],
      dev_id: _req['dev_id'],
      replace_same_paths: _req['replace_same_paths'],
      paths_to_replace: _req['paths_to_replace'],
      edit_version: _req['edit_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_playground_plugin_list */
  GetPlaygroundPluginList(
    req?: plugin_develop.GetPlaygroundPluginListRequest,
    options?: T,
  ): Promise<plugin_develop.GetPlaygroundPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_playground_plugin_list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      name: _req['name'],
      space_id: _req['space_id'],
      plugin_ids: _req['plugin_ids'],
      plugin_types: _req['plugin_types'],
      channel_id: _req['channel_id'],
      self_created: _req['self_created'],
      order_by: _req['order_by'],
      is_get_offline: _req['is_get_offline'],
      Base: _req['Base'],
    };
    const headers = { Referer: _req['Referer'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/plugin_api/get_bot_default_params
   *
   * bot引用plugin
   */
  GetBotDefaultParams(
    req?: plugin_develop.GetBotDefaultParamsRequest,
    options?: T,
  ): Promise<plugin_develop.GetBotDefaultParamsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_bot_default_params');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      dev_id: _req['dev_id'],
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      plugin_referrer_id: _req['plugin_referrer_id'],
      plugin_referrer_scene: _req['plugin_referrer_scene'],
      plugin_is_debug: _req['plugin_is_debug'],
      workflow_id: _req['workflow_id'],
      plugin_publish_version_ts: _req['plugin_publish_version_ts'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/update_bot_default_params */
  UpdateBotDefaultParams(
    req?: plugin_develop.UpdateBotDefaultParamsRequest,
    options?: T,
  ): Promise<plugin_develop.UpdateBotDefaultParamsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/update_bot_default_params');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      dev_id: _req['dev_id'],
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      request_params: _req['request_params'],
      response_params: _req['response_params'],
      plugin_referrer_id: _req['plugin_referrer_id'],
      plugin_referrer_scene: _req['plugin_referrer_scene'],
      response_style: _req['response_style'],
      workflow_id: _req['workflow_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/check_and_lock_plugin_edit */
  CheckAndLockPluginEdit(
    req: plugin_develop.CheckAndLockPluginEditRequest,
    options?: T,
  ): Promise<plugin_develop.CheckAndLockPluginEditResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/check_and_lock_plugin_edit');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/unlock_plugin_edit */
  UnlockPluginEdit(
    req: plugin_develop.UnlockPluginEditRequest,
    options?: T,
  ): Promise<plugin_develop.UnlockPluginEditResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/unlock_plugin_edit');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_user_authority */
  GetUserAuthority(
    req: plugin_develop.GetUserAuthorityRequest,
    options?: T,
  ): Promise<plugin_develop.GetUserAuthorityResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_user_authority');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      creation_method: _req['creation_method'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_plugin_changelog */
  GetPluginChangelog(
    req: plugin_develop.GetPluginChangelogRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginChangelogResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_plugin_changelog');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/delete_bot_default_params */
  DeleteBotDefaultParams(
    req?: plugin_develop.DeleteBotDefaultParamsRequest,
    options?: T,
  ): Promise<plugin_develop.DeleteBotDefaultParamsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/delete_bot_default_params');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      dev_id: _req['dev_id'],
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      delete_bot: _req['delete_bot'],
      space_id: _req['space_id'],
      plugin_referrer_id: _req['plugin_referrer_id'],
      plugin_referrer_scene: _req['plugin_referrer_scene'],
      workflow_id: _req['workflow_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/plugin_api/message_nodes */
  GetWorkflowMessageNodes(
    req?: plugin_develop.GetWorkflowMessageNodesRequest,
    options?: T,
  ): Promise<plugin_develop.GetWorkflowMessageNodesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/message_nodes');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      plugin_id: _req['plugin_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/plugin_api/get_card_resp_struct
   *
   * Card
   */
  GetCardRespStruct(
    req?: plugin_develop.GetCardRespStructRequest,
    options?: T,
  ): Promise<plugin_develop.GetCardRespStructResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_card_resp_struct');
    const method = 'POST';
    const data = {
      biz_type: _req['biz_type'],
      plugin_id: _req['plugin_id'],
      unique_id: _req['unique_id'],
      space_id: _req['space_id'],
      from_plugin_preset: _req['from_plugin_preset'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open_api/plugin/upsert_plugin
   *
   * plugin openapi
   */
  UpsertPlugin(
    req: plugin_develop.UpsertPluginRequest,
    options?: T,
  ): Promise<plugin_develop.UpsertPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/open_api/plugin/upsert_plugin');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      space_id: _req['space_id'],
      plugin_desc: _req['plugin_desc'],
      api_desc: _req['api_desc'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_plugin_preset_card */
  GetPluginPresetCard(
    req?: plugin_develop.GetPluginPresetCardRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginPresetCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_plugin_preset_card');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/bind_plugin_preset_card */
  BindPluginPresetCard(
    req?: plugin_develop.BindPluginPresetCardRequest,
    options?: T,
  ): Promise<plugin_develop.BindPluginPresetCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/bind_plugin_preset_card');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      card_id: _req['card_id'],
      api_id: _req['api_id'],
      mapping_rule: _req['mapping_rule'],
      max_display_rows: _req['max_display_rows'],
      card_version_num: _req['card_version_num'],
      llm_text_card: _req['llm_text_card'],
      edit_version: _req['edit_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/quick_bind_plugin_preset_card */
  QuickBindPluginPresetCard(
    req?: plugin_develop.QuickBindPluginPresetCardRequest,
    options?: T,
  ): Promise<plugin_develop.QuickBindPluginPresetCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/quick_bind_plugin_preset_card');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      bot_id: _req['bot_id'],
      agent_id: _req['agent_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/get_plugin_preset_card_bind
   *
   * 预置卡片
   */
  GetPluginPresetCardBind(
    req?: plugin_develop.GetPluginPresetCardBindRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginPresetCardBindResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_plugin_preset_card_bind');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/delete_card */
  DeleteCard(
    req?: plugin_develop.DeleteCardRequest,
    options?: T,
  ): Promise<plugin_develop.DeleteCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/delete_card');
    const method = 'POST';
    const data = { card_id: _req['card_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_api_resp_struct */
  GetAPIRespStruct(
    req?: plugin_develop.GetAPIRespStructRequest,
    options?: T,
  ): Promise<plugin_develop.GetCardRespStructResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_api_resp_struct');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_card_bind */
  GetCardBind(
    req?: plugin_develop.GetCardBindRequest,
    options?: T,
  ): Promise<plugin_develop.GetCardBindResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_card_bind');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      bot_id: _req['bot_id'],
      agent_id: _req['agent_id'],
      biz_type: _req['biz_type'],
      business_id: _req['business_id'],
      unique_id: _req['unique_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/create_card */
  CreateCard(
    req?: plugin_develop.CreateCardRequest,
    options?: T,
  ): Promise<plugin_develop.CreateCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/create_card');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      card_id: _req['card_id'],
      version_num: _req['version_num'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_bind_card_status */
  GetBindCardsStatus(
    req?: plugin_develop.GetBindCardsStatusRequest,
    options?: T,
  ): Promise<plugin_develop.GetBindCardsStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_bind_card_status');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      business_list: _req['business_list'],
      agent_id: _req['agent_id'],
      using_master: _req['using_master'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/bind_card */
  BindCard(
    req?: plugin_develop.BindCardRequest,
    options?: T,
  ): Promise<plugin_develop.BindCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/bind_card');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      card_id: _req['card_id'],
      bot_id: _req['bot_id'],
      mapping_rule: _req['mapping_rule'],
      max_display_rows: _req['max_display_rows'],
      card_version_num: _req['card_version_num'],
      agent_id: _req['agent_id'],
      llm_text_card: _req['llm_text_card'],
      biz_type: _req['biz_type'],
      business_id: _req['business_id'],
      unique_id: _req['unique_id'],
      plugin_preset_card_selected: _req['plugin_preset_card_selected'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/query_card_list */
  QueryCardList(
    req?: plugin_develop.QueryCardListRequest,
    options?: T,
  ): Promise<plugin_develop.QueryCardListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/query_card_list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      category: _req['category'],
      bind_card_id: _req['bind_card_id'],
      status: _req['status'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/query_card_detail */
  QueryCardDetail(
    req?: plugin_develop.QueryCardDetailRequest,
    options?: T,
  ): Promise<plugin_develop.QueryCardDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/query_card_detail');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      card_id: _req['card_id'],
      card_version: _req['card_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_plugin_cards */
  GetPluginCards(
    req?: plugin_develop.GetPluginCardsRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginCardsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_plugin_cards');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      plugins: _req['plugins'],
      agent_id: _req['agent_id'],
      using_master: _req['using_master'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/create_feedback */
  CreatePluginFeedback(
    req?: plugin_develop.CreatePluginFeedbackRequest,
    options?: T,
  ): Promise<plugin_develop.CreatePluginFeedbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/create_feedback');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      feedback_type: _req['feedback_type'],
      feedback: _req['feedback'],
      Base: _req['Base'],
    };
    const headers = { Referer: _req['Referer'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/plugin_api/display_example_resp_by_card */
  BuildCardBodyByResp(
    req: plugin_develop.BuildCardBodyByRespRequest,
    options?: T,
  ): Promise<plugin_develop.BuildCardBodyByRespResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/display_example_resp_by_card');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      mapping_rule: _req['mapping_rule'],
      max_display_rows: _req['max_display_rows'],
      card_version: _req['card_version'],
      connector_id: _req['connector_id'],
      example_resp: _req['example_resp'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/revoke_auth_token */
  RevokeAuthToken(
    req: plugin_develop.RevokeAuthTokenRequest,
    options?: T,
  ): Promise<plugin_develop.RevokeAuthTokenResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/revoke_auth_token');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      bot_id: _req['bot_id'],
      context_type: _req['context_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_oauth_status */
  GetOAuthStatus(
    req: plugin_develop.GetOAuthStatusRequest,
    options?: T,
  ): Promise<plugin_develop.GetOAuthStatusResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_oauth_status');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      context_type: _req['context_type'],
      entity_id: _req['entity_id'],
      auth_mode: _req['auth_mode'],
      is_draft: _req['is_draft'],
      force_get_client_url: _req['force_get_client_url'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/optimize_param_prompt
   *
   * 利用 copilot chain task 优化 prompt
   */
  OptimizeParamPrompt(
    req: plugin_develop.OptimizeParamPromptRequest,
    options?: T,
  ): Promise<plugin_develop.OptimizeParamPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/optimize_param_prompt');
    const method = 'POST';
    const data = {
      request_params: _req['request_params'],
      response_params: _req['response_params'],
      api_desc: _req['api_desc'],
      space_id: _req['space_id'],
      plugin_id: _req['plugin_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/optimize_tool_prompt */
  OptimizeToolPrompt(
    req: plugin_develop.OptimizeToolPromptRequest,
    options?: T,
  ): Promise<plugin_develop.OptimizeToolPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/optimize_tool_prompt');
    const method = 'POST';
    const data = {
      api_desc: _req['api_desc'],
      space_id: _req['space_id'],
      plugin_id: _req['plugin_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/config_async */
  ConfigPluginAsync(
    req: plugin_develop.ConfigPluginAsyncRequest,
    options?: T,
  ): Promise<plugin_develop.ConfigPluginAsyncResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/config_async');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      plugin_id: _req['plugin_id'],
      switch_status: _req['switch_status'],
      message: _req['message'],
      api_name: _req['api_name'],
      by_user: _req['by_user'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/plugin_api/get_async_task_list
   *
   * 异步任务
   */
  GetPluginAsyncTaskList(
    req: plugin_develop.GetPluginAsyncTaskListRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginAsyncTaskListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_async_task_list');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/plugin_api/get_async_config */
  GetAsyncPluginConfig(
    req: plugin_develop.GetAsyncPluginConfigRequest,
    options?: T,
  ): Promise<plugin_develop.GetAsyncPluginConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_async_config');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/plugin_api/get_queried_oauth_plugins */
  GetQueriedOAuthPluginList(
    req: plugin_develop.GetQueriedOAuthPluginListRequest,
    options?: T,
  ): Promise<plugin_develop.GetQueriedOAuthPluginListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_queried_oauth_plugins');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/get_plugin_pricing_rules_by_workflow_id
   *
   * 插件计费
   */
  GetPluginPricingRulesByWorkflowID(
    req: plugin_develop.GetPluginPricingRulesByWorkflowIDRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginPricingRulesByWorkflowIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/plugin_api/get_plugin_pricing_rules_by_workflow_id',
    );
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/batch_get_plugin_pricing_rules */
  BatchGetPluginPricingRules(
    req?: plugin_develop.BatchGetPluginPricingRulesRequest,
    options?: T,
  ): Promise<plugin_develop.BatchGetPluginPricingRulesResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/plugin_api/batch_get_plugin_pricing_rules',
    );
    const method = 'POST';
    const data = { plugin_apis: _req['plugin_apis'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/library_resource_list
   *
   * Coze资源库列表，因新服务va访问不通，先在这里放
   */
  LibraryResourceList(
    req: resource.LibraryResourceListRequest,
    options?: T,
  ): Promise<resource.LibraryResourceListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/library_resource_list');
    const method = 'POST';
    const data = {
      user_filter: _req['user_filter'],
      res_type_filter: _req['res_type_filter'],
      name: _req['name'],
      publish_status_filter: _req['publish_status_filter'],
      space_id: _req['space_id'],
      size: _req['size'],
      cursor: _req['cursor'],
      search_keys: _req['search_keys'],
      is_get_imageflow: _req['is_get_imageflow'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/project_resource_list */
  ProjectResourceList(
    req: resource.ProjectResourceListRequest,
    options?: T,
  ): Promise<resource.ProjectResourceListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/project_resource_list');
    const method = 'POST';
    const data = {
      project_id: _req['project_id'],
      space_id: _req['space_id'],
      project_version: _req['project_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/resource_copy_dispatch
   *
   * Http功能入口，包括：复制Library资源到项目、复制项目资源到Library、移动项目资源到Library、项目内单复制资源
   */
  ResourceCopyDispatch(
    req?: resource.ResourceCopyDispatchRequest,
    options?: T,
  ): Promise<resource.ResourceCopyDispatchResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/resource_copy_dispatch');
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      res_id: _req['res_id'],
      res_type: _req['res_type'],
      project_id: _req['project_id'],
      res_name: _req['res_name'],
      target_space_id: _req['target_space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/resource_copy_retry */
  ResourceCopyRetry(
    req?: resource.ResourceCopyRetryRequest,
    options?: T,
  ): Promise<resource.ResourceCopyRetryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/resource_copy_retry');
    const method = 'POST';
    const data = { task_id: _req['task_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/resource_copy_cancel */
  ResourceCopyCancel(
    req?: resource.ResourceCopyCancelRequest,
    options?: T,
  ): Promise<resource.ResourceCopyCancelResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/resource_copy_cancel');
    const method = 'POST';
    const data = { task_id: _req['task_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_dev_plugin_list */
  GetDevPluginList(
    req: plugin_develop.GetDevPluginListRequest,
    options?: T,
  ): Promise<plugin_develop.GetDevPluginListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_dev_plugin_list');
    const method = 'POST';
    const data = {
      status: _req['status'],
      page: _req['page'],
      size: _req['size'],
      dev_id: _req['dev_id'],
      space_id: _req['space_id'],
      scope_type: _req['scope_type'],
      order_by: _req['order_by'],
      publish_status: _req['publish_status'],
      name: _req['name'],
      plugin_type_for_filter: _req['plugin_type_for_filter'],
      project_id: _req['project_id'],
      plugin_ids: _req['plugin_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/resource_copy_detail */
  ResourceCopyDetail(
    req?: resource.ResourceCopyDetailRequest,
    options?: T,
  ): Promise<resource.ResourceCopyDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/resource_copy_detail');
    const method = 'POST';
    const data = { task_id: _req['task_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/wakeup_ide_plugin
   *
   * IDE插件
   */
  WakeupIdePlugin(
    req?: plugin_develop.WakeupIdePluginRequest,
    options?: T,
  ): Promise<plugin_develop.WakeupIdePluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/wakeup_ide_plugin');
    const method = 'POST';
    const data = {
      project_id: _req['project_id'],
      dev_id: _req['dev_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_plugin_next_version */
  GetPluginNextVersion(
    req: plugin_develop.GetPluginNextVersionRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginNextVersionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_plugin_next_version');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/get_plugin_publish_history */
  GetPluginPublishHistory(
    req: plugin_develop.GetPluginPublishHistoryRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginPublishHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_plugin_publish_history');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      space_id: _req['space_id'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/get_nl2app_config
   *
   * NL2APP
   */
  GetNL2APPConfig(
    req?: plugin_develop.GetNL2APPConfigRequest,
    options?: T,
  ): Promise<plugin_develop.GetNL2APPConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/get_nl2app_config');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/task_api/set_agent_task_status
   *
   * 设置task渠道状态
   */
  SetAgentTaskStatus(
    req: plugin_develop.SetAgentTaskStatusRequest,
    options?: T,
  ): Promise<plugin_develop.SetAgentTaskStatusResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task_api/set_agent_task_status');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      trigger_id: _req['trigger_id'],
      connector_id: _req['connector_id'],
      set_invalid: _req['set_invalid'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/task_api/get_agent_task_exec_list
   *
   * 获取agent下任务执行记录
   */
  GetAgentTaskExecList(
    req: plugin_develop.GetAgentTaskExecListRequest,
    options?: T,
  ): Promise<plugin_develop.GetAgentTaskExecListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task_api/get_agent_task_exec_list');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      trigger_id: _req['trigger_id'],
      connector_ids: _req['connector_ids'],
      trigger_user_id: _req['trigger_user_id'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/task_api/get_agent_task_list
   *
   * 触发器管理
   *
   * 获取agent下的触发器列表
   */
  GetAgentTaskList(
    req: plugin_develop.GetAgentTaskListRequest,
    options?: T,
  ): Promise<plugin_develop.GetAgentTaskListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task_api/get_agent_task_list');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      trigger_event_type: _req['trigger_event_type'],
      connector_id: _req['connector_id'],
      trigger_type: _req['trigger_type'],
      trigger_name: _req['trigger_name'],
      trigger_id: _req['trigger_id'],
      trigger_user_id: _req['trigger_user_id'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/plugin_api/get_export_ip_config
   *
   * 插件支持固定出口ip
   */
  GetPluginExportIPConfig(
    req: plugin_develop.GetPluginExportIPConfigRequest,
    options?: T,
  ): Promise<plugin_develop.GetPluginExportIPConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_export_ip_config');
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/plugin_api/private_link/info */
  GetPrivateLinkInfo(
    req?: plugin_develop.GetPrivateLinkInfoRequest,
    options?: T,
  ): Promise<plugin_develop.GetPrivateLinkInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/private_link/info');
    const method = 'POST';
    const data = {
      private_link_id: _req['private_link_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin_api/private_link/upsert */
  UpsertPrivateLink(
    req?: plugin_develop.UpsertPrivateLinkRequest,
    options?: T,
  ): Promise<plugin_develop.UpsertPrivateLinkResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/private_link/upsert');
    const method = 'POST';
    const data = { private_link: _req['private_link'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/plugin_api/private_link/list
   *
   * private_link
   */
  PrivateLinkList(
    req?: plugin_develop.PrivateLinkListRequest,
    options?: T,
  ): Promise<plugin_develop.PrivateLinkListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/private_link/list');
    const method = 'GET';
    const params = { enterprise_id: _req['enterprise_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/plugin_api/private_link/operate */
  OperatePrivateLink(
    req?: plugin_develop.OperatePrivateLinkRequest,
    options?: T,
  ): Promise<plugin_develop.OperatePrivateLinkResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin_api/private_link/operate');
    const method = 'POST';
    const data = {
      private_link_id: _req['private_link_id'],
      operate_type: _req['operate_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/plugin_api/get_oauth_plugin_list
   *
   * Oauth
   */
  GetOAuthPluginList(
    req: plugin_develop.GetOAuthPluginListRequest,
    options?: T,
  ): Promise<plugin_develop.GetOAuthPluginListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin_api/get_oauth_plugin_list');
    const method = 'POST';
    const data = {
      entity_id: _req['entity_id'],
      context_type: _req['context_type'],
      entity_version: _req['entity_version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/authorized_plugins
   *
   * OpenAPI
   */
  OpenAPIGetOAuthPluginList(
    req?: plugin_openapi.OpenAPIGetOAuthPluginListRequest,
    options?: T,
  ): Promise<plugin_openapi.OpenAPIGetOAuthPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/authorized_plugins');
    const method = 'GET';
    const params = {
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
      connector_id: _req['connector_id'],
      user_id: _req['user_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /v1/authorized_plugins/revoke */
  OpenAPIRevokeAuthToken(
    req?: plugin_openapi.OpenAPIRevokeAuthTokenRequest,
    options?: T,
  ): Promise<plugin_openapi.OpenAPIRevokeAuthTokenResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/authorized_plugins/revoke');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
      connector_id: _req['connector_id'],
      user_id: _req['user_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
