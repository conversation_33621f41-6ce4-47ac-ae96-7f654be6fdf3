/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as flow_devops_ob_query_insight_indicators from './namespaces/flow_devops_ob_query_insight_indicators';
import * as flow_devops_ob_query_metrics from './namespaces/flow_devops_ob_query_metrics';
import * as flow_devops_ob_query_query from './namespaces/flow_devops_ob_query_query';
import * as flow_devops_ob_query_telemetry from './namespaces/flow_devops_ob_query_telemetry';
import * as flow_devops_ob_query_telemetry_common from './namespaces/flow_devops_ob_query_telemetry_common';
import * as flow_devops_ob_query_telemetry_field_filter from './namespaces/flow_devops_ob_query_telemetry_field_filter';
import * as flow_devops_ob_query_telemetry_span from './namespaces/flow_devops_ob_query_telemetry_span';
import * as ob_query from './namespaces/ob_query';
import * as ob_query_trace from './namespaces/ob_query_trace';

export {
  base,
  flow_devops_ob_query_insight_indicators,
  flow_devops_ob_query_metrics,
  flow_devops_ob_query_query,
  flow_devops_ob_query_telemetry,
  flow_devops_ob_query_telemetry_common,
  flow_devops_ob_query_telemetry_field_filter,
  flow_devops_ob_query_telemetry_span,
  ob_query,
  ob_query_trace,
};
export * from './namespaces/base';
export * from './namespaces/flow_devops_ob_query_insight_indicators';
export * from './namespaces/flow_devops_ob_query_metrics';
export * from './namespaces/flow_devops_ob_query_query';
export * from './namespaces/flow_devops_ob_query_telemetry';
export * from './namespaces/flow_devops_ob_query_telemetry_common';
export * from './namespaces/flow_devops_ob_query_telemetry_field_filter';
export * from './namespaces/flow_devops_ob_query_telemetry_span';
export * from './namespaces/ob_query';
export * from './namespaces/ob_query_trace';

export type Int64 = string | number;

export default class ObQueryApiService<T> {
  private request: any = () => {
    throw new Error('ObQueryApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/devops/ob_query/query_trace
   *
   * 豆包&cici特化: 通过 logID/traceID/messageID 查询 tracing 数据
   */
  QueryTrace(
    req?: ob_query.QueryTraceRequest,
    options?: T,
  ): Promise<ob_query.QueryTraceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/ob_query/query_trace');
    const method = 'GET';
    const params = {
      tenant: _req['tenant'],
      trace_id: _req['trace_id'],
      log_id: _req['log_id'],
      message_id: _req['message_id'],
      start_at: _req['start_at'],
      end_at: _req['end_at'],
      bot_id: _req['bot_id'],
      workspace_id: _req['workspace_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/list_spans
   *
   * 豆包&cici特化: 通过 tag 关键字 查询 tracing 数据
   */
  ListSpans(
    req: ob_query.ListSpansRequest,
    options?: T,
  ): Promise<ob_query.ListSpansResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/list_spans');
    const method = 'POST';
    const data = {
      tenant: _req['tenant'],
      filter_tags: _req['filter_tags'],
      start_at: _req['start_at'],
      end_at: _req['end_at'],
      limit: _req['limit'],
      desc_by_start_time: _req['desc_by_start_time'],
      context: _req['context'],
      bot_id: _req['bot_id'],
      workspace_id: _req['workspace_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/traces
   *
   * 查询通用链路列表
   */
  ListTraces(
    req: flow_devops_ob_query_telemetry.ListTracesRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_telemetry.ListTracesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/v1/traces');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filters: _req['filters'],
      full_text_search: _req['full_text_search'],
      limit: _req['limit'],
      order_by: _req['order_by'],
      page_token: _req['page_token'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/traces/stat
   *
   * 查询链路统计指标
   */
  GetTracesStat(
    req: flow_devops_ob_query_telemetry.GetTracesStatRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_telemetry.GetTracesStatResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/v1/traces/stat');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/v1/traces/meta_info
   *
   * 查询元信息，包括字段类型，filter opertor
   */
  GetTracesMetaInfo(
    req?: flow_devops_ob_query_telemetry.GetTracesMetaInfoRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_telemetry.GetTracesMetaInfoResponse> {
    const url = this.genBaseURL('/api/devops/ob_query/v1/traces/meta_info');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/devops/ob_query/v1/trace/by_log_id
   *
   * 通过log ID查询链路详情
   */
  GetTraceByLogID(
    req: flow_devops_ob_query_telemetry.GetTraceByLogIDRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_telemetry.GetTraceByLogIDResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/v1/trace/by_log_id');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      log_id: _req['log_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/traces/batch_get_advance_info
   *
   * 批量查询链路进阶信息
   */
  BatchGetTracesAdvanceInfo(
    req: flow_devops_ob_query_telemetry.BatchGetTracesAdvanceInfoRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_telemetry.BatchGetTracesAdvanceInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_query/v1/traces/batch_get_advance_info',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      traces: _req['traces'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/v1/trace/:trace_id
   *
   * 通过trace ID查询链路详情
   */
  GetTrace(
    req: flow_devops_ob_query_telemetry.GetTraceRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_telemetry.GetTraceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/ob_query/v1/trace/${_req['trace_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/metrics
   *
   * 获取 metrics 详情
   */
  GetMetrics(
    req: flow_devops_ob_query_metrics.GetMetricsRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_metrics.GetMetricsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/v1/metrics');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      metrics_type: _req['metrics_type'],
      aggregate_type: _req['aggregate_type'],
      tag_kvs: _req['tag_kvs'],
      top_k: _req['top_k'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/metrics/tag_v/latest_options
   *
   * 获取运维指标维度的值的最新选项
   */
  GetTagVLatestOptions(
    req: flow_devops_ob_query_metrics.GetTagVLatestOptionsRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_metrics.GetTagVLatestOptionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_query/v1/metrics/tag_v/latest_options',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      tag_k: _req['tag_k'],
      choices: _req['choices'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/metrics/tag_v/historical_options
   *
   * 获取运维指标维度的值的历史选项
   */
  GetTagVHistoricalOptions(
    req: flow_devops_ob_query_metrics.GetTagVHistoricalOptionsRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_metrics.GetTagVHistoricalOptionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_query/v1/metrics/tag_v/historical_options',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      tag_k: _req['tag_k'],
      choices: _req['choices'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      metrics_type: _req['metrics_type'],
      top_k: _req['top_k'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/search_trace
   *
   * 通过 logID/traceID/messageID 查询 tracing 数据
   */
  SearchTrace(
    req?: ob_query_trace.SearchTraceRequest,
    options?: T,
  ): Promise<ob_query_trace.SearchTraceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/ob_query/search_trace');
    const method = 'GET';
    const params = {
      tenant: _req['tenant'],
      trace_id: _req['trace_id'],
      log_id: _req['log_id'],
      message_id: _req['message_id'],
      start_at: _req['start_at'],
      end_at: _req['end_at'],
      tenant_level: _req['tenant_level'],
      token: _req['token'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/devops/ob_query/get_trace_frontend
   *
   * 查询 tracing 数据，前端 SDK 专用
   */
  GetTraceFrontend(
    req?: ob_query_trace.GetTraceFrontendRequest,
    options?: T,
  ): Promise<ob_query_trace.GetTraceFrontendResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/ob_query/get_trace_frontend');
    const method = 'GET';
    const params = {
      trace_id: _req['trace_id'],
      log_id: _req['log_id'],
      scene: _req['scene'],
      start_at: _req['start_at'],
      end_at: _req['end_at'],
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      tenant: _req['tenant'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/devops/ob_query/list_debug_queries
   *
   * 调试Query List
   */
  ListDebugQueries(
    req: ob_query_trace.ListDebugQueriesRequest,
    options?: T,
  ): Promise<ob_query_trace.ListDebugQueriesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/list_debug_queries');
    const method = 'GET';
    const params = {
      startAtMS: _req['startAtMS'],
      endAtMS: _req['endAtMS'],
      spaceID: _req['spaceID'],
      botID: _req['botID'],
      status: _req['status'],
      inputSearch: _req['inputSearch'],
      limit: _req['limit'],
      pageToken: _req['pageToken'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/query/list
   *
   * 查询 query 列表
   */
  ListQuery(
    req: flow_devops_ob_query_query.ListQueryRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_query.ListQueryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      query_filter: _req['query_filter'],
      limit: _req['limit'],
      page_token: _req['page_token'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/query/enums
   *
   * 获取 query 枚举选项
   */
  GetQueryEnums(
    req: flow_devops_ob_query_query.GetQueryEnumsRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_query.GetQueryEnumsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/enums');
    const method = 'GET';
    const params = { space_id: _req['space_id'], bot_id: _req['bot_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/query/export_to_csv
   *
   * 将 query 导出为csv
   */
  ExportQueryToCsv(
    req: flow_devops_ob_query_query.ExportQueryToCsvRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_query.ExportQueryToCsvResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/export_to_csv');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      query_filter: _req['query_filter'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/query/stat
   *
   * query 的统计指标
   */
  GetQueryStat(
    req: flow_devops_ob_query_query.GetQueryStatRequest,
    options?: T,
  ): Promise<flow_devops_ob_query_query.GetQueryStatResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/stat');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      query_filter: _req['query_filter'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
