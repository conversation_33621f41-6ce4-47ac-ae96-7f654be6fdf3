/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as flow_devops_prompt_callback from './namespaces/flow_devops_prompt_callback';
import * as flow_devops_prompt_commercial_openapi from './namespaces/flow_devops_prompt_commercial_openapi';
import * as flow_devops_prompt_common from './namespaces/flow_devops_prompt_common';
import * as flow_devops_prompt_debug from './namespaces/flow_devops_prompt_debug';
import * as flow_devops_prompt_manage from './namespaces/flow_devops_prompt_manage';
import * as flow_devops_prompt_mcp from './namespaces/flow_devops_prompt_mcp';
import * as flow_devops_prompt_optimize from './namespaces/flow_devops_prompt_optimize';
import * as flow_devops_prompt_paas from './namespaces/flow_devops_prompt_paas';
import * as flow_devops_prompt_platform from './namespaces/flow_devops_prompt_platform';
import * as mcp from './namespaces/mcp';
import * as model from './namespaces/model';
import * as permission from './namespaces/permission';
import * as release from './namespaces/release';

export {
  base,
  flow_devops_prompt_callback,
  flow_devops_prompt_commercial_openapi,
  flow_devops_prompt_common,
  flow_devops_prompt_debug,
  flow_devops_prompt_manage,
  flow_devops_prompt_mcp,
  flow_devops_prompt_optimize,
  flow_devops_prompt_paas,
  flow_devops_prompt_platform,
  mcp,
  model,
  permission,
  release,
};

export type Int64 = string | number;

export default class PromptApi2Service<T> {
  private request: any = () => {
    throw new Error('PromptApi2Service.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/auth/permission/check
   *
   * 批量鉴权函数，支持服务端和前端调用
   */
  MCheckPermission(
    req?: flow_devops_prompt_platform.MCheckPermissionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.MCheckPermissionResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/auth/permission/check',
    );
    const method = 'POST';
    const data = {
      auths: _req['auths'],
      app_id: _req['app_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/version/revert
   *
   * Prompt历史版本回滚
   */
  RevertPromptByVersion(
    req: flow_devops_prompt_manage.RevertPromptByVersionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.RevertPromptByVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/version/revert',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      space_id: _req['space_id'],
      personal_draft: _req['personal_draft'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/update
   *
   * 更新空间
   */
  UpdateSpace(
    req: flow_devops_prompt_platform.UpdateSpaceRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.UpdateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/space/update');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      release_approval_config: _req['release_approval_config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/prompt_platform/v1/prompt/create */
  CreatePrompt(
    req: flow_devops_prompt_manage.CreatePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.CreatePromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/create');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      prompt_type: _req['prompt_type'],
      display_name: _req['display_name'],
      prompt_key: _req['prompt_key'],
      description: _req['description'],
      model_config: _req['model_config'],
      prompt_text: _req['prompt_text'],
      prompt_input: _req['prompt_input'],
      labels: _req['labels'],
      tools: _req['tools'],
      security_level: _req['security_level'],
      tool_call_config: _req['tool_call_config'],
      template_type: _req['template_type'],
      metadata: _req['metadata'],
      tag_ids: _req['tag_ids'],
      mcp_servers: _req['mcp_servers'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/version/list
   *
   * Prompt发布历史版本
   */
  ListPromptVersion(
    req: flow_devops_prompt_manage.ListPromptVersionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListPromptVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/version/list',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      cursor: _req['cursor'],
      space_id: _req['space_id'],
      filter_draft: _req['filter_draft'],
      page_size: _req['page_size'],
      version_like: _req['version_like'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/mock_context/save
   *
   * 保存mock上下文
   */
  SaveMockContext(
    req: flow_devops_prompt_debug.SaveMockContextRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.SaveMockContextResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/mock_context/save',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      contexts: _req['contexts'],
      space_id: _req['space_id'],
      variables: _req['variables'],
      user_debug_config: _req['user_debug_config'],
      compare_config: _req['compare_config'],
      contexts_v2: _req['contexts_v2'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/user/session
   *
   * 登录态换取用户信息
   */
  GetSessionInfo(
    req?: flow_devops_prompt_platform.GetSessionInfoRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.GetSessionInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/user/session');
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/get
   *
   * 获取Prompt
   */
  GetPrompt(
    req: flow_devops_prompt_manage.GetPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.GetPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/get');
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      space_id: _req['space_id'],
      personal_draft: _req['personal_draft'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/user/batch_get
   *
   * 批量获取用户信息
   */
  MGetUserInfo(
    req?: flow_devops_prompt_platform.MGetUserInfoRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.MGetUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/user/batch_get',
    );
    const method = 'POST';
    const data = {
      user_ids: _req['user_ids'],
      user_names: _req['user_names'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/list_by_user
   *
   * 空间列表
   */
  ListUserSpace(
    req?: flow_devops_prompt_platform.ListUserSpaceRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.ListUserSpaceResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/space/list_by_user',
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/member/query
   *
   * 查询空间成员列表
   */
  QuerySpaceMember(
    req: flow_devops_prompt_platform.QuerySpaceMemberRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.QuerySpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/space/member/query',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      role_type: _req['role_type'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/member/add
   *
   * 空间角色授权
   */
  AddSpaceMember(
    req: flow_devops_prompt_platform.AddSpaceMemberRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.AddSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/space/member/add',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      space_members: _req['space_members'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/user/query
   *
   * 模糊搜索用户信息
   */
  QueryUserInfo(
    req: flow_devops_prompt_platform.QueryUserInfoRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.QueryUserInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/user/query');
    const method = 'POST';
    const data = {
      name_like: _req['name_like'],
      page_size: _req['page_size'],
      page_token: _req['page_token'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/save
   *
   * 保存Prompt
   */
  SavePrompt(
    req: flow_devops_prompt_manage.SavePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.SavePromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/save');
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      model_config: _req['model_config'],
      prompt_text: _req['prompt_text'],
      prompt_input: _req['prompt_input'],
      version: _req['version'],
      space_id: _req['space_id'],
      prompt_type: _req['prompt_type'],
      draft_base_version: _req['draft_base_version'],
      tools: _req['tools'],
      tool_call_config: _req['tool_call_config'],
      template_type: _req['template_type'],
      metadata: _req['metadata'],
      mcp_servers: _req['mcp_servers'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/member/remove
   *
   * 空间角色取消授权
   */
  RemoveSpaceMember(
    req: flow_devops_prompt_platform.RemoveSpaceMemberRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.RemoveSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/space/member/remove',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      space_members: _req['space_members'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/user/logout
   *
   * 用户登出
   */
  Logout(
    req?: flow_devops_prompt_platform.LogoutRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.LogoutResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/user/logout');
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/user/info
   *
   * 获取用户信息
   */
  GetUserInfo(
    req?: flow_devops_prompt_platform.GetUserInfoRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.GetUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/user/info');
    const method = 'POST';
    const data = {
      user_id: _req['user_id'],
      user_name: _req['user_name'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/create
   *
   * --------------- 空间管理模块 ---------------
   *
   * 创建空间
   */
  CreateSpace(
    req: flow_devops_prompt_platform.CreateSpaceRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.CreateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/space/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      space_type: _req['space_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/publish
   *
   * 发布Prompt
   */
  PublishPrompt(
    req: flow_devops_prompt_manage.PublishPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.PublishPromptResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/publish',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      publish_description: _req['publish_description'],
      space_id: _req['space_id'],
      personal_draft: _req['personal_draft'],
      draft_base_version: _req['draft_base_version'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/mock_context/get
   *
   * 获取mock上下文
   */
  GetMockContext(
    req: flow_devops_prompt_debug.GetMockContextRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.GetMockContextResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/mock_context/get',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * GET /api/devops/prompt_platform/v1/user/login
   *
   * --------------- 用户模块 ---------------
   *
   * 用户登录
   */
  Login(
    req?: flow_devops_prompt_platform.LoginRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.LoginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/user/login');
    const method = 'GET';
    const params = {
      code: _req['code'],
      state: _req['state'],
      session_id: _req['session_id'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/send_message
   *
   * 非流式调试运行
   */
  SendMessage(
    req: flow_devops_prompt_debug.SendMessageRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.SendMessageResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/send_message',
    );
    const method = 'POST';
    const data = {
      prompt: _req['prompt'],
      message: _req['message'],
      contexts: _req['contexts'],
      variables: _req['variables'],
      space_id: _req['space_id'],
      single_step_debug: _req['single_step_debug'],
      debug_trace_key: _req['debug_trace_key'],
      mcp_execute_config: _req['mcp_execute_config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/delete
   *
   * 删除Prompt
   */
  DeletePrompt(
    req: flow_devops_prompt_manage.DeletePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.DeletePromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/delete');
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/component/auth
   *
   * 组件鉴权
   */
  AuthComponentSDK(
    req?: flow_devops_prompt_platform.AuthComponentSDKRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.AuthComponentSDKResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/component/auth',
    );
    const method = 'POST';
    const data = {
      noncestr: _req['noncestr'],
      timestamp: _req['timestamp'],
      url: _req['url'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/model/list_available
   *
   * 获取可用模型列表
   */
  ListAvailableModel(
    req?: flow_devops_prompt_manage.ListAvailableModelRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListAvailableModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/model/list_available',
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/update
   *
   * 更新Prompt
   */
  UpdatePrompt(
    req: flow_devops_prompt_manage.UpdatePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.UpdatePromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/update');
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      display_name: _req['display_name'],
      description: _req['description'],
      space_id: _req['space_id'],
      labels: _req['labels'],
      security_level: _req['security_level'],
      downgrade_reason: _req['downgrade_reason'],
      tag_ids: _req['tag_ids'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/get
   *
   * 查询空间信息
   */
  GetSpace(
    req: flow_devops_prompt_platform.GetSpaceRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.GetSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/space/get');
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/space/get_user_roles
   *
   * 获取用户空间权限
   */
  GetUserSpaceRoles(
    req: flow_devops_prompt_platform.GetUserSpaceRolesRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.GetUserSpaceRolesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/space/get_user_roles',
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/list
   *
   * Prompt列表
   */
  ListPrompt(
    req: flow_devops_prompt_manage.ListPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      key_word: _req['key_word'],
      creator: _req['creator'],
      filter_prompt_types: _req['filter_prompt_types'],
      publish_statuses: _req['publish_statuses'],
      tag_ids: _req['tag_ids'],
      order_param: _req['order_param'],
      creator_list: _req['creator_list'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/execute
   *
   * 非流式调试运行
   */
  Execute(
    req?: flow_devops_prompt_paas.ExecuteRequest,
    options?: T,
  ): Promise<flow_devops_prompt_paas.ExecuteResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/execute',
    );
    const method = 'POST';
    const data = {
      prompt_key: _req['prompt_key'],
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      message: _req['message'],
      contexts: _req['contexts'],
      variables: _req['variables'],
      account_mode: _req['account_mode'],
      custom_accounts: _req['custom_accounts'],
      single_step_debug: _req['single_step_debug'],
      debug_trace_key: _req['debug_trace_key'],
      usage_scenario: _req['usage_scenario'],
      traffic: _req['traffic'],
      request_extra: _req['request_extra'],
      custom_model_config: _req['custom_model_config'],
      release_label: _req['release_label'],
      mcp_execute_config: _req['mcp_execute_config'],
      base: _req['base'],
    };
    const headers = {
      token: _req['token'],
      'Agw-Auth': _req['Agw-Auth'],
      Authorization: _req['Authorization'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/streaming_send_message
   *
   * 流式调试运行
   */
  StreamingSendMessage(
    req: flow_devops_prompt_debug.StreamingSendMessageRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.StreamingSendMessageResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/streaming_send_message',
    );
    const method = 'POST';
    const data = {
      prompt: _req['prompt'],
      message: _req['message'],
      contexts: _req['contexts'],
      variables: _req['variables'],
      space_id: _req['space_id'],
      single_step_debug: _req['single_step_debug'],
      debug_trace_key: _req['debug_trace_key'],
      mcp_execute_config: _req['mcp_execute_config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/streaming_execute
   *
   * 流式调试运行
   */
  StreamingExecute(
    req?: flow_devops_prompt_paas.StreamingExecuteRequest,
    options?: T,
  ): Promise<flow_devops_prompt_paas.StreamingExecuteResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/streaming_execute',
    );
    const method = 'POST';
    const data = {
      prompt_key: _req['prompt_key'],
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      message: _req['message'],
      contexts: _req['contexts'],
      variables: _req['variables'],
      account_mode: _req['account_mode'],
      custom_accounts: _req['custom_accounts'],
      single_step_debug: _req['single_step_debug'],
      debug_trace_key: _req['debug_trace_key'],
      usage_scenario: _req['usage_scenario'],
      traffic: _req['traffic'],
      request_extra: _req['request_extra'],
      custom_model_config: _req['custom_model_config'],
      release_label: _req['release_label'],
      mcp_execute_config: _req['mcp_execute_config'],
      base: _req['base'],
    };
    const headers = {
      token: _req['token'],
      'Agw-Auth': _req['Agw-Auth'],
      Authorization: _req['Authorization'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/devops/prompt_platform/v1/prompt/chat_test */
  ChatTest(
    req: flow_devops_prompt_debug.ChatTestRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.ChatTestResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/chat_test',
    );
    const method = 'POST';
    const data = {
      prompt: _req['prompt'],
      message: _req['message'],
      contexts: _req['contexts'],
      variables: _req['variables'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/list_label
   *
   * 获取标签列表
   */
  ListLabel(
    req?: flow_devops_prompt_manage.ListLabelRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListLabelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/list_label',
    );
    const method = 'POST';
    const data = { label_type: _req['label_type'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/list_official
   *
   * 获取官方prompt列表
   */
  ListOfficialPrompt(
    req: flow_devops_prompt_manage.ListOfficialPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListOfficialPromptResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/list_official',
    );
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      key_word: _req['key_word'],
      labels: _req['labels'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/report_event
   *
   * 事件上报
   */
  ReportEvent(
    req?: flow_devops_prompt_manage.ReportEventRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ReportEventResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/report_event',
    );
    const method = 'POST';
    const data = { report_event: _req['report_event'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/generate
   *
   * 生成prompt
   */
  GeneratePrompt(
    req?: flow_devops_prompt_manage.GeneratePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.GeneratePromptResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/generate',
    );
    const method = 'POST';
    const data = {
      generate_prompt_type: _req['generate_prompt_type'],
      original_prompt: _req['original_prompt'],
      prompt_name: _req['prompt_name'],
      prompt_desc: _req['prompt_desc'],
      prompt_key: _req['prompt_key'],
      space_id: _req['space_id'],
      prompt_id: _req['prompt_id'],
      is_retry: _req['is_retry'],
      structured_prompt_type: _req['structured_prompt_type'],
      structured_context: _req['structured_context'],
      user_message: _req['user_message'],
      assistant_message: _req['assistant_message'],
      variables: _req['variables'],
      feedback: _req['feedback'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/chat_with_bot
   *
   * Prompt Bot对话
   */
  ChatWithPromptBot(
    req?: flow_devops_prompt_manage.ChatWithPromptBotRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ChatWithPromptBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/chat_with_bot',
    );
    const method = 'POST';
    const data = {
      message: _req['message'],
      contexts: _req['contexts'],
      get_next_guide: _req['get_next_guide'],
      last_generated_prompt: _req['last_generated_prompt'],
      prompt_name: _req['prompt_name'],
      prompt_desc: _req['prompt_desc'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/streaming_send_message_without_permission_check
   *
   * 流式调试运行for playground
   */
  StreamingSendMessageWithoutPermissionCheck(
    req: flow_devops_prompt_debug.StreamingSendMessageWithoutPermissionCheckRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.StreamingSendMessageWithoutPermissionCheckResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/streaming_send_message_without_permission_check',
    );
    const method = 'POST';
    const data = {
      prompt: _req['prompt'],
      message: _req['message'],
      contexts: _req['contexts'],
      variables: _req['variables'],
      space_id: _req['space_id'],
      single_step_debug: _req['single_step_debug'],
      debug_trace_key: _req['debug_trace_key'],
      mcp_execute_config: _req['mcp_execute_config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/debug/get
   *
   * 调试历史详情
   */
  GetDebugDetail(
    req?: flow_devops_prompt_debug.GetDebugDetailRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.GetDebugDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/debug/get',
    );
    const method = 'POST';
    const data = {
      debug_id: _req['debug_id'],
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/debug/list_history
   *
   * 调试历史
   */
  ListDebugHistory(
    req?: flow_devops_prompt_debug.ListDebugHistoryRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.ListDebugHistoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/debug/list_history',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      list_all: _req['list_all'],
      cursor: _req['cursor'],
      offset: _req['offset'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/check_publish
   *
   * 发布Prompt前检查
   */
  CheckPublishPrompt(
    req?: flow_devops_prompt_manage.CheckPublishPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.CheckPublishPromptResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/check_publish',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      draft_base_version: _req['draft_base_version'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/mpull
   *
   * --------------- OpenAPI ---------------
   *
   * 批量获取Prompt
   */
  MPullPrompt(
    req: flow_devops_prompt_paas.MPullPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_paas.MPullPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/prompt_platform/v1/prompt/mpull');
    const method = 'POST';
    const data = {
      prompt_queries: _req['prompt_queries'],
      encrypt_option: _req['encrypt_option'],
      base: _req['base'],
    };
    const headers = {
      token: _req['token'],
      'Agw-Auth': _req['Agw-Auth'],
      Authorization: _req['Authorization'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/get
   *
   * 获取Task详情，包含关联资源
   */
  GetOptimizeTask(
    req: flow_devops_prompt_optimize.GetOptimizeTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.GetOptimizeTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/get',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/update
   *
   * 更新Task
   */
  UpdateOptimizeTask(
    req: flow_devops_prompt_optimize.UpdateOptimizeTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.UpdateOptimizeTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/update',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      name: _req['name'],
      desc: _req['desc'],
      target: _req['target'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/execution/cancel
   *
   * 取消执行
   */
  CancelOptimizeExecution(
    req: flow_devops_prompt_optimize.CancelOptimizeExecutionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.CancelOptimizeExecutionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/execution/cancel',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      execution_id: _req['execution_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/execution/dashboard
   *
   * 获取优化报告详情
   */
  GetOptimizeExecutionDashboard(
    req: flow_devops_prompt_optimize.GetOptimizeExecutionDashboardRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.GetOptimizeExecutionDashboardResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/execution/dashboard',
    );
    const method = 'POST';
    const data = {
      execution_id: _req['execution_id'],
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/create
   *
   * --------------- Prompt优化模块 ---------------
   *
   * --------------- Prompt优化管理模块 ---------------
   *
   * 创建Task
   */
  CreateOptimizeTask(
    req: flow_devops_prompt_optimize.CreateOptimizeTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.CreateOptimizeTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/create',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      display_name: _req['display_name'],
      description: _req['description'],
      target: _req['target'],
      visible: _req['visible'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/execution/get
   *
   * 获取Execution详情
   */
  GetOptimizeExecution(
    req: flow_devops_prompt_optimize.GetOptimizeExecutionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.GetOptimizeExecutionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/execution/get',
    );
    const method = 'POST';
    const data = {
      execution_id: _req['execution_id'],
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/delete
   *
   * 删除Task
   */
  DeleteOptimizeTask(
    req: flow_devops_prompt_optimize.DeleteOptimizeTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.DeleteOptimizeTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/delete',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/executions/list
   *
   * 拉去历史执行记录列表
   */
  ListOptimizeExecution(
    req: flow_devops_prompt_optimize.ListOptimizeExecutionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.ListOptimizeExecutionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/executions/list',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/exec
   *
   * --------------- Prompt优化运行模块 ---------------
   *
   * 单次执行execution
   */
  ExecOptimizeTask(
    req: flow_devops_prompt_optimize.ExecOptimizeTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.ExecOptimizeTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/exec',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release
   *
   * prompt发布
   */
  ReleasePrompt(
    req: flow_devops_prompt_manage.ReleasePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ReleasePromptResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      env: _req['env'],
      feature: _req['feature'],
      space_id: _req['space_id'],
      gray_release_config: _req['gray_release_config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/list_promot_release_info
   *
   * 获取发布最终结果列表
   */
  ListPromptReleaseInfo(
    req: flow_devops_prompt_manage.ListPromptReleaseInfoRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListPromptReleaseInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/list_promot_release_info',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      env: _req['env'],
      feature: _req['feature'],
      status: _req['status'],
      label: _req['label'],
      version_like: _req['version_like'],
      cursor: _req['cursor'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/offline
   *
   * 下线发布的版本
   */
  OfflinePrompt(
    req: flow_devops_prompt_manage.OfflinePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.OfflinePromptResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/offline',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      env: _req['env'],
      feature: _req['feature'],
      space_id: _req['space_id'],
      label: _req['label'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/rollback_pre_version
   *
   * 发布回滚到上一个版本
   */
  RollBackPreVersionPrompt(
    req: flow_devops_prompt_manage.RollBackPreVersionPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.RollBackPreVersionPromptResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/rollback_pre_version',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      env: _req['env'],
      feature: _req['feature'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/prompt_platform/v1/optimize/task/execution/delete
   *
   * 删除执行记录
   */
  DeleteOptimizeExecution(
    req: flow_devops_prompt_optimize.DeleteOptimizeExecutionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.DeleteOptimizeExecutionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/execution/delete',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      execution_id: _req['execution_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/optimize/task/list
   *
   * 分页拉当前空间下的task列表
   */
  ListOptimizeTask(
    req: flow_devops_prompt_optimize.ListOptimizeTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_optimize.ListOptimizeTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/optimize/task/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      name: _req['name'],
      target_id: _req['target_id'],
      target_version: _req['target_version'],
      target_type: _req['target_type'],
      creator_id: _req['creator_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/prompt_platform/v1/prompt/releases/:release_id
   *
   * 获取prompt发布配置
   */
  GetPromptRelease(
    req: flow_devops_prompt_manage.GetPromptReleaseRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.GetPromptReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/prompt_platform/v1/prompt/releases/${_req['release_id']}`,
    );
    const method = 'GET';
    const data = { space_id: _req['space_id'], prompt_id: _req['prompt_id'] };
    const params = { base: _req['base'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/releases/:release_id/fulldose_gray_release
   *
   * 全量发布prompt灰度配置
   */
  FulldosePromptGrayRelease(
    req: flow_devops_prompt_manage.FulldosePromptGrayReleaseRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.FulldosePromptGrayReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/prompt_platform/v1/prompt/releases/${_req['release_id']}/fulldose_gray_release`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      prompt_id: _req['prompt_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/releases/:release_id/offline_gray_release
   *
   * 撤销prompt灰度发布
   */
  OfflinePromptGrayRelease(
    req: flow_devops_prompt_manage.OfflinePromptGrayReleaseRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.OfflinePromptGrayReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/prompt_platform/v1/prompt/releases/${_req['release_id']}/offline_gray_release`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      prompt_id: _req['prompt_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/devops/prompt_platform/v1/prompt/releases/:release_id
   *
   * 更新prompt发布配置
   */
  UpdatePromptRelease(
    req: flow_devops_prompt_manage.UpdatePromptReleaseRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.UpdatePromptReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/prompt_platform/v1/prompt/releases/${_req['release_id']}`,
    );
    const method = 'PUT';
    const data = {
      gray_release_config: _req['gray_release_config'],
      space_id: _req['space_id'],
      prompt_id: _req['prompt_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/eval_version
   *
   * 计算Prompt是否命中灰度，命中的灰度版本
   */
  EvalPromptVersion(
    req: flow_devops_prompt_paas.EvalPromptVersionRequest,
    options?: T,
  ): Promise<flow_devops_prompt_paas.EvalPromptVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/eval_version',
    );
    const method = 'POST';
    const data = {
      prompt_key: _req['prompt_key'],
      traffic: _req['traffic'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/clipboard/save
   *
   * 保存剪切板
   */
  SaveClipboard(
    req?: flow_devops_prompt_manage.SaveClipboardRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.SaveClipboardResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/clipboard/save',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      content: _req['content'],
      version: _req['version'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/clipboard/get
   *
   * 获取剪切板内容
   */
  GetClipboard(
    req?: flow_devops_prompt_manage.GetClipboardRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.GetClipboardResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/clipboard/get',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      content_key: _req['content_key'],
      version: _req['version'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/trigger_subtask
   *
   * 触发发布工单子任务状态变更
   */
  TriggerReleaseSubtask(
    req: flow_devops_prompt_manage.TriggerReleaseSubtaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.TriggerReleaseSubtaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/trigger_subtask',
    );
    const method = 'POST';
    const data = {
      release_task_id: _req['release_task_id'],
      subtask_key: _req['subtask_key'],
      trigger_operation: _req['trigger_operation'],
      comment: _req['comment'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/create
   *
   * --------------- 发布模块 ---------------
   *
   * 创建发布工单
   */
  CreateReleaseTask(
    req: flow_devops_prompt_manage.CreateReleaseTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.CreateReleaseTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/create',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      release_resources: _req['release_resources'],
      env: _req['env'],
      feature: _req['feature'],
      release_config: _req['release_config'],
      comment: _req['comment'],
      label: _req['label'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/get_detail
   *
   * 获取发布工单详情
   */
  GetReleaseTaskDetail(
    req: flow_devops_prompt_manage.GetReleaseTaskDetailRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.GetReleaseTaskDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/get_detail',
    );
    const method = 'POST';
    const data = {
      release_task_id: _req['release_task_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/cancel
   *
   * 取消发布工单
   */
  CancelReleaseTask(
    req: flow_devops_prompt_manage.CancelReleaseTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.CancelReleaseTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/cancel',
    );
    const method = 'POST';
    const data = {
      release_task_id: _req['release_task_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/notify_approver
   *
   * 通知审核人
   */
  NotifyReleaseApprover(
    req: flow_devops_prompt_manage.NotifyReleaseApproverRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.NotifyReleaseApproverResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/notify_approver',
    );
    const method = 'POST';
    const data = {
      release_task_id: _req['release_task_id'],
      notify_approvers: _req['notify_approvers'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/list_approver
   *
   * 获取审核人列表
   */
  ListReleaseApprover(
    req?: flow_devops_prompt_manage.ListReleaseApproverRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListReleaseApproverResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/list_approver',
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/rollback
   *
   * 回滚发布工单
   */
  RollbackReleaseTask(
    req: flow_devops_prompt_manage.RollbackReleaseTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.RollbackReleaseTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/rollback',
    );
    const method = 'POST';
    const data = {
      release_task_id: _req['release_task_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/release_task/list
   *
   * 获取发布工单列表
   */
  ListReleaseTask(
    req?: flow_devops_prompt_manage.ListReleaseTaskRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListReleaseTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/release_task/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      prompt_id: _req['prompt_id'],
      env: _req['env'],
      feature: _req['feature'],
      label: _req['label'],
      base: _req['base'],
    };
    const params = { page: _req['page'], page_size: _req['page_size'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/platform/lark_card_callback
   *
   * Lark卡片回调
   */
  LarkCardActionCallback(
    req?: flow_devops_prompt_platform.LarkCardActionCallbackRequest,
    options?: T,
  ): Promise<flow_devops_prompt_platform.LarkCardActionCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/platform/lark_card_callback',
    );
    const method = 'POST';
    const data = {
      challenge: _req['challenge'],
      schema: _req['schema'],
      header: _req['header'],
      event: _req['event'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/convert_uri_to_url
   *
   * uris批量转化为urls
   */
  MConvertURI2URL(
    req?: flow_devops_prompt_manage.MConvertURI2URLRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.MConvertURI2URLResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/convert_uri_to_url',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      uris: _req['uris'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /open-apis/prompt/v1/prompt/render_template */
  RenderPromptTemplate(
    req?: flow_devops_prompt_callback.RenderPromptTemplateRequest,
    options?: T,
  ): Promise<flow_devops_prompt_callback.RenderPromptTemplateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/open-apis/prompt/v1/prompt/render_template');
    const method = 'POST';
    const data = {
      prompt_template: _req['prompt_template'],
      variable_vals: _req['variable_vals'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-apis/prompt/v1/prompt/list
   *
   * Prompt平台管理模块对外接口
   *
   * Prompt列表
   */
  ListPromptBasicOApi(
    req: flow_devops_prompt_manage.ListPromptBasicOApiRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ListPromptBasicOApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/open-apis/prompt/v1/prompt/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      key_word: _req['key_word'],
      creator: _req['creator'],
      filter_prompt_types: _req['filter_prompt_types'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/batch_debug/load_info
   *
   * 加载批量调试信息
   */
  LoadBatchDebugInfo(
    req?: flow_devops_prompt_debug.LoadBatchDebugInfoRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.LoadBatchDebugInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/batch_debug/load_info',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/batch_execute
   *
   * 运行批量调试
   */
  ExecuteBatch(
    req?: flow_devops_prompt_debug.ExecuteBatchRequest,
    options?: T,
  ): Promise<flow_devops_prompt_debug.ExecuteBatchResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/batch_execute',
    );
    const method = 'POST';
    const data = {
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      case_id: _req['case_id'],
      dataset_id: _req['dataset_id'],
      row_group_ids: _req['row_group_ids'],
      mcp_execute_config: _req['mcp_execute_config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/prompt_platform/v1/prompt/clone
   *
   * 复制Prompt
   */
  ClonePrompt(
    req?: flow_devops_prompt_manage.ClonePromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.ClonePromptResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/prompt_platform/v1/prompt/clone');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      prompt_id: _req['prompt_id'],
      version: _req['version'],
      dest_prompt_key: _req['dest_prompt_key'],
      dest_display_name: _req['dest_display_name'],
      dest_description: _req['dest_description'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v1/loop/prompts/mget */
  MPullPromptCommercial(
    req?: flow_devops_prompt_commercial_openapi.MPullPromptRequest,
    options?: T,
  ): Promise<flow_devops_prompt_commercial_openapi.MPullPromptResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/loop/prompts/mget');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      queries: _req['queries'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/prompt_platform/v1/prompt/generate_record/update
   *
   * Prompt生成记录更新
   */
  UpdateGenerateRecord(
    req?: flow_devops_prompt_manage.UpdateGenerateRecordRequest,
    options?: T,
  ): Promise<flow_devops_prompt_manage.UpdateGenerateRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/prompt_platform/v1/prompt/generate_record/update',
    );
    const method = 'POST';
    const data = {
      record_id: _req['record_id'],
      prompt_id: _req['prompt_id'],
      space_id: _req['space_id'],
      is_liked: _req['is_liked'],
      is_disliked: _req['is_disliked'],
      is_accepted: _req['is_accepted'],
      is_canceled: _req['is_canceled'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/create
   *
   * 创建 MCP Server
   */
  CreateMCPServer(
    req?: flow_devops_prompt_mcp.CreateMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.CreateMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/mcp_manage/v1/mcp_servers/create');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      source_type: _req['source_type'],
      labels: _req['labels'],
      is_publish_supported: _req['is_publish_supported'],
      detail_page_url: _req['detail_page_url'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/list_official
   *
   * 官方MCP Server列表
   */
  ListOfficialMCPServers(
    req?: flow_devops_prompt_mcp.ListOfficialMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.ListOfficialMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/mcp_manage/v1/mcp_servers/list_official');
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      name_keyword: _req['name_keyword'],
      source_type: _req['source_type'],
      labels: _req['labels'],
      status: _req['status'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/list
   *
   * MCP Server列表
   */
  ListMCPServers(
    req?: flow_devops_prompt_mcp.ListMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.ListMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/mcp_manage/v1/mcp_servers/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      name_keyword: _req['name_keyword'],
      source_type: _req['source_type'],
      labels: _req['labels'],
      creator_list: _req['creator_list'],
      status: _req['status'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/delete
   *
   * 删除 MCP Server
   */
  DeleteMCPServer(
    req?: flow_devops_prompt_mcp.DeleteMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.DeleteMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/delete`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/update
   *
   * 更新 MCPServer AccessPoint
   */
  UpdateMCPServerAccessPoint(
    req?: flow_devops_prompt_mcp.UpdateMCPServerAccessPointRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.UpdateMCPServerAccessPointResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/access_points/${_req['access_point_id']}/update`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      env: _req['env'],
      lane: _req['lane'],
      transport_mode: _req['transport_mode'],
      server_url: _req['server_url'],
      server_psm: _req['server_psm'],
      config: _req['config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/create
   *
   * 创建 MCPServer AccessPoint 废弃
   */
  CreateMCPServerAccessPoint(
    req?: flow_devops_prompt_mcp.CreateMCPServerAccessPointRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.CreateMCPServerAccessPointResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/create',
    );
    const method = 'POST';
    const data = {
      mcp_server_id: _req['mcp_server_id'],
      space_id: _req['space_id'],
      env: _req['env'],
      lane: _req['lane'],
      transport_mode: _req['transport_mode'],
      server_url: _req['server_url'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/debug
   *
   * MCP页面调试Tools
   */
  DebugMCPServerTools(
    req?: flow_devops_prompt_mcp.DebugMCPServerToolsRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.DebugMCPServerToolsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/access_points/${_req['access_point_id']}/debug`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      tool_name: _req['tool_name'],
      parameters: _req['parameters'],
      dynamic_headers: _req['dynamic_headers'],
      dynamic_params: _req['dynamic_params'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/delete
   *
   * 删除 MCPServer AccessPoint
   */
  DeleteMCPServerAccessPoint(
    req?: flow_devops_prompt_mcp.DeleteMCPServerAccessPointRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.DeleteMCPServerAccessPointResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/access_points/${_req['access_point_id']}/delete`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/get
   *
   * 获取单个MCPServer详情
   */
  GetMCPServer(
    req?: flow_devops_prompt_mcp.GetMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.GetMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/get`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/update
   *
   * 更新 MCP Server
   */
  UpdateMCPServer(
    req?: flow_devops_prompt_mcp.UpdateMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.UpdateMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/update`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      source_type: _req['source_type'],
      labels: _req['labels'],
      is_publish_supported: _req['is_publish_supported'],
      detail_page_url: _req['detail_page_url'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/get
   *
   * 获取单个 MCPServer AccessPoint 详情
   */
  GetMCPServerAccessPoint(
    req?: flow_devops_prompt_mcp.GetMCPServerAccessPointRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.GetMCPServerAccessPointResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/access_points/${_req['access_point_id']}/get`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/create
   *
   * 创建 MCPServer AccessPoint v2
   */
  CreateMCPServerAccessPointV2(
    req?: flow_devops_prompt_mcp.CreateMCPServerAccessPointRequestV2,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.CreateMCPServerAccessPointResponseV2> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/access_points/create`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      env: _req['env'],
      lane: _req['lane'],
      transport_mode: _req['transport_mode'],
      server_url: _req['server_url'],
      server_psm: _req['server_psm'],
      config: _req['config'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/access_points/:access_point_id/update_status
   *
   * 更新 MCPServer AccessPoint 状态
   */
  UpdateMCPServerAccessPointStatus(
    req?: flow_devops_prompt_mcp.UpdateMCPServerAccessPointStatusRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.UpdateMCPServerAccessPointStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/access_points/${_req['access_point_id']}/update_status`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      status: _req['status'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/update_status
   *
   * 更新 MCP Server 状态
   */
  UpdateMCPServerStatus(
    req?: flow_devops_prompt_mcp.UpdateMCPServerStatusRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.UpdateMCPServerStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/update_status`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      status: _req['status'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/access_points/mget
   *
   * 批量获取 MCPServer AccessPoint 详情
   */
  MGetMCPServerAccessPoint(
    req?: flow_devops_prompt_mcp.MGetMCPServerAccessPointRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.MGetMCPServerAccessPointResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/mcp_manage/v1/mcp_servers/access_points/mget',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      accesspoint_ids: _req['accesspoint_ids'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/mcp_manage/v1/mcp_servers/:mcp_server_id/fork
   *
   * 复制 MCP Server
   */
  ForkMCPServer(
    req?: flow_devops_prompt_mcp.ForkMCPServerRequest,
    options?: T,
  ): Promise<flow_devops_prompt_mcp.ForkMCPServerResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/mcp_manage/v1/mcp_servers/${_req['mcp_server_id']}/fork`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
