/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as benefit_common from './namespaces/benefit_common';
import * as copilot_common from './namespaces/copilot_common';
import * as oapi from './namespaces/oapi';
import * as open_api from './namespaces/open_api';

export { base, benefit_common, copilot_common, oapi, open_api };
export * from './namespaces/base';
export * from './namespaces/benefit_common';
export * from './namespaces/copilot_common';
export * from './namespaces/oapi';
export * from './namespaces/open_api';

export type Int64 = string | number;

export default class DeveloperBackendService<T> {
  private request: any = () => {
    throw new Error('DeveloperBackendService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/open/permission/list
   *
   * API权限管理
   *
   * 获取权限列表
   */
  GetPermissionList(
    req?: open_api.GetPermissionListReq,
    options?: T,
  ): Promise<open_api.GetPermissionListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/permission/list');
    const method = 'POST';
    const data = {
      key_list: _req['key_list'],
      permission_id_list: _req['permission_id_list'],
      version: _req['version'],
      permission_type: _req['permission_type'],
      full_key_list: _req['full_key_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/data/analytics
   *
   * ---API 数据展示---
   *
   * 分析页
   */
  GetAPIAnalytics(
    req: open_api.APIAnalyticsReq,
    options?: T,
  ): Promise<open_api.APIAnalyticsResp> {
    const _req = req;
    const url = this.genBaseURL('/api/data/analytics');
    const method = 'GET';
    const params = {
      query_range: _req['query_range'],
      metrics_type: _req['metrics_type'],
      dimension: _req['dimension'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/data/details
   *
   * 详情页
   */
  GetAPIDetails(
    req: open_api.APIDetailsReq,
    options?: T,
  ): Promise<open_api.APIDetailsResp> {
    const _req = req;
    const url = this.genBaseURL('/api/data/details');
    const method = 'GET';
    const params = {
      query_range: _req['query_range'],
      details_type: _req['details_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/open/v2/permission/list */
  GetPermissionListV2(
    req?: open_api.GetPermissionListReq,
    options?: T,
  ): Promise<open_api.GetPermissionListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/v2/permission/list');
    const method = 'POST';
    const data = {
      key_list: _req['key_list'],
      permission_id_list: _req['permission_id_list'],
      version: _req['version'],
      permission_type: _req['permission_type'],
      full_key_list: _req['full_key_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/open/playground/item_list
   *
   * 以下是 playground 的接口, 技术方案： 
   *
   * 不需要登陆态
   *
   * 获取所有 playground 的所有接口与 websdk
   */
  GetPlaygroundItemList(
    req?: open_api.GetPlaygroundItemListReq,
    options?: T,
  ): Promise<open_api.GetPlaygroundItemListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/playground/item_list');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/open/playground/api_info
   *
   * 通过 playground api name 获取详情
   */
  GetPlaygroundApiInfo(
    req: open_api.GetPlaygroundApiInfoReq,
    options?: T,
  ): Promise<open_api.GetPlaygroundApiInfoResp> {
    const _req = req;
    const url = this.genBaseURL('/api/open/playground/api_info');
    const method = 'GET';
    const params = {
      url_key: _req['url_key'],
      workflow_id: _req['workflow_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/open/playground/websdk_info
   *
   * 获取 websdk 代码
   */
  GetPlaygroundWebSdkInfo(
    req?: open_api.GetPlaygroundWebSdkInfoReq,
    options?: T,
  ): Promise<open_api.GetPlaygroundWebSdkInfoResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/playground/websdk_info');
    const method = 'GET';
    const params = { version: _req['version'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/developer_backend/playground/sync_from_apihub
   *
   * openapi 用的 api
   *
   * 内网接口，从 apihub 同步 openapi swagger
   *
   * 从 apihub 同步最新的 openapi 接口与描述
   *
   * boe:
   */
  SyncFromApiHub(
    req?: open_api.SyncFromApiHubReq,
    options?: T,
  ): Promise<open_api.SyncFromApiHubResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/developer_backend/playground/sync_from_apihub',
    );
    const method = 'POST';
    const data = { raw_body: _req['raw_body'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/open/playground/doc
   *
   * arcosite 请求转发. 对外屏蔽 ak/sk
   */
  PlaygroundOpenApiDoc(
    req?: open_api.PlaygroundOpenApiDocReq,
    options?: T,
  ): Promise<open_api.PlaygroundOpenApiDocResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/playground/doc');
    const method = 'POST';
    const data = { body: _req['body'], Base: _req['Base'] };
    const headers = {
      'x-arcosite-action': _req['x-arcosite-action'],
      'Content-Type': _req['Content-Type'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/developer_backend/playground/all_api_info */
  GetPlaygroundAllApiInfo(
    req?: open_api.GetPlaygroundAllApiInfoReq,
    options?: T,
  ): Promise<open_api.GetPlaygroundAllApiInfoResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/developer_backend/playground/all_api_info',
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/open/permission/oauth_quickstart_config */
  OauthQuickstartConfig(
    req?: open_api.OauthQuickstartConfigReq,
    options?: T,
  ): Promise<open_api.OauthQuickstartConfigResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/permission/oauth_quickstart_config');
    const method = 'GET';
    const params = { client_type: _req['client_type'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/open/api_apps/update
   *
   * 4. 更新回调
   */
  UpdateApiApp(
    req: open_api.UpdateApiAppReq,
    options?: T,
  ): Promise<open_api.UpdateApiAppResp> {
    const _req = req;
    const url = this.genBaseURL('/api/open/api_apps/update');
    const method = 'POST';
    const data = {
      id: _req['id'],
      callback_url: _req['callback_url'],
      name: _req['name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/open/api_apps/delete
   *
   * 5. 删除回调
   */
  DeleteApiApp(
    req: open_api.DeleteApiAppReq,
    options?: T,
  ): Promise<open_api.DeleteApiAppResp> {
    const _req = req;
    const url = this.genBaseURL('/api/open/api_apps/delete');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/open/api_apps/list_normal
   *
   * --- API 回调能力 ---
   *
   * 技术方案: 
   *
   * 网页上用的 api
   *
   * 1. 获取普通回调列表
   */
  GetNormalApiAppList(
    req?: open_api.GetNormalApiAppListReq,
    options?: T,
  ): Promise<open_api.GetNormalApiAppListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/api_apps/list_normal');
    const method = 'GET';
    const params = {
      org_id: _req['org_id'],
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/open/api_apps/create
   *
   * 3. 创建回调
   */
  CreateApiApp(
    req: open_api.CreateApiAppReq,
    options?: T,
  ): Promise<open_api.CreateApiAppResp> {
    const _req = req;
    const url = this.genBaseURL('/api/open/api_apps/create');
    const method = 'POST';
    const data = {
      org_id: _req['org_id'],
      app_type: _req['app_type'],
      name: _req['name'],
      connector_id: _req['connector_id'],
      callback_url: _req['callback_url'],
      verify_token: _req['verify_token'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/open/api_apps/list_connector
   *
   * 2. 获取渠道回调列表
   */
  GetConnectorApiAppList(
    req?: open_api.GetConnectorApiAppListReq,
    options?: T,
  ): Promise<open_api.GetConnectorApiAppListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/api_apps/list_connector');
    const method = 'GET';
    const params = {
      org_id: _req['org_id'],
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/open/api_apps/unsubscribe
   *
   * 7. 取消订阅事件
   */
  UnsubscribeApiAppEvent(
    req: open_api.UnsubscribeApiAppEventReq,
    options?: T,
  ): Promise<open_api.UnsubscribeApiAppEventResp> {
    const _req = req;
    const url = this.genBaseURL('/api/open/api_apps/unsubscribe');
    const method = 'POST';
    const data = {
      id: _req['id'],
      event_type: _req['event_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/open/api_apps/subscribe
   *
   * 6. 订阅事件
   */
  SubscribeApiAppEvent(
    req: open_api.SubscribeApiAppEventReq,
    options?: T,
  ): Promise<open_api.SubscribeApiAppEventResp> {
    const _req = req;
    const url = this.genBaseURL('/api/open/api_apps/subscribe');
    const method = 'POST';
    const data = {
      id: _req['id'],
      event_type: _req['event_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/api_apps
   *
   * --- API 回调能力 ---
   *
   * 技术方案: 
   *
   * openapi
   *
   * 1. 获取普通回调列表
   */
  GetApiAppListOpen(
    req?: open_api.GetApiAppListOpenReq,
    options?: T,
  ): Promise<open_api.GetApiAppListOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/api_apps');
    const method = 'GET';
    const params = {
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      app_type: _req['app_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v1/api_apps
   *
   * 2. 创建回调
   */
  CreateApiAppOpen(
    req?: open_api.CreateApiAppOpenReq,
    options?: T,
  ): Promise<open_api.CreateApiAppOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/api_apps');
    const method = 'POST';
    const data = {
      app_type: _req['app_type'],
      name: _req['name'],
      connector_id: _req['connector_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/open/playground/save_run_history
   *
   * 调试页面，保存/获取运行记录
   */
  SavePlaygroundRunHistory(
    req?: open_api.SavePlaygroundRunHistoryReq,
    options?: T,
  ): Promise<open_api.SavePlaygroundRunHistoryResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/playground/save_run_history');
    const method = 'POST';
    const data = {
      path: _req['path'],
      method: _req['method'],
      record: _req['record'],
      org_id: _req['org_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/open/playground/run_histroty */
  GetPlaygroundRunHistory(
    req?: open_api.GetPlaygroundRunHistoryReq,
    options?: T,
  ): Promise<open_api.GetPlaygroundRunHistoryResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/open/playground/run_histroty');
    const method = 'GET';
    const params = {
      path: _req['path'],
      method: _req['method'],
      org_id: _req['org_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /v1/api_apps/:api_app_id/events
   *
   * 6. 取消订阅事件
   */
  UnsubscribeApiAppEventOpen(
    req?: open_api.UnsubscribeApiAppEventOpenReq,
    options?: T,
  ): Promise<open_api.UnsubscribeApiAppEventOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/api_apps/${_req['api_app_id']}/events`);
    const method = 'DELETE';
    const data = { event_types: _req['event_types'] };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * PUT /v1/api_apps/:api_app_id
   *
   * 3. 更新回调
   */
  UpdateApiAppOpen(
    req?: open_api.UpdateApiAppOpenReq,
    options?: T,
  ): Promise<open_api.UpdateApiAppOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/api_apps/${_req['api_app_id']}`);
    const method = 'PUT';
    const data = {
      name: _req['name'],
      callback_url: _req['callback_url'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /v1/api_apps/:api_app_id
   *
   * 4. 删除回调
   */
  DeleteApiAppOpen(
    req?: open_api.DeleteApiAppOpenReq,
    options?: T,
  ): Promise<open_api.DeleteApiAppOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/api_apps/${_req['api_app_id']}`);
    const method = 'DELETE';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v1/api_apps/:api_app_id/events
   *
   * 5. 订阅事件
   */
  SubscribeApiAppEventOpen(
    req?: open_api.SubscribeApiAppEventOpenReq,
    options?: T,
  ): Promise<open_api.SubscribeApiAppEventOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/api_apps/${_req['api_app_id']}/events`);
    const method = 'POST';
    const data = { event_types: _req['event_types'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/api_apps/:api_app_id/events
   *
   * 7. 获取订阅事件
   */
  ListSubscribedApiAppEventOpen(
    req?: open_api.ListSubscribedApiAppEventOpenReq,
    options?: T,
  ): Promise<open_api.ListSubscribedApiAppEventOpenResp> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/api_apps/${_req['api_app_id']}/events`);
    const method = 'GET';
    const params = {
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
