/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** *************************** audit ********************************* */
export enum AuditStatus {
  /** 审核中 */
  Auditing = 0,
  /** 审核通过 */
  Success = 1,
  /** 审核失败 */
  Failed = 2,
}

/** *************************** publish ********************************* */
export enum ConnectorDynamicStatus {
  Normal = 0,
  Offline = 1,
  TokenDisconnect = 2,
}

export enum FolderType {
  /** 项目/智能体文件夹 */
  App = 1,
  /** 资源文件夹 */
  Resource = 2,
}

export enum OrderByType {
  Asc = 1,
  Desc = 2,
}

export enum PermissionType {
  /** 不能查看详情 */
  NoDetail = 1,
  /** 可以查看详情 */
  Detail = 2,
  /** 可以查看和操作 */
  Operate = 3,
}

export enum ResourceType {
  Plugin = 1,
  Workflow = 2,
  Imageflow = 3,
  Knowledge = 4,
  UI = 5,
  Prompt = 6,
  Database = 7,
  Variable = 8,
}

export enum SpaceStatus {
  Valid = 1,
  Invalid = 2,
}

/** 审核结果 */
export interface AuditData {
  /** true：机审校验不通过 */
  check_not_pass?: boolean;
  /** 机审校验不通过文案 */
  check_not_pass_msg?: string;
}

export interface AuditInfo {
  audit_status?: AuditStatus;
  publish_id?: string;
  commit_version?: string;
}

export interface ConnectorInfo {
  id?: string;
  name?: string;
  icon?: string;
  connector_status?: ConnectorDynamicStatus;
  share_link?: string;
}

export interface FolderBasicInfo {
  id?: string;
  /** 父文件夹id */
  parent_id?: string;
  /** 文件夹类型 */
  folder_type?: FolderType;
  name?: string;
  description?: string;
  space_id?: string;
  creator_id?: string;
  /** 创建时间，秒级时间戳 */
  create_time?: string;
  /** 更新时间，秒级时间戳 */
  update_time?: string;
  /** 是否删除 */
  is_deleted?: boolean;
}

export interface FolderInfo {
  folder_basic_info?: FolderBasicInfo;
  /** 子文件夹列表，只有tree接口才返回 */
  children_list?: Array<FolderInfo>;
  /** 父级文件夹路径，不包含本文件夹 */
  parent_path?: Array<FolderBasicInfo>;
  /** 父级文件夹路径，包含本文件夹 */
  full_path?: Array<FolderBasicInfo>;
  /** 文件夹创建者信息 */
  folder_creator_info?: User;
}

export interface Space {
  id?: Int64;
  owner_id?: Int64;
  status?: SpaceStatus;
  name?: string;
}

export interface User {
  user_id?: string;
  /** 用户昵称 */
  nickname?: string;
  /** 用户头像 */
  avatar_url?: string;
  /** 用户名 */
  user_unique_name?: string;
  /** 用户标签 */
  user_label?: UserLabel;
}

/** *************************** user ********************************* */
export interface UserLabel {
  label_id?: string;
  label_name?: string;
  icon_uri?: string;
  icon_url?: string;
  jump_link?: string;
}

export interface Variable {
  /** 变量名 */
  keyword?: string;
  /** 默认值 */
  default_value?: string;
  /** 变量类型 */
  variable_type?: string;
  /** 变量来源 */
  channel?: string;
  /** 变量描述 */
  description?: string;
  /** 是否启用 */
  enable?: boolean;
  /** 变量默认支持在Prompt中访问，取消勾选后将不支持在Prompt中访问（仅能在Workflow中访问 */
  prompt_enable?: boolean;
}
/* eslint-enable */
