/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as ob_trace from './ob_trace';

export type Int64 = string | number;

export interface BatchGetTracesAdvanceInfoV2Request {
  /** space id */
  space_id: string;
  /** 场景参数  不同场景使用的通用参数 */
  scene_param: common.SceneCommonParam;
  traces: Array<ob_trace.TraceQueryParams>;
}

export interface BatchGetTracesAdvanceInfoV2Response {
  data: ob_trace.BatchGetTracesAdvanceInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTraceV2Request {
  /** space id */
  space_id: string;
  /** 场景参数  不同场景使用的通用参数 */
  scene_param: common.SceneCommonParam;
  trace_id: string;
  start_time: string;
  end_time: string;
}

export interface GetTraceV2Response {
  data: ob_trace.GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}
/* eslint-enable */
