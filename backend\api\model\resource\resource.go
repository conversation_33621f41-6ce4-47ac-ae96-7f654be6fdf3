// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package resource

import (
	"github.com/coze-dev/coze-studio/backend/api/model/base"
	"github.com/coze-dev/coze-studio/backend/api/model/resource/common"
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type LibraryResourceListRequest struct {
	// 是否由当前用户创建，0-不筛选，1-当前用户
	UserFilter *int32 `thrift:"user_filter,1,optional" form:"user_filter" json:"user_filter,omitempty" query:"user_filter"`
	// [4,1]   0代表不筛选
	ResTypeFilter []common.ResType `thrift:"res_type_filter,2,optional" form:"res_type_filter" json:"res_type_filter,omitempty" query:"res_type_filter"`
	// 名称
	Name *string `thrift:"name,3,optional" form:"name" json:"name,omitempty" query:"name"`
	// 发布状态，0-不筛选，1-未发布，2-已发布
	PublishStatusFilter *common.PublishStatus `thrift:"publish_status_filter,4,optional" form:"publish_status_filter" json:"publish_status_filter,omitempty" query:"publish_status_filter"`
	// 用户所在空间ID
	SpaceID int64 `thrift:"space_id,5,required" form:"space_id,required" json:"space_id,string,required" query:"space_id,required"`
	// 一次读取的数据条数，默认10，最大100.
	Size *int32 `thrift:"size,7,optional" form:"size" json:"size,omitempty" query:"size"`
	// 游标，用于分页，默认0，第一次请求可以不传，后续请求需要带上上次返回的cursor
	Cursor *string `thrift:"cursor,9,optional" form:"cursor" json:"cursor,omitempty" query:"cursor"`
	// 用来指定自定义搜索的字段 不填默认只name匹配，eg []string{name,自定} 匹配name和自定义字段full_text
	SearchKeys []string `thrift:"search_keys,10,optional" form:"search_keys" json:"search_keys,omitempty" query:"search_keys"`
	// 当res_type_filter为[2 workflow]时，是否需要返回图片流
	IsGetImageflow *bool      `thrift:"is_get_imageflow,11,optional" form:"is_get_imageflow" json:"is_get_imageflow,omitempty" query:"is_get_imageflow"`
	Base           *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewLibraryResourceListRequest() *LibraryResourceListRequest {
	return &LibraryResourceListRequest{}
}

func (p *LibraryResourceListRequest) InitDefault() {
}

var LibraryResourceListRequest_UserFilter_DEFAULT int32

func (p *LibraryResourceListRequest) GetUserFilter() (v int32) {
	if !p.IsSetUserFilter() {
		return LibraryResourceListRequest_UserFilter_DEFAULT
	}
	return *p.UserFilter
}

var LibraryResourceListRequest_ResTypeFilter_DEFAULT []common.ResType

func (p *LibraryResourceListRequest) GetResTypeFilter() (v []common.ResType) {
	if !p.IsSetResTypeFilter() {
		return LibraryResourceListRequest_ResTypeFilter_DEFAULT
	}
	return p.ResTypeFilter
}

var LibraryResourceListRequest_Name_DEFAULT string

func (p *LibraryResourceListRequest) GetName() (v string) {
	if !p.IsSetName() {
		return LibraryResourceListRequest_Name_DEFAULT
	}
	return *p.Name
}

var LibraryResourceListRequest_PublishStatusFilter_DEFAULT common.PublishStatus

func (p *LibraryResourceListRequest) GetPublishStatusFilter() (v common.PublishStatus) {
	if !p.IsSetPublishStatusFilter() {
		return LibraryResourceListRequest_PublishStatusFilter_DEFAULT
	}
	return *p.PublishStatusFilter
}

func (p *LibraryResourceListRequest) GetSpaceID() (v int64) {
	return p.SpaceID
}

var LibraryResourceListRequest_Size_DEFAULT int32

func (p *LibraryResourceListRequest) GetSize() (v int32) {
	if !p.IsSetSize() {
		return LibraryResourceListRequest_Size_DEFAULT
	}
	return *p.Size
}

var LibraryResourceListRequest_Cursor_DEFAULT string

func (p *LibraryResourceListRequest) GetCursor() (v string) {
	if !p.IsSetCursor() {
		return LibraryResourceListRequest_Cursor_DEFAULT
	}
	return *p.Cursor
}

var LibraryResourceListRequest_SearchKeys_DEFAULT []string

func (p *LibraryResourceListRequest) GetSearchKeys() (v []string) {
	if !p.IsSetSearchKeys() {
		return LibraryResourceListRequest_SearchKeys_DEFAULT
	}
	return p.SearchKeys
}

var LibraryResourceListRequest_IsGetImageflow_DEFAULT bool

func (p *LibraryResourceListRequest) GetIsGetImageflow() (v bool) {
	if !p.IsSetIsGetImageflow() {
		return LibraryResourceListRequest_IsGetImageflow_DEFAULT
	}
	return *p.IsGetImageflow
}

var LibraryResourceListRequest_Base_DEFAULT *base.Base

func (p *LibraryResourceListRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return LibraryResourceListRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_LibraryResourceListRequest = map[int16]string{
	1:   "user_filter",
	2:   "res_type_filter",
	3:   "name",
	4:   "publish_status_filter",
	5:   "space_id",
	7:   "size",
	9:   "cursor",
	10:  "search_keys",
	11:  "is_get_imageflow",
	255: "Base",
}

func (p *LibraryResourceListRequest) IsSetUserFilter() bool {
	return p.UserFilter != nil
}

func (p *LibraryResourceListRequest) IsSetResTypeFilter() bool {
	return p.ResTypeFilter != nil
}

func (p *LibraryResourceListRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *LibraryResourceListRequest) IsSetPublishStatusFilter() bool {
	return p.PublishStatusFilter != nil
}

func (p *LibraryResourceListRequest) IsSetSize() bool {
	return p.Size != nil
}

func (p *LibraryResourceListRequest) IsSetCursor() bool {
	return p.Cursor != nil
}

func (p *LibraryResourceListRequest) IsSetSearchKeys() bool {
	return p.SearchKeys != nil
}

func (p *LibraryResourceListRequest) IsSetIsGetImageflow() bool {
	return p.IsGetImageflow != nil
}

func (p *LibraryResourceListRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *LibraryResourceListRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetSpaceID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
				issetSpaceID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetSpaceID {
		fieldId = 5
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LibraryResourceListRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LibraryResourceListRequest[fieldId]))
}

func (p *LibraryResourceListRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UserFilter = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]common.ResType, 0, size)
	for i := 0; i < size; i++ {

		var _elem common.ResType
		if v, err := iprot.ReadI32(); err != nil {
			return err
		} else {
			_elem = common.ResType(v)
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResTypeFilter = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *common.PublishStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := common.PublishStatus(v)
		_field = &tmp
	}
	p.PublishStatusFilter = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField7(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Size = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField9(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Cursor = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]string, 0, size)
	for i := 0; i < size; i++ {

		var _elem string
		if v, err := iprot.ReadString(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.SearchKeys = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField11(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsGetImageflow = _field
	return nil
}
func (p *LibraryResourceListRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *LibraryResourceListRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LibraryResourceListRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LibraryResourceListRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetUserFilter() {
		if err = oprot.WriteFieldBegin("user_filter", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.UserFilter); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResTypeFilter() {
		if err = oprot.WriteFieldBegin("res_type_filter", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ResTypeFilter)); err != nil {
			return err
		}
		for _, v := range p.ResTypeFilter {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishStatusFilter() {
		if err = oprot.WriteFieldBegin("publish_status_filter", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.PublishStatusFilter)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_id", thrift.I64, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSize() {
		if err = oprot.WriteFieldBegin("size", thrift.I32, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Size); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetCursor() {
		if err = oprot.WriteFieldBegin("cursor", thrift.STRING, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Cursor); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchKeys() {
		if err = oprot.WriteFieldBegin("search_keys", thrift.LIST, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SearchKeys)); err != nil {
			return err
		}
		for _, v := range p.SearchKeys {
			if err := oprot.WriteString(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsGetImageflow() {
		if err = oprot.WriteFieldBegin("is_get_imageflow", thrift.BOOL, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsGetImageflow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}
func (p *LibraryResourceListRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *LibraryResourceListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LibraryResourceListRequest(%+v)", *p)

}

type LibraryResourceListResponse struct {
	Code         int64                  `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg          string                 `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	ResourceList []*common.ResourceInfo `thrift:"resource_list,3" form:"resource_list" json:"resource_list" query:"resource_list"`
	// 游标，用于下次请求的cursor
	Cursor *string `thrift:"cursor,5,optional" form:"cursor" json:"cursor,omitempty" query:"cursor"`
	// 是否还有数据待拉取
	HasMore  bool           `thrift:"has_more,6" form:"has_more" json:"has_more" query:"has_more"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewLibraryResourceListResponse() *LibraryResourceListResponse {
	return &LibraryResourceListResponse{}
}

func (p *LibraryResourceListResponse) InitDefault() {
}

func (p *LibraryResourceListResponse) GetCode() (v int64) {
	return p.Code
}

func (p *LibraryResourceListResponse) GetMsg() (v string) {
	return p.Msg
}

func (p *LibraryResourceListResponse) GetResourceList() (v []*common.ResourceInfo) {
	return p.ResourceList
}

var LibraryResourceListResponse_Cursor_DEFAULT string

func (p *LibraryResourceListResponse) GetCursor() (v string) {
	if !p.IsSetCursor() {
		return LibraryResourceListResponse_Cursor_DEFAULT
	}
	return *p.Cursor
}

func (p *LibraryResourceListResponse) GetHasMore() (v bool) {
	return p.HasMore
}

var LibraryResourceListResponse_BaseResp_DEFAULT *base.BaseResp

func (p *LibraryResourceListResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return LibraryResourceListResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_LibraryResourceListResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	3:   "resource_list",
	5:   "cursor",
	6:   "has_more",
	255: "BaseResp",
}

func (p *LibraryResourceListResponse) IsSetCursor() bool {
	return p.Cursor != nil
}

func (p *LibraryResourceListResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *LibraryResourceListResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_LibraryResourceListResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_LibraryResourceListResponse[fieldId]))
}

func (p *LibraryResourceListResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *LibraryResourceListResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *LibraryResourceListResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.ResourceInfo, 0, size)
	values := make([]common.ResourceInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResourceList = _field
	return nil
}
func (p *LibraryResourceListResponse) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Cursor = _field
	return nil
}
func (p *LibraryResourceListResponse) ReadField6(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasMore = _field
	return nil
}
func (p *LibraryResourceListResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *LibraryResourceListResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LibraryResourceListResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *LibraryResourceListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *LibraryResourceListResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *LibraryResourceListResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("resource_list", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceList)); err != nil {
		return err
	}
	for _, v := range p.ResourceList {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *LibraryResourceListResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCursor() {
		if err = oprot.WriteFieldBegin("cursor", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Cursor); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *LibraryResourceListResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("has_more", thrift.BOOL, 6); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasMore); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *LibraryResourceListResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *LibraryResourceListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LibraryResourceListResponse(%+v)", *p)

}

type ProjectResourceListRequest struct {
	// 项目ID
	ProjectID int64 `thrift:"project_id,1,required" form:"project_id,required" json:"project_id,string,required" query:"project_id,required"`
	// 用户所在space id
	SpaceID int64 `thrift:"space_id,2" form:"space_id" json:"space_id,string" query:"space_id"`
	// 指定获取某个版本的project的资源
	ProjectVersion *string    `thrift:"project_version,3,optional" form:"project_version" json:"project_version,omitempty" query:"project_version"`
	Base           *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewProjectResourceListRequest() *ProjectResourceListRequest {
	return &ProjectResourceListRequest{}
}

func (p *ProjectResourceListRequest) InitDefault() {
}

func (p *ProjectResourceListRequest) GetProjectID() (v int64) {
	return p.ProjectID
}

func (p *ProjectResourceListRequest) GetSpaceID() (v int64) {
	return p.SpaceID
}

var ProjectResourceListRequest_ProjectVersion_DEFAULT string

func (p *ProjectResourceListRequest) GetProjectVersion() (v string) {
	if !p.IsSetProjectVersion() {
		return ProjectResourceListRequest_ProjectVersion_DEFAULT
	}
	return *p.ProjectVersion
}

var ProjectResourceListRequest_Base_DEFAULT *base.Base

func (p *ProjectResourceListRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ProjectResourceListRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ProjectResourceListRequest = map[int16]string{
	1:   "project_id",
	2:   "space_id",
	3:   "project_version",
	255: "Base",
}

func (p *ProjectResourceListRequest) IsSetProjectVersion() bool {
	return p.ProjectVersion != nil
}

func (p *ProjectResourceListRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ProjectResourceListRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetProjectID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetProjectID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetProjectID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProjectResourceListRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ProjectResourceListRequest[fieldId]))
}

func (p *ProjectResourceListRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ProjectID = _field
	return nil
}
func (p *ProjectResourceListRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.SpaceID = _field
	return nil
}
func (p *ProjectResourceListRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectVersion = _field
	return nil
}
func (p *ProjectResourceListRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ProjectResourceListRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceListRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ProjectResourceListRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("project_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ProjectID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ProjectResourceListRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("space_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.SpaceID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ProjectResourceListRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectVersion() {
		if err = oprot.WriteFieldBegin("project_version", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ProjectVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ProjectResourceListRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ProjectResourceListRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectResourceListRequest(%+v)", *p)

}

type ProjectResourceListResponse struct {
	Code           int64                          `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg            string                         `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	ResourceGroups []*common.ProjectResourceGroup `thrift:"resource_groups,3" form:"resource_groups" json:"resource_groups" query:"resource_groups"`
	BaseResp       *base.BaseResp                 `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewProjectResourceListResponse() *ProjectResourceListResponse {
	return &ProjectResourceListResponse{}
}

func (p *ProjectResourceListResponse) InitDefault() {
}

func (p *ProjectResourceListResponse) GetCode() (v int64) {
	return p.Code
}

func (p *ProjectResourceListResponse) GetMsg() (v string) {
	return p.Msg
}

func (p *ProjectResourceListResponse) GetResourceGroups() (v []*common.ProjectResourceGroup) {
	return p.ResourceGroups
}

var ProjectResourceListResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ProjectResourceListResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ProjectResourceListResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_ProjectResourceListResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	3:   "resource_groups",
	255: "BaseResp",
}

func (p *ProjectResourceListResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ProjectResourceListResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ProjectResourceListResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ProjectResourceListResponse[fieldId]))
}

func (p *ProjectResourceListResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ProjectResourceListResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *ProjectResourceListResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.ProjectResourceGroup, 0, size)
	values := make([]common.ProjectResourceGroup, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ResourceGroups = _field
	return nil
}
func (p *ProjectResourceListResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ProjectResourceListResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceListResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ProjectResourceListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ProjectResourceListResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ProjectResourceListResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("resource_groups", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResourceGroups)); err != nil {
		return err
	}
	for _, v := range p.ResourceGroups {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ProjectResourceListResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ProjectResourceListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectResourceListResponse(%+v)", *p)

}

type ResourceCopyDispatchRequest struct {
	// 场景，只支持单资源的操作
	Scene common.ResourceCopyScene `thrift:"scene,1" form:"scene" json:"scene" query:"scene"`
	// 被用户选择复制/移动的资源ID
	ResID   int64          `thrift:"res_id,2" form:"res_id" json:"res_id,string"`
	ResType common.ResType `thrift:"res_type,3" form:"res_type" json:"res_type" query:"res_type"`
	// 所在项目ID
	ProjectID *int64  `thrift:"project_id,4,optional" form:"project_id" json:"project_id,string,omitempty"`
	ResName   *string `thrift:"res_name,5,optional" form:"res_name" json:"res_name,omitempty" query:"res_name"`
	// 跨空间复制的目标space id
	TargetSpaceID *int64     `thrift:"target_space_id,6,optional" form:"target_space_id" json:"target_space_id,string,omitempty"`
	Base          *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewResourceCopyDispatchRequest() *ResourceCopyDispatchRequest {
	return &ResourceCopyDispatchRequest{}
}

func (p *ResourceCopyDispatchRequest) InitDefault() {
}

func (p *ResourceCopyDispatchRequest) GetScene() (v common.ResourceCopyScene) {
	return p.Scene
}

func (p *ResourceCopyDispatchRequest) GetResID() (v int64) {
	return p.ResID
}

func (p *ResourceCopyDispatchRequest) GetResType() (v common.ResType) {
	return p.ResType
}

var ResourceCopyDispatchRequest_ProjectID_DEFAULT int64

func (p *ResourceCopyDispatchRequest) GetProjectID() (v int64) {
	if !p.IsSetProjectID() {
		return ResourceCopyDispatchRequest_ProjectID_DEFAULT
	}
	return *p.ProjectID
}

var ResourceCopyDispatchRequest_ResName_DEFAULT string

func (p *ResourceCopyDispatchRequest) GetResName() (v string) {
	if !p.IsSetResName() {
		return ResourceCopyDispatchRequest_ResName_DEFAULT
	}
	return *p.ResName
}

var ResourceCopyDispatchRequest_TargetSpaceID_DEFAULT int64

func (p *ResourceCopyDispatchRequest) GetTargetSpaceID() (v int64) {
	if !p.IsSetTargetSpaceID() {
		return ResourceCopyDispatchRequest_TargetSpaceID_DEFAULT
	}
	return *p.TargetSpaceID
}

var ResourceCopyDispatchRequest_Base_DEFAULT *base.Base

func (p *ResourceCopyDispatchRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ResourceCopyDispatchRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ResourceCopyDispatchRequest = map[int16]string{
	1:   "scene",
	2:   "res_id",
	3:   "res_type",
	4:   "project_id",
	5:   "res_name",
	6:   "target_space_id",
	255: "Base",
}

func (p *ResourceCopyDispatchRequest) IsSetProjectID() bool {
	return p.ProjectID != nil
}

func (p *ResourceCopyDispatchRequest) IsSetResName() bool {
	return p.ResName != nil
}

func (p *ResourceCopyDispatchRequest) IsSetTargetSpaceID() bool {
	return p.TargetSpaceID != nil
}

func (p *ResourceCopyDispatchRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ResourceCopyDispatchRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyDispatchRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCopyDispatchRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field common.ResourceCopyScene
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.ResourceCopyScene(v)
	}
	p.Scene = _field
	return nil
}
func (p *ResourceCopyDispatchRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ResID = _field
	return nil
}
func (p *ResourceCopyDispatchRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field common.ResType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = common.ResType(v)
	}
	p.ResType = _field
	return nil
}
func (p *ResourceCopyDispatchRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectID = _field
	return nil
}
func (p *ResourceCopyDispatchRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ResName = _field
	return nil
}
func (p *ResourceCopyDispatchRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TargetSpaceID = _field
	return nil
}
func (p *ResourceCopyDispatchRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ResourceCopyDispatchRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDispatchRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyDispatchRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("scene", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Scene)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyDispatchRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("res_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ResID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyDispatchRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("res_type", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ResType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceCopyDispatchRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectID() {
		if err = oprot.WriteFieldBegin("project_id", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ProjectID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceCopyDispatchRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetResName() {
		if err = oprot.WriteFieldBegin("res_name", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ResName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ResourceCopyDispatchRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTargetSpaceID() {
		if err = oprot.WriteFieldBegin("target_space_id", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.TargetSpaceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *ResourceCopyDispatchRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyDispatchRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyDispatchRequest(%+v)", *p)

}

type ResourceCopyDispatchResponse struct {
	Code int64  `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg  string `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	// 复制任务id, 用于查询任务状态或取消、重试任务
	TaskID *string `thrift:"task_id,3,optional" form:"task_id" json:"task_id,omitempty" query:"task_id"`
	// 不可以进行操作的原因，返回多语言文本
	FailedReasons []*common.ResourceCopyFailedReason `thrift:"failed_reasons,4,optional" form:"failed_reasons" json:"failed_reasons,omitempty" query:"failed_reasons"`
	BaseResp      *base.BaseResp                     `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewResourceCopyDispatchResponse() *ResourceCopyDispatchResponse {
	return &ResourceCopyDispatchResponse{}
}

func (p *ResourceCopyDispatchResponse) InitDefault() {
}

func (p *ResourceCopyDispatchResponse) GetCode() (v int64) {
	return p.Code
}

func (p *ResourceCopyDispatchResponse) GetMsg() (v string) {
	return p.Msg
}

var ResourceCopyDispatchResponse_TaskID_DEFAULT string

func (p *ResourceCopyDispatchResponse) GetTaskID() (v string) {
	if !p.IsSetTaskID() {
		return ResourceCopyDispatchResponse_TaskID_DEFAULT
	}
	return *p.TaskID
}

var ResourceCopyDispatchResponse_FailedReasons_DEFAULT []*common.ResourceCopyFailedReason

func (p *ResourceCopyDispatchResponse) GetFailedReasons() (v []*common.ResourceCopyFailedReason) {
	if !p.IsSetFailedReasons() {
		return ResourceCopyDispatchResponse_FailedReasons_DEFAULT
	}
	return p.FailedReasons
}

var ResourceCopyDispatchResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ResourceCopyDispatchResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ResourceCopyDispatchResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_ResourceCopyDispatchResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	3:   "task_id",
	4:   "failed_reasons",
	255: "BaseResp",
}

func (p *ResourceCopyDispatchResponse) IsSetTaskID() bool {
	return p.TaskID != nil
}

func (p *ResourceCopyDispatchResponse) IsSetFailedReasons() bool {
	return p.FailedReasons != nil
}

func (p *ResourceCopyDispatchResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ResourceCopyDispatchResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyDispatchResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResourceCopyDispatchResponse[fieldId]))
}

func (p *ResourceCopyDispatchResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ResourceCopyDispatchResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *ResourceCopyDispatchResponse) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.TaskID = _field
	return nil
}
func (p *ResourceCopyDispatchResponse) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.ResourceCopyFailedReason, 0, size)
	values := make([]common.ResourceCopyFailedReason, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FailedReasons = _field
	return nil
}
func (p *ResourceCopyDispatchResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ResourceCopyDispatchResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDispatchResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyDispatchResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyDispatchResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyDispatchResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskID() {
		if err = oprot.WriteFieldBegin("task_id", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.TaskID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceCopyDispatchResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailedReasons() {
		if err = oprot.WriteFieldBegin("failed_reasons", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FailedReasons)); err != nil {
			return err
		}
		for _, v := range p.FailedReasons {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceCopyDispatchResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyDispatchResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyDispatchResponse(%+v)", *p)

}

type ResourceCopyDetailRequest struct {
	// 复制任务id, 用于查询任务状态或取消、重试任务
	TaskID string     `thrift:"task_id,1" form:"task_id" json:"task_id" query:"task_id"`
	Base   *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewResourceCopyDetailRequest() *ResourceCopyDetailRequest {
	return &ResourceCopyDetailRequest{}
}

func (p *ResourceCopyDetailRequest) InitDefault() {
}

func (p *ResourceCopyDetailRequest) GetTaskID() (v string) {
	return p.TaskID
}

var ResourceCopyDetailRequest_Base_DEFAULT *base.Base

func (p *ResourceCopyDetailRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ResourceCopyDetailRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ResourceCopyDetailRequest = map[int16]string{
	1:   "task_id",
	255: "Base",
}

func (p *ResourceCopyDetailRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ResourceCopyDetailRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyDetailRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCopyDetailRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *ResourceCopyDetailRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ResourceCopyDetailRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDetailRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyDetailRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyDetailRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyDetailRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyDetailRequest(%+v)", *p)

}

type ResourceCopyDetailResponse struct {
	Code       int64                          `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg        string                         `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	TaskDetail *common.ResourceCopyTaskDetail `thrift:"task_detail,3,optional" form:"task_detail" json:"task_detail,omitempty" query:"task_detail"`
	BaseResp   *base.BaseResp                 `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewResourceCopyDetailResponse() *ResourceCopyDetailResponse {
	return &ResourceCopyDetailResponse{}
}

func (p *ResourceCopyDetailResponse) InitDefault() {
}

func (p *ResourceCopyDetailResponse) GetCode() (v int64) {
	return p.Code
}

func (p *ResourceCopyDetailResponse) GetMsg() (v string) {
	return p.Msg
}

var ResourceCopyDetailResponse_TaskDetail_DEFAULT *common.ResourceCopyTaskDetail

func (p *ResourceCopyDetailResponse) GetTaskDetail() (v *common.ResourceCopyTaskDetail) {
	if !p.IsSetTaskDetail() {
		return ResourceCopyDetailResponse_TaskDetail_DEFAULT
	}
	return p.TaskDetail
}

var ResourceCopyDetailResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ResourceCopyDetailResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ResourceCopyDetailResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_ResourceCopyDetailResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	3:   "task_detail",
	255: "BaseResp",
}

func (p *ResourceCopyDetailResponse) IsSetTaskDetail() bool {
	return p.TaskDetail != nil
}

func (p *ResourceCopyDetailResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ResourceCopyDetailResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyDetailResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResourceCopyDetailResponse[fieldId]))
}

func (p *ResourceCopyDetailResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ResourceCopyDetailResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *ResourceCopyDetailResponse) ReadField3(iprot thrift.TProtocol) error {
	_field := common.NewResourceCopyTaskDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.TaskDetail = _field
	return nil
}
func (p *ResourceCopyDetailResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ResourceCopyDetailResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDetailResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyDetailResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyDetailResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyDetailResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskDetail() {
		if err = oprot.WriteFieldBegin("task_detail", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.TaskDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ResourceCopyDetailResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyDetailResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyDetailResponse(%+v)", *p)

}

type ResourceCopyRetryRequest struct {
	// 复制任务id, 用于查询任务状态或取消、重试任务
	TaskID string     `thrift:"task_id,1" form:"task_id" json:"task_id" query:"task_id"`
	Base   *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewResourceCopyRetryRequest() *ResourceCopyRetryRequest {
	return &ResourceCopyRetryRequest{}
}

func (p *ResourceCopyRetryRequest) InitDefault() {
}

func (p *ResourceCopyRetryRequest) GetTaskID() (v string) {
	return p.TaskID
}

var ResourceCopyRetryRequest_Base_DEFAULT *base.Base

func (p *ResourceCopyRetryRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ResourceCopyRetryRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ResourceCopyRetryRequest = map[int16]string{
	1:   "task_id",
	255: "Base",
}

func (p *ResourceCopyRetryRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ResourceCopyRetryRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyRetryRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCopyRetryRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *ResourceCopyRetryRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ResourceCopyRetryRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyRetryRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyRetryRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyRetryRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyRetryRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyRetryRequest(%+v)", *p)

}

type ResourceCopyRetryResponse struct {
	Code int64  `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg  string `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	// 不可以进行操作的原因，返回多语言文本
	FailedReasons []*common.ResourceCopyFailedReason `thrift:"failed_reasons,4,optional" form:"failed_reasons" json:"failed_reasons,omitempty" query:"failed_reasons"`
	BaseResp      *base.BaseResp                     `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewResourceCopyRetryResponse() *ResourceCopyRetryResponse {
	return &ResourceCopyRetryResponse{}
}

func (p *ResourceCopyRetryResponse) InitDefault() {
}

func (p *ResourceCopyRetryResponse) GetCode() (v int64) {
	return p.Code
}

func (p *ResourceCopyRetryResponse) GetMsg() (v string) {
	return p.Msg
}

var ResourceCopyRetryResponse_FailedReasons_DEFAULT []*common.ResourceCopyFailedReason

func (p *ResourceCopyRetryResponse) GetFailedReasons() (v []*common.ResourceCopyFailedReason) {
	if !p.IsSetFailedReasons() {
		return ResourceCopyRetryResponse_FailedReasons_DEFAULT
	}
	return p.FailedReasons
}

var ResourceCopyRetryResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ResourceCopyRetryResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ResourceCopyRetryResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_ResourceCopyRetryResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	4:   "failed_reasons",
	255: "BaseResp",
}

func (p *ResourceCopyRetryResponse) IsSetFailedReasons() bool {
	return p.FailedReasons != nil
}

func (p *ResourceCopyRetryResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ResourceCopyRetryResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyRetryResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResourceCopyRetryResponse[fieldId]))
}

func (p *ResourceCopyRetryResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ResourceCopyRetryResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *ResourceCopyRetryResponse) ReadField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*common.ResourceCopyFailedReason, 0, size)
	values := make([]common.ResourceCopyFailedReason, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.FailedReasons = _field
	return nil
}
func (p *ResourceCopyRetryResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ResourceCopyRetryResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyRetryResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyRetryResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyRetryResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyRetryResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailedReasons() {
		if err = oprot.WriteFieldBegin("failed_reasons", thrift.LIST, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FailedReasons)); err != nil {
			return err
		}
		for _, v := range p.FailedReasons {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ResourceCopyRetryResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyRetryResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyRetryResponse(%+v)", *p)

}

type ResourceCopyCancelRequest struct {
	// 复制任务id, 用于查询任务状态或取消、重试任务
	TaskID string     `thrift:"task_id,1" form:"task_id" json:"task_id" query:"task_id"`
	Base   *base.Base `thrift:"Base,255" form:"Base" json:"Base" query:"Base"`
}

func NewResourceCopyCancelRequest() *ResourceCopyCancelRequest {
	return &ResourceCopyCancelRequest{}
}

func (p *ResourceCopyCancelRequest) InitDefault() {
}

func (p *ResourceCopyCancelRequest) GetTaskID() (v string) {
	return p.TaskID
}

var ResourceCopyCancelRequest_Base_DEFAULT *base.Base

func (p *ResourceCopyCancelRequest) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return ResourceCopyCancelRequest_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_ResourceCopyCancelRequest = map[int16]string{
	1:   "task_id",
	255: "Base",
}

func (p *ResourceCopyCancelRequest) IsSetBase() bool {
	return p.Base != nil
}

func (p *ResourceCopyCancelRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyCancelRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceCopyCancelRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.TaskID = _field
	return nil
}
func (p *ResourceCopyCancelRequest) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *ResourceCopyCancelRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyCancelRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyCancelRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("task_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.TaskID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyCancelRequest) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Base.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyCancelRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyCancelRequest(%+v)", *p)

}

type ResourceCopyCancelResponse struct {
	Code     int64          `thrift:"code,1" form:"code" json:"code" query:"code"`
	Msg      string         `thrift:"msg,2" form:"msg" json:"msg" query:"msg"`
	BaseResp *base.BaseResp `thrift:"BaseResp,255,required" form:"BaseResp,required" json:"BaseResp,required" query:"BaseResp,required"`
}

func NewResourceCopyCancelResponse() *ResourceCopyCancelResponse {
	return &ResourceCopyCancelResponse{}
}

func (p *ResourceCopyCancelResponse) InitDefault() {
}

func (p *ResourceCopyCancelResponse) GetCode() (v int64) {
	return p.Code
}

func (p *ResourceCopyCancelResponse) GetMsg() (v string) {
	return p.Msg
}

var ResourceCopyCancelResponse_BaseResp_DEFAULT *base.BaseResp

func (p *ResourceCopyCancelResponse) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return ResourceCopyCancelResponse_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_ResourceCopyCancelResponse = map[int16]string{
	1:   "code",
	2:   "msg",
	255: "BaseResp",
}

func (p *ResourceCopyCancelResponse) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *ResourceCopyCancelResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetBaseResp bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
				issetBaseResp = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetBaseResp {
		fieldId = 255
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceCopyCancelResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ResourceCopyCancelResponse[fieldId]))
}

func (p *ResourceCopyCancelResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ResourceCopyCancelResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Msg = _field
	return nil
}
func (p *ResourceCopyCancelResponse) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *ResourceCopyCancelResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyCancelResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceCopyCancelResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ResourceCopyCancelResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Msg); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ResourceCopyCancelResponse) writeField255(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.BaseResp.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *ResourceCopyCancelResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceCopyCancelResponse(%+v)", *p)

}

type ResourceService interface {
	LibraryResourceList(ctx context.Context, request *LibraryResourceListRequest) (r *LibraryResourceListResponse, err error)

	ProjectResourceList(ctx context.Context, request *ProjectResourceListRequest) (r *ProjectResourceListResponse, err error)
	// 复制Library资源到项目、复制项目资源到Library、移动项目资源到Library、项目内单复制资源
	ResourceCopyDispatch(ctx context.Context, req *ResourceCopyDispatchRequest) (r *ResourceCopyDispatchResponse, err error)

	ResourceCopyDetail(ctx context.Context, req *ResourceCopyDetailRequest) (r *ResourceCopyDetailResponse, err error)

	ResourceCopyRetry(ctx context.Context, req *ResourceCopyRetryRequest) (r *ResourceCopyRetryResponse, err error)

	ResourceCopyCancel(ctx context.Context, req *ResourceCopyCancelRequest) (r *ResourceCopyCancelResponse, err error)
}

type ResourceServiceClient struct {
	c thrift.TClient
}

func NewResourceServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ResourceServiceClient {
	return &ResourceServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewResourceServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ResourceServiceClient {
	return &ResourceServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewResourceServiceClient(c thrift.TClient) *ResourceServiceClient {
	return &ResourceServiceClient{
		c: c,
	}
}

func (p *ResourceServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *ResourceServiceClient) LibraryResourceList(ctx context.Context, request *LibraryResourceListRequest) (r *LibraryResourceListResponse, err error) {
	var _args ResourceServiceLibraryResourceListArgs
	_args.Request = request
	var _result ResourceServiceLibraryResourceListResult
	if err = p.Client_().Call(ctx, "LibraryResourceList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ResourceServiceClient) ProjectResourceList(ctx context.Context, request *ProjectResourceListRequest) (r *ProjectResourceListResponse, err error) {
	var _args ResourceServiceProjectResourceListArgs
	_args.Request = request
	var _result ResourceServiceProjectResourceListResult
	if err = p.Client_().Call(ctx, "ProjectResourceList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ResourceServiceClient) ResourceCopyDispatch(ctx context.Context, req *ResourceCopyDispatchRequest) (r *ResourceCopyDispatchResponse, err error) {
	var _args ResourceServiceResourceCopyDispatchArgs
	_args.Req = req
	var _result ResourceServiceResourceCopyDispatchResult
	if err = p.Client_().Call(ctx, "ResourceCopyDispatch", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ResourceServiceClient) ResourceCopyDetail(ctx context.Context, req *ResourceCopyDetailRequest) (r *ResourceCopyDetailResponse, err error) {
	var _args ResourceServiceResourceCopyDetailArgs
	_args.Req = req
	var _result ResourceServiceResourceCopyDetailResult
	if err = p.Client_().Call(ctx, "ResourceCopyDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ResourceServiceClient) ResourceCopyRetry(ctx context.Context, req *ResourceCopyRetryRequest) (r *ResourceCopyRetryResponse, err error) {
	var _args ResourceServiceResourceCopyRetryArgs
	_args.Req = req
	var _result ResourceServiceResourceCopyRetryResult
	if err = p.Client_().Call(ctx, "ResourceCopyRetry", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *ResourceServiceClient) ResourceCopyCancel(ctx context.Context, req *ResourceCopyCancelRequest) (r *ResourceCopyCancelResponse, err error) {
	var _args ResourceServiceResourceCopyCancelArgs
	_args.Req = req
	var _result ResourceServiceResourceCopyCancelResult
	if err = p.Client_().Call(ctx, "ResourceCopyCancel", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type ResourceServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ResourceService
}

func (p *ResourceServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ResourceServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ResourceServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewResourceServiceProcessor(handler ResourceService) *ResourceServiceProcessor {
	self := &ResourceServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("LibraryResourceList", &resourceServiceProcessorLibraryResourceList{handler: handler})
	self.AddToProcessorMap("ProjectResourceList", &resourceServiceProcessorProjectResourceList{handler: handler})
	self.AddToProcessorMap("ResourceCopyDispatch", &resourceServiceProcessorResourceCopyDispatch{handler: handler})
	self.AddToProcessorMap("ResourceCopyDetail", &resourceServiceProcessorResourceCopyDetail{handler: handler})
	self.AddToProcessorMap("ResourceCopyRetry", &resourceServiceProcessorResourceCopyRetry{handler: handler})
	self.AddToProcessorMap("ResourceCopyCancel", &resourceServiceProcessorResourceCopyCancel{handler: handler})
	return self
}
func (p *ResourceServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type resourceServiceProcessorLibraryResourceList struct {
	handler ResourceService
}

func (p *resourceServiceProcessorLibraryResourceList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ResourceServiceLibraryResourceListArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("LibraryResourceList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ResourceServiceLibraryResourceListResult{}
	var retval *LibraryResourceListResponse
	if retval, err2 = p.handler.LibraryResourceList(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing LibraryResourceList: "+err2.Error())
		oprot.WriteMessageBegin("LibraryResourceList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("LibraryResourceList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type resourceServiceProcessorProjectResourceList struct {
	handler ResourceService
}

func (p *resourceServiceProcessorProjectResourceList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ResourceServiceProjectResourceListArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ProjectResourceList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ResourceServiceProjectResourceListResult{}
	var retval *ProjectResourceListResponse
	if retval, err2 = p.handler.ProjectResourceList(ctx, args.Request); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ProjectResourceList: "+err2.Error())
		oprot.WriteMessageBegin("ProjectResourceList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ProjectResourceList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type resourceServiceProcessorResourceCopyDispatch struct {
	handler ResourceService
}

func (p *resourceServiceProcessorResourceCopyDispatch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ResourceServiceResourceCopyDispatchArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ResourceCopyDispatch", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ResourceServiceResourceCopyDispatchResult{}
	var retval *ResourceCopyDispatchResponse
	if retval, err2 = p.handler.ResourceCopyDispatch(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ResourceCopyDispatch: "+err2.Error())
		oprot.WriteMessageBegin("ResourceCopyDispatch", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ResourceCopyDispatch", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type resourceServiceProcessorResourceCopyDetail struct {
	handler ResourceService
}

func (p *resourceServiceProcessorResourceCopyDetail) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ResourceServiceResourceCopyDetailArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ResourceCopyDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ResourceServiceResourceCopyDetailResult{}
	var retval *ResourceCopyDetailResponse
	if retval, err2 = p.handler.ResourceCopyDetail(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ResourceCopyDetail: "+err2.Error())
		oprot.WriteMessageBegin("ResourceCopyDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ResourceCopyDetail", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type resourceServiceProcessorResourceCopyRetry struct {
	handler ResourceService
}

func (p *resourceServiceProcessorResourceCopyRetry) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ResourceServiceResourceCopyRetryArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ResourceCopyRetry", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ResourceServiceResourceCopyRetryResult{}
	var retval *ResourceCopyRetryResponse
	if retval, err2 = p.handler.ResourceCopyRetry(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ResourceCopyRetry: "+err2.Error())
		oprot.WriteMessageBegin("ResourceCopyRetry", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ResourceCopyRetry", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type resourceServiceProcessorResourceCopyCancel struct {
	handler ResourceService
}

func (p *resourceServiceProcessorResourceCopyCancel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := ResourceServiceResourceCopyCancelArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ResourceCopyCancel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := ResourceServiceResourceCopyCancelResult{}
	var retval *ResourceCopyCancelResponse
	if retval, err2 = p.handler.ResourceCopyCancel(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ResourceCopyCancel: "+err2.Error())
		oprot.WriteMessageBegin("ResourceCopyCancel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ResourceCopyCancel", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type ResourceServiceLibraryResourceListArgs struct {
	Request *LibraryResourceListRequest `thrift:"request,1"`
}

func NewResourceServiceLibraryResourceListArgs() *ResourceServiceLibraryResourceListArgs {
	return &ResourceServiceLibraryResourceListArgs{}
}

func (p *ResourceServiceLibraryResourceListArgs) InitDefault() {
}

var ResourceServiceLibraryResourceListArgs_Request_DEFAULT *LibraryResourceListRequest

func (p *ResourceServiceLibraryResourceListArgs) GetRequest() (v *LibraryResourceListRequest) {
	if !p.IsSetRequest() {
		return ResourceServiceLibraryResourceListArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ResourceServiceLibraryResourceListArgs = map[int16]string{
	1: "request",
}

func (p *ResourceServiceLibraryResourceListArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ResourceServiceLibraryResourceListArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceLibraryResourceListArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceLibraryResourceListArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewLibraryResourceListRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ResourceServiceLibraryResourceListArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LibraryResourceList_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceLibraryResourceListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceServiceLibraryResourceListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceLibraryResourceListArgs(%+v)", *p)

}

type ResourceServiceLibraryResourceListResult struct {
	Success *LibraryResourceListResponse `thrift:"success,0,optional"`
}

func NewResourceServiceLibraryResourceListResult() *ResourceServiceLibraryResourceListResult {
	return &ResourceServiceLibraryResourceListResult{}
}

func (p *ResourceServiceLibraryResourceListResult) InitDefault() {
}

var ResourceServiceLibraryResourceListResult_Success_DEFAULT *LibraryResourceListResponse

func (p *ResourceServiceLibraryResourceListResult) GetSuccess() (v *LibraryResourceListResponse) {
	if !p.IsSetSuccess() {
		return ResourceServiceLibraryResourceListResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ResourceServiceLibraryResourceListResult = map[int16]string{
	0: "success",
}

func (p *ResourceServiceLibraryResourceListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceServiceLibraryResourceListResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceLibraryResourceListResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceLibraryResourceListResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewLibraryResourceListResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceServiceLibraryResourceListResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("LibraryResourceList_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceLibraryResourceListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceServiceLibraryResourceListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceLibraryResourceListResult(%+v)", *p)

}

type ResourceServiceProjectResourceListArgs struct {
	Request *ProjectResourceListRequest `thrift:"request,1"`
}

func NewResourceServiceProjectResourceListArgs() *ResourceServiceProjectResourceListArgs {
	return &ResourceServiceProjectResourceListArgs{}
}

func (p *ResourceServiceProjectResourceListArgs) InitDefault() {
}

var ResourceServiceProjectResourceListArgs_Request_DEFAULT *ProjectResourceListRequest

func (p *ResourceServiceProjectResourceListArgs) GetRequest() (v *ProjectResourceListRequest) {
	if !p.IsSetRequest() {
		return ResourceServiceProjectResourceListArgs_Request_DEFAULT
	}
	return p.Request
}

var fieldIDToName_ResourceServiceProjectResourceListArgs = map[int16]string{
	1: "request",
}

func (p *ResourceServiceProjectResourceListArgs) IsSetRequest() bool {
	return p.Request != nil
}

func (p *ResourceServiceProjectResourceListArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceProjectResourceListArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceProjectResourceListArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewProjectResourceListRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Request = _field
	return nil
}

func (p *ResourceServiceProjectResourceListArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceList_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceProjectResourceListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("request", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Request.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceServiceProjectResourceListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceProjectResourceListArgs(%+v)", *p)

}

type ResourceServiceProjectResourceListResult struct {
	Success *ProjectResourceListResponse `thrift:"success,0,optional"`
}

func NewResourceServiceProjectResourceListResult() *ResourceServiceProjectResourceListResult {
	return &ResourceServiceProjectResourceListResult{}
}

func (p *ResourceServiceProjectResourceListResult) InitDefault() {
}

var ResourceServiceProjectResourceListResult_Success_DEFAULT *ProjectResourceListResponse

func (p *ResourceServiceProjectResourceListResult) GetSuccess() (v *ProjectResourceListResponse) {
	if !p.IsSetSuccess() {
		return ResourceServiceProjectResourceListResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ResourceServiceProjectResourceListResult = map[int16]string{
	0: "success",
}

func (p *ResourceServiceProjectResourceListResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceServiceProjectResourceListResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceProjectResourceListResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceProjectResourceListResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewProjectResourceListResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceServiceProjectResourceListResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ProjectResourceList_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceProjectResourceListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceServiceProjectResourceListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceProjectResourceListResult(%+v)", *p)

}

type ResourceServiceResourceCopyDispatchArgs struct {
	Req *ResourceCopyDispatchRequest `thrift:"req,1"`
}

func NewResourceServiceResourceCopyDispatchArgs() *ResourceServiceResourceCopyDispatchArgs {
	return &ResourceServiceResourceCopyDispatchArgs{}
}

func (p *ResourceServiceResourceCopyDispatchArgs) InitDefault() {
}

var ResourceServiceResourceCopyDispatchArgs_Req_DEFAULT *ResourceCopyDispatchRequest

func (p *ResourceServiceResourceCopyDispatchArgs) GetReq() (v *ResourceCopyDispatchRequest) {
	if !p.IsSetReq() {
		return ResourceServiceResourceCopyDispatchArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_ResourceServiceResourceCopyDispatchArgs = map[int16]string{
	1: "req",
}

func (p *ResourceServiceResourceCopyDispatchArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ResourceServiceResourceCopyDispatchArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyDispatchArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDispatchArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResourceCopyDispatchRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ResourceServiceResourceCopyDispatchArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDispatch_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDispatchArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDispatchArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyDispatchArgs(%+v)", *p)

}

type ResourceServiceResourceCopyDispatchResult struct {
	Success *ResourceCopyDispatchResponse `thrift:"success,0,optional"`
}

func NewResourceServiceResourceCopyDispatchResult() *ResourceServiceResourceCopyDispatchResult {
	return &ResourceServiceResourceCopyDispatchResult{}
}

func (p *ResourceServiceResourceCopyDispatchResult) InitDefault() {
}

var ResourceServiceResourceCopyDispatchResult_Success_DEFAULT *ResourceCopyDispatchResponse

func (p *ResourceServiceResourceCopyDispatchResult) GetSuccess() (v *ResourceCopyDispatchResponse) {
	if !p.IsSetSuccess() {
		return ResourceServiceResourceCopyDispatchResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ResourceServiceResourceCopyDispatchResult = map[int16]string{
	0: "success",
}

func (p *ResourceServiceResourceCopyDispatchResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceServiceResourceCopyDispatchResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyDispatchResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDispatchResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewResourceCopyDispatchResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceServiceResourceCopyDispatchResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDispatch_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDispatchResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDispatchResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyDispatchResult(%+v)", *p)

}

type ResourceServiceResourceCopyDetailArgs struct {
	Req *ResourceCopyDetailRequest `thrift:"req,1"`
}

func NewResourceServiceResourceCopyDetailArgs() *ResourceServiceResourceCopyDetailArgs {
	return &ResourceServiceResourceCopyDetailArgs{}
}

func (p *ResourceServiceResourceCopyDetailArgs) InitDefault() {
}

var ResourceServiceResourceCopyDetailArgs_Req_DEFAULT *ResourceCopyDetailRequest

func (p *ResourceServiceResourceCopyDetailArgs) GetReq() (v *ResourceCopyDetailRequest) {
	if !p.IsSetReq() {
		return ResourceServiceResourceCopyDetailArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_ResourceServiceResourceCopyDetailArgs = map[int16]string{
	1: "req",
}

func (p *ResourceServiceResourceCopyDetailArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ResourceServiceResourceCopyDetailArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyDetailArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDetailArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResourceCopyDetailRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ResourceServiceResourceCopyDetailArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDetail_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyDetailArgs(%+v)", *p)

}

type ResourceServiceResourceCopyDetailResult struct {
	Success *ResourceCopyDetailResponse `thrift:"success,0,optional"`
}

func NewResourceServiceResourceCopyDetailResult() *ResourceServiceResourceCopyDetailResult {
	return &ResourceServiceResourceCopyDetailResult{}
}

func (p *ResourceServiceResourceCopyDetailResult) InitDefault() {
}

var ResourceServiceResourceCopyDetailResult_Success_DEFAULT *ResourceCopyDetailResponse

func (p *ResourceServiceResourceCopyDetailResult) GetSuccess() (v *ResourceCopyDetailResponse) {
	if !p.IsSetSuccess() {
		return ResourceServiceResourceCopyDetailResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ResourceServiceResourceCopyDetailResult = map[int16]string{
	0: "success",
}

func (p *ResourceServiceResourceCopyDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceServiceResourceCopyDetailResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyDetailResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDetailResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewResourceCopyDetailResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceServiceResourceCopyDetailResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyDetail_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyDetailResult(%+v)", *p)

}

type ResourceServiceResourceCopyRetryArgs struct {
	Req *ResourceCopyRetryRequest `thrift:"req,1"`
}

func NewResourceServiceResourceCopyRetryArgs() *ResourceServiceResourceCopyRetryArgs {
	return &ResourceServiceResourceCopyRetryArgs{}
}

func (p *ResourceServiceResourceCopyRetryArgs) InitDefault() {
}

var ResourceServiceResourceCopyRetryArgs_Req_DEFAULT *ResourceCopyRetryRequest

func (p *ResourceServiceResourceCopyRetryArgs) GetReq() (v *ResourceCopyRetryRequest) {
	if !p.IsSetReq() {
		return ResourceServiceResourceCopyRetryArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_ResourceServiceResourceCopyRetryArgs = map[int16]string{
	1: "req",
}

func (p *ResourceServiceResourceCopyRetryArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ResourceServiceResourceCopyRetryArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyRetryArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyRetryArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResourceCopyRetryRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ResourceServiceResourceCopyRetryArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyRetry_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyRetryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyRetryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyRetryArgs(%+v)", *p)

}

type ResourceServiceResourceCopyRetryResult struct {
	Success *ResourceCopyRetryResponse `thrift:"success,0,optional"`
}

func NewResourceServiceResourceCopyRetryResult() *ResourceServiceResourceCopyRetryResult {
	return &ResourceServiceResourceCopyRetryResult{}
}

func (p *ResourceServiceResourceCopyRetryResult) InitDefault() {
}

var ResourceServiceResourceCopyRetryResult_Success_DEFAULT *ResourceCopyRetryResponse

func (p *ResourceServiceResourceCopyRetryResult) GetSuccess() (v *ResourceCopyRetryResponse) {
	if !p.IsSetSuccess() {
		return ResourceServiceResourceCopyRetryResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ResourceServiceResourceCopyRetryResult = map[int16]string{
	0: "success",
}

func (p *ResourceServiceResourceCopyRetryResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceServiceResourceCopyRetryResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyRetryResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyRetryResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewResourceCopyRetryResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceServiceResourceCopyRetryResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyRetry_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyRetryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyRetryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyRetryResult(%+v)", *p)

}

type ResourceServiceResourceCopyCancelArgs struct {
	Req *ResourceCopyCancelRequest `thrift:"req,1"`
}

func NewResourceServiceResourceCopyCancelArgs() *ResourceServiceResourceCopyCancelArgs {
	return &ResourceServiceResourceCopyCancelArgs{}
}

func (p *ResourceServiceResourceCopyCancelArgs) InitDefault() {
}

var ResourceServiceResourceCopyCancelArgs_Req_DEFAULT *ResourceCopyCancelRequest

func (p *ResourceServiceResourceCopyCancelArgs) GetReq() (v *ResourceCopyCancelRequest) {
	if !p.IsSetReq() {
		return ResourceServiceResourceCopyCancelArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_ResourceServiceResourceCopyCancelArgs = map[int16]string{
	1: "req",
}

func (p *ResourceServiceResourceCopyCancelArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *ResourceServiceResourceCopyCancelArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyCancelArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyCancelArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResourceCopyCancelRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *ResourceServiceResourceCopyCancelArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyCancel_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyCancelArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyCancelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyCancelArgs(%+v)", *p)

}

type ResourceServiceResourceCopyCancelResult struct {
	Success *ResourceCopyCancelResponse `thrift:"success,0,optional"`
}

func NewResourceServiceResourceCopyCancelResult() *ResourceServiceResourceCopyCancelResult {
	return &ResourceServiceResourceCopyCancelResult{}
}

func (p *ResourceServiceResourceCopyCancelResult) InitDefault() {
}

var ResourceServiceResourceCopyCancelResult_Success_DEFAULT *ResourceCopyCancelResponse

func (p *ResourceServiceResourceCopyCancelResult) GetSuccess() (v *ResourceCopyCancelResponse) {
	if !p.IsSetSuccess() {
		return ResourceServiceResourceCopyCancelResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_ResourceServiceResourceCopyCancelResult = map[int16]string{
	0: "success",
}

func (p *ResourceServiceResourceCopyCancelResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *ResourceServiceResourceCopyCancelResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ResourceServiceResourceCopyCancelResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyCancelResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewResourceCopyCancelResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *ResourceServiceResourceCopyCancelResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ResourceCopyCancel_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ResourceServiceResourceCopyCancelResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *ResourceServiceResourceCopyCancelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceServiceResourceCopyCancelResult(%+v)", *p)

}
