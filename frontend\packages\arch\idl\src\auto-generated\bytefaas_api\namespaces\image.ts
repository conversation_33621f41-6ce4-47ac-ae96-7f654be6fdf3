/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AddImageCICDRecordsRequest {
  /** User who created the record */
  create_by?: string;
  /** Type of the image */
  image_type?: string;
  /** Application environment */
  app_env?: string;
  /** New values for the record */
  value?: Array<TargetSetting>;
  /** Old values for the record */
  old_record_value?: Array<TargetSetting>;
  /** Description of the CICD record */
  description?: string;
}

export interface AddImageCICDRecordsResponse {
  code?: number;
  error?: string;
  data?: AddImageCICDRecordsResponseData;
}

export interface AddImageCICDRecordsResponseData {
  /** Unique identifier for the CICD record */
  id?: string;
  /** User who created the record */
  created_by?: string;
  /** Time when the record was created */
  created_at?: string;
  /** Type of the image */
  image_type?: string;
  /** Application environment */
  app_env?: string;
  /** List of differences in the record */
  record_diff?: Array<RecordDiff>;
  /** Description of the CICD record */
  description?: string;
  /** Rollback record ID if applicable */
  rollback_id?: string;
  /** Source of the CICD record */
  source?: string;
}

export interface CheckImagesVersionRequest {
  /** Key for the image */
  key?: string;
  /** SCM version string */
  scm_version?: string;
}

export interface CheckImagesVersionResponse {
  status?: string;
  message?: string;
  data?: scmVersion;
}

export interface GrayConfig {
  /** Gray configuration for PSMs */
  psms?: GrayKeys;
  /** Gray configuration for clusters */
  clusters?: GrayKeys;
  /** Gray configuration for data centers */
  dcs?: GrayKeys;
  /** Gray configuration for stages */
  stages?: GrayKeys;
}

export interface GrayKeys {
  /** Whether to gray all keys */
  gray_all?: boolean;
  /** List of keys to gray */
  keys?: Array<string>;
}

export interface PreUpdateBaseImagesRequest {
  /** Key for the base image */
  key?: string;
  /** Additional data for pre-updating base images */
  data?: Record<string, string>;
}

export interface PreUpdateBaseImagesResponse {
  code?: number;
  error?: string;
  data?: Record<string, string>;
}

export interface RecordDiff {
  /** Key of the record that changed */
  key?: string;
  /** Old value before the change */
  old_value?: string;
  /** New value after the change */
  new_value?: string;
}

export interface scmVersion {
  /** Branch name */
  branch?: string;
  /** Commit hash */
  commit_hash?: string;
  /** Repository ID */
  repo_id?: number;
  /** Type of SCM */
  scm_type?: string;
  /** Version string for the SCM */
  version_version?: string;
}

export interface Setting {
  /** Name of the setting */
  name?: string;
  /** Value of the setting */
  value?: string;
  /** Region for the setting */
  region?: string;
  /** Tag for the setting */
  tag?: string;
  /** Time when the setting was last updated */
  updated_at?: string;
  /** User who last updated the setting */
  updated_by?: string;
  /** Whether the setting is deleted */
  is_deleted?: boolean;
  /** Time when the setting was deleted */
  deleted_at?: string;
  /** User who deleted the setting */
  deleted_by?: string;
  /** Whether the metadata is synced */
  meta_synced?: boolean;
  /** Time when the metadata was synced */
  meta_synced_at?: string;
  /** Cell for the setting */
  cell?: string;
  /** Gray value for the setting */
  gray_value?: string;
  /** Whether gray is needed */
  need_gray?: boolean;
  /** Gray configuration for the setting */
  gray_conf?: GrayConfig;
  /** Description of the setting */
  description?: string;
}

export interface TargetSetting {
  /** Key for the target setting */
  key?: string;
  /** Value for the target setting */
  value?: string;
}

export interface UpdateBaseImagesRequest {
  /** Key for the base image */
  key?: string;
  /** Header value used to trigger base image update */
  UpdateBaseImages?: string;
}

export interface UpdateBaseImagesResponse {
  code?: number;
  error?: string;
  data?: Record<string, string>;
  /** Status of the update operation */
  status?: string;
  /** Message describing the update result */
  message?: string;
}

export interface UpdateImageScmVersionRequest {
  /** Version to update */
  version?: string;
  /** Git commit hash */
  git_commit?: string;
  /** Key for the image */
  key?: string;
}

export interface UpdateImageScmVersionResponse {
  code?: number;
  error?: string;
  data?: Setting;
}
/* eslint-enable */
