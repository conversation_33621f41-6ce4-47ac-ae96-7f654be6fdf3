/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string;

export enum AuthorType {
  User = 1,
  Bot = 2,
}

export enum CommentAuditStatus {
  /** 已提交审核 */
  SubmitAudit = 1,
  /** 审核自见 */
  SelfView = 2,
  /** 审核通过，全员可见 */
  All = 3,
  /** 审核删除 */
  Deleted = 4,
}

export enum CommentBotReplyStatus {
  /** 正在回复 */
  Replying = 1,
  /** 回复完成 */
  Done = 2,
}

export enum CommentStatus {
  /** 自见 */
  SelfView = 1,
  /** 全员可见 */
  All = 2,
  /** 已删除 */
  Deleted = 3,
}

export enum CommentType {
  /** 主贴回复 */
  MainReply = 1,
  /** 楼中楼 */
  SubReply = 2,
}

export enum ConfigStatus {
  Existed = 0,
  Deleted = 1,
}

export enum ConfigType {
  NotSet = 0,
  AlreadySet = 1,
}

export enum DiscussionGlobalStatus {
  /** 讨论区全局状态 */
  Normal = 0,
  /** 禁言 */
  Ban = 1,
}

export enum DiscussionStatus {
  Close = 1,
  Open = 2,
  Ban = 3,
}

export enum DiscussionUpdateSource {
  /** 运营触发 */
  ByAdmin = 1,
  /** 商品变更触发 */
  ByProduct = 2,
}

export enum EmojiType {
  EmojiThumbsUp = 1,
  EmojiThumbsDown = 2,
  EmojiFaceWithTearsOfJoy = 3,
  EmojiExplodingHead = 4,
  EmojiFoldedHands = 5,
}

export enum FileType {
  Image = 1,
}

export enum InteractionEntityType {
  Bot = 1,
  /** 竞技场 */
  Arena = 2,
  /** 个人主页 */
  Homepage = 3,
}

export enum InteractionItemType {
  /** 机器人 */
  Bot = 1,
  /** 用户 */
  User = 2,
  /** 帖子 */
  Post = 3,
  /** 评论 */
  Comment = 4,
  /** 商品 */
  Product = 5,
  /** 讨论区 */
  Discussion = 6,
}

export enum InvitationMethod {
  Link = 1,
  QRCode = 2,
}

export enum PinType {
  /** 非置顶 */
  NoPin = 0,
  /** owner 置顶 */
  Owner = 1,
  /** 全局置顶 */
  Golbal = 2,
}

export enum PostAuditStatus {
  /** 已提交审核 */
  SubmitAudit = 1,
  /** 审核自见 */
  SelfView = 2,
  /** 审核通过，全员可见 */
  All = 3,
  /** 审核删除 */
  Deleted = 4,
}

export enum PostStatus {
  /** 自见 */
  SelfView = 1,
  /** 全员可见 */
  All = 2,
  /** 已删除 */
  Deleted = 3,
}

export enum ReactionType {
  Like = 1,
  DisLike = 2,
  Emoji = 3,
}

export enum ShareChannel {
  CopyLink = 1,
  Twitter = 2,
  Reddit = 3,
  WeChat = 4,
  WeiBo = 5,
  JueJin = 6,
  Qzone = 7,
  Image = 8,
}

export enum SortType {
  /** 发布时间从新到旧 */
  PublishDesc = 1,
  /** 回复数从多到少 */
  CommentCountDesc = 2,
}

export enum UserBehaviorType {
  Used = 1,
  Visited = 2,
}

export enum UserStatus {
  Normal = 0,
  /** 讨论区禁言 */
  DisussionBan = 1,
  /** 注销 */
  Deactivated = 2,
}

export interface Conversation {
  /** 对话示例 */
  snippets?: Array<string>;
  /** 开场白 */
  opening_dialog?: OpeningDialog;
  message_id_list?: Array<Int64>;
}

export interface Image {
  key?: string;
  url?: string;
  origin_url?: string;
}

export interface Mention {
  item_id?: string;
  item_type?: InteractionItemType;
  name?: string;
  avatar_url?: string;
}

export interface OpeningDialog {
  /** Bot开场白 */
  content?: string;
}

export interface PostLabel {
  label_id?: string;
  label_name?: string;
  label_icon?: string;
}

export interface Resource {
  file_type?: FileType;
  image?: Image;
}

export interface UploadTokenData {
  access_key_id?: string;
  secret_access_key?: string;
  session_token?: string;
  expired_time?: string;
  current_time?: string;
  service_id?: string;
  upload_host?: string;
}

export interface UserLabel {
  label_id?: string;
  label_name?: string;
  icon_uri?: string;
  icon_url?: string;
  jump_link?: string;
}
/* eslint-enable */
