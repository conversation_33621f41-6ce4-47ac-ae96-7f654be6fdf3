/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export {
  /** @deprecated 该使用方式已废弃，后续请使用@coze-arch/foundation-sdk导出的方法*/
  useSpaceStore,
  /** @deprecated 该使用方式已废弃，后续请使用@coze-arch/foundation-sdk导出的方法*/
  useSpace,
  /** @deprecated 该使用方式已废弃，后续请使用@coze-arch/foundation-sdk导出的方法*/
  useSpaceList,
} from '@coze-foundation/space-store';

export { useAuthStore } from './auth';

/** @deprecated - 持久化方案有问题，废弃 */
export { clearStorage } from './utils/get-storage';

export { useSpaceGrayStore, TccKey } from './space-gray';
