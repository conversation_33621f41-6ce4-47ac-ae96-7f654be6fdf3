/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as product_common from './product_common';
import * as common from './common';
import * as product from './product';

export type Int64 = string | number;

export interface AdminGetSubscriptionProductDetailData {
  product_info?: SubscriptionProductDetail;
  is_hit_ppe?: boolean;
  is_activated?: boolean;
  version_code?: number;
}

export interface AdminGetSubscriptionProductDetailRequest {
  product_type: product_common.ProductEntityType;
  product_id?: string;
}

export interface AdminGetSubscriptionProductDetailResponse {
  data?: AdminGetSubscriptionProductDetailData;
  code: number;
  message: string;
}

export interface BenefitItem {
  name?: string;
  use_mode?: common.BenefitUseMode;
  /** 可用额度 */
  quota?: number;
  /** 已使用额度 */
  used?: number;
  /** 单次消耗额度 */
  quota_once_cost?: number;
  icon_url?: string;
}

export interface BounsCredit {
  name?: string;
  quota?: number;
  expired_at?: string;
}

export interface CancelSubscription {
  subscribe_id?: string;
}

export interface CollaborateQuota {
  /** 协作者数量。-1标识无限制 */
  collaborators_quota?: number;
  /** 协作机器人数量。-1标识无限制 */
  collaborative_bots_quota?: number;
  /** 协作工作流数量。-1标识无限制 */
  collaborative_workflows_quota?: number;
}

export interface CostRule {
  type?: common.ChargeableEntityType;
  name?: string;
  items?: Array<CostRuleItem>;
  tips_link?: string;
}

export interface CostRuleItem {
  name?: string;
  icon_url?: string;
  price_list?: Array<CostRulePrice>;
}

export interface CostRulePrice {
  usage_quantity?: number;
  usage_unit?: string;
  cost_quantity?: number;
  cost_unit?: string;
}

export interface CreateSubscription {
  /** 订阅单号 */
  subscribe_id?: string;
  /** 跳转链接 */
  subscribe_params?: string;
}

export interface CreditDetail {
  credit_type?: common.CreditType;
  expired_at?: string;
  /** 总额度 */
  total_quota?: number;
  /** 余额 */
  remain_quota?: number;
  /** 是否是正在使用的 */
  is_in_use?: boolean;
  /** bouns 特有 */
  bouns_details?: Array<BounsCredit>;
}

export interface GetSubscriptionRecordRequest {
  subscribe_id?: string;
  /** 是否实时查询支付侧的支付状态（适用于未收到支付回调时） */
  real_time_subs_status?: boolean;
}

export interface GetSubscriptionRecordResponse {
  data?: SubscriptionRecordInfo;
  code: number;
  message: string;
}

export interface MemberVersionRights {
  /** 配置对应套餐版本 */
  member_version?: common.UserLevel;
  /** 配置名称 */
  configuration_name?: string;
  /** 配置备注 */
  configuration_remark?: string;
  /** 购买链接 */
  purchase_link?: PurchaseLink;
  /** 价格信息 */
  price_info?: PriceInfo;
  /** 权益概览 */
  right_overview?: Array<string>;
  /** 权益列表 */
  right_list?: Array<RightDetail>;
}

export interface PackageContent {
  content?: string;
}

export interface PackageItemDetail {
  name?: string;
  items?: Array<PackageContent>;
}

/** 价格信息 */
export interface PriceInfo {
  /** 月付价格 */
  monthly_price?: number;
  /** 月付原价 */
  monthly_orgin_price?: number;
  /** 年付价格 */
  annual_price?: number;
  /** 年付原价 */
  annual_orgin_price?: number;
  /** 年付折扣 */
  annual_discount?: number;
  /** 年付每月价格 */
  annual_per_month_price?: number;
  /** 年付折扣差价 */
  annual_discount_price?: number;
}

export interface PublicCancelSubscriptionRequest {
  subscribe_id?: string;
  /** 用于风控 */
  Cookie?: string;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PublicCancelSubscriptionResponse {
  data?: CancelSubscription;
  code: number;
  message: string;
}

export interface PublicCreateSubscriptionRequest {
  /** 通常为 sku_id。 */
  goods_id?: string;
  /** 升降级时，传入原订阅单号 */
  pre_subscription_id?: string;
  /** 来源渠道，用于跳转对应渠道绑定页面 */
  channel?: string;
  /** 订阅导流实体类型 */
  source_type?: common.TradeSourceType;
  /** 订阅导流实体ID */
  source_id?: string;
  /** 用于风控 */
  Cookie?: string;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PublicCreateSubscriptionResponse {
  data?: CreateSubscription;
  code: number;
  message: string;
}

export interface PublicGetSpaceBenefitRequest {
  SpaceID: Int64;
  /** 不传仅返回 Space Owner 信息 */
  benefit_types?: Array<common.BenefitType>;
}

export interface PublicGetSpaceBenefitResponse {
  data?: SpaceBenefit;
  code: number;
  message: string;
}

export interface PublicGetSubscriptionDetailRequest {
  subscribe_type?: common.SubscriptionType;
}

export interface PublicGetSubscriptionDetailResponse {
  data?: SubscriptionDetail;
  code: number;
  message: string;
}

export interface PublicGetSubscriptionDetailV2Request {
  /** 不传仅返回用户信息 */
  benefit_types?: Array<common.BenefitType>;
  /** 必填。这里指的是Coze的AccountID */
  coze_account_id?: string;
  /** 这里指的是Coze的AccountType */
  coze_account_type?: common.CozeAccountType;
  /** 是否需要返回资源包信息 */
  with_resource_package?: boolean;
}

export interface PublicGetSubscriptionDetailV2Response {
  data?: SubscriptionDetailV2;
  code: number;
  message: string;
}

export interface PublicGetSubscriptionProductDetailRequest {
  product_type?: product_common.ProductEntityType;
}

export interface PublicGetSubscriptionProductDetailResponse {
  data?: SubscriptionProductDetail;
  code: number;
  message: string;
}

export interface PublicGetSubscriptionProductDetailV2Request {
  /** 这里指的是Coze的AccountID, 不传则返回默认套餐价格 */
  coze_account_id?: string;
  /** 这里指的是Coze的AccountType */
  coze_account_type?: common.CozeAccountType;
}

export interface PublicGetSubscriptionProductDetailV2Response {
  data?: SubscriptionProductDetailV2;
  code: number;
  message: string;
}

export interface PublicReSubscriptionRequest {
  subscribe_id?: string;
  /** 用于风控 */
  Cookie?: string;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PublicReSubscriptionResponse {
  data?: SubscriptionDetail;
  code: number;
  message: string;
}

/** 购买链接 */
export interface PurchaseLink {
  /** pc端新购链接 */
  pc_new_link?: string;
  /** app端新购链接 */
  app_new_link?: string;
  /** pc端更配链接 */
  pc_modify_link?: string;
  /** app端更配链接 */
  app_modify_link?: string;
  /** pc端年付新购链接 */
  pc_annual_new_link?: string;
  /** app端年付新购链接 */
  app_annual_new_link?: string;
}

export interface ResourcePackage {
  /** 资源包详情 */
  detail?: ResourcePackageDetail;
  /** 资源包类型 */
  package_type?: common.ResourcePackageType;
  /** 明细 */
  item?: Array<ResourcePackageDetail>;
}

export interface ResourcePackageDetail {
  package_name: string;
  package_type: common.ResourcePackageType;
  /** 开始时间，单位秒 */
  start_at?: Int64;
  /** 结束时间，单位秒 */
  end_at?: Int64;
  /** 总额度 */
  total_quota?: number;
  /** 余额 */
  remain_quota?: number;
  input_quota?: Int64;
  output_quota?: Int64;
}

export interface ResourcePointRatio {
  /** 资源点数量 */
  point?: Int64;
  /** 资源点数量对应的价格，如 1元 = 1000 Point，则 Point = 1000，Price = 1 */
  price?: Int64;
  /** 币种，如USD、CNY */
  currency?: string;
  /** 小数位数 */
  decimal_num?: number;
}

/** 权益详情 */
export interface RightDetail {
  /** 权益类别编码 */
  right_type_code?: string;
  /** 权益类别名称 */
  right_type_name?: string;
  /** 权益类别备注 */
  right_type_remark?: string;
  /** 权益列表 */
  right?: Array<RightDetailItem>;
}

/** 权益 */
export interface RightDetailItem {
  /** 权益编码 */
  right_code?: string;
  /** 权益名称 */
  right_name?: string;
  /** 权益备注 */
  right_remark?: string;
  /** 权益展示值 */
  right_show_value?: Array<RightShowValue>;
  /** 权益值列表 */
  right_value_list?: Array<RightValue>;
}

export interface RightShowValue {
  /** 权益值类型:  string:字符, bool: 布尔类型 */
  type?: string;
  /** 名称 */
  value?: string;
}

/** 权益值 */
export interface RightValue {
  /** 编码 */
  code?: string;
  /** 名称 */
  name?: string;
  /** 值 */
  value?: string;
  /** 单位 */
  unit?: string;
}

export interface SpaceBenefit {
  /** 用户基本信息 */
  user_basic_info?: UserBasicInfo;
  benefit_type_infos?: Partial<
    Record<common.BenefitType, common.CommonCounter>
  >;
}

export interface SubscriptionBenefitDetail {
  name?: string;
  benefit_type?: common.BenefitType;
  /** 总Message Credit额度 */
  total_quota?: number;
  /** 总已使用额度 */
  used_quota?: number;
  /** model */
  benefit_items?: Array<BenefitItem>;
  /** plugin */
  plugin_benefit_items?: Array<BenefitItem>;
}

export interface SubscriptionDetail {
  subscribe_id?: string;
  subscribe_type?: common.SubscriptionType;
  status?: common.SubscriptionStatus;
  sku_detail?: product.SKUInfo;
  benefit_detail?: Array<SubscriptionBenefitDetail>;
  plan?: SubscriptionPlan;
  credit_details?: Array<CreditDetail>;
  /** 下列字段只有 CN 有 */
  cost_rules?: Array<CostRule>;
  resource_point_ratio?: ResourcePointRatio;
}

export interface SubscriptionDetailV2 {
  /** 用户基本信息 */
  user_basic_info?: UserBasicInfo;
  benefit_type_infos?: Partial<
    Record<common.BenefitType, common.CommonCounter>
  >;
  resource_packages?: Array<ResourcePackage>;
  /** 续费信息 */
  renewal_info?: SubscriptionRenewalInfo;
}

export interface SubscriptionPlan {
  active_time?: string;
  expire_time?: string;
}

export interface SubscriptionProductDetail {
  /** 订阅商品及套餐（SKU）信息 */
  product_info: product.ProductInfo;
  /** 用户订阅信息 */
  subs_user?: Record<Int64, SubscriptionUserInfo>;
  /** 订阅Message Credit权益 */
  benefit_info?: Record<Int64, SubscriptionBenefitDetail>;
  /** 是否有试用 */
  has_trial?: boolean;
  /** 订阅会员相关联权益 */
  relate_benefit?: Record<Int64, SubscriptionRelateBenefit>;
  /** 套餐详情 */
  package_item_details?: Record<Int64, Array<PackageItemDetail>>;
}

export interface SubscriptionProductDetailV2 {
  /** 会员权益集合 */
  member_version_rights?: Array<MemberVersionRights>;
}

export interface SubscriptionRecordInfo {
  subscribe_id?: string;
  subscribe_type?: common.SubscriptionType;
  /** Closed标识订阅支付失败 */
  status?: common.SubscriptionStatus;
}

export interface SubscriptionRelateBenefit {
  /** 协作额度 */
  collaborate_quota?: CollaborateQuota;
}

export interface SubscriptionRenewalInfo {
  /** 续费类型 */
  renewal_type?: common.SubscriptionRenewalType;
  /** 单次自动续费的周期数量，比如包月，就是每次自动续费几个月 */
  renewal_period_times?: Int64;
}

export interface SubscriptionUserInfo {
  subs_status?: common.SubscriptionStatus;
  /** 对于Free套餐，无该字段 */
  subscribe_id?: string;
}

export interface UserBasicInfo {
  user_level?: common.UserLevel;
  /** 火山账户信息。CN返回 */
  volc_account_info?: VolcAccountInfo;
  /** 火山用户信息。CN返回 */
  volc_user_info?: VolcUserInfo;
}

export interface VolcAccountInfo {
  /** 火山账户ID */
  account_id?: Int64;
  /** 是否为火山专业版账户，即是否开通过，当UserLevel in (100,110,120,130)时为true（即使账户关停回收仍为true） */
  is_volcano_pro_account?: boolean;
  /** 实例ID */
  instance_id?: string;
  /** 扣子专业版是否可用（含套餐及存量专业版）。存量专业版仅返回此字段 */
  coze_instance_status?: common.AccountStatus;
  /** 套餐（实例）状态。仅订阅套餐返回此字段 */
  instance_status?: common.InstanceStatus;
  /** 套餐（实例）是否受限（欠费）。仅订阅套餐返回此字段 */
  limit_status?: common.InstanceLimitStatus;
  /** 火山用户类型 */
  volcano_user_type?: common.VolcanoUserType;
  /** 权益生效时间（秒级） */
  start_at?: Int64;
  /** 权益失效时间（秒级） */
  end_at?: Int64;
  /** 套餐对应周期资源包实例Id,如果用户购买的是仅版本，则该字段为空 */
  period_pack_instance_id?: string;
}

export interface VolcUserInfo {
  /** 火山身份中心实例ID */
  volc_auth_instance_id?: string;
  /** 火山用户等级 */
  volc_user_level?: common.UserLevel;
  /** 火山用户实例版本 */
  volc_instance_type?: common.VolcInstanceType;
}
/* eslint-enable */
