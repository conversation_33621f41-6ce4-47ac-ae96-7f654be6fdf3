/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as base from './base';

export type Int64 = string | number;

export enum AppendType {
  /** 占位 */
  SegmentAppendType_None = 0,
  /** 尾部追加 */
  SegmentAppendType_Tail = 1,
}

export enum ColumnType {
  Unknown = 0,
  /** 文本 */
  Text = 1,
  /** 数字 */
  Number = 2,
  /** 时间 */
  Date = 3,
  /** float */
  Float = 4,
  /** bool */
  Boolean = 5,
  /** 图片 */
  Image = 6,
}

export enum TableDataType {
  /** schema sheets 和 preview data */
  AllData = 0,
  /** 只需要 schema 结构 & Sheets */
  OnlySchema = 1,
  /** 只需要 preview data */
  OnlyPreview = 2,
}

export interface AuthFrequencyInfo {
  auth_id: string;
  auth_frequency_type: common.FrequencyType;
  auth_name?: string;
}

export interface BatchSubmitWebUrlRequest {
  web_urls?: Array<string>;
  Base?: base.Base;
}

export interface BatchSubmitWebUrlResponse {
  web_ids?: Array<string>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface BatchUpdateDocumentRequest {
  document_ids?: Array<string>;
  update_rule?: UpdateRule;
  Base?: base.Base;
}

export interface BatchUpdateDocumentResponse {
  /** deprecated 兼容老接口，更新内容时会返回。 */
  document_info?: Array<DocumentInfo>;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

/** 前端爬取插件获取到的内容 */
export interface CrawlContent {
  /** 标题 */
  title?: string;
  /** 表头 */
  headers?: Array<string>;
  /** 抓取到的完整信息 */
  content?: Array<Record<string, string>>;
  /** 抓取页面的 URL */
  url?: string;
  /** 抓取信息的 XPATH */
  marks?: Record<string, string>;
  /** 存储标记的类型，类型是 Array<'text' | 'image' | 'link'>，与 headers 一一对应 */
  tags?: Array<string>;
  /** 新增分页配置 */
  pagination?: Pagination;
  /** 子页面抓取信息的 XPATH, key 对应于 marks 中的 key */
  sub_marks?: Record<string, Record<string, string>>;
}

export interface CreateDocumentRequest {
  dataset_id?: string;
  format_type?: common.FormatType;
  /** 表格类型一次只能创建一个
待创建的文档信息 */
  document_bases?: Array<DocumentBase>;
  /** 只在知识库中没有文档时需要传递，已有则从知识库获取.切片规则，为空则自动按段落切片，具体规则见IDP： */
  chunk_strategy?: common.ChunkStrategy;
  /** 数据导入的时候落库规则 */
  sink_strategy?: common.SinkStrategy;
  /** 是否为追加内容，用于表格添加内容
为 true 时向已有的 document 追加内容。text 类型不能使用 */
  is_append?: boolean;
  /** 解析策略 */
  parsing_strategy?: common.ParsingStrategy;
  index_strategy?: common.IndexStrategy;
  storage_strategy?: common.StorageStrategy;
  Base?: base.Base;
}

export interface CreateDocumentResponse {
  document_infos?: Array<DocumentInfo>;
  code?: number;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface DeleteDocumentRequest {
  document_ids?: Array<string>;
  /** 由于火山侧document是非数字串，这个字段存储新的知识库id，，服务端会聚合document_ids后去重 */
  document_ids_new?: Array<string>;
  /** 用来区分是否是火山知识库，不传默认为coze知识库 */
  dataset_id?: string;
  Base?: base.Base;
}

export interface DeleteDocumentResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface DocTableSheet {
  /** sheet 的编号 */
  id?: Int64;
  /** sheet 名 */
  sheet_name?: string;
  /** 总行数 */
  total_row?: Int64;
}

/** 用于创建文档的基本信息 */
export interface DocumentBase {
  name?: string;
  source_info?: SourceInfo;
  /** api 类型更新配置, 其他类型不需要传 */
  update_rule?: UpdateRule;
  /** 以下参数表格类型需要传递
表格元数据 */
  table_meta?: Array<TableColumn>;
  /** 表格解析信息 */
  table_sheet?: TableSheet;
  /** 过滤策略 */
  filter_strategy?: common.FilterStrategy;
  /** 图片类型，人工标注时的图片描述，目前只支持openapi调用 */
  caption?: string;
  /** 火山知识库专用，<标签名，标签值> */
  volcano_tab_kv?: Record<string, common.TabValue>;
}

/** 仅提供Document表中的字段。请勿增加其他需要跨表、RPC 调用才能得到的字段。 */
export interface DocumentBrief {
  /** 更新时即使传了也不更新 */
  id?: Int64;
  /** flink 使用的更新字段
文件后缀 csv, pdf 等 */
  type?: string;
  /** 文件大小 字节数 */
  size?: number;
  /** 包含分段数量 */
  slice_count?: number;
  /** 字符数 */
  char_count?: number;
  /** 状态 */
  status?: common.DocumentStatus;
  /** json 格式字符串 */
  sink_status?: string;
  dataset_id?: Int64;
  format_type?: common.FormatType;
  space_id?: Int64;
}

export interface DocumentInfo {
  name?: string;
  document_id?: string;
  /** 文件链接 */
  tos_uri?: string;
  /** 使用的bot数量 deprecated */
  bot_used_count?: number;
  /** 创建时间 */
  create_time?: number;
  /** 更新时间 */
  update_time?: number;
  /** 创建人 */
  creator_id?: string;
  /** 包含分段数量 */
  slice_count?: number;
  /** 文件后缀 csv, pdf 等 */
  type?: string;
  /** 文件大小 字节数 */
  size?: number;
  /** 字符数 */
  char_count?: number;
  /** 状态 */
  status?: common.DocumentStatus;
  /** 命中次数 */
  hit_count?: number;
  /** 来源 */
  source_type?: common.DocumentSource;
  /** 更新类型 */
  update_type?: common.UpdateType;
  /** 更新间隔 */
  update_interval?: number;
  /** 文件类型 */
  format_type?: common.FormatType;
  /** 表格类型元数据 */
  table_meta?: Array<TableColumn>;
  /** url 地址 */
  web_url?: string;
  /** 状态的详细信息；如果切片失败，返回失败信息 */
  status_descript?: string;
  source_file_id?: string;
  is_disconnect?: boolean;
  space_id?: string;
  /** 以下字段仅针对重构后的表格类型有用，用于前端判断
仅针对表格类型，是否允许编辑更新频率 */
  editable_update_rule?: boolean;
  /** 仅针对表格类型，是否允许添加内容、修改表结构 */
  editable_append_content?: boolean;
  /** 切片规则 */
  chunk_strategy?: common.ChunkStrategy;
  /** imagex 存储的文件链接 */
  imagex_uri?: string;
  /** 层级分段文档树Json (未使用) */
  doc_outline?: string;
  /** 解析策略 */
  parsing_strategy?: common.ParsingStrategy;
  index_strategy?: common.IndexStrategy;
  filter_strategy?: common.FilterStrategy;
  /** 层级分段文档树 tos_url */
  doc_tree_tos_url?: string;
  /** 预览用的原文档 tos_url */
  preview_tos_url?: string;
  /** 预览用的原文档 tos_url */
  review_id?: Int64;
  /** 由于火山侧document是非数字串，新增这个字段返回string类型 */
  document_id_new?: string;
}

export interface DocumentProgress {
  document_id?: string;
  progress?: number;
  status?: common.DocumentStatus;
  /** 状态的详细描述；如果切片失败，返回失败信息 */
  status_descript?: string;
  document_name?: string;
  remaining_time?: Int64;
  size?: Int64;
  type?: string;
  url?: string;
  /** 更新类型 */
  update_type?: common.UpdateType;
  /** 更新间隔 */
  update_interval?: number;
}

export interface ExtractPhotoCaptionRequest {
  document_id: string;
  Base?: base.Base;
}

export interface ExtractPhotoCaptionResponse {
  caption?: string;
  code?: Int64;
  msg?: string;
}

export interface FetchWebUrlRequest {
  document_ids?: Array<string>;
  Base?: base.Base;
}

export interface FetchWebUrlResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetAppendFrequencyRequest {
  dataset_id: string;
  Base?: base.Base;
}

export interface GetAppendFrequencyResponse {
  auth_frequency_info: Array<AuthFrequencyInfo>;
  code: Int64;
  msg: string;
}

export interface GetDocumentProgressRequest {
  document_ids?: Array<string>;
  Base?: base.Base;
}

export interface GetDocumentProgressResponse {
  data?: Array<DocumentProgress>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetTableSchemaRequest {
  /** 表格解析信息, 默认初始值0,0,1 */
  table_sheet?: TableSheet;
  /** 不传默认返回所有数据 */
  table_data_type?: TableDataType;
  /** 兼容重构前的版本：如果需要拉取的是当前 document 的 schema 时传递该值 */
  document_id?: string;
  /** source file 的信息，新增 segment / 之前逻辑迁移到这里 */
  source_file?: SourceInfo;
  /** 表格预览前端需要传递原始的数据表结构 */
  origin_table_meta?: Array<TableColumn>;
  /** 表格预览前端需要传递用户编辑之后的数据表结构 */
  preview_table_meta?: Array<TableColumn>;
  Base?: base.Base;
}

export interface GetTableSchemaResponse {
  code?: number;
  msg?: string;
  sheet_list?: Array<DocTableSheet>;
  /** 选中的 sheet 的 schema, 不选择默认返回第一个 sheet */
  table_meta?: Array<TableColumn>;
  /** knowledge table 场景中会返回 */
  preview_data?: Array<Record<Int64, string>>;
}

export interface GetWebInfoRequest {
  web_ids?: Array<string>;
  /** 是否包含内容 */
  include_content?: boolean;
  Base?: base.Base;
}

export interface GetWebInfoResponse {
  data?: Record<Int64, RootWebData>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListDocumentRequest {
  dataset_id: string;
  document_ids?: Array<string>;
  page?: number;
  size?: number;
  /** 根据名称搜索 */
  keyword?: string;
  Base?: base.Base;
}

export interface ListDocumentResponse {
  document_infos?: Array<DocumentInfo>;
  total?: number;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface ListModelRequest {
  Base?: base.Base;
}

export interface ListModelResponse {
  models: Array<ModelInfo>;
  BaseResp: base.BaseResp;
}

export interface ListPhotoRequest {
  dataset_id: string;
  /** 页数，从 1 开始 */
  page?: number;
  size?: number;
  filter?: PhotoFilter;
  Base?: base.Base;
}

export interface ListPhotoResponse {
  photo_infos?: Array<PhotoInfo>;
  total?: number;
  code?: Int64;
  msg?: string;
}

export interface ModelInfo {
  name?: string;
}

export interface Pagination {
  /** 列表类型采集的最大条数 */
  max_row_count?: number;
  /** 分页方式：0-不分页 1-滚动加载 2-下一页按钮 */
  type?: number;
  /** 当类型为 2 时，需要存储用户标记的下一页按钮 */
  next_page_xpath?: string;
}

export interface PhotoDetailRequest {
  document_ids: Array<string>;
  dataset_id: string;
  Base?: base.Base;
}

export interface PhotoDetailResponse {
  photo_infos?: Record<Int64, PhotoInfo>;
  code?: Int64;
  msg?: string;
}

export interface PhotoFilter {
  /** true 筛选 “已标注” 的图片，false 筛选 “未标注” 的图片 */
  has_caption?: boolean;
  /** 搜索关键字，对图片名称和图片描述进行搜索 */
  keyword?: string;
  /** 状态 */
  status?: common.DocumentStatus;
}

export interface PhotoInfo {
  name?: string;
  document_id?: string;
  /** 图片链接 */
  url?: string;
  /** 图片描述信息 */
  caption?: string;
  /** 创建时间 */
  create_time?: number;
  /** 更新时间 */
  update_time?: number;
  /** 创建人 */
  creator_id?: string;
  /** 图片后缀 jpg, png 等 */
  type?: string;
  /** 图片大小 */
  size?: number;
  /** 状态 */
  status?: common.DocumentStatus;
  /** 来源 */
  source_type?: common.DocumentSource;
}

export interface RefreshDocumentRequest {
  dataset_id?: string;
  document_id?: string;
  /** 分段策略 */
  chunk_strategy?: common.ChunkStrategy;
  Base?: base.Base;
}

export interface RefreshDocumentResponse {
  /** 返回的是新生成文档的 documentID */
  document_id?: string;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface ResegmentRequest {
  dataset_id?: string;
  /** 要重新分段的接口 */
  document_ids?: Array<string>;
  /** 分段策略 */
  chunk_strategy?: common.ChunkStrategy;
  /** 预切片的审阅ID列表 */
  review_ids?: Array<string>;
  /** 解析策略 */
  parsing_strategy?: common.ParsingStrategy;
  index_strategy?: common.IndexStrategy;
  filter_strategy?: common.FilterStrategy;
  Base?: base.Base;
}

export interface ResegmentResponse {
  /** 老版需要. 仅返回id 和名称即可 */
  document_infos?: Array<DocumentInfo>;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface RootWebData {
  progress?: number;
  web_info?: WebInfo;
  status?: common.WebStatus;
  status_descript?: string;
}

export interface SetAppendFrequencyRequest {
  auth_frequency_info: Array<AuthFrequencyInfo>;
  dataset_id: string;
  Base?: base.Base;
}

export interface SetAppendFrequencyResponse {
  code: Int64;
  msg: string;
}

/** 支持多种数据源 */
export interface SourceInfo {
  /** document_source 本地、飞书: 文件上传的 tos 地址 */
  tos_uri?: string;
  /** document_source weburl, 传通过 knowledge 创建的 web_id */
  web_id?: string;
  /** document_source google, notion: 三方源文件 id
document_source openapi: openapi上传的文件 id */
  source_file_id?: string;
  document_source?: common.DocumentSource;
  /** document_source 自定义原始内容: json list<map<string, string>> */
  custom_content?: string;
  /** document_source 前端抓取: 传递前端爬取插件获取到的内容 */
  crawl_content?: CrawlContent;
  /** document_source 本地: 如果不传 tos 地址, 则需要传文件 base64, 类型
文件经过 base64 后的字符串 */
  file_base64?: string;
  /** 文件类型, 比如 pdf */
  file_type?: string;
  /** document_source weburl: 如果不传 web_id, 则需要传 weburl */
  web_url?: string;
  /** imagex_uri, 和 tos_uri 二选一, imagex_uri 优先，需要通过 imagex 的方法获取数据和签发 url */
  imagex_uri?: string;
  /** review_id: 经过预切片后的审阅ID，会直接取预切片的结果数据向量化，如果不传或传0，会重新切片 */
  review_id?: string;
}

export interface SubmitWebUrlRequest {
  web_url?: string;
  /** 0 不包换子页面 */
  subpages_count?: number;
  /** 文件格式类型 */
  format_type?: common.FormatType;
  /** 网页标题 url 类型必传 */
  title?: string;
  Base?: base.Base;
}

export interface SubmitWebUrlResponse {
  web_id?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

/** 表格的列信息 */
export interface TableColumn {
  /** 列 id */
  id?: string;
  /** 列名 */
  column_name?: string;
  /** 是否为语义匹配列 */
  is_semantic?: boolean;
  /** 列原本在 excel 的序号 */
  sequence?: string;
  /** 列类型 */
  column_type?: ColumnType;
  contains_empty_value?: boolean;
  /** 描述 */
  desc?: string;
}

export interface TableSheet {
  /** 用户选择的 sheet id */
  sheet_id?: string;
  /** 用户选择的表头行数，从 0 开始编号 */
  header_line_idx?: string;
  /** 用户选择的起始行号，从 0 开始编号 */
  start_line_idx?: string;
}

export interface UpdateDocumentRequest {
  document_id?: string;
  /** 重构后文档没有启用状态，给老接口使用 */
  status?: common.DocumentStatus;
  /** 需要更新就传, 更新名称 */
  document_name?: string;
  /** web 类型
web 类型更新配置 */
  update_rule?: UpdateRule;
  /** 更新表结构
表格元数据 */
  table_meta?: Array<TableColumn>;
  Base?: base.Base;
}

export interface UpdateDocumentResponse {
  /** deprecated 兼容老接口，更新内容时会返回。 */
  document_info?: DocumentInfo;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UpdatePhotoCaptionRequest {
  document_id: string;
  /** 描述信息 */
  caption: string;
  Base?: base.Base;
}

export interface UpdatePhotoCaptionResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateRule {
  /** 更新类型 */
  update_type?: common.UpdateType;
  /** 更新间隔，单位(天) */
  update_interval?: number;
}

export interface ValidateTableSchemaRequest {
  space_id?: string;
  document_id?: string;
  /** source file 的信息 */
  source_file?: SourceInfo;
  table_sheet?: TableSheet;
  Base?: base.Base;
}

export interface ValidateTableSchemaResponse {
  column_valid_result?: Record<string, string>;
  /** 如果失败会返回错误码 */
  code: Int64;
  msg: string;
}

export interface WebInfo {
  id?: string;
  url?: string;
  content?: string;
  title?: string;
  subpages?: Array<WebInfo>;
  subpages_count?: number;
  status?: common.WebStatus;
}
/* eslint-enable */
