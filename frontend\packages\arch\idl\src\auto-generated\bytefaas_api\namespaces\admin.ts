/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface AdminCreateUpdateFunctionBatchTaskRequest {
  clusters: Array<AdminUpdateFunctionBatchTaskClusterParams>;
  runtime: string;
  target_image: string;
  /** strategy of upgrade image, enum value: [ only_once, always ] */
  strategy: string;
  rolling_step?: number;
  /** envs array */
  format_envs?: Array<common.FormatEnvs>;
  /** will skip for approval while critical is true */
  critical?: boolean;
  auto_start?: boolean;
  /** Description for this upgrade, will append to lark message card */
  description?: string;
}

export interface AdminCreateUpdateFunctionBatchTaskResponse {
  code?: number;
  error?: string;
  data?: Record<string, string>;
}

export interface AdminGetAllEtcdSettingsRequest {
  cell: string;
  region: string;
}

export interface AdminGetAllEtcdSettingsResponse {
  code?: number;
  error?: string;
  data?: Array<EtcdSetting>;
}

export interface AdminGetAvailableCellsRequest {
  region: string;
}

export interface AdminGetAvailableCellsResponse {
  code?: number;
  error?: string;
  data?: Array<string>;
}

export interface AdminGetBaseImageByRuntimeAndIdRequest {
  runtime: string;
  image_id: string;
}

export interface AdminGetBaseImageByRuntimeAndIdResponse {
  code?: number;
  error?: string;
  data?: FaasBaseImageInfo;
}

export interface AdminGetBatchTaskRequest {
  batch_task_id?: string;
  task_type?: string;
  offset?: number;
  limit?: number;
  status?: string;
}

export interface AdminGetBatchTaskResponse {
  code?: number;
  error?: string;
  data?: Array<BatchTask>;
}

export interface AdminGetClustersRequest {
  service_id?: string;
  function_id?: string;
  psm?: string;
  region?: string;
  runtime?: string;
  limit?: number;
  offset?: number;
  active?: string;
  env_name?: string;
}

export interface AdminGetClustersResponse {
  code?: number;
  error?: string;
  data?: Array<BasicCluster>;
}

export interface AdminGetEtcdSettingsRequest {
  setting_name: string;
  cell?: string;
  region?: string;
}

export interface AdminGetEtcdSettingsResponse {
  code?: number;
  error?: string;
  data?: EtcdSetting;
}

export interface AdminGetParentTaskDetailRequest {
  parent_task_id: string;
}

export interface AdminGetParentTaskDetailResponse {
  code?: number;
  error?: string;
  data?: ParentTask;
}

export interface AdminGetParentTaskRequest {
  status?: string;
  /** enum value: mqevent/function */
  task_type?: string;
  limit?: number;
  offset?: number;
}

export interface AdminGetParentTaskResponse {
  code?: number;
  error?: string;
  data?: Array<ParentTask>;
}

export interface AdminRollbackRequest {
  targets?: Array<AdminRollbackRequestTargetsMessage>;
}

export interface AdminRollbackRequestTargetsMessage {
  /** the ID of the target rollback ticket */
  ticket_id?: string;
}

export interface AdminRollbackResponse {
  code?: number;
  data?: string;
  error?: string;
}

export interface AdminUpdateBatchTaskRequset {
  parent_task_id?: string;
  batch_task_id?: string;
  /** enum: [ "initial", "approved", "dispatched", "skipped", "success", "failed" ] */
  status: string;
}

export interface AdminUpdateBatchTaskResponse {
  code?: number;
  error?: string;
  data?: BatchTask;
}

export interface AdminUpdateFunctionBatchTaskClusterParams {
  service_id: string;
  region: string;
  cluster: string;
  psm: string;
  function_id: string;
}

export interface AdminUpdateParentTaskRequest {
  batch_task_id: string;
  /** enum value: pending/failed */
  status: string;
  concurrency?: number;
}

export interface AdminUpdateParentTaskResponse {
  code?: number;
  error?: string;
  data?: Record<string, string>;
}

export interface AdminUpsertEtcdSettingRequest {
  name: string;
  value: string;
  cell: string;
  region: string;
}

export interface AdminUpsertEtcdSettingResponse {
  code?: number;
  error?: string;
  data?: EtcdSetting;
}

export interface AsyncBuildImageVersionsRequest {
  faas_release_form_base64: string;
  trigger_user?: string;
}

export interface AsyncBuildImageVersionsResponse {
  code?: number;
  error?: string;
  data?: string;
}

/** basic info of cluster. */
export interface BasicCluster {
  adaptive_concurrency_mode?: string;
  /** traffic aliases */
  aliases?: Record<string, common.Alias>;
  /** restricted access, only open to administrators. 保留字段，仅 admin 可修改 */
  async_mode?: boolean;
  /** auth switch. 鉴权开关 */
  auth_enable?: boolean;
  cell?: string;
  /** cluster name, starts with faas-. 集群名 */
  cluster: string;
  /** ID of code revision. 部署代码版本 ID */
  code_revision_id?: string;
  /** number of code revision. 部署代码版本号 */
  code_revision_number?: string;
  /** cold start switch. 冷启动开关，true 为关闭 */
  cold_start_disabled?: boolean;
  /** CORS switch. CORS 开关 */
  cors_enable?: boolean;
  created_at: string;
  enable_colocate_scheduling?: boolean;
  enable_scale_optimise?: boolean;
  enable_scale_strategy?: boolean;
  env_name: string;
  /** exclusive mode. 独占模式 */
  exclusive_mode?: boolean;
  format_envs?: Array<common.FormatEnvs>;
  function_id: string;
  /** GDPR switch. GDPR 鉴权开关 */
  gdpr_enable?: boolean;
  global_kv_namespace_ids?: Array<string>;
  handler: string;
  http_trigger_disable?: boolean;
  id: string;
  initializer: string;
  initializer_sec?: number;
  is_ipv6_only?: boolean;
  /** disable zones in a region */
  is_this_zone_disabled?: Record<string, boolean>;
  latency_sec?: number;
  lazyload?: boolean;
  max_concurrency?: number;
  memory_mb?: number;
  region: string;
  /** function reserved mode switch. 函数预留模式开关 */
  reserved_dp_enabled?: boolean;
  resource_limit?: common.ResourceLimitWithAlias;
  /** ID of revision. 版本 ID */
  revision_id?: string;
  /** number of revision. 版本号 */
  revision_number?: number;
  /** function routing strategy, enums：prefer_reserved, prefer_elastic. 函数路由调度策略 */
  routing_strategy?: string;
  /** runtime. Optional values: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1 */
  runtime: string;
  scale_enabled?: boolean;
  scale_threshold?: number;
  scale_type?: number;
  service_id: string;
  trace_enable?: boolean;
  updated_at: string;
  /** zone throttle log bytes */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** ZTI switch. ZTI 鉴权开关 */
  zti_enable?: boolean;
  online_mode?: boolean;
  enable_runtime_file_log?: boolean;
  enable_runtime_console_log?: boolean;
  enable_runtime_stream_log?: boolean;
  enable_runtime_es_log?: boolean;
  enable_runtime_json_log?: boolean;
  enable_system_stream_log?: boolean;
  enable_system_es_log?: boolean;
  runtime_stream_log_bytes_per_sec?: number;
  system_stream_log_bytes_per_sec?: number;
  enable_reserve_frozen_instance?: boolean;
  /** overload_protect_enabled */
  overload_protect_enabled?: boolean;
  privileged?: boolean;
}

export interface BatchTask {
  batch_task_id: string;
  task_id: string;
  /** json string, meta info of task, e.g.: service_id/psm/function_id */
  task_meta: string;
  /** type of task, enum value: [ function, mqevent ] */
  type: string;
  /** status of task, enum value: [ initial, reviewing, dispatched, failed, success, skipped, approved, rejected ] */
  status: string;
  /** the person who created this task */
  operator: string;
  /** all events */
  events?: Array<BatchTaskEvent>;
  dispatched_at?: string;
  approved_by?: string;
  created_at?: string;
  updated_at?: string;
}

export interface BatchTaskEvent {
  name: string;
  time: string;
  message: string;
}

export interface BytecycleWebhookData {
  field_name?: string;
  value?: string;
}

export interface CheckUserIsAdministratorRequest {
  user: string;
}

export interface CheckUserIsAdministratorResponse {
  code?: number;
  data?: boolean;
  error?: string;
}

export interface CreateImageVersionByAdminRequest {
  region: string;
  type: string;
  status: string;
  tag: string;
  image_id: string;
  publisher: string;
  version: string;
  changelog: string;
  branch?: string;
  commit?: string;
  alias?: string;
  canary_ratio?: number;
  extra?: ImageVersionExtra;
  published_at?: number;
}

export interface CreateImageVersionByAdminResponse {
  code?: number;
  error?: string;
  data?: ImageVersion;
}

export interface CreateMqEventImageVersionsByBytecycleRequest {
  Authorization: string;
  trigger_user: string;
  faas_release_form_base64: string;
  commit_id: string;
  git_mr_detail: GitMrDetail;
}

export interface CreateMqEventImageVersionsByBytecycleResponse {
  status?: string;
  message?: string;
  data?: Array<BytecycleWebhookData>;
}

export interface DataMessage2 {
  /** Lark Group ID */
  ID?: string;
  /** Lark Group Name */
  Name?: string;
}

export interface DataMessage5 {
  /** MQ trigger announcement template content */
  content?: string;
}

export interface DeleteImageVersionByIDRequest {
  id: string;
}

export interface DeleteImageVersionByIDResponse {
  code?: number;
  error?: string;
  data?: ImageVersion;
}

export interface EmergencyTransferItem {
  value_type?: string;
  zone?: string;
  cluster_type?: string;
  cell?: string;
  region?: string;
  dps?: number;
}

export interface EtcdSetting {
  name: string;
  value: string;
  updated_by?: string;
  updated_at?: string;
}

export interface FaasBaseImageInfo {
  build_time_stamp: string;
  image_id: string;
  runtime: string;
  /** repo name */
  runtime_agent: ScmVersionInfo;
  /** repo name */
  runtime_agent_dp: ScmVersionInfo;
}

export interface FuncCommonScaleSettings {
  scale_down_max_step?: number;
  scale_up_max_step?: number;
  continuous_scale_down_min_interval_sec?: number;
  /** 缩容步长比例 */
  scale_down_max_step_ratio?: number;
  /** 扩容步长比例 */
  scale_up_max_step_ratio?: number;
  /** 消费托管是否开启rebalance敏感型 */
  mqapp_rebalance_sensitive?: boolean;
}

export interface FuncConcurrencyScaleSettings {
  concurrency_scale_out_threshold?: number;
  concurrency_scale_in_threshold?: number;
  concurrency_scale_target?: number;
  concurrency_continuous_down_dur_sec?: number;
}

export interface FuncMEMScaleSettings {
  mem_scale_out_threshold?: number;
  mem_scale_in_threshold?: number;
  mem_scale_target?: number;
}

export interface FuncScaleSettings {
  scale_set_name?: string;
  cpu_scale_settings?: common.FuncCPUScaleSettings;
  mem_scale_settings?: FuncMEMScaleSettings;
  concurrency_scale_settings?: FuncConcurrencyScaleSettings;
  fast_scale_settings?: common.FuncFastScaleSettings;
  predictive_scaling_setting?: common.FuncPredictiveScalingSettings;
  lag_scale_settings?: common.FuncLagScaleSettings;
  cpu_zone_scale_settings?: Record<string, common.FuncCPUScaleSettings>;
  common_scale_settings?: FuncCommonScaleSettings;
}

export interface getAllAdministratorRequest {}

export interface GetAllAdministratorResponse {
  code?: number;
  data?: Array<string>;
  error?: string;
}

export interface GetEmergencyTransferMetricsRequest {
  cells?: Array<string>;
  cluster_types?: Array<string>;
  regions?: Array<string>;
  value_types?: Array<string>;
  zones?: Array<string>;
}

export interface GetEmergencyTransferMetricsResponse {
  items?: Array<EmergencyTransferItem>;
}

export interface GetImageVersionsRequest {
  region?: string;
  type?: string;
  tag?: string;
  limit?: string;
  offset?: string;
  with_history_images?: boolean;
  version_number?: string;
}

export interface GetImageVersionsResponse {
  code?: number;
  error?: string;
  data?: Array<ImageVersion>;
}

export interface getLarkBotChatGroupsRequest {}

export interface GetLarkBotChatGroupsResponse {
  code?: number;
  data?: Array<DataMessage2>;
  error?: string;
}

export interface getMQTriggerTemplateRequest {}

export interface GetMQTriggerTemplateResponse {
  code?: number;
  data?: DataMessage5;
  error?: string;
}

export interface GetReleaseOverviewRequest {
  /** 格式 2024-01-04T06:49:59+00:00 */
  start_time?: string;
  /** 格式 2024-01-04T06:49:59+00:00 */
  end_time?: string;
}

export interface GetReleaseOverviewResponse {
  code?: number;
  data?: Array<ReleaseOverviewItem>;
  error?: string;
}

export interface GetServiceTreesRequest {}

export interface GetServiceTreesResponse {
  code: number;
  data: Array<ServiceTreeNode>;
  error: string;
}

export interface GetTicketsByFilterRequest {
  category?: string;
  change_type?: string;
  cluster?: string;
  /** ID of function. */
  function_id?: string;
  /** ID of ticket. */
  id?: string;
  max_create_time?: string;
  min_create_time?: string;
  /** If set true, only return admin tickets */
  only_admin_ticket?: boolean;
  /** Parent ID of a ticket, ie, the ID of a batch ticket */
  parent_id?: string;
  region: string;
  /** ID of service */
  service_id: string;
  /** status of ticket. Enums: "pending","running","building","canceling","failed","success","canceled" */
  status?: string;
  trigger_id?: string;
  trigger_type?: string;
  /** ticket type. */
  type?: string;
  /** pagination query, specify the number for one page */
  limit?: number;
  /** pagination query, specify the offset, default 0 */
  offset?: number;
}

export interface GetTicketsByFilterResponse {
  code?: number;
  data?: Array<common.Ticket>;
  error?: string;
}

export interface GetUnusedResourceMetricsRequest {
  regions?: Array<string>;
  cells?: Array<string>;
  zones?: Array<string>;
  node_levels?: Array<string>;
  service_levels?: Array<string>;
  item_types?: Array<string>;
  function_ids?: Array<string>;
  trigger_ids?: Array<string>;
  scale_types?: Array<string>;
  strategies?: Array<string>;
  start_timestamp?: Int64;
  end_timestamp?: Int64;
  top_k?: string;
}

export interface GetUnusedResourceMetricsResponse {
  items?: Array<UnusedResourceItem>;
}

export interface GitMrDetail {
  description: string;
}

export interface ImageVersion {
  id: string;
  region: string;
  type: string;
  status: string;
  tag: string;
  image_id: string;
  pulisher: string;
  version: string;
  branch?: string;
  commit?: string;
  canary_ratio?: string;
  changelog?: string;
  alias?: string;
  extra?: ImageVersionExtra;
  created_at?: string;
  updated_at?: string;
  published_at?: string;
  is_history_data?: boolean;
  counter?: number;
}

export interface ImageVersionExtra {
  sdk_version?: string;
}

export interface ParentTask {
  batch_task_id: string;
  operator?: string;
  type?: string;
  /** enum value: pending/running/failed/success */
  status?: string;
  concurrency?: number;
  created_at?: string;
  updated_at?: string;
  updated_by?: string;
  status_group?: Array<ParentTaskStatusGroup>;
  total?: number;
}

export interface ParentTaskStatusGroup {
  status: string;
  count: number;
}

export interface RecoverEmergencyTransferRequest {
  function_id?: string;
  region?: string;
  resource_type?: string;
  trigger_id?: string;
  'X-Jwt-Token'?: string;
}

export interface RecoverEmergencyTransferResponse {
  code?: number;
  data?: string;
  error?: string;
}

export interface ReleaseOverviewItem {
  count?: number;
  error_code?: string;
  rate?: number;
  status_message?: string;
  total?: number;
}

export interface ScmVersionInfo {
  version: string;
  type: string;
  desc: string;
  status: string;
}

export interface SendNotificationsToLarkBotGroupsRequest {
  /** Content of the notification message */
  content?: string;
  receiver_ids?: Array<string>;
}

export interface SendNotificationsToLarkBotGroupsResponse {
  code?: number;
  data?: string;
  error?: string;
}

export interface ServiceTreeNode {
  name: string;
}

export interface SkipCheckForBatchTaskRequest {
  parent_id: string;
  id: string;
}

export interface SkipCheckForBatchTaskResponse {
  code?: number;
  error?: string;
}

export interface SyncImageVersionsRequest {
  from_region: string;
  to_regions: Array<string>;
  tags: Array<string>;
  types: Array<string>;
  insert?: boolean;
  image_version_number?: string;
  image_version_type?: string;
}

export interface SyncImageVersionsResponse {
  code?: number;
  error?: string;
  data?: Array<ImageVersion>;
}

export interface TransferFuncScaleSettings {
  expired_time?: Int64;
  cpu_scale_settings?: common.FuncCPUScaleSettings;
  mem_scale_settings?: common.MQEventMEMScaleSettings;
}

export interface TransferMQEventScaleSettings {
  expired_time?: Int64;
  cpu_scale_settings?: common.MQEventCPUScaleSettings;
  mem_scale_settings?: common.MQEventMEMScaleSettings;
}

export interface UnusedResourceItem {
  function_id?: string;
  trigger_id?: string;
  item_type?: string;
  region?: string;
  cell?: string;
  zone?: string;
  node_level?: string;
  service_level?: string;
  scale_type?: string;
  scale_settings?: FuncScaleSettings;
  transfer_scale_settings?: common.FuncCPUScaleSettings;
  transfer_mem_scale_settings?: FuncMEMScaleSettings;
  mqevent_scale_settings?: common.MQEventScaleSettings;
  transfer_mqevent_cpu_scale_settings?: common.MQEventCPUScaleSettings;
  transfer_mqevent_mem_scale_settings?: common.MQEventMEMScaleSettings;
  dps?: number;
}

export interface UpdateEmergencyTransferRequest {
  function_id: string;
  region: string;
  resource_type: string;
  trigger_id: string;
  transfer_func_scale_settings?: TransferFuncScaleSettings;
  transfer_mq_event_scale_settings?: TransferMQEventScaleSettings;
  'X-Jwt-Token'?: string;
}

export interface UpdateEmergencyTransferResponse {
  code?: number;
  data?: string;
  error?: string;
}

export interface UpdateImageVersionByIDRequest {
  id: string;
  changelog?: string;
  status?: string;
  tag?: string;
  canary_ratio?: number;
  published_at?: number;
  alias?: string;
  extra?: ImageVersionExtra;
}

export interface UpdateImageVersionByIDResponse {
  code?: number;
  error?: string;
  data?: ImageVersion;
}
/* eslint-enable */
