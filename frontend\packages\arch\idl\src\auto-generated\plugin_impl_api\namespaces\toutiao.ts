/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface InputSearchRequest {
  input_query: string;
  search_id?: string;
  count?: number;
  cursor?: number;
}

export interface InputSearchResponse {
  ok: boolean;
  errcode: Int64;
  errmsg: string;
  data: SearchLinkResp;
}

export interface LinkInfo {
  sitename?: string;
  summary?: string;
  title?: string;
  url: string;
}

export interface ReadLinkResp {
  content: string;
  title: string;
}

export interface SearchLinkResp {
  cursor: number;
  doc_results?: Array<LinkInfo>;
  has_more: boolean;
  search_id: string;
}

export interface UrlSearchRequest {
  url: string;
  prompt?: string;
}

export interface UrlSearchResponse {
  ok: boolean;
  errcode: Int64;
  errmsg: string;
  data: ReadLinkResp;
}
/* eslint-enable */
