/* stylelint-disable no-descending-specificity */
/* stylelint-disable declaration-no-important */
/* stylelint-disable max-nesting-depth */
.expression-editor-suggestion-pin {
  position: absolute;
  width: 0;
  height: 1.5rem;
  transform: translateY(-0.5rem);
}

.expression-editor-suggestion {
  max-height: 236px;
  width: 272px;
  z-index: 1000;
  background: var(--light-usage-bg-color-bg-3, #FFF);
  border-radius: 8px;
  border: 0.5px solid rgba(153, 182, 255, 12%);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 25%);
  overflow: auto;
}

.expression-editor-suggestion-empty {
  z-index: 1000;
  border-radius: 8px;
  border: 0.5px solid rgba(153, 182, 255, 12%);
  background: #FFF;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 25%);

  p {
    margin: 4px 6px;
    color: var(--light-usage-text-color-text-2, rgba(29, 28, 35, 60%));
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
  }
}

.expression-editor-suggestion-tree {
  :global {
    .semi-tree-search-wrapper {
      display: none;
    }

    .semi-tree-option-list {
      width: fit-content;
      min-width: 100%;
      padding: 4px;
      padding-left: 0;

      li {
        height: 32px;
      }

      .semi-tree-option {
        background-color: transparent;
        pointer-events: none;
      }

      .semi-tree-option-label {
        height: 24px;
        padding: 0 4px;
        margin-right: 4px;
        border-radius: 4px;
        pointer-events: auto;

        &:hover {
          background: var(--light-usage-fill-color-fill-1, rgb(46 46 56 / 8%));
        }

        &:active {
          background: var(--light-usage-fill-color-fill-2, rgb(46 46 56 / 12%));
        }

        .semi-tree-option-label-text {
          width: fit-content;
          display: inline-block;
          white-space: nowrap;

          & span {
            width: fit-content;
            display: inline-block;
            white-space: nowrap;
          }


          .semi-tree-option-highlight {
            color: var(--light-usage-warning-color-warning, #FF9600)
          }
        }

      }

      .semi-tree-option-selected {
        color: var(--light-usage-primary-color-primary, #4D53E8);
        font-weight: 600;
      }

      .semi-tree-option-disabled {
        .semi-tree-option-label {
          cursor: not-allowed;
          background: transparent;
        }

        .semi-icon+.semi-tree-option-label {
          color: var(--light-usage-text-color-text-0, #1D1C23);
        }
      }
    }

    .semi-tree-option-empty-icon {
      width: 16px;
    }

    .semi-tree-option-expand-icon {
      width: 16px;
      height: 16px;
      padding: 4px;
      margin-right: 0;
      border-radius: 4px;
      pointer-events: auto;

      &:hover {
        background: var(--light-usage-fill-color-fill-1, rgb(46 46 56 / 8%));
      }

      &:active {
        background: var(--light-usage-fill-color-fill-2, rgb(46 46 56 / 12%));
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }
}

.expression-editor-suggestion-keyboard-selected {
  :global {
    .semi-tree-option-label {
      background: var(--light-usage-fill-color-fill-1, rgba(46, 46, 56, 8%)) !important;
    }
  }
}
