// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAPIKey = "api_key"

// APIKey api key table
type APIKey struct {
	ID         int64  `gorm:"column:id;primaryKey;autoIncrement:true;comment:Primary Key ID" json:"id"`                              // Primary Key ID
	APIKey     string `gorm:"column:api_key;not null;comment:API Key hash" json:"api_key"`                                           // API Key hash
	Name       string `gorm:"column:name;not null;comment:API Key Name" json:"name"`                                                 // API Key Name
	Status     int32  `gorm:"column:status;not null;comment:0 normal, 1 deleted" json:"status"`                                      // 0 normal, 1 deleted
	UserID     int64  `gorm:"column:user_id;not null;comment:API Key Owner" json:"user_id"`                                          // API Key Owner
	ExpiredAt  int64  `gorm:"column:expired_at;not null;comment:API Key Expired Time" json:"expired_at"`                             // API Key Expired Time
	CreatedAt  int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt  int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	LastUsedAt int64  `gorm:"column:last_used_at;not null;comment:Used Time in Milliseconds" json:"last_used_at"`                    // Used Time in Milliseconds
}

// TableName APIKey's table name
func (*APIKey) TableName() string {
	return TableNameAPIKey
}
