/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as plugin_common from './plugin_common';
import * as base from './base';

export type Int64 = string;

export interface CreateRecordData {
  /** 工单id */
  record_id?: number;
}

export interface CreateRecordRequest {
  /** 素材id */
  material_id: string;
  /** 类别id */
  category_id?: string;
  /** 操作类型 */
  operate_type: plugin_common.ListUnlistType;
  /** cookie */
  Cookie?: string;
  Base?: base.Base;
}

export interface CreateRecordResponse {
  code?: number;
  msg?: string;
  data?: CreateRecordData;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
