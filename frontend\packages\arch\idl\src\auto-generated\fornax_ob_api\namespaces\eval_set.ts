/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as evaluation_domain_common from './evaluation_domain_common';
import * as datasetv2 from './datasetv2';

export type Int64 = string | number;

/** EvaluationSetSchema 评测集 Schema，包含字段的类型限制等信息 */
export interface EvaluationSetSchema {
  /** 主键&外键 */
  id?: Int64;
  app_id?: number;
  space_id?: Int64;
  evaluation_set_id?: Int64;
  /** 数据集字段约束 */
  field_schemas?: Array<FieldSchema>;
  /** 系统信息 */
  base_info?: evaluation_domain_common.BaseInfo;
}

export interface FieldSchema {
  /** 唯一键 */
  key?: string;
  /** 展示名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 类型，如 文本，图片，etc. */
  content_type?: string;
  /** 默认渲染格式，如 code, json, etc.mai */
  default_display_format?: datasetv2.FieldDisplayFormat;
  /** 当前列的状态 */
  status?: datasetv2.FieldStatus;
  /** [20,50) 内容格式限制相关
文本内容格式限制，格式为 JSON schema，协议参考 https://json-schema.org/specification */
  text_schema?: string;
  /** 多模态规格限制 */
  multi_model_spec?: datasetv2.MultiModalSpec;
  /** 用户是否不可见 */
  hidden?: boolean;
}
/* eslint-enable */
