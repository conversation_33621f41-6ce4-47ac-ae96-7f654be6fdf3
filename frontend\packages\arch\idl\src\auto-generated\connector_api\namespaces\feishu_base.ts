/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum InputComponentType {
  /** 文本输入框，图 4 控件 */
  Text = 1,
  /** 单选，图 4 控件 */
  SingleSelect = 2,
  /** 多选，图 4 控件 */
  MultiSelect = 3,
  /** 字段选择器，图 4 控件 */
  FieldSelector = 4,
}

export enum OutputSubComponentType {
  /** 没有子 component */
  None = 0,
  /** 输出类型为 object */
  Object = 1,
}

/** base 就是多维表格的英文名。所以飞书多维表格是 FeishuBase */
export interface FeishuBaseConfig {
  /** markdown，对应“#配置多维飞书表格\n将你的...“ */
  description: string;
  /** 所有类型 id 到 info 的映射 */
  output_type_list: Array<OutputTypeInfo>;
  output_type: number;
  /** markdown, 对应“捷径输出类型”旁边的 i */
  output_desc?: string;
  output_sub_component: OutputSubComponent;
  input_config: Array<InputConfig>;
  /** markdown, 对应“配置多维表格字段”旁边的 i */
  input_desc?: string;
  input_type_list: Array<InputTypeInfo>;
  /** 输出类型选中 object 后，object value 可选的类型列表 */
  object_value_type_list: Array<OutputTypeInfo>;
  /** 完善上架信息,  */
  to_complete_info?: ToCompleteInfo;
}

export interface GetFeishuBaseConfigRequest {
  bot_id: string;
}

export interface GetFeishuBaseConfigResponse {
  config?: FeishuBaseConfig;
  code: number;
  msg?: string;
}

export interface InputComponent {
  type: InputComponentType;
  /** 仅 Type == Text 有用 */
  max_char?: number;
  /** Type in [SingleSelect, MultiSelect] */
  choice?: Array<string>;
  /** Type == FieldSelector */
  supported_type?: Array<number>;
}

export interface InputConfig {
  field: string;
  title: string;
  placeholder?: string;
  input_component: InputComponent;
  required?: boolean;
  /** markdown, 对应 user_query 旁边的 i */
  desc?: string;
  /** 失效了。需要重新配置的时候才会出现 */
  invalid?: boolean;
}

export interface InputTypeInfo {
  id: number;
  name: string;
}

export interface OutputSubComponent {
  type: OutputSubComponentType;
  /** 仅 Type == object 会有 */
  item_list?: Array<OutputSubComponentItem>;
  /** eg: 结构化输出配置右边的 i */
  struct_output_desc?: string;
  /** 结构化输出，id 列的说明 */
  struct_id_desc?: string;
  /** 结构化输出，主属性说明 */
  struct_primary_desc?: string;
}

export interface OutputSubComponentItem {
  key: string;
  /** 同 FeishuBaseConfig.OutputType */
  output_type: number;
  /** 主属性, 标记该属性为用于排序的主属性。 */
  is_primary?: boolean;
  /** 标记该属性会作为Object字段的筛选、分组依据。 */
  is_group_by_key?: boolean;
}

export interface OutputTypeInfo {
  /** 会增加，就不整成 enum 了。 */
  id: number;
  /** 名称，例如“文本”，“对象”等 */
  name: string;
  /** markdown, 对应图 3 中 “需要在 Persona ...“ */
  tips?: string;
}

export interface ToCompleteInfo {
  /** 完善信息的填写地址 */
  url: string;
  /** 是否填好 */
  completed: boolean;
}

export interface UpdateFeishuBaseCompleteStatusRequest {
  bot_id: string;
}

export interface UpdateFeishuBaseCompleteStatusResponse {
  code: number;
  msg?: string;
}
/* eslint-enable */
