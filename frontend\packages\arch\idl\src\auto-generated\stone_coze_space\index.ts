/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as admin from './namespaces/admin';
import * as contract from './namespaces/contract';
import * as domain_machine_task from './namespaces/domain_machine_task';
import * as exper_agent from './namespaces/exper_agent';
import * as external_agent from './namespaces/external_agent';
import * as invite from './namespaces/invite';
import * as market from './namespaces/market';
import * as mobile from './namespaces/mobile';
import * as resource from './namespaces/resource';
import * as task from './namespaces/task';
import * as user from './namespaces/user';

export {
  admin,
  contract,
  domain_machine_task,
  exper_agent,
  external_agent,
  invite,
  market,
  mobile,
  resource,
  task,
  user,
};
export * from './namespaces/admin';
export * from './namespaces/contract';
export * from './namespaces/domain_machine_task';
export * from './namespaces/exper_agent';
export * from './namespaces/external_agent';
export * from './namespaces/invite';
export * from './namespaces/market';
export * from './namespaces/mobile';
export * from './namespaces/resource';
export * from './namespaces/task';
export * from './namespaces/user';

export type Int64 = string | number;

export default class StoneCozeSpaceService<T> {
  private request: any = () => {
    throw new Error('StoneCozeSpaceService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/coze_space/create_task_replay */
  CreateTaskReplay(
    req?: task.CreateTaskReplayRequest,
    options?: T,
  ): Promise<task.CreateTaskReplayResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/create_task_replay');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      need_qr_code: _req['need_qr_code'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_task_replay */
  GetTaskReplay(
    req?: task.GetTaskReplayRequest,
    options?: T,
  ): Promise<task.GetTaskReplayResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_task_replay');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/delete_task */
  DeleteCozeSpaceTask(
    req: task.DeleteCozeSpaceTaskRequest,
    options?: T,
  ): Promise<task.DeleteCozeSpaceTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/delete_task');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_task */
  UpdateCozeSpaceTask(
    req: task.UpdateCozeSpaceTaskRequest,
    options?: T,
  ): Promise<task.UpdateCozeSpaceTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/update_task');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      task_name: _req['task_name'],
      mcp_tool_list: _req['mcp_tool_list'],
      expert_agent_config: _req['expert_agent_config'],
      scheduled_task_setting: _req['scheduled_task_setting'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/create_task */
  CreateCozeSpaceTask(
    req: task.CreateCozeSpaceTaskRequest,
    options?: T,
  ): Promise<task.CreateCozeSpaceTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/create_task');
    const method = 'POST';
    const data = {
      task_name: _req['task_name'],
      task_type: _req['task_type'],
      file_uri_list: _req['file_uri_list'],
      mcp_tool_list: _req['mcp_tool_list'],
      agent_ids: _req['agent_ids'],
      scheduled_task_setting: _req['scheduled_task_setting'],
      source_from: _req['source_from'],
      expert_agent_config: _req['expert_agent_config'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/chat */
  CozeSpaceChat(
    req?: task.CozeSpaceChatRequest,
    options?: T,
  ): Promise<task.CozeSpaceChatResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/chat');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      query: _req['query'],
      files: _req['files'],
      mcp_list: _req['mcp_list'],
      chat_type: _req['chat_type'],
      pause_reason: _req['pause_reason'],
      task_run_mode: _req['task_run_mode'],
      expert_agent_run_config: _req['expert_agent_run_config'],
      scheduled_task_config: _req['scheduled_task_config'],
      disable_team_mode: _req['disable_team_mode'],
      resume_data: _req['resume_data'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/poll_step_list */
  PollStepList(
    req?: task.PollStepListRequest,
    options?: T,
  ): Promise<task.PollStepListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/poll_step_list');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      answer_id: _req['answer_id'],
      next_key: _req['next_key'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/operate_task */
  OperateTask(
    req?: task.OperateTaskRequest,
    options?: T,
  ): Promise<task.OperateTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/operate_task');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      operate_type: _req['operate_type'],
      pause_reason: _req['pause_reason'],
      browser: _req['browser'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_task_list */
  GetCozeSpaceTaskList(
    req?: task.GetCozeSpaceTaskListRequest,
    options?: T,
  ): Promise<task.GetCozeSpaceTaskListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_task_list');
    const method = 'POST';
    const data = { cursor: _req['cursor'], size: _req['size'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_message_list */
  GetMessageList(
    req?: task.GetMessageListRequest,
    options?: T,
  ): Promise<task.GetMessageListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_message_list');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      cursor: _req['cursor'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_task_plan */
  UpdateTaskPlan(
    req: task.UpdateTaskPlanRequest,
    options?: T,
  ): Promise<task.UpdateTaskPlanResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/update_task_plan');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      action_id: _req['action_id'],
      task_plan: _req['task_plan'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_task_replay_by_id */
  GetTaskReplayById(
    req?: task.GetTaskReplayByIdRequest,
    options?: T,
  ): Promise<task.GetTaskReplayByIdResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_task_replay_by_id');
    const method = 'POST';
    const data = {
      task_share_id: _req['task_share_id'],
      secret: _req['secret'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/operate_task_replay */
  OperateTaskReplay(
    req?: task.OperateTaskReplayRequest,
    options?: T,
  ): Promise<task.OperateTaskReplayResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/operate_task_replay');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      task_share_id: _req['task_share_id'],
      operate_type: _req['operate_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/upload_task_file */
  UploadTaskFile(
    req?: task.UploadTaskFileRequest,
    options?: T,
  ): Promise<task.UploadTaskFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/upload_task_file');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      file_name: _req['file_name'],
      file_content: _req['file_content'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/upload_user_research_file */
  UploadUserResearchFile(
    req: task.UploadUserResearchFileRequest,
    options?: T,
  ): Promise<task.UploadUserResearchFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/upload_user_research_file');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      action: _req['action'],
      file_type: _req['file_type'],
      file_name: _req['file_name'],
      file_content: _req['file_content'],
      desc: _req['desc'],
      fields: _req['fields'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/landing_page/email_subscribe */
  LandingPageEmailSubscribe(
    req?: market.LandingPageEmailSubscribeRequest,
    options?: T,
  ): Promise<market.LandingPageEmailSubscribeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/landing_page/email_subscribe');
    const method = 'POST';
    const data = { email: _req['email'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_url */
  GetUrl(
    req: resource.GetUrlRequest,
    options?: T,
  ): Promise<resource.GetUrlResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/get_url');
    const method = 'POST';
    const data = {
      uri: _req['uri'],
      expire_seconds: _req['expire_seconds'],
      is_short_url: _req['is_short_url'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/landing_page */
  LandingPage(
    req?: market.LandingPageRequest,
    options?: T,
  ): Promise<market.LandingPageResponse> {
    const url = this.genBaseURL('/api/coze_space/landing_page');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/expert_product_details */
  ExpertProductDetails(
    req: market.ExpertProductDetailsRequest,
    options?: T,
  ): Promise<market.ExpertProductDetailsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/expert_product_details');
    const method = 'POST';
    const data = { agent_id: _req['agent_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/digg */
  Digg(req: market.DiggRequest, options?: T): Promise<market.DiggResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/digg');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      action_type: _req['action_type'],
      is_cancel: _req['is_cancel'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/search_stock */
  SearchStock(
    req: task.SearchStockRequest,
    options?: T,
  ): Promise<task.SearchStockResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/search_stock');
    const method = 'POST';
    const data = {
      search_type: _req['search_type'],
      stock_search_word: _req['stock_search_word'],
      sector_search_word: _req['sector_search_word'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/coze_space/text2image */
  Text2Image(
    req: resource.Text2ImageRequest,
    options?: T,
  ): Promise<resource.Text2ImageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/text2image');
    const method = 'GET';
    const params = {
      prompt: _req['prompt'],
      width: _req['width'],
      height: _req['height'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/coze_space/get_invite_info
   *
   * invite
   */
  GetInviteInfo(
    req?: invite.GetInviteInfoRequest,
    options?: T,
  ): Promise<invite.GetInviteInfoResponse> {
    const url = this.genBaseURL('/api/coze_space/get_invite_info');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/check_invite_code */
  CheckInviteCode(
    req?: invite.CheckInviteCodeRequest,
    options?: T,
  ): Promise<invite.CheckInviteCodeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/check_invite_code');
    const method = 'POST';
    const data = { code: _req['code'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/expert_product_list */
  ExpertProductList(
    req?: market.ExpertProductListRequest,
    options?: T,
  ): Promise<market.ExpertProductListResponse> {
    const url = this.genBaseURL('/api/coze_space/expert_product_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/check_in_wait_list */
  CheckInWaitList(
    req?: invite.CheckInWaitListRequest,
    options?: T,
  ): Promise<invite.CheckInWaitListResponse> {
    const url = this.genBaseURL('/api/coze_space/check_in_wait_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/join_wait_list */
  JoinWaitList(
    req?: invite.JoinWaitListRequest,
    options?: T,
  ): Promise<invite.JoinWaitListResponse> {
    const url = this.genBaseURL('/api/coze_space/join_wait_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/get_sandbox_token */
  GetSandboxToken(
    req: task.GetSandboxTokenRequest,
    options?: T,
  ): Promise<task.GetSandboxTokenResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/get_sandbox_token');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      action_url_code: _req['action_url_code'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_user_scheduled_tasks */
  GetUserScheduledTasks(
    req?: task.GetUserScheduledTasksRequest,
    options?: T,
  ): Promise<task.GetUserScheduledTasksResponse> {
    const url = this.genBaseURL('/api/coze_space/get_user_scheduled_tasks');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/generate_related_words */
  GenerateRelatedWords(
    req?: exper_agent.GenerateRelatedWordsRequest,
    options?: T,
  ): Promise<exper_agent.GenerateRelatedWordsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/generate_related_words');
    const method = 'POST';
    const data = {
      original_word: _req['original_word'],
      describe: _req['describe'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/code_repair */
  CodeRepair(
    req?: resource.CodeRepairRequest,
    options?: T,
  ): Promise<resource.CodeRepairResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/code_repair');
    const method = 'POST';
    const data = { uri: _req['uri'], error_msg: _req['error_msg'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/authorized_channel_match */
  AuthorizedChannelMatch(
    req?: exper_agent.AuthorizedChannelMatchRequest,
    options?: T,
  ): Promise<exper_agent.AuthorizedChannelMatchResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/authorized_channel_match');
    const method = 'POST';
    const data = { web_rul: _req['web_rul'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/update_task_status
   *
   * external agent
   */
  UpdateTaskStatus(
    req?: external_agent.UpdateTaskStatusRequest,
    options?: T,
  ): Promise<external_agent.UpdateTaskStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/update_task_status');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      sk: _req['sk'],
      task_id: _req['task_id'],
      task_status: _req['task_status'],
      result: _req['result'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_web_url */
  GetWebUrl(
    req?: resource.GetWebUrlRequest,
    options?: T,
  ): Promise<resource.GetWebUrlResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_web_url');
    const method = 'POST';
    const data = {
      original_url: _req['original_url'],
      exchange_for_short_url: _req['exchange_for_short_url'],
      exchange_for_qr_code: _req['exchange_for_qr_code'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/operate_sandbox */
  OperateSandbox(
    req?: resource.OperateSandboxRequest,
    options?: T,
  ): Promise<resource.OperateSandboxResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/operate_sandbox');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      action: _req['action'],
      delete_cookie_info: _req['delete_cookie_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_task_name */
  UpdateTaskName(
    req?: external_agent.UpdateTaskNameRequest,
    options?: T,
  ): Promise<external_agent.UpdateTaskNameResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/update_task_name');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      sk: _req['sk'],
      task_id: _req['task_id'],
      task_name: _req['task_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/gen_doc */
  GenDoc(
    req: resource.GenDocRequest,
    options?: T,
  ): Promise<resource.GenDocResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/gen_doc');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      target_file_type: _req['target_file_type'],
      original_file_uri: _req['original_file_uri'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/pre_get_contract_info
   *
   * milv agent 幂律专家agent
   */
  PreGetContractInfo(
    req?: contract.PreGetContractInfoRequest,
    options?: T,
  ): Promise<contract.PreGetContractInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/pre_get_contract_info');
    const method = 'POST';
    const data = {
      file_content: _req['file_content'],
      task_id: _req['task_id'],
      file_name: _req['file_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_contract_case_info */
  GetContractCaseInfo(
    req?: contract.GetContractCaseInfoRequest,
    options?: T,
  ): Promise<contract.GetContractCaseInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_contract_case_info');
    const method = 'POST';
    const data = { case_no: _req['case_no'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_contract_law_info */
  GetContractLawInfo(
    req?: contract.GetContractLawInfoRequest,
    options?: T,
  ): Promise<contract.GetContractLawInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_contract_law_info');
    const method = 'POST';
    const data = { law: _req['law'], num: _req['num'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_user_info */
  GetCozeSpaceUserInfo(
    req?: user.GetCozeSpaceUserInfoRequest,
    options?: T,
  ): Promise<user.GetCozeSpaceUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_user_info');
    const method = 'POST';
    const data = {
      need_volcano_info: _req['need_volcano_info'],
      need_user_job_info: _req['need_user_job_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/search_city_info
   *
   * tongcheng
   */
  SearchCityInfo(
    req?: exper_agent.SearchCityInfoRequest,
    options?: T,
  ): Promise<exper_agent.SearchCityInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/search_city_info');
    const method = 'POST';
    const data = { search_type: _req['search_type'], keyword: _req['keyword'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_scheduled_task */
  UpdateScheduledTask(
    req: task.UpdateCozeScheduledTaskRequest,
    options?: T,
  ): Promise<task.UpdateCozeScheduledTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/update_scheduled_task');
    const method = 'POST';
    const data = {
      scheduled_task_id: _req['scheduled_task_id'],
      task_type: _req['task_type'],
      status: _req['status'],
      scheduled_task_name: _req['scheduled_task_name'],
      trigger: _req['trigger'],
      mcp_tool_list: _req['mcp_tool_list'],
      expert_agent_config: _req['expert_agent_config'],
      task_plan: _req['task_plan'],
      is_need_try_run: _req['is_need_try_run'],
      description: _req['description'],
      task_id: _req['task_id'],
      daily_executed_at: _req['daily_executed_at'],
      task_plan_v2: _req['task_plan_v2'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_scheduled_tasks */
  GetScheduledTasks(
    req?: task.GetUserScheduledTaskRequest,
    options?: T,
  ): Promise<task.GetUserScheduledTaskResponse> {
    const url = this.genBaseURL('/api/coze_space/get_scheduled_tasks');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/try_run_scheduled_task */
  TryRunScheduledTask(
    req: task.TryRunCozeScheduledTaskRequest,
    options?: T,
  ): Promise<task.TryRunCozeScheduledTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/try_run_scheduled_task');
    const method = 'POST';
    const data = { scheduled_task_id: _req['scheduled_task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/delete_scheduled_task */
  DeleteScheduledTask(
    req: task.DeleteCozeScheduledTaskRequest,
    options?: T,
  ): Promise<task.DeleteCozeScheduledTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/delete_scheduled_task');
    const method = 'POST';
    const data = { scheduled_task_id: _req['scheduled_task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/create_scheduled_task */
  CreateScheduledTask(
    req?: task.CreateCozeScheduledTaskRequest,
    options?: T,
  ): Promise<task.CreateCozeScheduledTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/create_scheduled_task');
    const method = 'POST';
    const data = {
      is_need_try_run: _req['is_need_try_run'],
      scheduled_task_name: _req['scheduled_task_name'],
      trigger: _req['trigger'],
      source_task_id: _req['source_task_id'],
      task_plan: _req['task_plan'],
      description: _req['description'],
      daily_executed_at: _req['daily_executed_at'],
      task_plan_v2: _req['task_plan_v2'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_team_info */
  UpdateTeamInfo(
    req?: user.UpdateTeamInfoRequest,
    options?: T,
  ): Promise<user.UpdateTeamInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/update_team_info');
    const method = 'POST';
    const data = { switch: _req['switch'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_team_info */
  GetTeamInfo(
    req?: user.GetTeamInfoRequest,
    options?: T,
  ): Promise<user.GetTeamInfoResponse> {
    const url = this.genBaseURL('/api/coze_space/get_team_info');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/get_task_info */
  GetTaskInfo(
    req: task.GetTaskInfoRequest,
    options?: T,
  ): Promise<task.GetTaskInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/get_task_info');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/feel_good */
  FeelGood(
    req?: task.FeelGoodRequest,
    options?: T,
  ): Promise<task.FeelGoodResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/feel_good');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      answer_id: _req['answer_id'],
      feel: _req['feel'],
      content: _req['content'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_scheduled_task_quota */
  GetUserScheduledTaskQuota(
    req?: task.CheckUserScheduledTaskQuotaRequest,
    options?: T,
  ): Promise<task.CheckUserScheduledTaskQuotaResponse> {
    const url = this.genBaseURL('/api/coze_space/get_scheduled_task_quota');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/coze_space/get_single_scheduled_task
   *
   * scheduled task
   */
  GetSingleScheduledTask(
    req: task.GetSingleScheduledTaskRequest,
    options?: T,
  ): Promise<task.GetSingleScheduledTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/get_single_scheduled_task');
    const method = 'POST';
    const data = { scheduled_task_id: _req['scheduled_task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/delete_user_authorization */
  DeleteUserAuthorization(
    req?: user.DeleteUserAuthorizationRequest,
    options?: T,
  ): Promise<user.DeleteUserAuthorizationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/delete_user_authorization');
    const method = 'POST';
    const data = { url: _req['url'], clear_arg: _req['clear_arg'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_user_authorization_list */
  GetUserAuthorizationList(
    req?: user.GetUserAuthorizationListRequest,
    options?: T,
  ): Promise<user.GetUserAuthorizationListResponse> {
    const url = this.genBaseURL('/api/coze_space/get_user_authorization_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/add_user_authorization */
  AddUserAuthorization(
    req?: user.AddUserAuthorizationRequest,
    options?: T,
  ): Promise<user.AddUserAuthorizationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/add_user_authorization');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      url: _req['url'],
      clear_arg: _req['clear_arg'],
      add_arg: _req['add_arg'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/search_feel_good */
  SearchFeelGood(
    req?: task.SearchFeelGoodRequest,
    options?: T,
  ): Promise<task.SearchFeelGoodResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/search_feel_good');
    const method = 'POST';
    const data = { task_id: _req['task_id'], answer_id: _req['answer_id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/coze_space/get_authorized_channel_list */
  GetAuthorizedChannelList(
    req?: exper_agent.GetAuthorizedChannelListRequest,
    options?: T,
  ): Promise<exper_agent.GetAuthorizedChannelListResponse> {
    const url = this.genBaseURL('/api/coze_space/get_authorized_channel_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/get_task_example */
  GetTaskExample(
    req: task.GetTaskExampleRequest,
    options?: T,
  ): Promise<task.GetTaskExampleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/get_task_example');
    const method = 'POST';
    const data = { task_example_id: _req['task_example_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/admin/list_task_example */
  AdminListTaskExample(
    req: admin.ListTaskExampleRequest,
    options?: T,
  ): Promise<admin.ListTaskExampleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/admin/list_task_example');
    const method = 'POST';
    const data = {
      size: _req['size'],
      page: _req['page'],
      task_ids: _req['task_ids'],
      category_id: _req['category_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/admin/delete_task_example */
  AdminDeleteTaskExample(
    req: admin.DeleteTaskExampleRequest,
    options?: T,
  ): Promise<admin.DeleteTaskExampleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/admin/delete_task_example');
    const method = 'POST';
    const data = { example_id: _req['example_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_user_info */
  UpdateCozeSpaceUserInfo(
    req?: user.UpdateCozeSpaceUserInfoRequest,
    options?: T,
  ): Promise<user.UpdateCozeSpaceUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/update_user_info');
    const method = 'POST';
    const data = { job_id: _req['job_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/get_task_example_list
   *
   * task example 相关接口
   */
  GetTaskExampleList(
    req?: task.GetTaskExampleListRequest,
    options?: T,
  ): Promise<task.GetTaskExampleListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_task_example_list');
    const method = 'POST';
    const data = {
      category_id: _req['category_id'],
      page_size: _req['page_size'],
      page: _req['page'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/admin/create_task_example
   *
   * 管理后台相关接口，先放在这
   */
  AdminCreateTaskExample(
    req: admin.CreateTaskExampleRequest,
    options?: T,
  ): Promise<admin.CreateTaskExampleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/admin/create_task_example');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      example_name: _req['example_name'],
      example_description: _req['example_description'],
      icon_uri: _req['icon_uri'],
      index: _req['index'],
      category_id: _req['category_id'],
      user_query: _req['user_query'],
      share_id: _req['share_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/admin/update_task_example */
  AdminUpdateTaskExample(
    req: admin.UpdateTaskExampleRequest,
    options?: T,
  ): Promise<admin.UpdateTaskExampleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/admin/update_task_example');
    const method = 'POST';
    const data = {
      example_id: _req['example_id'],
      example_name: _req['example_name'],
      example_description: _req['example_description'],
      icon_uri: _req['icon_uri'],
      index: _req['index'],
      category_id: _req['category_id'],
      user_query: _req['user_query'],
      status: _req['status'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/coze_space/get_task_example_category */
  GetTaskExampleCategory(
    req?: task.GetTaskExampleCategoryListRequest,
    options?: T,
  ): Promise<task.GetTaskExampleCategoryListResponse> {
    const url = this.genBaseURL('/api/coze_space/get_task_example_category');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/coze_space/get_user_job_list */
  GetUserJobList(
    req?: user.GetUserJobListRequest,
    options?: T,
  ): Promise<user.GetUserJobListResponse> {
    const url = this.genBaseURL('/api/coze_space/get_user_job_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/admin/parse_task_share_url */
  AdminParseTaskShareURL(
    req: admin.ParseTaskShareURLRequest,
    options?: T,
  ): Promise<admin.ParseTaskShareURLResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/admin/parse_task_share_url');
    const method = 'POST';
    const data = { share_url: _req['share_url'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/coze_space/get_app_info */
  GetAppInfo(
    req?: mobile.GetAppInfoRequest,
    options?: T,
  ): Promise<mobile.GetAppInfoResponse> {
    const url = this.genBaseURL('/api/coze_space/get_app_info');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/coze_space/admin/get_task_example_category */
  AdminGetTaskExampleCategory(
    req?: admin.GetTaskExampleCategoryListRequest,
    options?: T,
  ): Promise<admin.GetTaskExampleCategoryListResponse> {
    const url = this.genBaseURL(
      '/api/coze_space/admin/get_task_example_category',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/coze_space/admin/upload_task_file */
  AdminUploadFile(
    req?: admin.UploadTaskFileRequest,
    options?: T,
  ): Promise<admin.UploadTaskFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/admin/upload_task_file');
    const method = 'POST';
    const data = {
      file_name: _req['file_name'],
      file_content: _req['file_content'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/update_file_content */
  UpdateFileContent(
    req?: task.UpdateFileContentRequest,
    options?: T,
  ): Promise<task.UpdateFileContentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/update_file_content');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      file_uri: _req['file_uri'],
      file_content: _req['file_content'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/export_ppt_agent_file
   *
   * ppt agent
   */
  ExportPPTAgentFile(
    req?: exper_agent.ExportPPTAgentFileRequest,
    options?: T,
  ): Promise<exper_agent.ExportPPTAgentFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/export_ppt_agent_file');
    const method = 'POST';
    const data = {
      source_uri: _req['source_uri'],
      task_id: _req['task_id'],
      export_type: _req['export_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/coze_space/restore_example_file
   *
   * 用来给前端转存文件的，需要把案例的文件转存到 task 的文件中去
   */
  RestoreExampleFile(
    req: task.RestoreExampleFileRequest,
    options?: T,
  ): Promise<task.RestoreExampleFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/restore_example_file');
    const method = 'POST';
    const data = { task_id: _req['task_id'], example_id: _req['example_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/get_html_height_fix */
  GetHtmlHeightFix(
    req?: exper_agent.GetHtmlHeightFixRequest,
    options?: T,
  ): Promise<exper_agent.GetHtmlHeightFixResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/get_html_height_fix');
    const method = 'POST';
    const data = { code: _req['code'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/operate_paused_task */
  OperatePausedTask(
    req?: task.OperatePausedTaskRequest,
    options?: T,
  ): Promise<task.OperatePausedTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/coze_space/operate_paused_task');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      pause_reason: _req['pause_reason'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/coze_space/search_image */
  SearchImage(
    req: resource.SearchImageRequest,
    options?: T,
  ): Promise<resource.SearchImageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/coze_space/search_image');
    const method = 'POST';
    const data = { query: _req['query'], count: _req['count'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
