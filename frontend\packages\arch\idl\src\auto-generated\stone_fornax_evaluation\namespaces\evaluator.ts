/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export enum BuiltinTemplateType {
  Prompt = 1,
  Code = 2,
}

export enum EvaluatorRunStatus {
  /** 运行状态, 异步下状态流转, 同步下只有 Success / Fail */
  Unknown = 0,
  Success = 1,
  Fail = 2,
}

export enum EvaluatorType {
  Prompt = 1,
  Code = 2,
}

export enum LanguageType {
  Python = 1,
  JS = 2,
}

export enum PromptSourceType {
  BuiltinTemplate = 1,
  FornaxPrompt = 2,
  Custom = 3,
}

export enum ToolType {
  Function = 1,
  /** for gemini native tool */
  GoogleSearch = 2,
}

export interface CodeEvaluator {
  language_type?: LanguageType;
  code?: string;
}

export interface Correction {
  /** 人工校准得分 */
  score?: number;
  /** 人工校准理由 */
  explain?: string;
  /** 修正人 */
  updated_by?: string;
}

export interface Evaluator {
  /** 评估器 id */
  evaluator_id?: Int64;
  /** 空间 id */
  space_id?: Int64;
  /** 评估器类型 */
  evaluator_type?: EvaluatorType;
  /** 展示用名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 草稿是否提交过 */
  draft_submitted?: boolean;
  base_info?: common.BaseInfo;
  /** 当前版本内容，可以是草稿版本 */
  current_version?: EvaluatorVersion;
  latest_version?: string;
}

export interface EvaluatorContent {
  /** 是否接收历史消息 */
  receive_chat_history?: boolean;
  /** 输入schema */
  input_schemas?: Array<common.ArgsSchema>;
  /** 101-200 Evaluator类型 */
  prompt_evaluator?: PromptEvaluator;
  code_evaluator?: CodeEvaluator;
}

export interface EvaluatorInputData {
  /** 历史会话记录 */
  history_messages?: Array<common.Message>;
  /** 变量 */
  input_fields?: Record<string, common.Content>;
}

export interface EvaluatorOutputData {
  /** 运行结果 */
  evaluator_result?: EvaluatorResult;
  /** 运行消耗 */
  evaluator_usage?: EvaluatorUsage;
  /** 运行报错 */
  evaluator_run_error?: EvaluatorRunError;
  /** 运行耗时 */
  time_consuming_ms?: Int64;
}

export interface EvaluatorRecord {
  /** 评估记录ID */
  id?: Int64;
  /** 新增实验ID字段 */
  experiment_id?: Int64;
  /** 实验ID */
  experiment_run_id?: Int64;
  /** 对话ID */
  item_id?: Int64;
  /** 对话ID */
  turn_id?: Int64;
  /** 评估器版本ID */
  evaluator_version_id?: Int64;
  /** 链路ID */
  trace_id?: string;
  /** 链路ID */
  log_id?: string;
  /** 输入数据 */
  evaluator_input_data?: EvaluatorInputData;
  /** 输出数据 */
  evaluator_output_data?: EvaluatorOutputData;
  status?: EvaluatorRunStatus;
  base_info?: common.BaseInfo;
}

export interface EvaluatorResult {
  /** 打分 */
  score?: number;
  /** 校准打分 */
  correction?: Correction;
  /** 推理过程 */
  reasoning?: string;
}

export interface EvaluatorRunError {
  code?: number;
  message?: string;
}

export interface EvaluatorUsage {
  input_tokens?: Int64;
  output_tokens?: Int64;
}

export interface EvaluatorVersion {
  /** 版本id */
  id?: Int64;
  /** 版本号 */
  version?: string;
  /** 版本描述 */
  description?: string;
  base_info?: common.BaseInfo;
  evaluator_content?: EvaluatorContent;
}

export interface Function {
  name?: string;
  description?: string;
  parameters?: string;
}

export interface PromptEvaluator {
  /** MessageList */
  message_list?: Array<common.Message>;
  model_config?: common.ModelConfig;
  prompt_source_type?: PromptSourceType;
  prompt_template_key?: string;
  prompt_template_name?: string;
  tools?: Array<ToolCombine>;
}

export interface Tool {
  type?: ToolType;
  function_def?: Function;
}

export interface ToolCombine {
  /** 工具定义 */
  tool_def?: Tool;
  /** mock 数据 */
  mock_response?: Array<string>;
  /** 是否禁用 */
  disable?: boolean;
}
/* eslint-enable */
