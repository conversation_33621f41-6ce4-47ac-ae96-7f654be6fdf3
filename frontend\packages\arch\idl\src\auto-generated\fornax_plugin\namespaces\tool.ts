/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as definition from './definition';

export type Int64 = string | number;

export enum HttpServiceAuthType {
  None = 1,
}

export enum ServiceType {
  FornaxHttp = 1,
  FornaxThrift = 2,
  /** 详情见  */
  SeedPluginPlatform = 3,
}

export interface HttpMethodInfo {
  path?: string;
  method?: string;
}

export interface HttpServiceInfo {
  base_u_r_l?: string;
  auth_type?: HttpServiceAuthType;
  headers?: Record<string, string>;
}

/** Plugin 表示注册在Fornax上插件，围绕大模型应用构建的插件体系。一个Plugin可以认为是一个Tool Group。 */
export interface Plugin {
  id?: Int64;
  name?: string;
  desc?: string;
  /** Plugin Service Related Info */
  service_type?: ServiceType;
  thrift_service_info?: ThriftServiceInfo;
  http_service_info?: HttpServiceInfo;
}

export interface SeedPluginPlatformToolInfo {
  plugin_name?: string;
  plugin_id?: string;
  tool_name?: string;
  plugin_info?: string;
  template_info?: string;
}

export interface ThriftMethodInfo {
  p_s_m?: string;
  cluster?: string;
  method?: string;
}

export interface ThriftServiceInfo {
  p_s_m: string;
}

export interface Tool {
  id?: Int64;
  name?: string;
  desc?: string;
  plugin_id?: Int64;
  request_definition?: definition.Definition;
  response_definition?: definition.Definition;
  service_type?: ServiceType;
  thrift_service_info?: ThriftServiceInfo;
  thrift_method_info?: ThriftMethodInfo;
  http_method_info?: HttpMethodInfo;
  http_service_info?: HttpServiceInfo;
  seed_plugin_platform_tool_info?: SeedPluginPlatformToolInfo;
}

export interface ToolPayload {
  payload?: string;
}
/* eslint-enable */
