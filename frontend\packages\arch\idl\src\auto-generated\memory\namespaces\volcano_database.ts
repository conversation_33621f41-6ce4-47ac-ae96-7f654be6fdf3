/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum InstanceStatus {
  Creating = 1,
  Running = 2,
  Deleting = 3,
  Restarting = 4,
  Updating = 5,
  Restoring = 6,
  Error = 7,
  Upgrading = 8,
  Released = 9,
  Recycled = 10,
  MasterChanging = 11,
  TDEUpdating = 12,
  Closed = 13,
  Maintaining = 14,
  CreateFailed = 15,
  Closing = 16,
  SSLUpdating = 17,
  AllowListMaintaining = 18,
  Unknown = 19,
  ProxyCreating = 20,
  ProxyDeleting = 21,
  ChargeTypeChanging = 22,
  NetWorkChanging = 23,
}

export enum TableRightStatus {
  /** 有权限 */
  HasRight = 1,
  /** 无权限 */
  HasNoRight = 2,
}

/** 火山数据库状态（Coze的火山数据库其实就是一张火山数据表，这里本质就是表的状态） */
export enum VolcanoDatabaseStatus {
  /** 生效 */
  Valid = 1,
  /** 失效 */
  Invalid = 2,
}

export interface BatchAssociateResult {
  /** 保存table_name和database id的映射 */
  table_map?: Record<string, string>;
}

export interface ConnectResult {
  is_pass?: boolean;
  fail_reason?: string;
}

export interface DatabaseDetail {
  database_name?: string;
}

export interface DatabaseDetailList {
  database_list?: Array<DatabaseDetail>;
  total?: Int64;
  has_more?: boolean;
}

export interface MysqlInstanceDetail {
  instance_name?: string;
  instance_id?: string;
  status?: InstanceStatus;
}

export interface MysqlInstanceDetailList {
  instance_detailes?: Array<MysqlInstanceDetail>;
  total?: Int64;
  has_more?: boolean;
}

export interface RegionDetail {
  /** 区域id */
  region_id?: string;
  /** 区域名称 */
  region_name?: string;
}

export interface TableDetail {
  table_name?: string;
  /** 是否已经被关联 */
  has_associate?: boolean;
}

export interface TableDetailList {
  table_list?: Array<TableDetail>;
  total?: Int64;
  has_more?: boolean;
}

export interface TableRightDetail {
  table_name?: string;
  database_id?: string;
  origin_user_right?: TableRightStatus;
  trans_user_right?: TableRightStatus;
}

export interface TableRightDetailList {
  user_rights?: Array<TableRightDetail>;
  total?: Int64;
}

export interface VolcaDatabaseBatchAssociateRequest {
  space_id?: string;
  region_id?: string;
  database_bind_info?: VolcanoDatabaseBindInfo;
  /** 火山创建的table名称列表 */
  volcano_table_list?: Array<string>;
  /** project id */
  project_id?: string;
  Base?: base.Base;
}

export interface VolcaDatabaseBatchAssociateResponse {
  data?: BatchAssociateResult;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseChangeTableRightRequest {
  space_id?: string;
  /** 这里只需要传入 user_name 和 user_pwd 就可以了 */
  connect_info?: VolcanoDatabaseConnectInfo;
  /** 转移权限的id列表（online id） */
  database_ids?: Array<string>;
  Base?: base.Base;
}

export interface VolcaDatabaseChangeTableRightResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseConnectCheckRequest {
  space_id?: string;
  region_id?: string;
  connect_info?: VolcanoDatabaseConnectInfo;
  Base?: base.Base;
}

export interface VolcaDatabaseConnectCheckResponse {
  data?: ConnectResult;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseGetTableRightRequest {
  space_id?: string;
  database_id?: string;
  /** 这里只需要传入 user_name 和 user_pwd 就可以了 */
  connect_info?: VolcanoDatabaseConnectInfo;
  Base?: base.Base;
}

export interface VolcaDatabaseGetTableRightResponse {
  data?: TableRightDetailList;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseListDatabaseRequest {
  space_id: string;
  /** 区域id */
  region_id: string;
  connect_info?: VolcanoDatabaseConnectInfo;
  page_num?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface VolcaDatabaseListDatabaseResponse {
  data?: DatabaseDetailList;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseListInstanceRequest {
  space_id: string;
  project_name: string;
  /** 区域id */
  region_id: string;
  page_num?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface VolcaDatabaseListInstanceResponse {
  data?: MysqlInstanceDetailList;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseListProjectRequest {
  space_id: string;
  Base?: base.Base;
}

export interface VolcaDatabaseListProjectResponse {
  data?: VolcanoProjectList;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcaDatabaseListTableRequest {
  space_id: string;
  /** 区域id */
  region_id?: string;
  /** 传入user_name、user_pwd、instance_id */
  database_bind_info?: VolcanoDatabaseBindInfo;
  page_num?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface VolcaDatabaseListTableResponse {
  data?: TableDetailList;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface VolcanoDatabaseBindInfo {
  connect_info?: VolcanoDatabaseConnectInfo;
  db_name?: string;
}

export interface VolcanoDatabaseConnectInfo {
  user_name?: string;
  user_pwd?: string;
  instance_id?: string;
}

/** 火山数据库详情 */
export interface VolcanoDatabaseDetail {
  mysql_instance_detail?: MysqlInstanceDetail;
  /** 火山数据库名 */
  volcano_db_name?: string;
  /** 火山数据表名 */
  volcano_table_name?: string;
  /** 火山数据库详情连接 */
  link?: string;
  /** 火山数据库状态 */
  status?: VolcanoDatabaseStatus;
}

export interface VolcanoProject {
  display_name?: string;
  description?: string;
  region_list?: Array<RegionDetail>;
}

export interface VolcanoProjectList {
  volcano_project_list?: Array<VolcanoProject>;
}

export interface VolcanoStorageConfig {
  volcano_database_config?: VolcanoDatabaseDetail;
}
/* eslint-enable */
