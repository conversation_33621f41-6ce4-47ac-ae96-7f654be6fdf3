/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as product_common from './product_common';
import * as marketplace_common from './marketplace_common';

export type Int64 = string | number;

/** 订单状态 */
export enum OrderOp {
  Unknown = 0,
  /** 超时关单 */
  Close = 1,
}

export interface CreateChargeOrder {
  /** coze交易单号 */
  order_id?: string;
  /** checkout_url */
  pay_params?: string;
}

export interface CreateChargeOrderRequest {
  UserID?: string;
  /** 支付的币种 */
  currency_code?: string;
  /** 通常为 sku_id。固定面额时填充 */
  goods_id?: string;
  /** 购买数量 */
  quantity?: number;
  /** extra 信息 */
  extra?: string;
  /** Coze用户 传common文件里的 ConnectorIDBotStudio */
  UserType?: Int64;
  Cookie?: string;
  'Tt-Agw-Client-Ip'?: string;
}

export interface CreateChargeOrderResponse {
  data?: CreateChargeOrder;
  code: number;
  message: string;
}

export interface GetTradeOrder {
  order_id?: string;
  scene?: common.Scene;
  status?: common.OrderStatus;
  order_type?: common.OrderType;
  pay_status?: common.PayStatus;
}

export interface GetTradeOrderRequest {
  order_id: string;
  /** 是否实时查询支付侧的支付状态（适用于未收到支付回调时） */
  real_time_pay_status?: boolean;
}

export interface GetTradeOrderResponse {
  data?: GetTradeOrder;
  code: number;
  message: string;
}

export interface Order {
  OrderID?: string;
  UserType?: Int64;
  UserID?: string;
  Scene?: common.Scene;
  OrderStatus?: common.OrderStatus;
  OrderType?: common.OrderType;
  TotalAmount?: Int64;
  RealAmount?: Int64;
  Currency?: string;
  BizContent?: string;
  OrderReverseStatus?: common.OrderReverseStatus;
  PayStatus?: common.PayStatus;
  CreatedAt?: Int64;
}

export interface PublicCreateChargeOrderRequest {
  /** 支付的币种 */
  currency_code?: string;
  /** 通常为 sku_id。固定面额时填充 */
  goods_id?: string;
  /** 购买数量 */
  quantity?: number;
  /** extra 信息 */
  extra?: string;
  /** 充值场景 */
  charge_scene?: common.ChargeScene;
  Cookie?: string;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PublicCreateChargeOrderResponse {
  data?: CreateChargeOrder;
  code: number;
  message: string;
}

export interface PublicGetTemplatePurchaseOrderListRequest {
  /** 第一次不用传 */
  index?: string;
  /** 每页数量 */
  count?: Int64;
}

export interface PublicGetTemplatePurchaseOrderListResponse {
  code?: number;
  message?: string;
  data?: TemplatePurchaseOrderData;
}

export interface PublicPlaceOrderRequest {
  /** 通常为 sku_id。 */
  goods_id?: string;
  /** 购买数量 */
  quantity?: number;
  /** 模版购买填 PurchaseTemplate */
  scene?: common.Scene;
  /** 模版购买后的跳转链接 */
  return_url?: string;
  /** 传 product id */
  product_id?: string;
  Cookie?: string;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PublicPlaceOrderResponse {
  data?: CreateChargeOrder;
  code: number;
  message: string;
}

export interface RefundOrder {
  RefundOrderID?: string;
  UserType?: Int64;
  UserID?: string;
  OrderType?: common.OrderType;
  RefundStatus?: common.RefundStatus;
  RefundAmount?: Int64;
  RefundCurrency?: string;
  BizContent?: RefundOrderBizContent;
}

export interface RefundOrderBizContent {
  /** 退款原因 */
  Reason?: string;
}

export interface TemplatePurchaseOrder {
  id?: string;
  order_id?: string;
  product_icon?: string;
  product_name?: string;
  product_desc?: string;
  order_type?: common.OrderType;
  amount?: string;
  currency?: string;
  product_type?: product_common.ProductEntityType;
  place_order_user_role?: marketplace_common.UserRole;
  /** 单位：秒 */
  purchase_timestamp?: string;
  /** 商品 id */
  product_id?: string;
}

export interface TemplatePurchaseOrderData {
  order_list?: Array<TemplatePurchaseOrder>;
  /** 是否还有下一页 */
  has_more?: boolean;
  /** 下次请求的分页 id */
  next_index?: string;
}
/* eslint-enable */
