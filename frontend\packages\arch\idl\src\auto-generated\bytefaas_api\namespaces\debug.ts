/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

/** Debug execution data message */
export interface DataMessage114 {
  /** Additional debug execution data */
  additional_data?: DataMessage114AdditionalDataMessage2;
  /** Main debug data payload */
  data?: string;
}

/** Additional debug execution data */
export interface DataMessage114AdditionalDataMessage2 {
  /** CPU usage during debug execution */
  cpuUsage?: string;
  /** Duration of debug execution */
  executionDuration?: string;
  /** Memory usage during debug execution */
  memoryUsage?: string;
  /** Request payload sent for debugging */
  request?: string;
  /** Response payload from debugging */
  response?: string;
}

export interface DebugFunctionRequest {
  /** Enable batch debug mode */
  batch?: boolean;
  /** Name of the cluster */
  cluster: string;
  /** Input data for debugging */
  data?: string;
  /** Additional debug extensions */
  extensions?: Record<string, common.EmptyObject>;
  /** Name of the region */
  region: string;
  /** Service ID to debug */
  service_id: string;
  /** Debug type */
  type?: string;
  /** Enable verbose debug output */
  verbose?: boolean;
  /** Event name for debugging */
  event_name?: string;
}

export interface DebugFunctionResponse {
  code?: number;
  data?: DataMessage114;
  error?: string;
}
/* eslint-enable */
