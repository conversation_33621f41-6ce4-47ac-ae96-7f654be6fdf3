/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/* eslint-disable */
/* prettier-ignore */
// 基于src/index.ts自动生成，请勿手动修改
declare const APP_ID: number;
declare const APP_KEY: string;
declare const AWEME_ORIGIN: string;
declare const AWEME_PLATFORM_APP_KEY: string;
declare const AWEME_PLATFORM_ID: number;
declare const BOT_BRAND_NAME: string;
declare const BUILD_BRANCH: string;
declare const BUILD_TYPE: 'local' | 'online' | 'offline' | 'test';
declare const BYTE_UPLOADER_REGION: 'cn-north-1' | 'us-east-1' | 'ap-singapore-1' | 'us-east-red' | 'boe' | 'boei18n' | 'US-TTP' | 'gcp';
declare const CARD_BUILDER_ENV_STR: string;
declare const CDN: string | undefined;
declare const CDN_PATH_PREFIX: string;
declare const CN_PRIVATE_POLICY: string;
declare const CN_TERMS_OF_SERVICE: string;
declare const CONSUMER_BUILD_VERSION: string;
declare const COZE_API_TTS_BASE_URL: string;
declare const COZE_DOMAIN: string | null;
declare const COZE_FEISHU_APP: string;
declare const COZE_LARK_APP: string;
declare const CUSTOM_PLAT_APPLY_PUBLIC_PLAT_FORM_LINK: string;
declare const CUSTOM_VERSION: 'inhouse' | 'release';
declare const DISCORD_PUBLISH_ID: string;
declare const DOMAIN_RELEASE_CN: string;
declare const DOMAIN_RELEASE_OVERSEA: string;
declare const EMBEDDED_PAGE_URL: string;
declare const FACEBOOK_APP_ID: string | null;
declare const FEATURE_AWEME_LOGIN: boolean;
declare const FEATURE_ENABLE_APP_GUIDE: boolean;
declare const FEATURE_ENABLE_BANNER: boolean;
declare const FEATURE_ENABLE_BOT_STORE: boolean;
declare const FEATURE_ENABLE_CODE_PYTHON: boolean;
declare const FEATURE_ENABLE_DATABASE_TABLE: boolean;
declare const FEATURE_ENABLE_FEEDBACK_MAILTO: boolean;
declare const FEATURE_ENABLE_MSG_DEBUG: boolean;
declare const FEATURE_ENABLE_NEW_DELETE_ACCOUNT: boolean;
declare const FEATURE_ENABLE_QUERY_ENTRY: boolean;
declare const FEATURE_ENABLE_SSO: boolean;
declare const FEATURE_ENABLE_TABLE_MEMORY: boolean;
declare const FEATURE_ENABLE_TABLE_VARIABLE: boolean;
declare const FEATURE_ENABLE_TCS: boolean;
declare const FEATURE_ENABLE_TEA_UG: boolean;
declare const FEATURE_ENABLE_VARIABLE: boolean;
declare const FEATURE_ENABLE_WORKFLOW_LLM_PAYMENT: boolean;
declare const FEATURE_GOOGLE_LOGIN: boolean;
declare const FEEL_GOOD_HOST: string;
declare const FEEL_GOOD_PID: '';
declare const FEISHU_PUBLISH_ID: string;
declare const FLOW_BRAND_NAME: string;
declare const FLOW_PUBLISH_ID: string;
declare const FORNAX_DOMAIN: string | null;
declare const GOOGLE_CLIENT_ID: string | null;
declare const GOOGLE_PLATFORM_ID: number | null;
declare const IMAGE_FALLBACK_HOST: string;
declare const IS_BOE: boolean;
declare const IS_BOT_OP: boolean;
declare const IS_CN_REGION: boolean;
declare const IS_DEV_MODE: boolean;
declare const IS_OPEN_SOURCE: boolean;
declare const IS_OVERSEA: boolean;
declare const IS_OVERSEA_RELEASE: boolean;
declare const IS_PROD: boolean;
declare const IS_RELEASE_VERSION: boolean;
declare const IS_VA_REGION: boolean;
declare const JUEJIN_PUBLISH_ID: string;
declare const LARK_PUBLISH_ID: string;
declare const MONACO_EDITOR_PUBLIC_PATH: string;
declare const MYAI_PUBLISH_ID: string;
declare const NODE_ENV: 'test' | 'production' | 'development';
declare const OBRIC_PUBLISH_ID: string;
declare const OPEN_DOCS_APP_ID: string;
declare const OPEN_DOCS_LIB_ID: string;
declare const OPEN_WEB_SDK_BOT_ID: string;
declare const OUTER_CDN: string | undefined;
declare const PLUGIN_IDE_EDITION: string;
declare const PRIVATE_POLICY: string;
declare const REDDIT_PUBLISH_ID: string;
declare const REGION: 'cn' | 'sg' | 'va';
declare const SAMI_APP_KEY: string;
declare const SAMI_CHAT_WS_URL: string;
declare const SAMI_WS_ORIGIN: string;
declare const SEC_SDK_ASSERT_URL: string | null;
declare const SOCIAL_SCENE_FRONTIER_SERVICE_KEY: number;
declare const TERMS_OF_SERVICE: string;
declare const UPLOAD_CDN: string;
declare const UPLOAD_CDN_CN: string;
declare const UPLOAD_CDN_SG: string;
declare const UPLOAD_CDN_VA: string;
declare const VOLCANO_IDENTITY_DOMAIN: string | null;
declare const VOLCANO_PLATFORM_APP_KEY: string | null;
declare const VOLCANO_PLATFORM_ID: number | null;
declare const VOLC_PRIVATE_POLICY: string;
declare const VOLC_TERMS_OF_SERVICE: string;
