/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"math/rand"
	"time"
	"unicode/utf8"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	developer_api "github.com/coze-dev/coze-studio/backend/api/model/ocean/cloud/developer_api"
	"github.com/coze-dev/coze-studio/backend/application/base/ctxutil"
	"github.com/coze-dev/coze-studio/backend/application/modelmgr"
	"github.com/coze-dev/coze-studio/backend/application/singleagent"
	application "github.com/coze-dev/coze-studio/backend/application/singleagent"
	"github.com/coze-dev/coze-studio/backend/application/upload"
	"github.com/coze-dev/coze-studio/backend/application/user"
	"github.com/coze-dev/coze-studio/backend/pkg/errorx"
	"github.com/coze-dev/coze-studio/backend/pkg/lang/ptr"
	"github.com/coze-dev/coze-studio/backend/types/errno"
)

// DraftBotCreate .
// @router /api/draftbot/create [POST]
func DraftBotCreate(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.DraftBotCreateRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	if req.SpaceID <= 0 {
		invalidParamRequestResponse(c, "space id is not set")
		return
	}

	if req.Name == "" {
		invalidParamRequestResponse(c, "name is nil")
		return
	}

	if req.IconURI == "" {
		invalidParamRequestResponse(c, "icon uri is nil")
		return
	}

	if utf8.RuneCountInString(req.Name) > 50 {
		invalidParamRequestResponse(c, "name is too long")
		return
	}

	if utf8.RuneCountInString(req.Description) > 2000 {
		invalidParamRequestResponse(c, "description is too long")
		return
	}

	resp, err := application.SingleAgentSVC.CreateSingleAgentDraft(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DeleteDraftBot .
// @router /api/draftbot/delete [POST]
func DeleteDraftBot(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.DeleteDraftBotRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := application.SingleAgentSVC.DeleteAgentDraft(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateDraftBotDisplayInfo .
// @router /api/draftbot/update_display_info [POST]
func UpdateDraftBotDisplayInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.UpdateDraftBotDisplayInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := application.SingleAgentSVC.UpdateAgentDraftDisplayInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DuplicateDraftBot .
// @router /api/draftbot/duplicate [POST]
func DuplicateDraftBot(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.DuplicateDraftBotRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := application.SingleAgentSVC.DuplicateDraftBot(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDraftBotDisplayInfo .
// @router /api/draftbot/get_display_info [POST]
func GetDraftBotDisplayInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.GetDraftBotDisplayInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := application.SingleAgentSVC.GetAgentDraftDisplayInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// PublishDraftBot .
// @router /api/draftbot/publish [POST]
func PublishDraftBot(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.PublishDraftBotRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if len(req.Connectors) == 0 {
		invalidParamRequestResponse(c, "connectors is nil")
		return
	}

	resp, err := application.SingleAgentSVC.PublishAgent(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// ListDraftBotHistory .
// @router /api/draftbot/list_draft_history [POST]
func ListDraftBotHistory(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.ListDraftBotHistoryRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.BotID == 0 {
		invalidParamRequestResponse(c, "bot id is not set")
		return
	}

	if req.PageIndex <= 0 {
		req.PageIndex = 1
	}

	if req.PageSize <= 0 {
		req.PageSize = 30
	}

	resp, err := application.SingleAgentSVC.ListAgentPublishHistory(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetIcon .
// @router /api/developer/get_icon [POST]
func GetIcon(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.GetIconRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	resp, err := upload.SVC.GetIcon(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetUploadAuthToken .
// @router /api/playground/upload/auth_token [POST]
func GetUploadAuthToken(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.GetUploadAuthTokenRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := application.SingleAgentSVC.GetUploadAuthToken(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

func createSecret(uid int64, fileType string) string {
	num := 10
	input := fmt.Sprintf("upload_%d_Ma*9)fhi_%d_gou_%s_rand_%d", uid, time.Now().Unix(), fileType, rand.Intn(100000))
	// 做md5，取前20个,// mapIntToBase62 把数字映射到 Base62
	hash := sha256.Sum256([]byte(fmt.Sprintf("%s", input)))
	hashString := base64.StdEncoding.EncodeToString(hash[:])
	if len(hashString) > num {
		hashString = hashString[:num]
	}

	result := ""
	for _, char := range hashString {
		index := int(char) % 62
		result += string(baseWord[index])
	}
	return result
}

// UploadFile .
// @router /api/bot/upload_file [POST]
func UploadFile(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.UploadFileRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(developer_api.UploadFileResponse)
	fileContent, err := base64.StdEncoding.DecodeString(req.Data)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}
	userID := ctxutil.GetUIDFromCtx(ctx)
	if userID == nil {
		internalServerErrorResponse(ctx, c, errorx.New(errno.ErrUploadPermissionCode, errorx.KV("msg", "session required")))
		return
	}
	secret := createSecret(ptr.From(userID), req.FileHead.FileType)
	fileName := fmt.Sprintf("%d_%d_%s.%s", ptr.From(userID), time.Now().UnixNano(), secret, req.FileHead.FileType)
	objectName := fmt.Sprintf("%s/%s", req.FileHead.BizType.String(), fileName)
	resp, err = upload.SVC.UploadFile(ctx, fileContent, objectName)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

const baseWord = "1Aa2Bb3Cc4Dd5Ee6Ff7Gg8Hh9Ii0JjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz"

// GetOnboarding .
// @router /api/playground/get_onboarding [POST]
func GetOnboarding(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.GetOnboardingRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(developer_api.GetOnboardingResponse)

	c.JSON(consts.StatusOK, resp)
}

// PublishConnectorList .
// @router /api/draftbot/publish/connector/list [POST]
func PublishConnectorList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.PublishConnectorListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.BotID == 0 {
		invalidParamRequestResponse(c, "bot id is not set")
		return
	}

	resp, err := singleagent.SingleAgentSVC.GetPublishConnectorList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// CheckDraftBotCommit .
// @router /api/draftbot/commit_check [POST]
func CheckDraftBotCommit(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.CheckDraftBotCommitRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return

	}
	resp := new(developer_api.CheckDraftBotCommitResponse)
	c.JSON(consts.StatusOK, resp)
}

// UpdateUserProfileCheck .
// @router /api/user/update_profile_check [POST]
func UpdateUserProfileCheck(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.UpdateUserProfileCheckRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := user.UserApplicationSVC.UpdateUserProfileCheck(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetTypeList .
// @router /api/bot/get_type_list [POST]
func GetTypeList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req developer_api.GetTypeListRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := modelmgr.ModelmgrApplicationSVC.GetModelList(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
