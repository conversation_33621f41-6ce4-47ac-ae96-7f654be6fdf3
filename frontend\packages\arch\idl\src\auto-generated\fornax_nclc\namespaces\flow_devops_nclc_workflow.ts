/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum NodeExecutionStatus {
  NotStarted = 0,
  Executing = 1,
  Completed = 2,
  Error = 3,
}

export enum WorkflowExecutionStatus {
  NotStarted = 0,
  Executing = 1,
  Completed = 2,
  Error = 3,
}

export interface CreateFlowRequest {
  flow: Workflow;
  'FlowDevops-Agw-UserId'?: string;
  base?: base.Base;
}

export interface CreateFlowResponse {
  /** 如果生成成功，会返回服务端生成的 flow ID */
  flow_id?: string;
  base_resp?: base.BaseResp;
}

export interface DeleteFlowRequest {
  flow_id: string;
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  base?: base.Base;
}

export interface DeleteFlowResponse {
  base_resp?: base.BaseResp;
}

export interface ExportRequest {
  flow_id: string;
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  data: string;
  base?: base.Base;
}

export interface ExportResponse {
  compressed_file?: string;
  base_resp?: base.BaseResp;
}

export interface ListFlowsRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  base?: base.Base;
}

export interface ListFlowsResponse {
  flows?: Array<Workflow>;
  base_resp?: base.BaseResp;
}

export interface QueryFlowRequest {
  flow_id: string;
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  base?: base.Base;
}

export interface QueryFlowResponse {
  flow?: Workflow;
  base_resp?: base.BaseResp;
}

export interface TestRunRequest {
  flow_id: string;
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  data: string;
  /** key 是 node ID，value 是该 node 的 input 的 json 序列化结果 */
  inputs: Record<string, string>;
  base?: base.Base;
}

export interface TestRunResponse {
  /** test run 的 ID */
  id?: string;
  log_id?: string;
  /** 如执行成功，这里是最终 output 的 json 序列化结果 */
  result?: string;
  base_resp?: base.BaseResp;
}

export interface UpdateFlowMetaRequest {
  flow_id: string;
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  meta: WorkflowMeta;
  base?: base.Base;
}

export interface UpdateFlowMetaResponse {
  base_resp?: base.BaseResp;
}

export interface UpdateFlowRequest {
  flow_id: string;
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  data: string;
  base?: base.Base;
}

export interface UpdateFlowResponse {
  base_resp?: base.BaseResp;
}

export interface Workflow {
  id?: string;
  space_id?: string;
  creator?: string;
  meta?: WorkflowMeta;
  /** 包含配置数据的一个 workflow 的描述，前端决定格式，服务端理解 */
  data?: string;
}

export interface WorkflowMeta {
  name?: string;
  description?: string;
  icon?: string;
}
/* eslint-enable */
