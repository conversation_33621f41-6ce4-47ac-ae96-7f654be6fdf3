/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agw_common from './namespaces/agw_common';
import * as agw_common_param from './namespaces/agw_common_param';
import * as base from './namespaces/base';
import * as common from './namespaces/common';
import * as field_filter from './namespaces/field_filter';
import * as ob_data from './namespaces/ob_data';
import * as ob_query from './namespaces/ob_query';
import * as ob_span from './namespaces/ob_span';
import * as ob_trace from './namespaces/ob_trace';
import * as observe_query from './namespaces/observe_query';
import * as observe_trace from './namespaces/observe_trace';
import * as performance from './namespaces/performance';
import * as queries from './namespaces/queries';

export {
  agw_common,
  agw_common_param,
  base,
  common,
  field_filter,
  ob_data,
  ob_query,
  ob_span,
  ob_trace,
  observe_query,
  observe_trace,
  performance,
  queries,
};
export * from './namespaces/agw_common';
export * from './namespaces/agw_common_param';
export * from './namespaces/base';
export * from './namespaces/common';
export * from './namespaces/field_filter';
export * from './namespaces/ob_data';
export * from './namespaces/ob_query';
export * from './namespaces/ob_span';
export * from './namespaces/ob_trace';
export * from './namespaces/observe_query';
export * from './namespaces/observe_trace';
export * from './namespaces/performance';
export * from './namespaces/queries';

export type Int64 = string | number;

export default class ObDataService<T> {
  private request: any = () => {
    throw new Error('ObDataService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/devops/ob_data/query */
  APIQuery(
    req: ob_data.APIQueryRequest,
    options?: T,
  ): Promise<ob_data.APIQueryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/query');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      metrics_type: _req['metrics_type'],
      time_type: _req['time_type'],
      time_range: _req['time_range'],
      filter: _req['filter'],
      criterias: _req['criterias'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/devops/ob_data/evaluation/support-channel */
  EvalSupportChannel(
    req: ob_data.EvalSupportChannelRequest,
    options?: T,
  ): Promise<ob_data.EvalSupportChannelResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_data/evaluation/support-channel',
    );
    const method = 'GET';
    const params = { bot_id: _req['bot_id'], space_id: _req['space_id'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/devops/ob_data/evaluation/user-intent-satisfaction */
  UserIntentSatisfactionRanking(
    req: ob_data.UserIntentSatisfactionRankingRequest,
    options?: T,
  ): Promise<ob_data.UserIntentSatisfactionRankingResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_data/evaluation/user-intent-satisfaction',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      time_range: _req['time_range'],
      label_depth: _req['label_depth'],
      connector_ids: _req['connector_ids'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/evaluation/bot-satisfaction */
  BotSatisfaction(
    req: ob_data.BotSatisfactionRequest,
    options?: T,
  ): Promise<ob_data.BotSatisfactionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_data/evaluation/bot-satisfaction',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      time_range: _req['time_range'],
      connector_ids: _req['connector_ids'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/batch_query */
  BatchAPIQuery(
    req: ob_data.BatchAPIQueryRequest,
    options?: T,
  ): Promise<ob_data.BatchAPIQueryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/batch_query');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      metrics_types: _req['metrics_types'],
      time_range: _req['time_range'],
      data_type: _req['data_type'],
      filter: _req['filter'],
      group_type: _req['group_type'],
      period_type: _req['period_type'],
      time_range_type: _req['time_range_type'],
      is_realtime: _req['is_realtime'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/queries/data */
  GetQueriesData(
    req: ob_data.GetQueriesDataRequest,
    options?: T,
  ): Promise<ob_data.GetQueriesDataResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/queries/data');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      time_range: _req['time_range'],
      filters: _req['filters'],
      order_by: _req['order_by'],
      sort_order: _req['sort_order'],
      page_token: _req['page_token'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/fg */
  GetBotFg(
    req: ob_data.GetBotFgRequest,
    options?: T,
  ): Promise<ob_data.GetBotFgResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/fg');
    const method = 'POST';
    const data = { space_id: _req['space_id'], bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/queries/field_metas */
  GetQueriesFieldMetas(
    req: ob_data.GetQueriesFieldMetasRequest,
    options?: T,
  ): Promise<ob_data.GetQueriesFieldMetasResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/queries/field_metas');
    const method = 'POST';
    const data = { space_id: _req['space_id'], bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_data/bot/pre_check_add_auth
   *
   * bot分析页授权相关接口（地理位置授权等）
   */
  PreCheckAddBotAuth(
    req: ob_data.PreCheckAddBotAuthReq,
    options?: T,
  ): Promise<ob_data.PreCheckAddBotAuthResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/bot/pre_check_add_auth');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      auth_type: _req['auth_type'],
      agw_common_param: _req['agw_common_param'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/bot/add_auth */
  AddBotAuth(
    req: ob_data.AddBotAuthReq,
    options?: T,
  ): Promise<ob_data.AddBotAuthResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/bot/add_auth');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      auth_type: _req['auth_type'],
      agw_common_param: _req['agw_common_param'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/bot/delete_auth */
  DeleteBotAuth(
    req: ob_data.DeleteBotAuthReq,
    options?: T,
  ): Promise<ob_data.DeleteBotAuthResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/bot/delete_auth');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], auth_type: _req['auth_type'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/ob_data/bot/view_auth */
  ViewBotAuth(
    req: ob_data.ViewBotAuthReq,
    options?: T,
  ): Promise<ob_data.ViewBotAuthResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_data/bot/view_auth');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/metrics
   *
   * bot性能页面相关接口
   *
   * 获取 metrics 详情
   */
  GetMetrics(
    req: performance.GetMetricsRequest,
    options?: T,
  ): Promise<performance.GetMetricsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/v1/metrics');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      metrics_type: _req['metrics_type'],
      aggregate_type: _req['aggregate_type'],
      tag_kvs: _req['tag_kvs'],
      top_k: _req['top_k'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/metrics/tag_v/historical_options
   *
   * 获取运维指标维度的值的历史选项
   */
  GetTagVHistoricalOptions(
    req: performance.GetTagVHistoricalOptionsRequest,
    options?: T,
  ): Promise<performance.GetTagVHistoricalOptionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_query/v1/metrics/tag_v/historical_options',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      tag_k: _req['tag_k'],
      choices: _req['choices'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      metrics_type: _req['metrics_type'],
      top_k: _req['top_k'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/metrics/tag_v/latest_options
   *
   * 获取运维指标维度的值的最新选项
   */
  GetTagVLatestOptions(
    req: performance.GetTagVLatestOptionsRequest,
    options?: T,
  ): Promise<performance.GetTagVLatestOptionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_query/v1/metrics/tag_v/latest_options',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      tag_k: _req['tag_k'],
      choices: _req['choices'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/list_debug_queries
   *
   * 调试台trace List
   */
  ListDebugQueries(
    req: ob_trace.ListDebugQueriesRequest,
    options?: T,
  ): Promise<ob_trace.ListDebugQueriesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/list_debug_queries');
    const method = 'GET';
    const params = {
      startAtMS: _req['startAtMS'],
      endAtMS: _req['endAtMS'],
      spaceID: _req['spaceID'],
      botID: _req['botID'],
      status: _req['status'],
      inputSearch: _req['inputSearch'],
      limit: _req['limit'],
      pageToken: _req['pageToken'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/devops/ob_query/query/enums
   *
   * 获取 query 枚举选项
   */
  GetQueryEnums(
    req: ob_query.GetQueryEnumsRequest,
    options?: T,
  ): Promise<ob_query.GetQueryEnumsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/enums');
    const method = 'GET';
    const params = { space_id: _req['space_id'], bot_id: _req['bot_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/v1/traces/batch_get_advance_info
   *
   * 批量查询链路进阶信息
   */
  BatchGetTracesAdvanceInfo(
    req: ob_trace.BatchGetTracesAdvanceInfoRequest,
    options?: T,
  ): Promise<ob_trace.BatchGetTracesAdvanceInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/ob_query/v1/traces/batch_get_advance_info',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      traces: _req['traces'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/query/list
   *
   * bot query相关接口
   *
   * 查询 query 列表
   */
  ListQuery(
    req: ob_query.ListQueryRequest,
    options?: T,
  ): Promise<ob_query.ListQueryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      query_filter: _req['query_filter'],
      limit: _req['limit'],
      page_token: _req['page_token'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/ob_query/query/stat
   *
   * query 的统计指标
   */
  GetQueryStat(
    req: ob_query.GetQueryStatRequest,
    options?: T,
  ): Promise<ob_query.GetQueryStatResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/stat');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      query_filter: _req['query_filter'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/v1/trace/by_log_id
   *
   * 通过log ID查询链路详情
   */
  GetTraceByLogID(
    req: ob_trace.GetTraceByLogIDRequest,
    options?: T,
  ): Promise<ob_trace.GetTraceByLogIDResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/v1/trace/by_log_id');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      log_id: _req['log_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/ob_query/query/export_to_csv
   *
   * 将 query 导出为csv
   */
  ExportQueryToCsv(
    req: ob_query.ExportQueryToCsvRequest,
    options?: T,
  ): Promise<ob_query.ExportQueryToCsvResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/ob_query/query/export_to_csv');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      query_filter: _req['query_filter'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/ob_query/v1/trace/:trace_id
   *
   * bot trace相关接口
   *
   * 通过trace ID查询链路详情
   */
  GetTrace(
    req: ob_trace.GetTraceRequest,
    options?: T,
  ): Promise<ob_trace.GetTraceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/ob_query/v1/trace/${_req['trace_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/devops/ob_query/v1/traces/meta_info
   *
   * 查询元信息，包括字段类型，filter opertor
   */
  GetTracesMetaInfo(
    req?: ob_trace.GetTracesMetaInfoRequest,
    options?: T,
  ): Promise<ob_trace.GetTracesMetaInfoResponse> {
    const url = this.genBaseURL('/api/devops/ob_query/v1/traces/meta_info');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/observe/query/stat
   *
   * query 的统计指标
   */
  GetQueryStatV2(
    req: observe_query.GetQueryStatV2Request,
    options?: T,
  ): Promise<observe_query.GetQueryStatV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observe/query/stat');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      scene_param: _req['scene_param'],
      query_filter: _req['query_filter'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observe/query/enums
   *
   * 获取 query 枚举选项
   */
  GetQueryEnumsV2(
    req: observe_query.GetQueryEnumsV2Request,
    options?: T,
  ): Promise<observe_query.GetQueryEnumsV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observe/query/enums');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      scene_param: _req['scene_param'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observe/query/list
   *
   * bot query v2相关接口
   *
   * 查询 query 列表
   */
  ListQueryV2(
    req: observe_query.ListQueryV2Request,
    options?: T,
  ): Promise<observe_query.ListQueryV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observe/query/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      scene_param: _req['scene_param'],
      query_filter: _req['query_filter'],
      limit: _req['limit'],
      page_token: _req['page_token'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observe/query/export_to_csv
   *
   * 将 query 导出为csv
   */
  ExportQueryToCsvV2(
    req: observe_query.ExportQueryToCsvV2Request,
    options?: T,
  ): Promise<observe_query.ExportQueryToCsvV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observe/query/export_to_csv');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      scene_param: _req['scene_param'],
      query_filter: _req['query_filter'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observe/trace/batch_get_advance_info
   *
   * 批量查询链路进阶信息
   */
  BatchGetTracesAdvanceInfoV2(
    req: observe_trace.BatchGetTracesAdvanceInfoV2Request,
    options?: T,
  ): Promise<observe_trace.BatchGetTracesAdvanceInfoV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observe/trace/batch_get_advance_info');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      scene_param: _req['scene_param'],
      traces: _req['traces'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observe/trace/get/:trace_id
   *
   * 通过trace ID查询链路详情
   */
  GetTraceV2(
    req: observe_trace.GetTraceV2Request,
    options?: T,
  ): Promise<observe_trace.GetTraceV2Response> {
    const _req = req;
    const url = this.genBaseURL(`/api/observe/trace/get/${_req['trace_id']}`);
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      scene_param: _req['scene_param'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/observe/data/batch_query
   *
   * 运营接口
   *
   * 查询分析数据
   */
  BatchAPIQueryV2(
    req: ob_data.BatchAPIQueryV2Request,
    options?: T,
  ): Promise<ob_data.BatchAPIQueryV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/observe/data/batch_query');
    const method = 'POST';
    const data = {
      scene_param: _req['scene_param'],
      metrics_types: _req['metrics_types'],
      data_type: _req['data_type'],
      data_source_type: _req['data_source_type'],
      filter: _req['filter'],
      group_type: _req['group_type'],
      period_type: _req['period_type'],
      time_range_type: _req['time_range_type'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
