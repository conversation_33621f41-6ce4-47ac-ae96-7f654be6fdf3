/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_prompt_common from './flow_devops_prompt_common';

export type Int64 = string | number;

export enum MCPStatus {
  /** 未定义 */
  Undefined = 0,
  /** 正常 */
  Normal = 1,
  /** 已停用 */
  Disabled = 2,
}

export interface Content {
  type?: string;
  text?: string;
}

export interface Function {
  name?: string;
  description?: string;
  input_schema?: string;
}

export interface MCPExecuteConfig {
  /** 动态请求头 accessPointID -> key -> value */
  headers?: Record<string, Record<string, string>>;
  /** 动态请求参数 accessPointID -> key -> value */
  params?: Record<string, Record<string, string>>;
  /** 通用动态请求头 */
  common_headers?: Record<string, string>;
  /** 通用动态参数 */
  common_params?: Record<string, string>;
}

export interface MCPServer {
  /** 主键ID */
  id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 服务名称 */
  name?: string;
  /** 是否官方服务 */
  is_official?: boolean;
  /** 服务描述 */
  description?: string;
  /** 来源类型，ByteFaaS或其他 */
  source_type?: string;
  /** 标签 */
  labels?: Array<flow_devops_prompt_common.Label>;
  /** 服务的访问点列表 */
  mcp_server_access_points?: Array<MCPServerAccessPoint>;
  /** 创建人 */
  creator?: string;
  /** 创建时间 */
  create_time_ms?: Int64;
  /** 更新时间 */
  update_time_ms?: Int64;
  /** 是否支持与prompt一起发布  已废弃，不消费不更改 */
  is_publish_supported?: boolean;
  /** 服务状态 */
  status?: MCPStatus;
  /** MCP 对应的文档或详情页 */
  detail_page_url?: string;
}

export interface MCPServerAccessPoint {
  /** 主键ID */
  id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 关联的MCP Server ID */
  mcp_server_id?: Int64;
  /** 环境，BOE/PPE/ONLINE */
  env?: string;
  /** 泳道 */
  lane?: string;
  /** 服务模式，SSE或STDIO */
  transport_mode?: string;
  /** 接口地址 */
  server_url?: string;
  /** 工具的json schema */
  tools?: Array<Function>;
  /** 地址验证状态 */
  validation_status?: string;
  /** 最近一次地址校验时间 */
  last_validation_time_ms?: Int64;
  /** 最新操作发生时间 */
  lastest_op_time_ms?: Int64;
  /** 创建时间 */
  create_time_ms?: Int64;
  /** 更新时间 */
  update_time_ms?: Int64;
  /** 接口PSM */
  server_psm?: string;
  /** 配置 */
  config?: flow_devops_prompt_common.AccessPointConfig;
  /** 服务状态 */
  status?: MCPStatus;
  /** 是否支持与prompt一起发布 */
  is_publish_supported?: boolean;
}
/* eslint-enable */
