/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface ListTaskRequest {
  page: Int64;
  size: Int64;
  task_id?: Int64;
  task_status?: common.TaskStatus;
  task_name?: string;
}

export interface ListTaskResponse {
  code: number;
  message: string;
  data?: ListTaskResult;
}

export interface ListTaskResult {
  task_list?: Array<Task>;
}

export interface ListUserTaskRequest {
  page_size: Int64;
  page_token?: string;
}

export interface ListUserTaskResponse {
  code: number;
  message: string;
  data?: ListUserTaskResult;
}

export interface ListUserTaskResult {
  user_task_list?: Array<UserTask>;
  has_more?: boolean;
  page_token?: string;
}

export interface Task {
  task_id?: string;
  task_name?: string;
  desc?: string;
  /** 展示
账单文案 */
  bill_desc_starling_key?: string;
  /** 用户任务中心展示文案 */
  user_task_desc_starling_key?: string;
  /** 按钮相关配置 */
  button?: common.UserTaskButton;
  /** 奖励，一期暂定每个任务只发一个奖励，奖励只能是 Token */
  reward?: common.Reward;
  /** 限制次数
限制次数 */
  times_limit?: number;
  /** 是否限制 */
  limit_restriction?: boolean;
  /** 刷新
刷新类型 */
  peroid_type?: common.PeriodType;
  /** 周期时长 */
  peroid_value?: Int64;
  /** 状态 */
  task_status?: common.TaskStatus;
}

export interface UpdateTaskRequest {
  task_id: string;
  /** 展示设置
账单文案 */
  bill_desc_starling_key?: string;
  /** 用户任务中心展示文案 */
  user_task_desc_starling_key?: string;
  /** 按钮相关配置 */
  button?: common.UserTaskButton;
  /** 奖励设置，一期暂定每个任务只发一个奖励，奖励只能是 Token
若出现其他奖励或多个奖励，直接报错 */
  reward?: common.Reward;
  /** 限制次数设置
限制次数 */
  times_limit?: number;
  /** 是否限制 */
  limit_restriction?: boolean;
  /** 刷新设置，一期暂定每个任务组下只有一个任务
若任务组下出现多个任务，则直接报错
刷新类型 */
  peroid_type?: common.PeriodType;
  /** 周期时长 */
  peroid_value?: Int64;
  /** 其他配置
是否一直隐藏 */
  hide?: boolean;
  /** 完成后隐藏 */
  drop_when_finish?: boolean;
  /** 任务描述 */
  task_desc?: string;
  /** 事件触发key, 原tcc配置，改为存储在DB中，用于在规则引擎中将任务名映射为任务ID */
  event_trigger_key?: string;
  /** 用户控制 */
  user_control?: common.UserControl;
  /** 黑名单 */
  black_list?: Array<Int64>;
  /** 白名单 */
  white_list?: Array<Int64>;
}

export interface UpdateTaskResponse {
  code: number;
  message: string;
}

export interface UpdateTaskStatusRequest {
  task_id: string;
  task_status?: common.TaskStatus;
}

export interface UpdateTaskStatusResponse {
  code: number;
  message: string;
}

export interface UserTask {
  task_id?: string;
  task_name?: string;
  desc?: string;
  reward_list?: Array<common.Reward>;
  button?: common.UserTaskButton;
  user_task_progress?: UserTaskProgress;
}

export interface UserTaskProgress {
  current?: Int64;
  total?: Int64;
}
/* eslint-enable */
