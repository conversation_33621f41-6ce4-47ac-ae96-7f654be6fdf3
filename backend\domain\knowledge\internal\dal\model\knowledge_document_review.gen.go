// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameKnowledgeDocumentReview = "knowledge_document_review"

// KnowledgeDocumentReview 文档审阅表
type KnowledgeDocumentReview struct {
	ID           int64          `gorm:"column:id;primaryKey;comment:主键ID" json:"id"`                                      // 主键ID
	KnowledgeID  int64          `gorm:"column:knowledge_id;not null;comment:knowledge id" json:"knowledge_id"`            // knowledge id
	SpaceID      int64          `gorm:"column:space_id;not null;comment:空间id" json:"space_id"`                            // 空间id
	Name         string         `gorm:"column:name;not null;comment:文档名称" json:"name"`                                    // 文档名称
	Type         string         `gorm:"column:type;not null;default:0;comment:文档类型" json:"type"`                          // 文档类型
	URI          string         `gorm:"column:uri;comment:资源标识" json:"uri"`                                               // 资源标识
	FormatType   int32          `gorm:"column:format_type;not null;comment:0 文本, 1 表格, 2 图片" json:"format_type"`          // 0 文本, 1 表格, 2 图片
	Status       int32          `gorm:"column:status;not null;comment:0 处理中，1 已完成，2 失败，3 失效" json:"status"`               // 0 处理中，1 已完成，2 失败，3 失效
	ChunkRespURI string         `gorm:"column:chunk_resp_uri;comment:预切片tos资源标识" json:"chunk_resp_uri"`                   // 预切片tos资源标识
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time in Milliseconds" json:"deleted_at"`          // Delete Time in Milliseconds
	CreatedAt    int64          `gorm:"column:created_at;not null;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt    int64          `gorm:"column:updated_at;not null;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	CreatorID    int64          `gorm:"column:creator_id;not null;comment:创建者ID" json:"creator_id"`                       // 创建者ID
}

// TableName KnowledgeDocumentReview's table name
func (*KnowledgeDocumentReview) TableName() string {
	return TableNameKnowledgeDocumentReview
}
