/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as card from './namespaces/card';
import * as copilot_common from './namespaces/copilot_common';
import * as permission from './namespaces/permission';
import * as resource_common from './namespaces/resource_common';
import * as retriever from './namespaces/retriever';
import * as shortcut_command from './namespaces/shortcut_command';

export {
  base,
  bot_common,
  card,
  copilot_common,
  permission,
  resource_common,
  retriever,
  shortcut_command,
};
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/card';
export * from './namespaces/copilot_common';
export * from './namespaces/permission';
export * from './namespaces/resource_common';
export * from './namespaces/retriever';
export * from './namespaces/shortcut_command';

export type Int64 = string | number;

export default class CardService<T> {
  private request: any = () => {
    throw new Error('CardService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/card_builder/delete_template
   *
   * 删除卡片模版
   */
  DeleteCardTemplate(
    req: card.DeleteCardTemplateRequest,
    options?: T,
  ): Promise<card.DeleteCardTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/delete_template');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      template_id: _req['template_id'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/upload_file
   *
   * 上传卡片相关资源
   */
  CardUploadFile(
    req?: card.CardUploadFileRequest,
    options?: T,
  ): Promise<card.CardUploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/upload_file');
    const method = 'POST';
    const data = {
      file_name: _req['file_name'],
      data: _req['data'],
      scene: _req['scene'],
      base: _req['base'],
    };
    const headers = { 'Content-Type': _req['Content-Type'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/query_history
   *
   * 卡片发布历史
   */
  QueryCardHistory(
    req: card.QueryCardHistoryRequest,
    options?: T,
  ): Promise<card.QueryCardHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/query_history');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      user_id: _req['user_id'],
      page: _req['page'],
      size: _req['size'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/publish
   *
   * 发布卡片
   */
  CardMetaPublish(
    req: card.CardMetaPublishRequest,
    options?: T,
  ): Promise<card.CardMetaPublishResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/publish');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      draft_card_id: _req['draft_card_id'],
      card_id: _req['card_id'],
      description: _req['description'],
      version_name: _req['version_name'],
      thumbnail_info: _req['thumbnail_info'],
      base: _req['base'],
    };
    const headers = { 'use-builder-psm': _req['use-builder-psm'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/update
   *
   * 新的卡片更新接口，agw上线原卡片更新作废
   */
  UpdateCardInfo(
    req: card.UpdateCardInfoRequest,
    options?: T,
  ): Promise<card.UpdateCardInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/update');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      name: _req['name'],
      card_status: _req['card_status'],
      creator_id: _req['creator_id'],
      edit_card: _req['edit_card'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/save_template
   *
   * 保存卡片模版
   */
  SaveCardTemplate(
    req: card.SaveCardTemplateRequest,
    options?: T,
  ): Promise<card.SaveCardTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/save_template');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      channel_type: _req['channel_type'],
      thumbnail: _req['thumbnail'],
      name: _req['name'],
      dsl_content: _req['dsl_content'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/mget_template
   *
   * 批量获取卡片模版
   */
  MGetCardTemplate(
    req: card.MGetCardTemplateRequest,
    options?: T,
  ): Promise<card.MGetCardTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/mget_template');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      size: _req['size'],
      page: _req['page'],
      channel_type: _req['channel_type'],
      category: _req['category'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/get_card_info
   *
   * 查询卡片信息
   */
  GetCardInfo(
    req: card.GetCardInfoRequest,
    options?: T,
  ): Promise<card.GetCardInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/get_card_info');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      channel_type: _req['channel_type'],
      version_num: _req['version_num'],
      creator_id: _req['creator_id'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/op_card/query_cards
   *
   * -------------- 运营平台接口 --------------
   */
  QueryCards(
    req?: card.QueryCardsRequest,
    options?: T,
  ): Promise<card.QueryCardsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_card/query_cards');
    const method = 'POST';
    const data = { card_condition: _req['card_condition'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/save_generate_history
   *
   * 保存生成卡片结果
   */
  SaveGenerateHistory(
    req: card.SaveGenerateHistoryRequest,
    options?: T,
  ): Promise<card.SaveGenerateHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/save_generate_history');
    const method = 'POST';
    const data = {
      record_id: _req['record_id'],
      card_id: _req['card_id'],
      UserID: _req['UserID'],
      transfer_status: _req['transfer_status'],
      channel_info: _req['channel_info'],
      ai_schema: _req['ai_schema'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/list_card_generate_history
   *
   * 查询AI生成卡片历史列表
   */
  ListCardGenerateHistory(
    req: card.ListCardGenerateHistoryRequest,
    options?: T,
  ): Promise<card.ListCardGenerateHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/list_card_generate_history');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      card_id: _req['card_id'],
      page: _req['page'],
      size: _req['size'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/get_default_prompt
   *
   * 获取生成卡片默认提示词
   */
  GetDefaultPrompt(
    req: card.GetDefaultPromptRequest,
    options?: T,
  ): Promise<card.GetDefaultPromptResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/get_default_prompt');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      tool_type: _req['tool_type'],
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      workflow_id: _req['workflow_id'],
      language: _req['language'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/generate_card
   *
   * AI 生成卡片
   */
  GenerateCard(
    req: card.GenerateCardRequest,
    options?: T,
  ): Promise<card.GenerateCardResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/generate_card');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      description: _req['description'],
      UserID: _req['UserID'],
      language: _req['language'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/get_card_tcc_info
   *
   * 查询tcc配置接口
   */
  GetCardTccInfo(
    req?: card.GetCardTccInfoRequest,
    options?: T,
  ): Promise<card.GetCardTccInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/get_card_tcc_info');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/preview
   *
   * 卡片预览接口
   */
  PreviewCard(
    req?: card.PreviewCardRequest,
    options?: T,
  ): Promise<card.PreviewCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/preview');
    const method = 'POST';
    const data = {
      DraftID: _req['DraftID'],
      ChannelType: _req['ChannelType'],
      Base: _req['Base'],
    };
    const headers = { 'use-builder-psm': _req['use-builder-psm'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/query_card_list
   *
   * 查询卡片列表 (一级导航栏页面)
   */
  QueryCardList(
    req?: card.QueryCardListRequest,
    options?: T,
  ): Promise<card.QueryCardListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/query_card_list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      condition: _req['condition'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/mget_card_info
   *
   * 批量查询卡片信息
   */
  MGetCardInfo(
    req: card.MGetCardInfoRequest,
    options?: T,
  ): Promise<card.MGetCardInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/mget_card_info');
    const method = 'POST';
    const data = { card_ids: _req['card_ids'], base: _req['base'] };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/generate_cards_thumbnail
   *
   * 批量生成卡片缩略图
   */
  GenerateCardsThumbnail(
    req: card.GenerateCardsThumbnailRequest,
    options?: T,
  ): Promise<card.GenerateCardsThumbnailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/generate_cards_thumbnail');
    const method = 'POST';
    const data = { card_ids: _req['card_ids'], base: _req['base'] };
    const headers = {
      'space-id': _req['space-id'],
      'use-builder-psm': _req['use-builder-psm'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/generate_card_stream
   *
   * 生成卡片流式输出接口
   */
  GenerateCardStream(
    req: card.GenerateCardStreamRequest,
    options?: T,
  ): Promise<card.GenerateCardStreamResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/generate_card_stream');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      description: _req['description'],
      UserID: _req['UserID'],
      language: _req['language'],
      user_data: _req['user_data'],
      prompt_version: _req['prompt_version'],
      reference: _req['reference'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/gen_card_channel_thumbnail
   *
   * 生成卡片缩略图
   */
  GenCardChannelsThumbnail(
    req?: card.GenCardChannelsThumbnailRequest,
    options?: T,
  ): Promise<card.GenCardChannelsThumbnailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/gen_card_channel_thumbnail');
    const method = 'POST';
    const data = {
      DraftID: _req['DraftID'],
      Channels: _req['Channels'],
      CardID: _req['CardID'],
      Base: _req['Base'],
    };
    const headers = { 'use-builder-psm': _req['use-builder-psm'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/get_task_progress
   *
   * 查询论文解析情况（轮询）
   */
  GetTaskProgress(
    req: card.GetTaskProgressRequest,
    options?: T,
  ): Promise<card.GetTaskProgressResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/get_task_progress');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_ids: _req['document_ids'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/save_instance
   *
   * 更新实例信息
   */
  UpdateAgentInstanceInfo(
    req: card.UpdateAgentInstanceInfoRequest,
    options?: T,
  ): Promise<card.UpdateAgentInstanceInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/save_instance');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      instance_id: _req['instance_id'],
      update_param: _req['update_param'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/analyzing_paper
   *
   * 解析论文
   */
  AnalyzingPaper(
    req: card.AnalyzingPaperRequest,
    options?: T,
  ): Promise<card.AnalyzingPaperResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/analyzing_paper');
    const method = 'POST';
    const data = { uri: _req['uri'], name: _req['name'], base: _req['base'] };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/delete_instance
   *
   * 删除实例
   */
  DeleteAgentInstance(
    req: card.DeleteAgentInstanceRequest,
    options?: T,
  ): Promise<card.DeleteAgentInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/delete_instance');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      instance_id: _req['instance_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/create_instance
   *
   * 新建实例
   */
  CreateAgentInstance(
    req: card.CreateAgentInstanceRequest,
    options?: T,
  ): Promise<card.CreateAgentInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/create_instance');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      name: _req['name'],
      init_param: _req['init_param'],
      content: _req['content'],
      idem_key: _req['idem_key'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/get_instance_detail
   *
   * 查询实例详情
   */
  GetAgentInstanceDetail(
    req: card.GetAgentInstanceDetailRequest,
    options?: T,
  ): Promise<card.GetAgentInstanceDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/get_instance_detail');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      instance_id: _req['instance_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/instance_list
   *
   * 查询实例列表
   */
  ListAgentInstance(
    req: card.ListAgentInstanceRequest,
    options?: T,
  ): Promise<card.ListAgentInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/instance_list');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      size: _req['size'],
      page: _req['page'],
      sort_cond: _req['sort_cond'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/list_slice
   *
   * 查询分片信息
   */
  ListSlice(
    req: card.ListSliceRequest,
    options?: T,
  ): Promise<card.ListSliceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/list_slice');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      InstanceID: _req['InstanceID'],
      dataset_id: _req['dataset_id'],
      document_id: _req['document_id'],
      page: _req['page'],
      size: _req['size'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/get
   *
   * 查询agent详情
   */
  GetAgentDetail(
    req: card.GetAgentDetailRequest,
    options?: T,
  ): Promise<card.GetAgentDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/get');
    const method = 'POST';
    const data = { agent_id: _req['agent_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/biz_universal
   *
   * Agent 业务逻辑通用接口
   */
  AgentBizUniversal(
    req: card.AgentBizUniversalRequest,
    options?: T,
  ): Promise<card.AgentBizUniversalResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/biz_universal');
    const method = 'POST';
    const data = {
      api_path: _req['api_path'],
      method: _req['method'],
      query: _req['query'],
      body: _req['body'],
      Base: _req['Base'],
    };
    const headers = { 'Content-Type': _req['Content-Type'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/upload_file
   *
   * 上传文件资源接口
   */
  AgentUploadFile(
    req?: card.AgentUploadFileRequest,
    options?: T,
  ): Promise<card.AgentUploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/agent/upload_file');
    const method = 'POST';
    const data = { data: _req['data'], Base: _req['Base'] };
    const headers = { 'Content-Type': _req['Content-Type'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/generate_card_variables
   *
   * 生成用户数据接口
   */
  GenerateCardVariables(
    req: card.GenerateCardVariablesRequest,
    options?: T,
  ): Promise<card.GenerateCardVariablesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/generate_card_variables');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      language: _req['language'],
      desc: _req['desc'],
      channel_type: _req['channel_type'],
      base: _req['base'],
    };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/translate_result_get
   *
   * 查询翻译论文结果
   */
  GetPaperTranslateResult(
    req: card.GetPaperTranslateResultRequest,
    options?: T,
  ): Promise<card.GetPaperTranslateResultResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/translate_result_get');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      instance_id: _req['instance_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/translate_paper
   *
   * 翻译论文
   */
  TranslatePaper(
    req: card.TranslatePaperRequest,
    options?: T,
  ): Promise<card.TranslatePaperResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/translate_paper');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      instance_id: _req['instance_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/get_image_info
   *
   * 获取图片
   */
  GetImageInfo(
    req: card.GetImageInfoRequest,
    options?: T,
  ): Promise<card.GetImageInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/get_image_info');
    const method = 'POST';
    const data = {
      file_name: _req['file_name'],
      need_data: _req['need_data'],
      Base: _req['Base'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/get_jwt_token
   *
   * 获取图片jwt
   */
  GetJwtToken(
    req?: card.GetJwtTokenRequest,
    options?: T,
  ): Promise<card.GetJwtTokenResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/get_jwt_token');
    const method = 'POST';
    const data = { expire_time: _req['expire_time'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/translate_text
   *
   * 翻译文本
   */
  TranslateText(
    req?: card.TranslateTextRequest,
    options?: T,
  ): Promise<card.TranslateTextResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/agent/translate_text');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      instance_id: _req['instance_id'],
      text: _req['text'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/save_reference
   *
   * 解析并保存参考资料
   */
  AnalyseAgentReference(
    req?: card.AnalyseAgentReferenceRequest,
    options?: T,
  ): Promise<card.AnalyseAgentReferenceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/agent/save_reference');
    const method = 'POST';
    const data = {
      references: _req['references'],
      fast_mode: _req['fast_mode'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/get_reference
   *
   * 获取参考资料
   */
  GetAgentReference(
    req?: card.GetAgentReferenceRequest,
    options?: T,
  ): Promise<card.GetAgentReferenceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/agent/get_reference');
    const method = 'POST';
    const data = { reference_url: _req['reference_url'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/get_accompanied_image
   *
   * 利用workflow获取文本配图
   */
  GetAgentAccompaniedImage(
    req?: card.GetAgentAccompaniedImageRequest,
    options?: T,
  ): Promise<card.GetAgentAccompaniedImageResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/card_builder/agent/get_accompanied_image',
    );
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      param: _req['param'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/save_setting
   *
   * 保存agent用户配置
   */
  SaveAgentUserSettings(
    req: card.SaveAgentUserSettingsRequest,
    options?: T,
  ): Promise<card.SaveAgentUserSettingsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/save_setting');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      setting: _req['setting'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/get_settings
   *
   * 获取agent用户配置
   */
  GetAgentUserSettings(
    req: card.GetAgentUserSettingsRequest,
    options?: T,
  ): Promise<card.GetAgentUserSettingsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/get_settings');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      setting_keys: _req['setting_keys'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/api_trigger
   *
   * 转发流式api调用
   */
  AgentTriggerOpenAPIStream(
    req: card.AgentTriggerOpenAPIStreamRequest,
    options?: T,
  ): Promise<card.AgentTriggerOpenAPIStreamResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/api_trigger');
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      api_type: _req['api_type'],
      token: _req['token'],
      body: _req['body'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/get_file_info
   *
   * 获取文件
   */
  GetFileInfo(
    req?: card.GetFileInfoRequest,
    options?: T,
  ): Promise<card.GetFileInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/get_file_info');
    const method = 'POST';
    const data = {
      file_name: _req['file_name'],
      file_url: _req['file_url'],
      need_data: _req['need_data'],
      Base: _req['Base'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/plugin_get_jwt
   *
   * 创建plugin用 生成JWT
   */
  GetJwtTokenForPlugin(
    req: card.GetJwtTokenForPluginRequest,
    options?: T,
  ): Promise<card.GetJwtTokenForPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/plugin_get_jwt');
    const method = 'POST';
    const data = { scene: _req['scene'], Base: _req['Base'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/get_card_user_setting
   *
   * 获取卡片用户配置
   */
  GetCardUserSettings(
    req?: card.GetCardUserSettingsRequest,
    options?: T,
  ): Promise<card.GetCardUserSettingsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/get_card_user_setting');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      setting_keys: _req['setting_keys'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/save_card_user_setting
   *
   * 保存卡片用户配置
   */
  SaveCardUserSettings(
    req?: card.SaveCardUserSettingsRequest,
    options?: T,
  ): Promise<card.SaveCardUserSettingsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/save_card_user_setting');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      user_settings: _req['user_settings'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/agent/plugin_get_accompanied_image
   *
   * 创建plugin用 转存文本配图
   */
  GetAgentAccompaniedImageForPlugin(
    req: card.GetAgentAccompaniedImageForPluginRequest,
    options?: T,
  ): Promise<card.GetAgentAccompaniedImageForPluginResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/card_builder/agent/plugin_get_accompanied_image',
    );
    const method = 'POST';
    const data = { origin_url: _req['origin_url'], Base: _req['Base'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/agent/coze_api_trigger
   *
   * 转发非流式api调用
   */
  AgentTriggerCozeAPI(
    req: card.AgentTriggerCozeAPIRequest,
    options?: T,
  ): Promise<card.AgentTriggerCozeAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/agent/coze_api_trigger');
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      api_type: _req['api_type'],
      token: _req['token'],
      body: _req['body'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/get_tcc_config
   *
   * 获取tcc配置
   */
  GeneralGetTccConfig(
    req: card.GeneralGetTccConfigRequest,
    options?: T,
  ): Promise<card.GeneralGetTccConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/get_tcc_config');
    const method = 'POST';
    const data = {
      key: _req['key'],
      space: _req['space'],
      tcc_service_id: _req['tcc_service_id'],
      value_option: _req['value_option'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/op_card/update_template
   *
   * 更新官方模版
   */
  UpdateTemplate(
    req?: card.UpdateTemplateRequest,
    options?: T,
  ): Promise<card.UpdateTemplateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/op_card/update_template');
    const method = 'POST';
    const data = {
      ids: _req['ids'],
      operator: _req['operator'],
      stage: _req['stage'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/generate_card_suggestion
   *
   * 根据用户输入返回建议的卡片RAG信息
   */
  GenerateCardSuggestion(
    req: card.GenerateCardSuggestionRequest,
    options?: T,
  ): Promise<card.GenerateCardSuggestionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/generate_card_suggestion');
    const method = 'POST';
    const data = {
      description: _req['description'],
      candidates_number: _req['candidates_number'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder/async_gen_thumbnail
   *
   * 异步生成缩略图
   */
  AsyncGenThumbnail(
    req?: card.AsyncGenThumbnailRequest,
    options?: T,
  ): Promise<card.AsyncGenThumbnailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_builder/async_gen_thumbnail');
    const method = 'POST';
    const data = { messages: _req['messages'], Base: _req['Base'] };
    const headers = { 'space-id': _req['space-id'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/card_builder/check_card_length
   *
   * 用户创建过程中检查卡片是否超长
   */
  CheckCardLength(
    req: card.CheckCardLengthRequest,
    options?: T,
  ): Promise<card.CheckCardLengthResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_builder/check_card_length');
    const method = 'POST';
    const data = { DraftID: _req['DraftID'], Base: _req['Base'] };
    const headers = { 'use-builder-psm': _req['use-builder-psm'] };
    return this.request({ url, method, data, headers }, options);
  }
}
/* eslint-enable */
