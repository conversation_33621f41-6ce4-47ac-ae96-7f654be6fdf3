/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_marketplace_interaction_common from './flow_marketplace_interaction_common';

export type Int64 = string | number;

export enum TopPostStatus {
  InEffect = 1,
  NotBegun = 2,
  Finished = 3,
}

export interface AuthorInfo {
  id?: string;
  icon_url?: string;
  author_name?: string;
  author_type?: flow_marketplace_interaction_common.AuthorType;
  status?: flow_marketplace_interaction_common.UserStatus;
}

export interface Comment {
  id?: string;
  product_id?: string;
  product_name?: string;
  post_id?: string;
  author?: AuthorInfo;
  status?: flow_marketplace_interaction_common.CommentStatus;
  type?: flow_marketplace_interaction_common.CommentType;
  reply_count?: number;
  published_at?: string;
  post_status?: flow_marketplace_interaction_common.PostStatus;
  content?: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  /** ProductEntityType 所属商品的实体类型，当 ItemType = Product 时有对应的值：1-bot商品；2-插件商品；6-应用商品；21-bot模板；23-工作流模板；26-应用模板 */
  product_entity_type?: number;
}

export interface DeleteTopPostRequest {
  id: string;
}

export interface DeleteTopPostResponse {
  code: number;
  message: string;
}

export interface Discussion {
  id?: string;
  status?: flow_marketplace_interaction_common.DiscussionStatus;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  item_id?: string;
  /** 所属实体展示名称 */
  item_show_name?: string;
  /** 回复数 */
  comment_count?: string;
  /** 帖子数量 */
  post_count?: string;
  /** ProductEntityType 所属商品的实体类型，当 ItemType = Product 时有对应的值：1-bot商品；2-插件商品；6-应用商品；21-bot模板；23-工作流模板；26-应用模板 */
  product_entity_type?: number;
}

export interface GetCommentListData {
  comments?: Array<Comment>;
  total?: number;
}

export interface GetCommentListRequest {
  page_num: number;
  page_size: number;
  product_id?: string;
  comment_id?: string;
  author_name?: string;
  author_type?: flow_marketplace_interaction_common.AuthorType;
  author_id?: string;
  content_keyword?: string;
  post_id?: string;
  created_at_begin?: string;
  created_at_end?: string;
  status?: flow_marketplace_interaction_common.CommentStatus;
  type?: flow_marketplace_interaction_common.CommentType;
  root_id?: string;
  sort_type?: flow_marketplace_interaction_common.SortType;
}

export interface GetCommentListResponse {
  code: number;
  message: string;
  data?: GetCommentListData;
}

export interface GetDiscussionGlobalConfigData {
  status?: flow_marketplace_interaction_common.DiscussionGlobalStatus;
  audit_link?: string;
}

export interface GetDiscussionGlobalConfigRequest {}

export interface GetDiscussionGlobalConfigResponse {
  code: number;
  message: string;
  data?: GetDiscussionGlobalConfigData;
}

export interface GetDiscussionListData {
  discussions?: Array<Discussion>;
  total?: number;
}

export interface GetDiscussionListRequest {
  page_num?: number;
  page_size?: number;
  /** 讨论区关联的实体类型 */
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  /** 关联的实体名，用于搜索 */
  item_show_name?: string;
  /** 关联的实体ID */
  item_id?: string;
  status?: flow_marketplace_interaction_common.DiscussionStatus;
}

export interface GetDiscussionListResponse {
  code: number;
  message: string;
  data?: GetDiscussionListData;
}

export interface GetImgURLData {
  url?: string;
}

export interface GetImgURLRequest {
  Key?: string;
}

export interface GetImgURLResponse {
  code: number;
  message: string;
  data?: GetImgURLData;
}

export interface GetPostDetailData {
  post?: Post;
}

export interface GetPostDetailRequest {
  id: string;
}

export interface GetPostDetailResponse {
  code: number;
  message: string;
  data?: GetPostDetailData;
}

export interface GetPostLabelListData {
  labels?: Array<flow_marketplace_interaction_common.PostLabel>;
}

export interface GetPostLabelListRequest {}

export interface GetPostLabelListResponse {
  code: number;
  message: string;
  data?: GetPostLabelListData;
}

export interface GetPostListData {
  posts?: Array<Post>;
  total?: number;
}

export interface GetPostListRequest {
  page_num?: number;
  page_size?: number;
  /** 排序类型 */
  sort_type?: flow_marketplace_interaction_common.SortType;
  /** 搜索词 */
  keyword?: string;
  post_id?: string;
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  author_id?: string;
  author_name?: string;
  /** 发布时间筛选 - 开始时间 */
  publish_begin_at?: string;
  /** 发布时间筛选 - 结束时间 */
  publish_end_at?: string;
  status?: flow_marketplace_interaction_common.PostStatus;
  /** 标签 */
  label_id?: string;
  /** 正负向评分 */
  min_evaluation_score?: number;
  max_evaluation_score?: number;
}

export interface GetPostListResponse {
  code: number;
  message: string;
  data?: GetPostListData;
}

export interface GetPreviewTopPostData {
  preview_top_post_id?: string;
  product_id?: string;
}

export interface GetPreviewTopPostRequest {
  id: string;
}

export interface GetPreviewTopPostResponse {
  code: number;
  message: string;
  data?: GetPreviewTopPostData;
}

export interface GetTopPostListData {
  posts?: Array<TopPost>;
  total?: number;
}

export interface GetTopPostListRequest {
  page_num?: number;
  page_size?: number;
  config_type?: flow_marketplace_interaction_common.ConfigType;
}

export interface GetTopPostListResponse {
  code: number;
  message: string;
  data?: GetTopPostListData;
}

export interface GetUploadTokenRequest {}

export interface GetUploadTokenResponse {
  code: number;
  message: string;
  data?: flow_marketplace_interaction_common.UploadTokenData;
}

export interface Post {
  id?: string;
  title?: string;
  status?: flow_marketplace_interaction_common.PostStatus;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  item_id?: string;
  /** 所属实体展示名称 */
  item_show_name?: string;
  author?: AuthorInfo;
  /** 回复数 */
  comment_count?: number;
  /** 发布时间 */
  created_at?: string;
  label?: flow_marketplace_interaction_common.PostLabel;
  /** 内容（列表接口不返回，详情接口返回） */
  content?: string;
  /** 帖子内容涉及的资源（列表接口不返回，详情接口返回） */
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  /** （列表接口不返回，详情接口返回） */
  conversation?: flow_marketplace_interaction_common.Conversation;
  pin_type?: flow_marketplace_interaction_common.PinType;
  /** 所属商品的实体ID */
  product_entity_id?: string;
  /** 帖子的正负向评分，50 为中性，0 未评，100 是最正向 */
  evaluation_score?: number;
  /** ProductEntityType 所属商品的实体类型，当 ItemType = Product 时有对应的值：1-bot商品；2-插件商品；6-应用商品；21-bot模板；23-工作流模板；26-应用模板 */
  product_entity_type?: number;
}

export interface PublishPostData {
  id?: string;
  post_id?: string;
}

export interface PublishTopPostRequest {
  title: string;
  content: string;
  context_text: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
}

export interface PublishTopPostResponse {
  code: number;
  message: string;
  data?: PublishPostData;
}

export interface SetDiscussionGlobalConfigRequest {
  discussion_status?: flow_marketplace_interaction_common.DiscussionGlobalStatus;
  date_range?: Array<string>;
  'X-Jwt-Token'?: string;
  'X-Bcgw-Username'?: string;
  /** 审批流程ID，用来识别流程是否是线上的 */
  process_id?: number;
}

export interface SetDiscussionGlobalConfigResponse {
  code: number;
  message: string;
}

export interface SetTopPostConfigRequest {
  id: string;
  config: TopPostConfig;
}

export interface SetTopPostConfigResponse {
  code: number;
  message: string;
}

export interface TopPost {
  id?: string;
  post_id?: string;
  title?: string;
  config?: TopPostConfig;
  status?: TopPostStatus;
  comment_count?: string;
}

export interface TopPostConfig {
  begin_time_second?: string;
  end_time_second?: string;
}

export interface UpdateCommentStatusData {
  failed?: Record<Int64, string>;
}

export interface UpdateCommentStatusRequest {
  ids: Array<string>;
  status: flow_marketplace_interaction_common.CommentStatus;
}

export interface UpdateCommentStatusResponse {
  code: number;
  message: string;
  data?: UpdateCommentStatusData;
}

export interface UpdateDiscussionStatusRequest {
  ids: Array<string>;
  status: flow_marketplace_interaction_common.DiscussionStatus;
}

export interface UpdateDiscussionStatusResponse {
  code: number;
  message: string;
}

export interface UpdatePostStatusRequest {
  ids: Array<string>;
  status: flow_marketplace_interaction_common.PostStatus;
}

export interface UpdatePostStatusResponse {
  code: number;
  message: string;
}

export interface UpdateTopPostRequest {
  post_id?: string;
  title?: string;
  content?: string;
  content_text?: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
}

export interface UpdateTopPostResponse {
  code: number;
  message: string;
}

export interface UpsertUserStatusRequest {
  user_id: string;
  status: flow_marketplace_interaction_common.UserStatus;
}

export interface UpsertUserStatusResponse {
  code: number;
  message: string;
}
/* eslint-enable */
