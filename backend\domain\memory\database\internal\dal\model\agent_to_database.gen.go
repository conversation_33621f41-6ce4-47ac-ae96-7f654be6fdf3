// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAgentToDatabase = "agent_to_database"

// AgentToDatabase agent_to_database info
type AgentToDatabase struct {
	ID            int64 `gorm:"column:id;primaryKey;comment:ID" json:"id"`                                                                       // ID
	AgentID       int64 `gorm:"column:agent_id;not null;comment:Agent ID" json:"agent_id"`                                                       // Agent ID
	DatabaseID    int64 `gorm:"column:database_id;not null;comment:ID of database_info" json:"database_id"`                                      // ID of database_info
	IsDraft       bool  `gorm:"column:is_draft;not null;comment:Is draft" json:"is_draft"`                                                       // Is draft
	PromptDisable bool  `gorm:"column:prompt_disable;not null;comment:Support prompt calls: 1 not supported, 0 supported" json:"prompt_disable"` // Support prompt calls: 1 not supported, 0 supported
}

// TableName AgentToDatabase's table name
func (*AgentToDatabase) TableName() string {
	return TableNameAgentToDatabase
}
