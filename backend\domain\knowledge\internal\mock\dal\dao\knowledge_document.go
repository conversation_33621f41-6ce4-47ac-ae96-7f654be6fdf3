// Code generated by MockGen. DO NOT EDIT.
// Source: knowledge_document.go
//
// Generated by this command:
//
//	mockgen -destination ../../mock/dal/dao/knowledge_document.go --package dao -source knowledge_document.go
//

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"

	"github.com/coze-dev/coze-studio/backend/domain/knowledge/entity"
	model "github.com/coze-dev/coze-studio/backend/domain/knowledge/internal/dal/model"
)

// MockKnowledgeDocumentRepo is a mock of KnowledgeDocumentRepo interface.
type MockKnowledgeDocumentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockKnowledgeDocumentRepoMockRecorder
	isgomock struct{}
}

// MockKnowledgeDocumentRepoMockRecorder is the mock recorder for MockKnowledgeDocumentRepo.
type MockKnowledgeDocumentRepoMockRecorder struct {
	mock *MockKnowledgeDocumentRepo
}

// NewMockKnowledgeDocumentRepo creates a new mock instance.
func NewMockKnowledgeDocumentRepo(ctrl *gomock.Controller) *MockKnowledgeDocumentRepo {
	mock := &MockKnowledgeDocumentRepo{ctrl: ctrl}
	mock.recorder = &MockKnowledgeDocumentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKnowledgeDocumentRepo) EXPECT() *MockKnowledgeDocumentRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockKnowledgeDocumentRepo) Create(ctx context.Context, document *model.KnowledgeDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, document)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockKnowledgeDocumentRepoMockRecorder) Create(ctx, document any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).Create), ctx, document)
}

// CreateWithTx mocks base method.
func (m *MockKnowledgeDocumentRepo) CreateWithTx(ctx context.Context, tx *gorm.DB, document []*model.KnowledgeDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWithTx", ctx, tx, document)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateWithTx indicates an expected call of CreateWithTx.
func (mr *MockKnowledgeDocumentRepoMockRecorder) CreateWithTx(ctx, tx, document any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWithTx", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).CreateWithTx), ctx, tx, document)
}

// Delete mocks base method.
func (m *MockKnowledgeDocumentRepo) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockKnowledgeDocumentRepoMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).Delete), ctx, id)
}

// DeleteDocuments mocks base method.
func (m *MockKnowledgeDocumentRepo) DeleteDocuments(ctx context.Context, ids []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDocuments", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDocuments indicates an expected call of DeleteDocuments.
func (mr *MockKnowledgeDocumentRepoMockRecorder) DeleteDocuments(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDocuments", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).DeleteDocuments), ctx, ids)
}

// FindDocumentByCondition mocks base method.
func (m *MockKnowledgeDocumentRepo) FindDocumentByCondition(ctx context.Context, opts *entity.WhereDocumentOpt) ([]*model.KnowledgeDocument, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindDocumentByCondition", ctx, opts)
	ret0, _ := ret[0].([]*model.KnowledgeDocument)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// FindDocumentByCondition indicates an expected call of FindDocumentByCondition.
func (mr *MockKnowledgeDocumentRepoMockRecorder) FindDocumentByCondition(ctx, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindDocumentByCondition", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).FindDocumentByCondition), ctx, opts)
}

// GetByID mocks base method.
func (m *MockKnowledgeDocumentRepo) GetByID(ctx context.Context, id int64) (*model.KnowledgeDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.KnowledgeDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockKnowledgeDocumentRepoMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).GetByID), ctx, id)
}

// List mocks base method.
func (m *MockKnowledgeDocumentRepo) List(ctx context.Context, knowledgeID int64, name *string, limit int, cursor *string) ([]*model.KnowledgeDocument, *string, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, knowledgeID, name, limit, cursor)
	ret0, _ := ret[0].([]*model.KnowledgeDocument)
	ret1, _ := ret[1].(*string)
	ret2, _ := ret[2].(bool)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// List indicates an expected call of List.
func (mr *MockKnowledgeDocumentRepoMockRecorder) List(ctx, knowledgeID, name, limit, cursor any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).List), ctx, knowledgeID, name, limit, cursor)
}

// MGetByID mocks base method.
func (m *MockKnowledgeDocumentRepo) MGetByID(ctx context.Context, ids []int64) ([]*model.KnowledgeDocument, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetByID", ctx, ids)
	ret0, _ := ret[0].([]*model.KnowledgeDocument)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetByID indicates an expected call of MGetByID.
func (mr *MockKnowledgeDocumentRepoMockRecorder) MGetByID(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetByID", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).MGetByID), ctx, ids)
}

// SetStatus mocks base method.
func (m *MockKnowledgeDocumentRepo) SetStatus(ctx context.Context, documentID int64, status int32, reason string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetStatus", ctx, documentID, status, reason)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetStatus indicates an expected call of SetStatus.
func (mr *MockKnowledgeDocumentRepoMockRecorder) SetStatus(ctx, documentID, status, reason any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetStatus", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).SetStatus), ctx, documentID, status, reason)
}

// Update mocks base method.
func (m *MockKnowledgeDocumentRepo) Update(ctx context.Context, document *model.KnowledgeDocument) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, document)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockKnowledgeDocumentRepoMockRecorder) Update(ctx, document any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).Update), ctx, document)
}

// UpdateDocumentSliceInfo mocks base method.
func (m *MockKnowledgeDocumentRepo) UpdateDocumentSliceInfo(ctx context.Context, documentID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDocumentSliceInfo", ctx, documentID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDocumentSliceInfo indicates an expected call of UpdateDocumentSliceInfo.
func (mr *MockKnowledgeDocumentRepoMockRecorder) UpdateDocumentSliceInfo(ctx, documentID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDocumentSliceInfo", reflect.TypeOf((*MockKnowledgeDocumentRepo)(nil).UpdateDocumentSliceInfo), ctx, documentID)
}
