/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface GetFunctionResourcePackagesRequest {
  /** Whether the function is a plugin function */
  is_plugin_function?: boolean;
  /** Whether the function is a worker */
  is_worker?: boolean;
  /** Runtime type to filter packages */
  runtime?: string;
  /** Region to query packages for */
  region?: string;
  /** Whether the function is triggered by MQ event */
  is_mq_event_trigger?: boolean;
  /** Function category */
  category?: string;
  /** PSM name */
  psm?: string;
  /** Parent PSM ID */
  psm_parent_id?: Int64;
}

export interface GetFunctionResourcePackagesResponse {
  code: number;
  data: Array<common.ResourceLimitWithAlias>;
  error: string;
}

export interface GetPackageListRequest {
  /** Region to query packages for */
  region: string;
}

export interface GetPackageListResponse {
  code: number;
  data: Array<Package>;
  error: string;
}

export interface Package {
  /** Package name */
  name: string;
  /** Resource limits and alias for the package */
  resource_limit: common.ResourceLimitWithAlias;
}
/* eslint-enable */
