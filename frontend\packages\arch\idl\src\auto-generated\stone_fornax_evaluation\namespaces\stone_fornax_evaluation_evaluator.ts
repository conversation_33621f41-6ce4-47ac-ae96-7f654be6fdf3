/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as evaluator from './evaluator';
import * as common from './common';

export type Int64 = string | number;

export interface BatchGetEvaluatorRecordRequest {
  space_id: Int64;
  evaluator_record_ids?: Array<Int64>;
  /** 是否查询已删除的，默认不查询 */
  include_deleted?: boolean;
  Base?: base.Base;
}

export interface BatchGetEvaluatorRecordResponse {
  records: Array<evaluator.EvaluatorRecord>;
  BaseResp?: base.BaseResp;
}

export interface BatchGetEvaluatorRequest {
  space_id: Int64;
  evaluator_ids?: Array<Int64>;
  /** 是否查询已删除的评估器，默认不查询 */
  include_deleted?: boolean;
  Base?: base.Base;
}

export interface BatchGetEvaluatorResponse {
  evaluators?: Array<evaluator.Evaluator>;
  BaseResp?: base.BaseResp;
}

export interface BatchGetEvaluatorVersionRequest {
  space_id: Int64;
  evaluator_version_ids?: Array<Int64>;
  /** 是否查询已删除的评估器，默认不查询 */
  include_deleted?: boolean;
  Base?: base.Base;
}

export interface BatchGetEvaluatorVersionResponse {
  evaluators?: Array<evaluator.Evaluator>;
  BaseResp?: base.BaseResp;
}

export interface CheckEvaluatorNameRequest {
  space_id: Int64;
  name: string;
  evaluator_id?: Int64;
  Base?: base.Base;
}

export interface CheckEvaluatorNameResponse {
  pass?: boolean;
  message?: string;
  BaseResp?: base.BaseResp;
}

export interface CorrectEvaluatorRecordRequest {
  space_id: Int64;
  evaluator_record_id: Int64;
  correction: evaluator.Correction;
  Base?: base.Base;
}

export interface CorrectEvaluatorRecordResponse {
  record: evaluator.EvaluatorRecord;
  BaseResp?: base.BaseResp;
}

export interface CreateEvaluatorRequest {
  evaluator: evaluator.Evaluator;
  cid?: string;
  Base?: base.Base;
}

export interface CreateEvaluatorResponse {
  evaluator_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DebugEvaluatorRequest {
  /** 空间 id */
  space_id: Int64;
  /** 待调试评估器内容 */
  evaluator_content: evaluator.EvaluatorContent;
  /** 评测数据输入: 数据集行内容 + 评测目标输出内容与历史记录 + 评测目标的 trace */
  input_data: evaluator.EvaluatorInputData;
  evaluator_type: evaluator.EvaluatorType;
  Base?: base.Base;
}

export interface DebugEvaluatorResponse {
  /** 输出数据 */
  evaluator_output_data?: evaluator.EvaluatorOutputData;
  BaseResp?: base.BaseResp;
}

export interface DeleteEvaluatorRequest {
  evaluator_ids?: Array<Int64>;
  space_id: Int64;
  Base?: base.Base;
}

export interface DeleteEvaluatorResponse {
  BaseResp?: base.BaseResp;
}

export interface GetBuiltinTemplateInfoRequest {
  builtin_template_type: evaluator.BuiltinTemplateType;
  builtin_template_key: string;
  Base?: base.Base;
}

export interface GetBuiltinTemplateInfoResponse {
  builtin_template?: evaluator.EvaluatorContent;
  BaseResp?: base.BaseResp;
}

export interface GetDefaultPromptEvaluatorToolsRequest {
  Base?: base.Base;
}

export interface GetDefaultPromptEvaluatorToolsResponse {
  tools: Array<evaluator.ToolCombine>;
  BaseResp?: base.BaseResp;
}

export interface GetEvaluatorRecordRequest {
  space_id: Int64;
  evaluator_record_id: Int64;
  /** 是否查询已删除的，默认不查询 */
  include_deleted?: boolean;
  Base?: base.Base;
}

export interface GetEvaluatorRecordResponse {
  record: evaluator.EvaluatorRecord;
  BaseResp?: base.BaseResp;
}

export interface GetEvaluatorRequest {
  space_id: Int64;
  evaluator_id?: Int64;
  /** 是否查询已删除的评估器，默认不查询 */
  include_deleted?: boolean;
  Base?: base.Base;
}

export interface GetEvaluatorResponse {
  evaluator?: evaluator.Evaluator;
  BaseResp?: base.BaseResp;
}

export interface GetEvaluatorVersionRequest {
  space_id: Int64;
  evaluator_version_id: Int64;
  /** 是否查询已删除的评估器，默认不查询 */
  include_deleted?: boolean;
  Base?: base.Base;
}

export interface GetEvaluatorVersionResponse {
  evaluator?: evaluator.Evaluator;
  BaseResp?: base.BaseResp;
}

export interface ListBuiltinTemplateRequest {
  builtin_template_type: evaluator.BuiltinTemplateType;
  Base?: base.Base;
}

export interface ListBuiltinTemplateResponse {
  builtin_template_keys?: Array<evaluator.EvaluatorContent>;
  BaseResp?: base.BaseResp;
}

export interface ListEvaluatorRequest {
  space_id: Int64;
  search_name?: string;
  creator_ids?: Array<Int64>;
  evaluator_type?: Array<evaluator.EvaluatorType>;
  page_size?: Int64;
  page_num?: Int64;
  order_bys?: Array<common.OrderBy>;
  Base?: base.Base;
}

export interface ListEvaluatorResponse {
  evaluators?: Array<evaluator.Evaluator>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface ListEvaluatorVersionRequest {
  space_id: Int64;
  evaluator_id?: Int64;
  query_versions?: Array<string>;
  page_size?: Int64;
  page_num?: Int64;
  order_bys?: Array<common.OrderBy>;
  Base?: base.Base;
}

export interface ListEvaluatorVersionResponse {
  evaluator_versions?: Array<evaluator.EvaluatorVersion>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface RunEvaluatorRequest {
  /** 空间 id */
  space_id: Int64;
  /** 评测规则 id */
  evaluator_version_id: Int64;
  /** 评测数据输入: 数据集行内容 + 评测目标输出内容与历史记录 + 评测目标的 trace */
  input_data: evaluator.EvaluatorInputData;
  /** experiment id */
  experiment_id?: Int64;
  /** experiment run id */
  experiment_run_id?: Int64;
  item_id?: Int64;
  turn_id?: Int64;
  Base?: base.Base;
}

export interface RunEvaluatorResponse {
  Record: evaluator.EvaluatorRecord;
  BaseResp?: base.BaseResp;
}

export interface SubmitEvaluatorVersionRequest {
  space_id: Int64;
  evaluator_id: Int64;
  version: string;
  description?: string;
  cid?: string;
  Base?: base.Base;
}

export interface SubmitEvaluatorVersionResponse {
  evaluator?: evaluator.Evaluator;
  BaseResp?: base.BaseResp;
}

export interface UpdateEvaluatorDraftRequest {
  /** 评估器 id */
  evaluator_id: Int64;
  /** 空间 id */
  space_id: Int64;
  evaluator_content: evaluator.EvaluatorContent;
  evaluator_type: evaluator.EvaluatorType;
  Base?: base.Base;
}

export interface UpdateEvaluatorDraftResponse {
  evaluator?: evaluator.Evaluator;
  BaseResp?: base.BaseResp;
}

export interface UpdateEvaluatorMetaRequest {
  /** 评估器 id */
  evaluator_id: Int64;
  /** 空间 id */
  space_id: Int64;
  evaluator_type: evaluator.EvaluatorType;
  /** 展示用名称 */
  name?: string;
  /** 描述 */
  description?: string;
  Base?: base.Base;
}

export interface UpdateEvaluatorMetaResponse {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
