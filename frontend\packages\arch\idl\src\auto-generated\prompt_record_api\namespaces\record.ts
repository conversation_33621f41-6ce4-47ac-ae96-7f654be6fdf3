/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as copilot_common from './copilot_common';

export type Int64 = string | number;

export enum ActionType {
  MarkplaceTopUp = 1,
  ConsumeByRecord = 2,
  IncentiveTopUp = 3,
  OpPlatformTopUp = 4,
  RecordAutoTopUp = 5,
  MarkplaceRefund = 6,
  MarkplaceChargeBack = 7,
  MarkplaceChargeBackReverse = 8,
}

export enum AdminListConfParamType {
  ParamStruct = 1,
  SingleParam = 2,
}

export enum AdminListConfSortType {
  /** 升序 */
  Asc = 1,
  /** 降序 */
  Desc = 2,
}

export enum AgentType {
  Bot = 1,
  Workflow = 2,
}

export enum AggregratedType {
  Daily = 1,
}

export enum BabiBillStatus {
  Open = 1,
  Close = 2,
  ReOpen = 3,
}

export enum CheckConfOpGuardResultType {
  Pass = 1,
  Block = 2,
  Checking = 3,
}

export enum CheckConfOpReleaseTimeResultType {
  Pass = 1,
  Block = 2,
}

export enum CheckResourceResultType {
  Pass = 1,
  Block = 2,
}

export enum Dimension {
  User = 1,
  Model = 2,
  Space = 3,
  Chain = 4,
  Connector = 5,
  VolcAcc = 6,
  APISpace = 7,
  /** 用户维度限速（指定空间生效，和未指定空间的用户维度限速互斥，指定空间的优先级更高） */
  UserWithSpace = 101,
}

export enum HitLimitAction {
  Pass = 1,
  Block = 2,
  Downgrade = 3,
}

export enum LimitConfOpStatus {
  /** 处理中 */
  Processing = 0,
  /** 已完成 */
  Completed = 1,
  /** 已驳回 */
  Rejected = 2,
  /** 已取消 */
  Canceled = 3,
  /** 已回滚 */
  Rollback = 4,
}

export enum LimitConfOpType {
  BatchCreate = 1,
  Update = 2,
  BatchUpdate = 3,
  UpdateWithSpace = 4,
  BatchUpdateWithSpace = 5,
}

export enum LimitConfStatus {
  Effective = 0,
  Observe = 1,
}

export enum LimitType {
  QPS = 1,
  QPM = 2,
}

export enum MemberType {
  UnknownMemberType = 0,
  UserName = 1,
  UserId = 2,
}

export enum OwnerType {
  Developer = 1,
  ByteTreeNode = 2,
}

export enum PkgTypeScope {
  AllPkgType = 0,
  /** 内测包 */
  InhouseOrTest = 1,
  /** 正式包 */
  Release = 2,
}

export enum RecordScene {
  /** project入口 */
  ProjectEntrance = 1,
  /** 带fc的模型节点调用 */
  LLMNodeWithFC = 2,
}

export enum RecordType {
  Query = 1,
  Workflow = 2,
  Token = 3,
  Dataset = 4,
  QueryFinish = 5,
  Model = 6,
  Plugin = 7,
  /** project调试 */
  ProjectTest = 8,
  /** [上报类型][上报场景-3位][上报阶段-3位]
如QueryChat=1001001，1-表示query上报，001-表示对话，001-表示对话开始
对话开始请求 */
  ChatQueryStart = 1001001,
}

export enum ReportType {
  TokenReport = 1,
  QueryReport = 2,
}

export enum ResourceBlockType {
  /** 智能体调用按量余额不足 */
  VolcAgentReqOutOfBalance = 101001,
  /** 智能体调用超出RPM峰值 */
  VolcAgentReqOutOfRPM = 101002,
}

export enum ResourceType {
  CozeToken = 1,
  UserMessage = 2,
}

export enum ResultType {
  Pass = 1,
  ModelBlock = 2,
  UserBlock = 3,
  Downgrade = 4,
  CozeAPIKeyOwnerBlock = 5,
  /** coze token额度不足 */
  OutOfResource = 6,
  /** 空间维度限速 */
  SpaceBlock = 7,
  /** 回复降速 */
  SlowDown = 8,
  /** chain维度限速 */
  ChainBlock = 9,
  /** chain维度限速 */
  ConnectorBlock = 10,
  /** 火山账号维度限速（仅国内专业版有） */
  VolcAccBlock = 11,
}

export enum TimeUnit {
  Second = 1,
  Minute = 2,
  Hour = 3,
  Day = 4,
  Month = 5,
  Year = 6,
  TenSecond = 7,
}

export enum TrafficActionType {
  Pass = 1,
  Block = 2,
}

export enum TrafficCfgStatus {
  Deleted = 1,
  Deactivated = 2,
  Observe = 3,
  Effective = 4,
}

export enum TrafficParamKeyFormatType {
  Each = 1,
  All = 2,
}

export enum TrafficParamType {
  Number = 1,
  String = 2,
}

export enum TrafficParamValueConditionType {
  Null = 1,
  NotNull = 2,
  Valid = 3,
  Invalid = 4,
  InList = 5,
  NotInList = 6,
  InNumRange = 7,
  NotInNumRange = 8,
}

export enum TrafficResourceType {
  /** 火山智能体调用（coze专业版） */
  VolcAgentReq = 101,
}

export enum TrafficRuleType {
  CntWithTime = 1,
  CntWithTimeFromVolcAgtReq = 101,
}

export enum TypeOfUpdateConfWithOp {
  /** 配置 */
  ConfigConf = 1,
  /** 回滚 */
  RollbackConf = 2,
}

export enum UserMessageResourceID {
  BotAsAPI = 1,
  WebSDK = 2,
}

export enum WhitelistType {
  UnknownWhitelistType = 0,
  /** coze白名单，如果请求渠道是coze，且用户在白名单内，跳过所有限速 */
  Coze = 1,
}

export interface Action {
  /** 动作类型 */
  action_type?: ActionType;
  /** 动作对应的ID */
  action_id?: string;
  /** 更新余额对应的描述 */
  messsage?: string;
  /** 更新余额对应的starling key，通过starling key拉到对应文案，格式化amount字段 */
  starling_key?: string;
}

export interface AddModelAKReq {
  space_id?: string;
  model_ak?: ModelAK;
  Base?: base.Base;
}

export interface AddModelAKResp {
  code?: Int64;
  msg?: string;
}

export interface AdminCheckConfOpGuardData {
  result_type?: CheckConfOpGuardResultType;
  desc?: string;
}

export interface AdminCheckConfOpGuardReq {
  op_id?: string;
  op_creator?: string;
  'X-Jwt-Token-Acl-Acc'?: string;
  Base?: base.Base;
}

export interface AdminCheckConfOpGuardResp {
  data?: AdminCheckConfOpGuardData;
  code?: string;
  message?: string;
}

export interface AdminCheckConfOpReleaseTimeData {
  result_type?: CheckConfOpReleaseTimeResultType;
  desc?: string;
}

export interface AdminCheckConfOpReleaseTimeReq {
  op_id?: string;
  op_creator?: string;
  'X-Jwt-Token-Acl-Acc'?: string;
  Base?: base.Base;
}

export interface AdminCheckConfOpReleaseTimeResp {
  data?: AdminCheckConfOpReleaseTimeData;
  code?: string;
  message?: string;
}

export interface AdminListConfBpmDetail {
  id?: string;
  op_type?: string;
  op_status?: string;
  bpm?: string;
  op_operator?: string;
  op_reason?: string;
  create_time?: string;
  update_time?: string;
}

export interface AdminListConfDetail {
  id?: string;
  dimension?: string;
  connector?: string;
  model?: string;
  source?: string;
  scene?: string;
  pkg_type_scope?: string;
  limit?: string;
  space?: string;
  action?: string;
  active?: string;
  operator?: string;
  config_reason?: string;
  create_time?: string;
  update_time?: string;
  ori_limitation?: Int64;
  ori_time_unit?: number;
  ori_source?: number;
  ori_scene?: number;
  ori_pkg_type_scope?: number;
  ori_active?: boolean;
  volc_acc_id?: string;
  cfg_status?: string;
  ori_cfg_status?: number;
  lane_type?: string;
}

export interface AdminListConfParam {
  dimension?: Array<Dimension>;
  connector_id?: Array<string>;
  time_unit?: Array<TimeUnit>;
  model_id?: Array<string>;
  source?: Array<copilot_common.SourceType>;
  scene?: Array<copilot_common.LLMScene>;
  space_id?: Array<string>;
  action?: Array<HitLimitAction>;
  active?: boolean;
  pkg_type_scope?: Array<PkgTypeScope>;
  sort_type?: AdminListConfSortType;
  sort_field?: Array<string>;
}

export interface AdminUpdateConfOpReq {
  op_id?: string;
  op_status?: LimitConfOpStatus;
  'X-Jwt-Token-Acl-Acc'?: string;
  Base?: base.Base;
}

export interface AdminUpdateConfOpResp {
  code?: string;
  message?: string;
}

export interface AdminUpdateConfWithOpReq {
  op_id?: string;
  update_type?: TypeOfUpdateConfWithOp;
  'X-Jwt-Token-Acl-Acc'?: string;
  Base?: base.Base;
}

export interface AdminUpdateConfWithOpResp {
  code?: string;
  message?: string;
}

export interface APISpaceLiftInfo {
  connector_id?: Int64;
  pkg?: PkgTypeScope;
  lane_type?: string;
}

export interface CheckResourceResult {
  /** 检查结果 */
  result_type?: CheckResourceResultType;
  /** 当resulr=Block时，该字段有值 */
  block_info?: ResourceBlockInfo;
}

export interface CozeBalanceDetail {
  /** 资源类型 */
  resource_type?: ResourceType;
  /** 余额数量 */
  amount?: Int64;
  /** 最近的流水号 */
  last_transaction_id?: Int64;
  /** 最近更新时间戳 */
  updated_at?: Int64;
  /** 资源ID */
  resource_id?: string;
}

export interface CozeBillDetail {
  /** 账单的时间戳 */
  timestamp?: Int64;
  /** 聚合账单结束时间戳 */
  end_timestamp?: Int64;
  /** 资源类型 */
  resource_type?: ResourceType;
  /** 变动数量，为正是增加，为负是减少 */
  amount?: Int64;
  /** 更新余额对应的starling key，通过starling key拉到对应文案，格式化amount字段 */
  starling_key?: string;
  /** 更新余额对应的描述 */
  messsage?: string;
  /** 聚合账单时返回多个对应的actions，数据过多时不返回，不要取Action中的message和starling key */
  actions?: Array<Action>;
}

export interface DeleteModelAKReq {
  space_id?: string;
  ak_id?: string;
  Base?: base.Base;
}

export interface DeleteModelAKResp {
  code?: Int64;
  msg?: string;
}

export interface GetUserWithSpaceLimitReq {
  space_id?: string;
}

export interface GetUserWithSpaceLimitResp {
  limits?: Array<UserWithSpaceModelLimit>;
  code?: Int64;
  msg?: string;
}

export interface LimitConf {
  limit_type?: LimitType;
  limitation?: string;
}

export interface ListModelAKReq {
  space_id?: string;
  Base?: base.Base;
}

export interface ListModelAKResp {
  model_aks?: Array<ModelAK>;
  code?: Int64;
  msg?: string;
}

export interface MGetSpaceLimitReq {
  space_ids?: Array<string>;
}

export interface MGetSpaceLimitResp {
  limit_map?: Record<Int64, SpaceLimit>;
  code?: Int64;
  msg?: string;
}

export interface ModelAK {
  id?: string;
  model_family?: copilot_common.ModelFamily;
  name?: string;
  ak?: string;
  sk?: string;
  sts?: string;
}

export interface ModelTokenToCozeToken {
  /** 每1k输入token需要多少coze token */
  input_token_to_coze_token?: Int64;
  /** 每1k输出token需要多少coze token */
  output_token_to_coze_token?: Int64;
}

export interface NumRange {
  min_num?: string;
  max_num?: string;
}

export interface QueryReportInfo {
  cnt?: Int64;
}

export interface ReportInfo {
  /** 1-Token */
  report_type?: ReportType;
  /** // report_type=Token时，该字段有值 */
  token_info?: TokenReportInfo;
  /** report_type=Query时，该字段有值 */
  query_info?: QueryReportInfo;
}

export interface ResourceAmount {
  /** 资源类型 */
  resource_type?: ResourceType;
  /** 变动数量，为正是增加，为负是减少 */
  amount?: Int64;
}

export interface ResourceBlockInfo {
  block_type?: ResourceBlockType;
}

export interface ResourceRevertInfo {
  check_and_update_uuid?: string;
  check_and_update_timestamp?: Int64;
}

export interface SetSpaceLimitReq {
  limit_confs?: Array<LimitConf>;
  space_id?: string;
}

export interface SetSpaceLimitResp {
  code?: Int64;
  msg?: string;
}

export interface SetUserWithSpaceLimitReq {
  space_id?: string;
  limits?: Array<UserWithSpaceModelLimit>;
}

export interface SetUserWithSpaceLimitResp {
  code?: Int64;
  msg?: string;
}

export interface SlowDownConf {
  /** 首Token延长毫秒 */
  FirstTokenDelayMilliSec?: Int64;
  /** 包间距延长毫秒 */
  PacketDelayMilliSec?: Int64;
}

export interface SpaceLimit {
  can_limit?: boolean;
  need_limit?: boolean;
  limit_confs?: Array<LimitConf>;
}

export interface TimestampRange {
  /** 开始时间，毫秒级别时间戳 */
  start_timestamp_ms: Int64;
  /** 结束时间，毫秒级别时间戳 */
  end_timestamp_ms: Int64;
}

export interface TokenReportInfo {
  cnt?: Int64;
  input_cnt?: Int64;
  output_cnt?: Int64;
}

/** 指定空间生效的用户维度限速 */
export interface UserWithSpaceModelLimit {
  model_id?: string;
  model_name?: string;
  time_unit?: TimeUnit;
  limitation?: string;
  limitation_range?: NumRange;
  default_limitation?: string;
}

export interface WhitelistInfo {
  id?: string;
  whitelist_type?: string;
  member_type?: string;
  member?: string;
  description?: string;
}
/* eslint-enable */
