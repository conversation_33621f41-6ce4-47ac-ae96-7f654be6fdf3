// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameVariableInstance = "variable_instance"

// VariableInstance KV Memory
type VariableInstance struct {
	ID           int64  `gorm:"column:id;primaryKey;comment:主键ID" json:"id"`                                         // 主键ID
	BizType      int32  `gorm:"column:biz_type;not null;comment:1 for agent，2 for app" json:"biz_type"`              // 1 for agent，2 for app
	BizID        string `gorm:"column:biz_id;not null;comment:1 for agent_id，2 for app_id" json:"biz_id"`            // 1 for agent_id，2 for app_id
	Version      string `gorm:"column:version;not null;comment:agent or project 版本,为空代表草稿态" json:"version"`          // agent or project 版本,为空代表草稿态
	Keyword      string `gorm:"column:keyword;not null;comment:记忆的KEY" json:"keyword"`                               // 记忆的KEY
	Type         int32  `gorm:"column:type;not null;comment:记忆类型 1 KV 2 list" json:"type"`                           // 记忆类型 1 KV 2 list
	Content      string `gorm:"column:content;comment:记忆内容" json:"content"`                                          // 记忆内容
	ConnectorUID string `gorm:"column:connector_uid;not null;comment:二方用户ID" json:"connector_uid"`                   // 二方用户ID
	ConnectorID  int64  `gorm:"column:connector_id;not null;comment:二方id, e.g. coze = 10000010" json:"connector_id"` // 二方id, e.g. coze = 10000010
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间" json:"created_at"`      // 创建时间
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间" json:"updated_at"`      // 更新时间
}

// TableName VariableInstance's table name
func (*VariableInstance) TableName() string {
	return TableNameVariableInstance
}
