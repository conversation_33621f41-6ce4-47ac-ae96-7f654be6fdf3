/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 聚合方式 */
export enum AggregateType {
  Unknown = 1,
  Avg = 2,
  P50 = 3,
  P99 = 4,
  P999 = 5,
  P90 = 6,
  Sum = 7,
}

/** 降采样间隔 */
export enum DownsampleInterval {
  Unknown = 1,
  /** 30 second */
  DI30S = 2,
  /** 1 minute */
  DI1M = 3,
  /** 2 minute */
  DI2M = 4,
  /** 5 minute */
  DI5M = 5,
  /** 10 minute */
  DI10M = 6,
  /** 20 minute */
  DI20M = 7,
  /** 30 minute */
  DI30M = 8,
}

/** 运维视图的类型 */
export enum MetricsType {
  Unknown = 1,
  /** 每秒收到的查询次数，反映了该Bot的查询负载情况 */
  BotQueryQPS = 2,
  /** 查询成功率，反映了Bot在当前负载下的稳定性 */
  BotQuerySuccessRate = 3,
  /** 从被Bot收到查询到返回第一条Tokens的时延，反映了查询方可感知到的Bot响应速度 */
  BotQueryLatencyFirstResp = 4,
  /** 从Bot收到查询到返回全部Tokens的时延，反映了Bot的整体性能 */
  BotQueryLatency = 5,
  /** 每秒的工作流调用次数，反映了工作流的负载情况 */
  WorkflowRunQPS = 6,
  /** 工作流的调用成功率，反映了该工作流在当前负载下稳定性 */
  WorkflowRunSuccessRate = 7,
  /** 反映了工作流的执行耗时 */
  WorkflowRunLatency = 8,
  /** 每秒的插件调用次数。反映了插件负载情况 */
  PluginRunQPS = 9,
  /** 插件的调用成功率，反映了插件在当前负载下的稳定性 */
  PluginRunSuccessRate = 10,
  /** 插件的响应时长，反映了插件的整体性能 */
  PluginRunLatency = 11,
  /** 每秒的代码对应函数片段的被调用次数。反映了执行负载 */
  CodeRunQPS = 12,
  /** 代码对应函数的调用成功率，反映了对应负载下的稳定性 */
  CodeRunSuccessRate = 13,
  /** 代码对应函数的执行耗时，反映了整体性能 */
  CodeRunLatency = 14,
  /** 每秒知识库的召回次数，反映了知识库的召回负载 */
  KnowledgeRecallQPS = 15,
  /** 知识库的每次召回的片段数量 */
  KnowledgeRecallSegments = 16,
  /** 知识库的召回成功率，反映了知识库在当前负载下的稳定性情况 */
  KnowledgeRecallSuccessRate = 17,
  /** 知识库的召回耗时，反映了知识库的召回性能 */
  KnowledgeRecallLatency = 18,
  /** 每秒大模型的调用次数，反映了大模型的调用负载 */
  LLMQueryQPS = 19,
  /** 大模型的调用成功率，反映了大模型在当前负载下的稳定性 */
  LLMQuerySuccessRate = 20,
  /** 大模型的响应时长，反映了大模型的整体性能 */
  LLMQueryLatency = 21,
  /** 大模型每秒返回的Token数量 */
  LLMOutputTokensQPS = 22,
  /** 每秒的hook调用次数。反映了hook负载情况 */
  HookRunQPS = 23,
  /** hook的调用成功率，反映了hook在当前负载下的稳定性 */
  HookRunSuccessRate = 24,
  /** hook的平均耗时，反映了hook的整体性能 */
  HookRunLatency = 25,
}

export interface Curve {
  tag_kvs: Record<string, string>;
  points: Array<Point>;
}

export interface GetMetricsData {
  curves: Array<Curve>;
  /** 降采样间隔 */
  interval: DownsampleInterval;
}

export interface GetMetricsRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  /** s */
  start_time: string;
  /** s, end_at >= start_at */
  end_time: string;
  metrics_type: MetricsType;
  aggregate_type?: AggregateType;
  tag_kvs?: Record<string, Array<string>>;
  top_k?: number;
}

export interface GetMetricsResponse {
  data: GetMetricsData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTagVHistoricalOptionsData {
  options: Array<TagVOption>;
}

export interface GetTagVHistoricalOptionsRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  tag_k: string;
  choices?: Record<string, Array<string>>;
  /** s */
  start_time: string;
  /** s, end_at >= start_at */
  end_time: string;
  metrics_type: MetricsType;
  top_k?: number;
}

export interface GetTagVHistoricalOptionsResponse {
  data: GetTagVHistoricalOptionsData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTagVLatestOptionsData {
  options: Array<TagVOption>;
}

export interface GetTagVLatestOptionsRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  tag_k: string;
  choices?: Record<string, Array<string>>;
}

export interface GetTagVLatestOptionsResponse {
  data: GetTagVLatestOptionsData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface Point {
  x: Int64;
  x_alias: string;
  y: number;
}

export interface TagVOption {
  key: string;
  label: string;
}
/* eslint-enable */
