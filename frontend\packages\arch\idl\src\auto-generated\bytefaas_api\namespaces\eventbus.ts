/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CheckMqAppConsumerMetaRequest {
  /** PSM identifier */
  psm: string;
  /** Name of the MQ cluster */
  mq_cluster: string;
  /** Topic name in the MQ cluster */
  topic: string;
  /** Consumer group name */
  consumer_group: string;
}

export interface CheckMqAppConsumerMetaResponse {
  code?: number;
  error?: string;
  data?: string;
}

export interface GetMqAppConsumerMetaRequest {
  /** PSM identifier */
  psm: string;
}

export interface GetMqAppConsumerMetaResponse {
  code?: number;
  error?: string;
  data?: Array<common.ClusterMQConsumerMeta>;
}

export interface GetMqAppConsumerMetaStatusRequest {
  /** PSM identifier */
  psm: string;
}

export interface GetMqAppConsumerMetaStatusResponse {
  code?: number;
  error?: string;
  data?: MqAppConsumerMetaResult;
}

export interface MqAppConsumerMetaResult {
  /** PSM identifier */
  psm?: string;
  /** List of eventbus metadata for the consumer */
  eventbus_meta?: Array<MQConsumerMetaStatus>;
}

export interface MQConsumerMetaStatus {
  /** Region where the consumer group is located */
  region?: string;
  /** Name of the event (topic or eventbus event) */
  event_name?: string;
  /** Name of the consumer group */
  consumer_group?: string;
  /** Whether the consumer group metadata is updated */
  is_updated?: boolean;
}

export interface UpdateMqAppConsumerMetaStatusRequest {
  /** PSM identifier */
  psm: string;
}

export interface UpdateMqAppConsumerMetaStatusResponse {
  code?: number;
  error?: string;
  /** Result of updating eventbus consumer meta status */
  data?: MqAppConsumerMetaResult;
}
/* eslint-enable */
