/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as model from './model';

export type Int64 = string | number;

export enum AnnotateType {
  /** GSB评估规则场景, 仅包含badcase */
  BadCase = 1,
}

/** 主体类型 */
export enum AuthPrincipalType {
  Undefined = 0,
  /** 用户 */
  User = 1,
  /** 部门 */
  Department = 2,
}

export enum ComboType {
  ComboTypeUndefined = 0,
  ComboTypeAnd = 1,
  ComboTypeOr = 2,
}

export enum CompareType {
  Undefined = 0,
  /** SP 比较 */
  SystemPromptCmp = 1,
  /** 模型比较 */
  ModelCmp = 2,
  /** 自由比较 */
  SelfCmp = 3,
}

export enum ConditionType {
  ConditionTypeUndefined = 0,
  ConditionTypeExpr = 1,
  ConditionTypeCombo = 2,
}

export enum ContentType {
  Text = 1,
  Image = 2,
  Binary = 3,
  ImageVariable = 4,
}

export enum Env {
  Undefined = 0,
  /** boe */
  BOE = 1,
  /** onlien */
  Online = 2,
}

export enum EvalStrategyStrategyEnum {
  Undefined = 0,
  /** 一期仅支持AIPaas GSB */
  AIPassGSB = 1,
  /** 二期迁移Fornax */
  FornaxEval = 2,
}

export enum GrayReleaseFieldType {
  Undefined = 0,
  Uid = 1,
  Did = 2,
  CustomKey = 3,
}

export enum GrayReleaseStrategy {
  /** 不开启灰度 */
  None = 0,
  /** 实例灰度 */
  InstanceGrayRelease = 1,
}

export enum GSBResult {
  Undefined = 0,
  Good = 1,
  Same = 2,
  Bad = 3,
}

export enum MessageType {
  System = 1,
  User = 2,
  Assistant = 3,
  Tool = 4,
  Placeholder = 20,
}

/** 模型系列，特别指不同模型接入商 */
export enum ModelClass {
  Undefined = 0,
  /** gpt */
  GPT = 1,
  /** 字节 */
  SEED = 2,
  /** google */
  Gemini = 3,
  /** 亚马逊 */
  Claude = 4,
  /** 文心一言 */
  Ernie = 5,
  /** 百川 */
  Baichuan = 6,
  /** 阿里 */
  Qwen = 7,
  /** 智谱 */
  GML = 8,
  /** 深度求索 */
  DeepSeek = 9,
}

export enum OperatorType {
  OperatorTypeUndefined = 0,
  OperatorTypeEq = 1,
  OperatorTypeIn = 2,
  OperatorTypeNotIn = 3,
  OperatorTypeNotEq = 4,
  OperatorTypeGt = 5,
  OperatorTypeGte = 6,
  OperatorTypeLt = 7,
  OperatorTypeLte = 8,
  OperatorTypeIsNull = 9,
  OperatorTypeIsNotNull = 10,
}

export enum OptimizeEngine {
  Undefined = 0,
  AIPaas_GradientDescent = 1,
}

export enum OptimizeExecutionStatus {
  Undefined = 0,
  Init = 1,
  Running = 2,
  Canceled = 3,
  Fail = 4,
  Completed = 5,
}

export enum OptimizeTargetType {
  Prompt = 1,
}

export enum OrderField {
  CreateTime = 1,
  LastedPublishTime = 2,
}

/** Prompt 加密类型 */
export enum PromptEncryptOption {
  Undefined = 0,
  /** 加密且返回明文 */
  EncryptWithPlainText = 1,
  /** 加密且不返回明文 */
  EncryptWithoutPlainText = 2,
}

/** 提示词类型 */
export enum PromptType {
  Undefined = 0,
  /** 补全模式 */
  Completion = 1,
  /** 聊天模式 */
  Chat = 2,
  /** 补全模式V2 */
  Completion_V2 = 3,
  /** Prompt片段 */
  Segment = 4,
}

/** 发布状态 */
export enum PublishStatus {
  Undefined = 0,
  /** 未发布 */
  UnPublish = 1,
  /** 已发布 */
  Published = 2,
}

export enum ReleaseStatus {
  Undefined = 0,
  /** 在线 */
  Online = 1,
  /** 下线 */
  Offline = 2,
  /** 灰度中(废弃) */
  InGray = 3,
  /** 小流量 */
  Canary = 4,
  /** 单机房 */
  SingleDC = 5,
}

export enum ReleaseSubtaskStatus {
  /** 未开始 */
  PendingStart = 1,
  /** 进行中 */
  InProgress = 2,
  /** 执行成功待确认 */
  ExecuteSuccess = 3,
  /** 待审批 */
  PendingApproval = 4,
  /** 审批通过 */
  ApprovalPassed = 5,
  /** 审批驳回 */
  ApprovalRejected = 6,
  /** 已完成 */
  Finished = 7,
  /** 失败 */
  Failed = 8,
  /** 已跳过 */
  Skipped = 9,
  /** 已回滚 */
  Rollbacked = 10,
  /** 已取消 */
  Canceled = 11,
}

export enum ReleaseTaskStatus {
  /** 未开始 */
  PendingStart = 1,
  /** 进行中 */
  Inprogress = 2,
  /** 待审批 */
  PendingApproval = 3,
  /** 审批通过 */
  ApprovalPass = 4,
  /** 审批驳回 */
  ApprovalRejected = 5,
  /** 灰度发布中 */
  GrayReleasing = 6,
  /** 发布完成 */
  Finished = 7,
  /** 已取消 */
  Canceled = 8,
  /** 已回滚 */
  Rollbacked = 9,
}

export enum ReleaseType {
  Undefined = 0,
  /** 发布 */
  Release = 1,
  /** 回滚 */
  RollBack = 2,
  /** 下线 */
  Offline = 3,
  /** 灰度发布(废弃) */
  GrayRelease = 4,
  /** 灰度取消（废弃） */
  GrayCancel = 5,
  /** 小流量发布 */
  CanaryRelease = 6,
  /** 单机房发布 */
  SingleDCRelease = 7,
}

export enum ReplyType {
  /** 最终结果 */
  ReplyTypeFinalAnswer = 0,
  /** 工具调用 */
  ReplyTypeToolCall = 1,
}

export enum ReportEventType {
  Undefined = 0,
  /** 调试官方 Prompt */
  DebugOfficialPrompt = 1,
  /** 采用官方 Prompt */
  AdoptOfficialPrompt = 2,
}

/** 资源类型 */
export enum ResourceType {
  Undefined = 0,
  Space = 1,
  Prompt = 2,
  Application = 3,
  Evaluation = 4,
  Trace = 5,
  Agent = 6,
}

/** 密级标签 */
export enum SecurityLevel {
  Undefined = 0,
  L1 = 1,
  L2 = 2,
  L3 = 3,
  L4 = 4,
}

/** 空间角色类型 */
export enum SpaceRoleType {
  Undefined = 0,
  /** 负责人 */
  Owner = 1,
  /** 开发者 */
  Developer = 2,
  /** 测试人员 */
  Tester = 3,
}

/** 空间类型 */
export enum SpaceType {
  Undefined = 0,
  Personal = 1,
  Team = 2,
  /** 官方空间 */
  Official = 3,
}

export enum StreamState {
  /** 非流式 */
  StreamStateNone = 1,
  /** 流式传输开始（首包） */
  StreamStateBegin = 2,
  /** 流式传输中 */
  StreamStateStreaming = 3,
  /** 流失传输结束（尾包） */
  StreamStateEnd = 4,
}

export enum TenantType {
  /** 字节 */
  ByteDance = 0,
  /** 懂车帝 */
  Dcar = 1,
}

export enum ToolChoiceType {
  Auto = 1,
  None = 2,
  Specific = 3,
}

export enum ToolType {
  Function = 1,
  /** for gemini native tool */
  GoogleSearch = 2,
}

export enum TriggerOperation {
  /** 开始 */
  Start = 1,
  /** 审批通过 */
  ApprovalPass = 2,
  /** 审批驳回 */
  ApprovalReject = 3,
  /** 完成 */
  Finish = 4,
  /** 重试 */
  Retry = 5,
  /** 跳过 */
  Skip = 6,
  /** 下一步(目前自动触发，不需要前端触发) */
  Next = 7,
}

/** 变量类型 */
export enum VariableType {
  Undefined = 0,
  String = 1,
  /** 废弃，使用Number 不分区整数和浮点数 */
  Integer = 2,
  Boolean = 3,
  Number = 4,
  Array = 5,
  Object = 6,
  Placeholder = 7,
  Image = 8,
}

export interface AccessPointConfig {
  static_headers?: Array<AccessPointConfigDef>;
  dynamic_headers?: Array<AccessPointConfigDef>;
  static_params?: Array<AccessPointConfigDef>;
  dynamic_params?: Array<AccessPointConfigDef>;
}

export interface AccessPointConfigDef {
  key?: string;
  value?: string;
  is_required?: boolean;
}

export interface AllowList {
  /** 流量标识字段 */
  field?: GrayReleaseField;
  /** 名单取值 */
  values?: Array<string>;
}

export interface AnnotatedDataset {
  /** 标注数据集元信息 */
  meta?: AnnotateMeta;
  dataset_id?: Int64;
}

export interface AnnotateMeta {
  /** 数据标注类型 */
  type?: AnnotateType;
  /** 必填字段 */
  required_cols?: Array<string>;
}

export interface ApprovalInfo {
  /** 审批人 */
  approver?: string;
  /** 审批是否通过 */
  approval_pass?: boolean;
  /** 审批备注 */
  approval_comment?: string;
  /** 审批时间 */
  approval_time?: Int64;
}

/** 操作信息 */
export interface AuthAction {
  /** 唯一标识 */
  unique_key?: string;
  /** 操作展示名称 */
  name?: string;
  /** 实体类型，世纪不绑定实体，仅记录操作对象 */
  entity_type?: string;
}

/** 鉴权部门 */
export interface AuthDepartment {
  /** 部门ID */
  department_id?: string;
}

/** 鉴权资源，客体 */
export interface AuthEntity {
  /** 实体唯一ID */
  id?: string;
  /** 实体类型 */
  entity_type?: string;
}

/** 鉴权主体 */
export interface AuthPrincipal {
  /** 主体类型 */
  auth_principal_type?: AuthPrincipalType;
  /** 鉴权用户 */
  auth_user?: AuthUser;
  /** 鉴权部门 */
  auth_department?: AuthDepartment;
}

/** 角色信息 */
export interface AuthRole {
  /** 唯一标识 */
  unique_key?: string;
  /** 角色展示名称 */
  name?: string;
  /** 关联的Action列表 */
  actions?: Array<AuthAction>;
  /** 实体类型 */
  entity_type?: string;
}

/** 鉴权用户 */
export interface AuthUser {
  /** sso_username，与openUserID传一个即可 */
  sso_user_id?: string;
  /** fornax userID */
  open_user_id?: string;
  /** 租户类型 */
  tenant?: TenantType;
}

export interface BinaryContent {
  mime_type?: string;
  data?: Blob;
}

export interface BlockList {
  /** 流量标识字段 */
  field?: GrayReleaseField;
  /** 名单取值 */
  values?: Array<string>;
}

export interface ComboCondition {
  combo_type?: ComboType;
  conditions?: Array<Condition>;
}

/** 训练场比较配置 */
export interface CompareConfig {
  compare_type?: CompareType;
  /** JSON序列化存储比较内容 */
  compare_info?: string;
}

export interface Condition {
  /** 条件类型 */
  condition_type?: ConditionType;
  expr?: OperatorExpr;
  combo_condition?: ComboCondition;
}

export interface ContentPart {
  type?: ContentType;
  /** 文本内容 */
  text?: string;
  /** 图片URL */
  image?: Image;
  /** 二进制内容 */
  binary_content?: BinaryContent;
  /** 配置 */
  config?: ContentPartConfig;
}

export interface ContentPartConfig {
  image_resolution?: string;
}

export interface DebugBrief {
  /** 调试记录 ID */
  debug_id?: Int64;
  /** 调试 Prompt 缩略信息 */
  prompt_brief?: string;
  /** 响应结果，0表示成功 */
  status_code?: number;
  /** 请求发起时间（单步调试模式下对应第一步的开始时间） */
  start_time_ms?: Int64;
  /** 耗时 */
  cost_ms?: Int64;
  /** 响应最终完成时间（单步调试模式下对应最后一步的完成时间） */
  end_time_ms?: Int64;
  /** 输入 Token 数 */
  input_token?: Int64;
  /** 输出 Token 数 */
  output_token?: Int64;
}

export interface DebugDetail {
  /** 输入内容，预留 1-20
调试记录 ID */
  debug_id?: Int64;
  /** System Prompt和模型配置 */
  prompt?: Prompt;
  /** 用户输入消息 */
  message?: Message;
  /** 历史消息 */
  contexts?: Array<Message>;
  /** 变量值列表 */
  variables?: Array<Variable>;
  /** 操作人 UserID */
  operator?: string;
  /** 输出内容，预留 21-40 */
  log_id?: string;
  status_code?: number;
  status_message?: string;
  /** 请求发起时间 */
  start_time_ms?: Int64;
  /** 响应最终完成时间 */
  end_time_ms?: Int64;
  /** 耗时 */
  cost_ms?: Int64;
  /** 回复信息全文 */
  item?: ReplyItem;
  /** 参数替换后的 Prompt */
  real_prompt?: PromptText;
  /** 工具调用 */
  tool_calls?: Array<ToolCallCombine>;
}

export interface DebugMessage {
  message?: Message;
  debug_id?: string;
  output_token?: Int64;
  cost_ms?: Int64;
  input_token?: Int64;
  reasoning_content?: string;
}

export interface ErrInfo {
  log_id?: string;
  msg?: string;
}

export interface EvalCaseSource {
  /** 评估用例ID */
  eval_case_id?: Int64;
}

export interface EvalResult {
  gsb_result?: GSBResult;
}

export interface EvalResultStat {
  improve_cases_cnt?: number;
  reduced_cases_cnt?: number;
  constant_cases_cnt?: number;
}

export interface EvalStrategy {
  /** 评估 */
  strategy?: EvalStrategyStrategyEnum;
  /** 关联的Forna评估用例，评估策略为FornaxEval是赋值 */
  eval_case_source?: EvalCaseSource;
}

export interface Function {
  name?: string;
  description?: string;
  parameters?: string;
}

export interface FunctionCall {
  name?: string;
  arguments?: string;
}

export interface GrayReleaseBucket {
  /** 流量标识字段 */
  field?: GrayReleaseField;
  /** 灰度占比 */
  in_gray?: number;
  /** 灰度基数，如bucket_size=1000,in_gray=1,则1/1000的流量会命中灰度 */
  bucket_size?: number;
}

export interface GrayReleaseConfig {
  /** 白名单 */
  allow_list?: AllowList;
  /** 黑名单 */
  block_list?: BlockList;
  /** 灰度桶 */
  bucket?: GrayReleaseBucket;
  /** 过滤条件 */
  condition?: Condition;
}

export interface GrayReleaseField {
  /** 灰度字段类型 */
  type?: GrayReleaseFieldType;
  /** 灰度字段为customKey时，填写对应的key */
  custom_key?: string;
}

export interface Image {
  url?: string;
  uri?: string;
}

export interface ImageVariable {
  link?: Image;
  binary?: BinaryContent;
}

export interface Label {
  id?: Int64;
  name?: string;
}

export interface LarkAction {
  value?: string;
  name?: string;
}

export interface LarkCallbackEvent {
  type?: string;
  operator?: LarkOperator;
  token?: string;
  action?: LarkAction;
}

export interface LarkCallbackHeader {
  event_id?: string;
  token?: string;
  create_time?: string;
  event_type?: string;
  tenant_key?: string;
  app_id?: string;
}

export interface LarkCard {
  type?: string;
  data?: LarkCardTemplateData;
}

export interface LarkCardTemplateData {
  template_id?: string;
  template_variable?: Record<string, string>;
  template_version_name?: string;
}

export interface LarkOperator {
  tenant_key?: string;
  user_id?: string;
  open_id?: string;
}

export interface LarkToast {
  type?: string;
  content?: string;
}

export interface MCPServerCombine {
  mcp_server_id?: Int64;
  name?: string;
  tools?: Array<Tool>;
  access_point_id?: Int64;
  /** 环境，BOE/PPE/Online */
  env?: string;
  /** 泳道 */
  lane?: string;
  /** Server描述 */
  description?: string;
  /** 是否支持与prompt一起发布 */
  is_publish_supported?: boolean;
  /** MCP接入点配置 */
  access_point_config?: AccessPointConfig;
}

/** Message */
export interface Message {
  id?: Int64;
  message_type?: MessageType;
  content?: string;
  tool_calls?: Array<ToolCallCombine>;
  tool_call_id?: string;
  /** 消息内容分片 */
  parts?: Array<ContentPart>;
  /** 消息元数据 */
  metadata?: Record<string, string>;
  /** 加密后的消息内容 */
  encrypt_content?: string;
}

/** 模型信息 */
export interface Model {
  /** 模型ID */
  id?: Int64;
  /** 模型名称 */
  name?: string;
  /** 模型系列 */
  model_class?: ModelClass;
  /** 模型限额 */
  model_quota?: ModelQuota;
  temperature?: ModelParameter;
  top_k?: ModelParameter;
  top_p?: ModelParameter;
  function_call_enable?: boolean;
  json_mode_enable?: boolean;
}

/** 模型配置数据 */
export interface ModelConfig {
  /** 模型ID */
  id?: Int64;
  /** 模型名称 */
  name?: string;
  /** 模型系列 */
  model_class?: ModelClass;
  temperature?: number;
  max_tokens?: number;
  top_k?: number;
  top_p?: number;
  json_mode?: boolean;
  /** deprecated */
  function_call_mode?: boolean;
  presence_penalty?: number;
  frequency_penalty?: number;
  /** 模型提供方 */
  provider?: model.Provider;
  /** 模型提供方的模型唯一标识（应对saas无法传modelID的场景） */
  provider_model_id?: string;
  /** thinking参数 */
  thinking?: ThinkingConfig;
  /** 模型额外参数，会反序列化到具体调用模型的request中，优先级低于上述显式声明的参数 */
  extra?: string;
}

/** 模型参数 */
export interface ModelParameter {
  /** 模型参数名称 */
  name?: string;
  /** 默认值 */
  default_value?: string;
  /** 当前值 */
  value?: string;
  /** 步长 */
  step?: string;
  /** 最小值 */
  min?: string;
  /** 最大值 */
  max?: string;
}

/** 模型约束 */
export interface ModelQuota {
  /** token限制 */
  token_limit?: Int64;
}

export interface OperatorExpr {
  operator?: OperatorType;
  var?: string;
  const_val?: string;
}

export interface OptimizeEngineSource {
  /** 关联的任务ID */
  task_id?: string;
  optimize_engine?: OptimizeEngine;
}

export interface OptimizeExecution {
  id?: Int64;
  status?: OptimizeExecutionStatus;
  /** 固化DataSet */
  dataset?: AnnotatedDataset;
  /** run Task时候的优化对象实体snapshot，包含版本信息 */
  optimize_target?: OptimizeTarget;
  /** 结果评估评估策略snapshot */
  eval_startegy?: EvalStrategy;
  /** 关联的优化任务资源 */
  optimize_engine_source?: OptimizeEngineSource;
  /** 任务整体耗时 */
  cost_ms?: Int64;
  /** 任务开始执行时间 */
  start_time_ms?: Int64;
  /** 任务执行完成时间 */
  end_time_ms?: Int64;
  creator_id?: string;
  space_id?: Int64;
  log_id?: string;
  task_id?: Int64;
  /** 创建时间 */
  create_tsms?: Int64;
  /** 更新时间 */
  update_tsms?: Int64;
}

export interface OptimizeResult {
  /** 优化结果
在优化结果里的序列号 */
  sequence_id?: string;
  /** 关联的优化执行记录ID */
  execution_id?: Int64;
  /** 评估结果对象 */
  optimize_result?: OptimizeTarget;
  /** 评估结果
模型正确预测的样本比例或准确率 */
  accuracy?: number;
  /** 模型正确预测的样本比例或准确率 */
  eval_result_stat?: EvalResultStat;
  /** 一期评估结果结构, 二期预计复用Fornax评测Dashboard */
  row_eval_result_group?: Array<RowEvalResult>;
  /** 关联评测任务资源 */
  eval_case_source?: EvalCaseSource;
}

export interface OptimizeTarget {
  /** 待优化对象类型 */
  type?: OptimizeTargetType;
  /** prompt类型对应的数据结构 */
  prompt?: PromptOpTarget;
}

export interface OptimizeTask {
  /** Task ID */
  id?: Int64;
  /** Task名称 */
  display_name?: string;
  /** Task描述 */
  description?: string;
  /** 优化对象 */
  target?: OptimizeTarget;
  /** 创建者ID */
  creator?: string;
  /** 创建时间 */
  create_tsms?: Int64;
  /** 更新时间 */
  update_tsms?: Int64;
  /** 是否列表可见 */
  visible?: boolean;
  /** 标注数据集 */
  annotated_dataset?: AnnotatedDataset;
  /** 评估规则 */
  eval_strategy?: EvalStrategy;
  /** 优化引擎 */
  optimize_engine?: OptimizeEngine;
  space_id?: Int64;
  /** 关联的运行记录，list接口只返回最近一条 */
  executions?: Array<OptimizeExecution>;
}

export interface OrderParam {
  /** 排序字段 */
  order_by?: OrderField;
  /** 是否升序 */
  asc?: boolean;
}

/** Prompt */
export interface Prompt {
  /** Prompt ID */
  id?: Int64;
  /** 唯一标识 */
  prompt_key?: string;
  /** Prompt名称 */
  display_name?: string;
  /** Prompt描述 */
  description?: string;
  /** Prompt类型 */
  prompt_type?: PromptType;
  /** 创建者ID */
  creator?: string;
  /** 创建时间 */
  create_tsms?: Int64;
  /** 更新时间 */
  update_tsms?: Int64;
  /** 发布状态 */
  status?: PublishStatus;
  /** 版本 */
  version?: string;
  /** 模型配置 */
  model_config?: ModelConfig;
  /** PromptText */
  prompt_text?: PromptText;
  /** Prompt输入 */
  prompt_input?: PromptInput;
  /** 发布信息 */
  publish_info?: PromptPublishInfo;
  /** 标签 */
  labels?: Array<Label>;
  /** 热度 */
  heat?: Int64;
  /** 是否为草稿 */
  is_draft?: boolean;
  /** 草稿关联版本 */
  draft_base_version?: string;
  /** 工具定义 */
  tools?: Array<ToolCombine>;
  /** 发布最终信息 */
  infos?: Array<ReleaseInfo>;
  /** 密级标签 */
  security_level?: SecurityLevel;
  tool_call_config?: ToolCallConfig;
  /** 模版引擎的类型 */
  template_type?: string;
  /** 元数据 */
  metadata?: Record<string, string>;
  /** 是否嵌套Prompt片段 */
  has_segment?: boolean;
  /** 标签(仅平台接口使用，PromptHub暂时无法获取) */
  tags?: Array<Tag>;
  /** 是否批量调试 */
  is_batch_debug?: boolean;
  /** 创建人信息 */
  creator_info?: UserInfo;
  /** 草稿基于BaseVersion是否有编辑 */
  is_draft_edited?: boolean;
  /** MCP服务列表 */
  mcp_servers?: Array<MCPServerCombine>;
  /** 版本快照关联的上一个版本 */
  snapshot_base_version?: string;
}

/** Prompt 基础信息，不含版本相关的详情内容 */
export interface PromptBasic {
  /** Prompt ID */
  id?: Int64;
  /** 唯一标识 */
  prompt_key?: string;
  /** Prompt名称 */
  display_name?: string;
  /** Prompt描述 */
  description?: string;
  /** 创建者ID */
  creator?: string;
  /** 创建时间 */
  create_tsms?: Int64;
  /** 更新时间 */
  update_tsms?: Int64;
  /** 提交状态 */
  status?: PublishStatus;
  /** 最后提交版本 */
  last_publish_version?: string;
  /** 密级标签 */
  security_level?: SecurityLevel;
}

/** Prompt输入 */
export interface PromptInput {
  variables?: Array<Variable>;
}

export interface PromptOpTarget {
  /** Prompt唯一标识 */
  prompt_key?: string;
  /** 版本 */
  prompt_version?: string;
  /** Prompt内容，特定场景赋值 */
  prompt_text?: PromptText;
  /** Prompt名称 */
  prompt_name?: string;
  /** PromptID */
  prompt_id?: Int64;
}

/** 发布信息 */
export interface PromptPublishInfo {
  /** 发布者 */
  publisher?: string;
  /** 发布描述 */
  publish_description?: string;
  /** 发布时间 */
  publish_tsms?: Int64;
  /** 发布者详情 */
  publisher_info?: UserInfo;
}

/** PromptText */
export interface PromptText {
  /** System Prompt */
  system_prompt?: Message;
  /** User Prompt */
  user_prompt?: Message;
  /** MessageList */
  message_list?: Array<Message>;
}

export interface QualityInspectionInfo {
  /** 是否缺少质检策略 */
  is_guard_missing?: boolean;
  /** 质检id */
  guard_id?: Int64;
  /** 质检链接 */
  guard_url?: string;
}

export interface ReleaseApprovalConfig {
  /** 是否开启审核 */
  enable?: boolean;
  /** 灰度策略 */
  gray_release_strategy?: GrayReleaseStrategy;
}

export interface ReleaseConfig {
  /** 是否灰度发布(不再生效，废弃) */
  if_gray_release?: boolean;
  /** 灰度发布配置(不再生效，废弃) */
  gray_release_config?: GrayReleaseConfig;
  /** 是否逃逸审批 */
  approval_escape?: boolean;
  /** 逃逸审批备注 */
  approval_escape_comment?: string;
  /** 灰度发布策略 */
  gray_release_strategy?: GrayReleaseStrategy;
  /** 逃逸灰度发布备注 */
  gray_release_escape_comment?: string;
  /** 创建工单时空间是否开启发布审核 */
  space_approval_enable?: boolean;
  /** 创建工单时空间灰度发布策略 */
  space_gray_release_strategy?: GrayReleaseStrategy;
}

export interface ReleaseInfo {
  /** release id */
  id?: Int64;
  /** 版本 */
  version?: string;
  /** 上一个发布的版本 */
  pre_version?: string;
  /** 环境 */
  env?: Env;
  /** 泳道 */
  feature?: string;
  /** 发布人 */
  publisher?: string;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** 灰度发布配置 */
  gray_release_config?: GrayReleaseConfig;
  /** 发布状态 */
  status?: ReleaseStatus;
  /** 发布label */
  label?: string;
}

export interface ReleaseResource {
  prompt_id?: Int64;
  prompt_version?: string;
  prompt_pre_version?: string;
  release_id?: Int64;
}

export interface ReleaseSubtask {
  /** 子任务id */
  id?: Int64;
  /** 发布工单id */
  release_task_id?: Int64;
  /** 子任务唯一标识 */
  subtask_key?: string;
  /** 子任务状态 */
  status?: ReleaseSubtaskStatus;
  /** 开始人 */
  trigger_user?: string;
  /** 完成人 */
  finish_user?: string;
  /** 跳过人 */
  skip_user?: string;
  /** 审批信息 */
  approval_info?: ApprovalInfo;
  /** 错误信息 */
  err_info?: ErrInfo;
  /** 开始时间 */
  start_time?: Int64;
  /** 完成时间 */
  finish_time?: Int64;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** 子任务额外信息 */
  subtask_extra?: SubtaskExtra;
  /** crcc阶段id */
  crcc_stage_id?: string;
}

export interface ReleaseSubtaskConfig {
  /** 子任务唯一标识 */
  subtask_key?: string;
  /** 子任务展示名称 */
  display_name?: string;
  /** 子任务描述 */
  desc?: string;
  /** 是否可跳过 */
  skippable?: boolean;
  /** 是否可重试 */
  retryable?: boolean;
  /** 是否需要审批 */
  approval_required?: boolean;
}

export interface ReleaseTask {
  /** 发布工单id */
  id?: Int64;
  /** 空间id */
  space_id?: Int64;
  /** 发布资源列表 */
  release_resources?: Array<ReleaseResource>;
  /** 发布环境 */
  env?: Env;
  /** 发布泳道 */
  feature?: string;
  /** 发布配置 */
  release_config?: ReleaseConfig;
  /** 发布备注 */
  comment?: string;
  /** 发布人 */
  release_user?: string;
  /** 取消发布人 */
  cancel_user?: string;
  /** 回滚人 */
  rollback_user?: string;
  /** 发布状态 */
  status?: ReleaseTaskStatus;
  /** 子任务配置 */
  subtask_configs?: Array<ReleaseSubtaskConfig>;
  /** 当前子任务key */
  current_subtask_key?: string;
  /** 子任务列表(当前状态信息) */
  subtasks?: Record<string, ReleaseSubtask>;
  /** 是否可取消 */
  cancelable?: boolean;
  /** 是否可回滚 */
  rollbackable?: boolean;
  /** 开始时间 */
  start_time?: Int64;
  /** 完成时间 */
  finish_time?: Int64;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** 回调事件id */
  crcc_event_id?: string;
  /** 发布label */
  label?: string;
}

export interface ReplyItem {
  stream_state?: StreamState;
  reply_type?: ReplyType;
  content?: string;
  token_consumption?: TokenConsumption;
  tool_calls?: Array<ToolCallCombine>;
  debug_trace_key?: string;
  reasoning_content?: string;
  /** 多模态内容返回 */
  parts?: Array<ContentPart>;
  finish_reason?: string;
  prompt_version?: string;
  /** Tool执行结果 */
  tool_call_responses?: Array<ToolCallResponse>;
}

export interface ReportEvent {
  report_event_type?: ReportEventType;
  prompt_id?: Int64;
}

export interface RowEvalResult {
  input?: string;
  output_before?: string;
  /** 优化前的评估结果 */
  eval_before?: string;
  output_after?: string;
  /** 评估结果 */
  eval_result?: EvalResult;
  /** 额外变量 */
  variables?: Record<string, string>;
}

/** 空间 */
export interface Space {
  /** 空间ID */
  id?: Int64;
  /** 空间名称 */
  name?: string;
  /** 空间描述 */
  description?: string;
  /** 空间类型 */
  space_type?: SpaceType;
  /** 空间创建人 */
  creator?: string;
  /** 创建时间 */
  create_tsms?: Int64;
  /** 更新时间 */
  update_tsms?: Int64;
  /** 发布审核配置 */
  release_approval_config?: ReleaseApprovalConfig;
  /** 空间来源 */
  space_origin?: string;
}

/** 空间成员 */
export interface SpaceMember {
  /** 空间ID */
  space_id?: Int64;
  /** 成员 */
  member?: AuthPrincipal;
  /** 空间角色类型 */
  space_role_type?: SpaceRoleType;
}

/** 主体+客体+权限点，鉴权结果 */
export interface SubjectActionObjectAuthRes {
  /** 主体+客体+权限点 鉴权对 */
  subject_action_objects?: SubjectActionObjects;
  /** 是否允许 */
  is_allowed?: boolean;
}

/** 主体+客体+权限点，鉴权组合信息 */
export interface SubjectActionObjects {
  /** 主体，鉴权时通常为用户 */
  subject?: AuthPrincipal;
  /** 权限唯一标识 */
  action?: string;
  /** 客体列表，默认按照或的逻辑处理 */
  objects?: Array<AuthEntity>;
}

/** 主体+客体+角色 */
export interface SubjectRoleObject {
  /** 主体，授权时可以时用户或部门 */
  subject?: AuthPrincipal;
  /** 角色信息 */
  role?: AuthRole;
  /** 客体 */
  object?: AuthEntity;
}

export interface SubtaskExtra {
  /** 质检信息 */
  quality_inspection_info?: QualityInspectionInfo;
}

/** fornax 标签 */
export interface Tag {
  id?: Int64;
  name?: string;
}

export interface ThinkingConfig {
  /** 是否开启 */
  enabled?: boolean;
  /** thinking内容的最大输出token */
  budget_tokens?: Int64;
}

export interface TokenConsumption {
  input_token?: Int64;
  output_token?: Int64;
}

export interface Tool {
  type?: ToolType;
  function?: Function;
}

export interface ToolCall {
  id?: string;
  type?: ToolType;
  function_call?: FunctionCall;
}

export interface ToolCallCombine {
  tool_call?: ToolCall;
  /** mock 数据 */
  mock_response?: string;
}

export interface ToolCallConfig {
  tool_choice?: ToolChoiceType;
  specification?: ToolChoiceSpecification;
}

export interface ToolCallResponse {
  tool_call_id?: string;
  name?: string;
  content?: string;
}

export interface ToolChoiceSpecification {
  type?: ToolType;
  name?: string;
}

export interface ToolCombine {
  /** 工具定义 */
  tool_def?: Tool;
  /** mock 数据 */
  mock_response?: Array<string>;
  /** 是否禁用 */
  disable?: boolean;
}

export interface UserDebugConfig {
  /** 开启单步调试 */
  step_by_step?: boolean;
  /** Prompt 调试类型 */
  prompt_type?: PromptType;
}

export interface UserInfo {
  /** 姓名 */
  name?: string;
  /** 英文名称 */
  en_name?: string;
  /** 用户头像url */
  avatar_url?: string;
  /** 72 * 72 头像 */
  avatar_thumb?: string;
  /** 用户应用内唯一标识 */
  open_id?: string;
  /** 用户应用开发商内唯一标识 */
  union_id?: string;
  /** 企业标识 */
  tenant_key?: string;
  /** 用户在租户内的唯一标识 */
  user_id?: string;
  /** 用户邮箱 */
  email?: string;
  /** 租户 */
  tenant?: TenantType;
}

/** prompt变量 */
export interface Variable {
  /** 变量名字 */
  key?: string;
  /** 变量描述 */
  desc?: string;
  /** 变量类型 */
  variable_type?: VariableType;
  /** 是否必须 */
  is_required?: boolean;
  /** 变量值/mock值 */
  value?: string;
  placeholder_messages?: Array<Message>;
  image?: ImageVariable;
}
/* eslint-enable */
