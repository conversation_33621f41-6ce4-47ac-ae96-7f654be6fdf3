/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CreateTriggerDebugTplRequest {
  /** Service ID */
  service_id: string;
  /** Template type: custom/official */
  tpl_type?: string;
  /** List of CloudEvent messages */
  cloud_event?: Array<TriggerDebugCloudEvent>;
  /** Template name */
  name: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Message type: cloudevent/native */
  msg_type: string;
  /** List of native events */
  native_event?: Array<TriggerDebugNativeEvent>;
}

export interface CreateTriggerDebugTplResponse {
  code: number;
  data: TriggerDebugTplItem;
  error: string;
}

export interface DeleteTriggerDebugTplRequest {
  /** Service ID */
  service_id: string;
  /** Template ID */
  tpl_id: string;
}

export interface DeleteTriggerDebugTplResponse {
  code: number;
  data: boolean;
  error: string;
}

export interface EventBusTopicPreviewParams {
  /** EventBus event name */
  event: string;
  /** Search type: 0-时间范围 1-offset 2-key */
  search_type: number;
  /** Start time for the search */
  start_time?: Int64;
  /** End time for the search */
  end_time?: Int64;
  /** Start offset */
  start_offset?: Int64;
  /** End offset */
  end_offset?: Int64;
  /** Partition */
  partition?: string;
  /** Message key */
  msg_key?: string;
  /** List of storage descriptors */
  storage_descriptor?: Array<string>;
}

export interface GetTriggerDebugTplRequest {
  /** Service ID */
  service_id: string;
  /** Template type: custom/official */
  tpl_type?: string;
  /** Trigger type: timer/http/rocketmq/kafka/eventbus */
  trigger_type?: string;
}

export interface GetTriggerDebugTplResponse {
  code: number;
  data: Array<TriggerDebugTplItem>;
  error: string;
}

export interface KafkaTopicPreviewParams {
  /** Cluster name */
  cluster_name: string;
  /** Topic name */
  topic_name: string;
  /** Data type: str/pb */
  schema_type: string;
  /** Consumer type: whence/offset */
  consumer_type: string;
  /** Whence: latest/earliest/random */
  whence?: string;
  /** Relative offset */
  relative_offset?: Int64;
  /** Partition; if not specified, all partitions are included */
  partition?: string;
}

export interface MQQueueBaseInfo {
  /** Broker name */
  broker_name: string;
  /** Queue ID */
  queue_id: number;
  /** Topic name */
  topic: string;
}

export interface MQQueueInfoData {
  /** List of queue information */
  queue: Array<MQQueueItem>;
  /** List of data centers */
  dc: Array<string>;
}

export interface MQQueueInfoRequest {
  /** MQ region */
  mq_region: string;
  /** Region */
  region: string;
  /** MQ cluster name */
  cluster_name: string;
  /** Topic name */
  topic_name: string;
}

export interface MQQueueInfoResponse {
  code: number;
  data: MQQueueInfoData;
  error: string;
}

export interface MQQueueItem {
  /** Earliest message store time */
  earliest_msg_store_time: Int64;
  /** Maximum offset */
  max_offset: Int64;
  /** Minimum offset */
  min_offset: Int64;
  /** Message queue information */
  message_queue: MQQueueBaseInfo;
}

export interface MQTopicPreviewData {
  /** MQ message as JSON string */
  mq_msg?: string;
  /** 转换之后的cloudevent，如果数组里有多个元素则为批量消息 */
  cloud_event?: Array<TriggerDebugCloudEvent>;
  native_event?: Array<TriggerDebugNativeEvent>;
}

export interface MQTopicPreviewRequest {
  /** MQ type, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type: string;
  /** MQ region */
  mq_region: string;
  /** Service ID */
  service_id: string;
  /** Region */
  region: string;
  /** Cluster */
  cluster: string;
  /** Whether this is a batch message */
  is_batch_msg: boolean;
  /** Kafka topic preview parameters */
  kafka_topic_preview_params?: KafkaTopicPreviewParams;
  /** RocketMQ topic preview parameters */
  rocket_mq_topic_preview_params?: RocketmqTopicPreviewParams;
  /** EventBus topic preview parameters */
  eventbus_topic_preview_params?: EventBusTopicPreviewParams;
  /** Whether this is a native message */
  is_native_msg?: boolean;
}

export interface MQTopicPreviewResponse {
  code: number;
  data: MQTopicPreviewData;
  error: string;
}

export interface PatchTriggerDebugTplRequest {
  /** Service ID */
  service_id: string;
  /** Template ID */
  tpl_id: string;
  /** Template name */
  name?: string;
  /** List of CloudEvent messages */
  cloud_event?: Array<TriggerDebugCloudEvent>;
  /** Message type */
  msg_type?: string;
  /** List of native events */
  native_event?: Array<TriggerDebugNativeEvent>;
}

export interface PatchTriggerDebugTplResponse {
  code: number;
  data: TriggerDebugTplItem;
  error: string;
}

export interface RocketmqTopicPreviewParams {
  /** Cluster name */
  cluster_name: string;
  /** Topic name */
  topic_name: string;
  /** Pull type: 0-从最久的offset 1-从最新的offset 3-指定offset 4-指定时间戳 5-指定messageid */
  type: number;
  /** For pull types 3/4, specify direction: true for forward, false for backward */
  forward?: boolean;
  /** Number of messages to pull from a single queue */
  msg_num: Int64;
  /** For pull type 5, specify the message ID */
  message_id?: string;
  /** IDC */
  idc?: string;
  /** Broker name */
  broker_name?: string;
  /** Queue ID */
  queue_id?: string;
  /** Body encoding */
  body_encode?: string;
  /** Timestamp */
  time_stamp?: Int64;
  /** Offset */
  offset?: Int64;
}

export interface TriggerDebugCloudEvent {
  /** JSON string of CloudEvent extensions */
  extensions: string;
  /** CloudEvent data payload */
  data: string;
}

export interface TriggerDebugNativeBMQMessage {
  /** Message key */
  key: string;
  /** Message value */
  value: string;
  /** Topic name */
  topic: string;
  /** Message offset */
  offset: Int64;
  /** Message timestamp */
  timestamp: Int64;
}

export interface TriggerDebugNativeEvent {
  /** MQ event ID */
  mq_event_id: string;
  /** Cluster name */
  cluster: string;
  /** Consumer group name */
  consumer_group: string;
  /** Maximum retry number */
  max_retry_num: number;
  /** Number of retries for bad status requests */
  retries_for_bad_status_requests: number;
  /** Number of retries for error requests */
  retries_for_error_requests: number;
  /** Native RMQ message */
  rmq_native_message: TriggerDebugNativeRMQMessage;
  /** Native BMQ message */
  bmq_native_message: TriggerDebugNativeBMQMessage;
  /** Message body */
  msg_body: string;
}

export interface TriggerDebugNativeRMQMessage {
  /** Native RMQ message */
  msg: TriggerDebugNativeRMQMessageMsg;
  /** Message queue information */
  messageQueue: TriggerDebugNativeRMQMessageQueue;
  /** Size of the stored message */
  storeSize: number;
  /** Offset in the queue */
  queueOffset: Int64;
  /** Offset in the commit log */
  commitLogOffset: Int64;
  /** System flag */
  sysFlag: number;
  /** Timestamp when the message was born */
  bornTimestamp: Int64;
  /** Host where the message was born */
  bornHost: string;
  /** Timestamp when the message was stored */
  storeTimestamp: Int64;
  /** Host where the message was stored */
  storeHost: string;
  /** Message ID */
  msgId: string;
  /** Body CRC */
  bodyCRC: number;
  /** Offset message ID */
  offsetMsgId: string;
}

export interface TriggerDebugNativeRMQMessageMsg {
  /** Topic name */
  topic: string;
  /** Message flag */
  flag: string;
  /** Message properties as key-value pairs */
  properties: Record<string, string>;
  /** Message tags */
  tags: string;
  /** Message keys */
  keys: string;
}

export interface TriggerDebugNativeRMQMessageQueue {
  /** Topic name */
  topic: string;
  /** Broker name */
  brokerName: string;
  /** Queue ID */
  queueId: number;
}

export interface TriggerDebugRequest {
  /** Service ID */
  service_id: string;
  /** Region of the service */
  region: string;
  /** Cluster of the service */
  cluster: string;
  /** Zone of the service */
  zone?: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** List of CloudEvent messages */
  cloud_event?: Array<TriggerDebugCloudEvent>;
  /** Whether this is a batch message; if cloud_event has more than one element, this is true */
  is_batch_msg: boolean;
  /** Whether this is a native message */
  is_native_msg?: boolean;
  /** List of native events */
  native_event?: Array<TriggerDebugNativeEvent>;
}

export interface TriggerDebugResponse {
  code: number;
  data: TriggerDebugResponseData;
  error: string;
}

export interface TriggerDebugResponseData {
  /** Log ID for the debug session */
  log_id: string;
  /** Status of the debug (success/failed) */
  status: string;
  /** HTTP headers returned */
  http_headers?: Record<string, string>;
  /** HTTP status code */
  http_code?: number;
  /** HTTP response body */
  http_body?: string;
  /** CPU usage during execution */
  cpu_usage?: string;
  /** Memory usage during execution */
  mem_usage?: string;
  /** Duration of execution */
  execution_duration?: string;
  /** Name of the pod used for execution */
  pod_name?: string;
  /** Logs from the debug session */
  logs: Array<string>;
  /** List of CloudEvent messages used in debug */
  cloud_event: Array<TriggerDebugCloudEvent>;
  /** Failure message if any */
  failed_message: string;
  /** Argos log link for the debug session */
  argos_log_link: string;
  /** Pod zone */
  pod_zone?: string;
  /** List of native events used in debug */
  native_event?: Array<TriggerDebugNativeEvent>;
}

export interface TriggerDebugTplItem {
  /** Service ID */
  service_id: string;
  /** Template name */
  name: string;
  /** Template type */
  tpl_type: string;
  /** Creator of the template */
  creator: string;
  /** List of CloudEvent messages; if multiple, represents batch messages */
  cloud_event: Array<TriggerDebugCloudEvent>;
  /** Creation timestamp */
  created_at: string;
  /** Update timestamp */
  updated_at: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Message type */
  msg_type: string;
  /** Template ID */
  id: string;
  /** List of native events; if present, represents native messages */
  native_event?: Array<TriggerDebugNativeEvent>;
}
/* eslint-enable */
