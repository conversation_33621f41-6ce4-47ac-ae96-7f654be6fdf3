// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/knowledge/internal/dal/model"
)

func newKnowledgeDocument(db *gorm.DB, opts ...gen.DOOption) knowledgeDocument {
	_knowledgeDocument := knowledgeDocument{}

	_knowledgeDocument.knowledgeDocumentDo.UseDB(db, opts...)
	_knowledgeDocument.knowledgeDocumentDo.UseModel(&model.KnowledgeDocument{})

	tableName := _knowledgeDocument.knowledgeDocumentDo.TableName()
	_knowledgeDocument.ALL = field.NewAsterisk(tableName)
	_knowledgeDocument.ID = field.NewInt64(tableName, "id")
	_knowledgeDocument.KnowledgeID = field.NewInt64(tableName, "knowledge_id")
	_knowledgeDocument.Name = field.NewString(tableName, "name")
	_knowledgeDocument.FileExtension = field.NewString(tableName, "file_extension")
	_knowledgeDocument.DocumentType = field.NewInt32(tableName, "document_type")
	_knowledgeDocument.URI = field.NewString(tableName, "uri")
	_knowledgeDocument.Size = field.NewInt64(tableName, "size")
	_knowledgeDocument.SliceCount = field.NewInt64(tableName, "slice_count")
	_knowledgeDocument.CharCount = field.NewInt64(tableName, "char_count")
	_knowledgeDocument.CreatorID = field.NewInt64(tableName, "creator_id")
	_knowledgeDocument.SpaceID = field.NewInt64(tableName, "space_id")
	_knowledgeDocument.CreatedAt = field.NewInt64(tableName, "created_at")
	_knowledgeDocument.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_knowledgeDocument.DeletedAt = field.NewField(tableName, "deleted_at")
	_knowledgeDocument.SourceType = field.NewInt32(tableName, "source_type")
	_knowledgeDocument.Status = field.NewInt32(tableName, "status")
	_knowledgeDocument.FailReason = field.NewString(tableName, "fail_reason")
	_knowledgeDocument.ParseRule = field.NewField(tableName, "parse_rule")
	_knowledgeDocument.TableInfo = field.NewField(tableName, "table_info")

	_knowledgeDocument.fillFieldMap()

	return _knowledgeDocument
}

// knowledgeDocument 知识库文档表
type knowledgeDocument struct {
	knowledgeDocumentDo

	ALL           field.Asterisk
	ID            field.Int64  // 主键ID
	KnowledgeID   field.Int64  // 所属knowledge的ID
	Name          field.String // 文档名称
	FileExtension field.String // 文档类型, txt/pdf/csv/...
	DocumentType  field.Int32  // 文档类型: 0:文本 1:表格 2:图片
	URI           field.String // 资源uri
	Size          field.Int64  // 文档大小
	SliceCount    field.Int64  // 分片数量
	CharCount     field.Int64  // 字符数
	CreatorID     field.Int64  // 创建者ID
	SpaceID       field.Int64  // 空间id
	CreatedAt     field.Int64  // Create Time in Milliseconds
	UpdatedAt     field.Int64  // Update Time in Milliseconds
	DeletedAt     field.Field  // Delete Time in Milliseconds
	SourceType    field.Int32  // 0:本地文件上传, 2:自定义文本
	Status        field.Int32  // 状态
	FailReason    field.String // 失败原因
	ParseRule     field.Field  // 解析+切片规则
	TableInfo     field.Field  // 表格信息

	fieldMap map[string]field.Expr
}

func (k knowledgeDocument) Table(newTableName string) *knowledgeDocument {
	k.knowledgeDocumentDo.UseTable(newTableName)
	return k.updateTableName(newTableName)
}

func (k knowledgeDocument) As(alias string) *knowledgeDocument {
	k.knowledgeDocumentDo.DO = *(k.knowledgeDocumentDo.As(alias).(*gen.DO))
	return k.updateTableName(alias)
}

func (k *knowledgeDocument) updateTableName(table string) *knowledgeDocument {
	k.ALL = field.NewAsterisk(table)
	k.ID = field.NewInt64(table, "id")
	k.KnowledgeID = field.NewInt64(table, "knowledge_id")
	k.Name = field.NewString(table, "name")
	k.FileExtension = field.NewString(table, "file_extension")
	k.DocumentType = field.NewInt32(table, "document_type")
	k.URI = field.NewString(table, "uri")
	k.Size = field.NewInt64(table, "size")
	k.SliceCount = field.NewInt64(table, "slice_count")
	k.CharCount = field.NewInt64(table, "char_count")
	k.CreatorID = field.NewInt64(table, "creator_id")
	k.SpaceID = field.NewInt64(table, "space_id")
	k.CreatedAt = field.NewInt64(table, "created_at")
	k.UpdatedAt = field.NewInt64(table, "updated_at")
	k.DeletedAt = field.NewField(table, "deleted_at")
	k.SourceType = field.NewInt32(table, "source_type")
	k.Status = field.NewInt32(table, "status")
	k.FailReason = field.NewString(table, "fail_reason")
	k.ParseRule = field.NewField(table, "parse_rule")
	k.TableInfo = field.NewField(table, "table_info")

	k.fillFieldMap()

	return k
}

func (k *knowledgeDocument) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := k.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (k *knowledgeDocument) fillFieldMap() {
	k.fieldMap = make(map[string]field.Expr, 19)
	k.fieldMap["id"] = k.ID
	k.fieldMap["knowledge_id"] = k.KnowledgeID
	k.fieldMap["name"] = k.Name
	k.fieldMap["file_extension"] = k.FileExtension
	k.fieldMap["document_type"] = k.DocumentType
	k.fieldMap["uri"] = k.URI
	k.fieldMap["size"] = k.Size
	k.fieldMap["slice_count"] = k.SliceCount
	k.fieldMap["char_count"] = k.CharCount
	k.fieldMap["creator_id"] = k.CreatorID
	k.fieldMap["space_id"] = k.SpaceID
	k.fieldMap["created_at"] = k.CreatedAt
	k.fieldMap["updated_at"] = k.UpdatedAt
	k.fieldMap["deleted_at"] = k.DeletedAt
	k.fieldMap["source_type"] = k.SourceType
	k.fieldMap["status"] = k.Status
	k.fieldMap["fail_reason"] = k.FailReason
	k.fieldMap["parse_rule"] = k.ParseRule
	k.fieldMap["table_info"] = k.TableInfo
}

func (k knowledgeDocument) clone(db *gorm.DB) knowledgeDocument {
	k.knowledgeDocumentDo.ReplaceConnPool(db.Statement.ConnPool)
	return k
}

func (k knowledgeDocument) replaceDB(db *gorm.DB) knowledgeDocument {
	k.knowledgeDocumentDo.ReplaceDB(db)
	return k
}

type knowledgeDocumentDo struct{ gen.DO }

type IKnowledgeDocumentDo interface {
	gen.SubQuery
	Debug() IKnowledgeDocumentDo
	WithContext(ctx context.Context) IKnowledgeDocumentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IKnowledgeDocumentDo
	WriteDB() IKnowledgeDocumentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IKnowledgeDocumentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IKnowledgeDocumentDo
	Not(conds ...gen.Condition) IKnowledgeDocumentDo
	Or(conds ...gen.Condition) IKnowledgeDocumentDo
	Select(conds ...field.Expr) IKnowledgeDocumentDo
	Where(conds ...gen.Condition) IKnowledgeDocumentDo
	Order(conds ...field.Expr) IKnowledgeDocumentDo
	Distinct(cols ...field.Expr) IKnowledgeDocumentDo
	Omit(cols ...field.Expr) IKnowledgeDocumentDo
	Join(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentDo
	Group(cols ...field.Expr) IKnowledgeDocumentDo
	Having(conds ...gen.Condition) IKnowledgeDocumentDo
	Limit(limit int) IKnowledgeDocumentDo
	Offset(offset int) IKnowledgeDocumentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeDocumentDo
	Unscoped() IKnowledgeDocumentDo
	Create(values ...*model.KnowledgeDocument) error
	CreateInBatches(values []*model.KnowledgeDocument, batchSize int) error
	Save(values ...*model.KnowledgeDocument) error
	First() (*model.KnowledgeDocument, error)
	Take() (*model.KnowledgeDocument, error)
	Last() (*model.KnowledgeDocument, error)
	Find() ([]*model.KnowledgeDocument, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.KnowledgeDocument, err error)
	FindInBatches(result *[]*model.KnowledgeDocument, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.KnowledgeDocument) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IKnowledgeDocumentDo
	Assign(attrs ...field.AssignExpr) IKnowledgeDocumentDo
	Joins(fields ...field.RelationField) IKnowledgeDocumentDo
	Preload(fields ...field.RelationField) IKnowledgeDocumentDo
	FirstOrInit() (*model.KnowledgeDocument, error)
	FirstOrCreate() (*model.KnowledgeDocument, error)
	FindByPage(offset int, limit int) (result []*model.KnowledgeDocument, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IKnowledgeDocumentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (k knowledgeDocumentDo) Debug() IKnowledgeDocumentDo {
	return k.withDO(k.DO.Debug())
}

func (k knowledgeDocumentDo) WithContext(ctx context.Context) IKnowledgeDocumentDo {
	return k.withDO(k.DO.WithContext(ctx))
}

func (k knowledgeDocumentDo) ReadDB() IKnowledgeDocumentDo {
	return k.Clauses(dbresolver.Read)
}

func (k knowledgeDocumentDo) WriteDB() IKnowledgeDocumentDo {
	return k.Clauses(dbresolver.Write)
}

func (k knowledgeDocumentDo) Session(config *gorm.Session) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Session(config))
}

func (k knowledgeDocumentDo) Clauses(conds ...clause.Expression) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Clauses(conds...))
}

func (k knowledgeDocumentDo) Returning(value interface{}, columns ...string) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Returning(value, columns...))
}

func (k knowledgeDocumentDo) Not(conds ...gen.Condition) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Not(conds...))
}

func (k knowledgeDocumentDo) Or(conds ...gen.Condition) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Or(conds...))
}

func (k knowledgeDocumentDo) Select(conds ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Select(conds...))
}

func (k knowledgeDocumentDo) Where(conds ...gen.Condition) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Where(conds...))
}

func (k knowledgeDocumentDo) Order(conds ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Order(conds...))
}

func (k knowledgeDocumentDo) Distinct(cols ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Distinct(cols...))
}

func (k knowledgeDocumentDo) Omit(cols ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Omit(cols...))
}

func (k knowledgeDocumentDo) Join(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Join(table, on...))
}

func (k knowledgeDocumentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.LeftJoin(table, on...))
}

func (k knowledgeDocumentDo) RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.RightJoin(table, on...))
}

func (k knowledgeDocumentDo) Group(cols ...field.Expr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Group(cols...))
}

func (k knowledgeDocumentDo) Having(conds ...gen.Condition) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Having(conds...))
}

func (k knowledgeDocumentDo) Limit(limit int) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Limit(limit))
}

func (k knowledgeDocumentDo) Offset(offset int) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Offset(offset))
}

func (k knowledgeDocumentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Scopes(funcs...))
}

func (k knowledgeDocumentDo) Unscoped() IKnowledgeDocumentDo {
	return k.withDO(k.DO.Unscoped())
}

func (k knowledgeDocumentDo) Create(values ...*model.KnowledgeDocument) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Create(values)
}

func (k knowledgeDocumentDo) CreateInBatches(values []*model.KnowledgeDocument, batchSize int) error {
	return k.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (k knowledgeDocumentDo) Save(values ...*model.KnowledgeDocument) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Save(values)
}

func (k knowledgeDocumentDo) First() (*model.KnowledgeDocument, error) {
	if result, err := k.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocument), nil
	}
}

func (k knowledgeDocumentDo) Take() (*model.KnowledgeDocument, error) {
	if result, err := k.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocument), nil
	}
}

func (k knowledgeDocumentDo) Last() (*model.KnowledgeDocument, error) {
	if result, err := k.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocument), nil
	}
}

func (k knowledgeDocumentDo) Find() ([]*model.KnowledgeDocument, error) {
	result, err := k.DO.Find()
	return result.([]*model.KnowledgeDocument), err
}

func (k knowledgeDocumentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.KnowledgeDocument, err error) {
	buf := make([]*model.KnowledgeDocument, 0, batchSize)
	err = k.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (k knowledgeDocumentDo) FindInBatches(result *[]*model.KnowledgeDocument, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return k.DO.FindInBatches(result, batchSize, fc)
}

func (k knowledgeDocumentDo) Attrs(attrs ...field.AssignExpr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Attrs(attrs...))
}

func (k knowledgeDocumentDo) Assign(attrs ...field.AssignExpr) IKnowledgeDocumentDo {
	return k.withDO(k.DO.Assign(attrs...))
}

func (k knowledgeDocumentDo) Joins(fields ...field.RelationField) IKnowledgeDocumentDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Joins(_f))
	}
	return &k
}

func (k knowledgeDocumentDo) Preload(fields ...field.RelationField) IKnowledgeDocumentDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Preload(_f))
	}
	return &k
}

func (k knowledgeDocumentDo) FirstOrInit() (*model.KnowledgeDocument, error) {
	if result, err := k.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocument), nil
	}
}

func (k knowledgeDocumentDo) FirstOrCreate() (*model.KnowledgeDocument, error) {
	if result, err := k.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocument), nil
	}
}

func (k knowledgeDocumentDo) FindByPage(offset int, limit int) (result []*model.KnowledgeDocument, count int64, err error) {
	result, err = k.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = k.Offset(-1).Limit(-1).Count()
	return
}

func (k knowledgeDocumentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = k.Count()
	if err != nil {
		return
	}

	err = k.Offset(offset).Limit(limit).Scan(result)
	return
}

func (k knowledgeDocumentDo) Scan(result interface{}) (err error) {
	return k.DO.Scan(result)
}

func (k knowledgeDocumentDo) Delete(models ...*model.KnowledgeDocument) (result gen.ResultInfo, err error) {
	return k.DO.Delete(models)
}

func (k *knowledgeDocumentDo) withDO(do gen.Dao) *knowledgeDocumentDo {
	k.DO = *do.(*gen.DO)
	return k
}
