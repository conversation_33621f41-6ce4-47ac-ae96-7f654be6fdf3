/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum FieldItemType {
  /** 文本 */
  Text = 1,
  /** 数字 */
  Number = 2,
  /** 时间 */
  Date = 3,
  /** float */
  Float = 4,
  /** bool */
  Boolean = 5,
}

export interface AddTableRequest {
  bot_id: string;
  name: string;
  desc: string;
  field_list?: Array<FieldItem>;
}

export interface AddTableResponse {
  code?: Int64;
  msg?: string;
  table_id?: string;
}

export interface AlterTableRequest {
  bot_id: string;
  table_id: string;
  name: string;
  desc: string;
  field_list?: Array<FieldItem>;
}

export interface AlterTableResponse {
  code?: Int64;
  msg?: string;
  table_id?: string;
}

export interface DeleteTableRequest {
  bot_id: string;
  table_id: string;
}

export interface DeleteTableResponse {
  code?: Int64;
  msg?: string;
  table_id?: string;
}

export interface FieldItem {
  name?: string;
  desc?: string;
  type?: FieldItemType;
  must_required?: boolean;
  /** 字段Id 新增为0 */
  id?: Int64;
}

export interface ListTableRequest {
  bot_id: string;
  table_id: Array<string>;
}

export interface ListTableResponse {
  code?: Int64;
  msg?: string;
  table_infos?: Array<TableInfo>;
}

export interface TableInfo {
  table_id?: string;
  name?: string;
  desc?: string;
  field_list?: Array<FieldItem>;
}

export interface TableInfoAddRequest {
  bot_id?: string;
  table_id?: string;
  data_list?: Array<Record<string, string>>;
}

export interface TableInfoAddResponse {
  code?: Int64;
  msg?: string;
  table_id?: string;
}

export interface TableInfoDeleteRequest {
  bot_id?: string;
  table_id?: string;
  data_ids?: Array<string>;
}

export interface TableInfoDeleteResponse {
  code?: Int64;
  msg?: string;
  table_id?: string;
  data_ids?: Array<string>;
}

export interface TableInfoQueryRequest {
  keyword?: string;
  bot_id?: string;
  table_id?: string;
  offset?: Int64;
  limit?: Int64;
}

export interface TableInfoQueryResponse {
  code?: Int64;
  msg?: string;
  data_list?: Array<Record<string, string>>;
}

export interface TableInfoUpdateRequest {
  bot_id?: string;
  table_id?: string;
  data_list?: Array<Record<string, string>>;
}

export interface TableInfoUpdateResponse {
  code?: Int64;
  msg?: string;
  table_id?: string;
}
/* eslint-enable */
