/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface AbortBuildRequest {
  /** cluster name */
  cluster: string;
  /** region */
  region: string;
  /** Number of revision */
  revision_number: string;
  /** ID of service */
  service_id: string;
  'X-Jwt-Token'?: string;
}

export interface AbortBuildResponse {
  code?: number;
  data?: common.Revision;
  error?: string;
}

export interface BuildLatestRevisionRequest {
  /** cluster name */
  cluster: string;
  /** region */
  region: string;
  /** ID of service */
  service_id: string;
  'X-Jwt-Token'?: string;
}

export interface BuildLatestRevisionResponse {
  code?: number;
  data?: common.Revision;
  error?: string;
}

export interface BuildServiceRevisionRequest {
  /** cluster name */
  cluster: string;
  /** region */
  region: string;
  /** Number of revision */
  revision_number: string;
  /** ID of service */
  service_id: string;
  'X-Jwt-Token'?: string;
}

export interface BuildServiceRevisionResponse {
  code?: number;
  data?: common.Revision;
  error?: string;
}

export interface GetICMBaseImagesListRequest {}

export interface GetICMBaseImagesListResponse {
  code?: number;
  error?: string;
  data?: Array<ICMBaseImage>;
}

export interface ICMBaseImage {
  /** Version of the base image */
  base_version?: string;
  /** Name of the base image */
  name?: string;
  /** Labels associated with the base image */
  labels?: Array<string>;
  /** Unique identifier for the image */
  image_id?: number;
  /** Whether the image is recommended for use */
  recommend?: boolean;
  /** Whether the image is end-of-life (EOL) */
  eol?: boolean;
}
/* eslint-enable */
