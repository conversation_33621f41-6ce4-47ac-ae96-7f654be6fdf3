{"extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"jsx": "preserve", "useUnknownInCatchVariables": false, "types": ["vitest/globals"], "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["./src"], "references": [{"path": "../bot-http/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../idl/tsconfig.build.json"}, {"path": "../../../infra/utils/fs-enhance/tsconfig.build.json"}, {"path": "../logger/tsconfig.build.json"}], "$schema": "https://json.schemastore.org/tsconfig"}