{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "vitest.config.ts"], "exclude": ["**/node_modules", "./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"baseUrl": "./", "paths": {}, "types": ["react", "react-dom", "vitest/globals"], "jsx": "react", "isolatedModules": true, "preserveSymlinks": false, "strictPropertyInitialization": false, "strictNullChecks": true, "rootDir": "./", "outDir": "./dist"}}