{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "stories", "vitest.config.ts", "tailwind.config.ts", "src/**/*.test.ts"], "exclude": ["dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"jsx": "preserve", "strictNullChecks": true, "rootDir": "./", "outDir": "./dist", "types": ["vitest/globals"], "noImplicitReturns": false, "useUnknownInCatchVariables": false, "strictPropertyInitialization": false, "module": "ESNext", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}