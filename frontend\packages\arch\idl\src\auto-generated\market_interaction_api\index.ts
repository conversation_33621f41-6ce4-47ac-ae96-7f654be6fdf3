/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as admin_api from './namespaces/admin_api';
import * as counter from './namespaces/counter';
import * as flow_marketplace_interaction_common from './namespaces/flow_marketplace_interaction_common';
import * as flow_platform_audit_common from './namespaces/flow_platform_audit_common';
import * as marketplace_common from './namespaces/marketplace_common';
import * as public_api from './namespaces/public_api';
import * as rpc from './namespaces/rpc';
import * as share from './namespaces/share';

export {
  admin_api,
  counter,
  flow_marketplace_interaction_common,
  flow_platform_audit_common,
  marketplace_common,
  public_api,
  rpc,
  share,
};
export * from './namespaces/admin_api';
export * from './namespaces/counter';
export * from './namespaces/flow_marketplace_interaction_common';
export * from './namespaces/flow_platform_audit_common';
export * from './namespaces/marketplace_common';
export * from './namespaces/public_api';
export * from './namespaces/rpc';
export * from './namespaces/share';

export type Int64 = string | number;

export default class MarketInteractionApiService<T> {
  private request: any = () => {
    throw new Error('MarketInteractionApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/marketplace/interaction/share_link/get
   *
   * ------------------------------------ HTTP 接口 ------------------------------------
   *
   * 分享：生成分享链接
   */
  GetShareLink(
    req: share.GetShareLinkRequest,
    options?: T,
  ): Promise<share.GetShareLinkResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/interaction/share_link/get');
    const method = 'GET';
    const params = {
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/interaction/share_link/get.v2
   *
   * 分享：生成分享链接V2
   */
  GetShareLinkV2(
    req: share.GetShareLinkV2Request,
    options?: T,
  ): Promise<share.GetShareLinkV2Response> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/share_link/get.v2',
    );
    const method = 'GET';
    const params = {
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
      invitation_method: _req['invitation_method'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/interaction/short_url/get
   *
   * 分享：生成短链接
   */
  GetShortURL(
    req: share.GetShortURLRequest,
    options?: T,
  ): Promise<share.GetShortURLResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/interaction/short_url/get');
    const method = 'POST';
    const data = { source_urls: _req['source_urls'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/post/list */
  PublicGetPostList(
    req?: public_api.GetPostListRequest,
    options?: T,
  ): Promise<public_api.GetPostListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/post/list');
    const method = 'GET';
    const params = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      preview_top_post_id: _req['preview_top_post_id'],
      is_need_latest_data: _req['is_need_latest_data'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/marketplace/interaction/post/publish */
  PublicPublishPost(
    req: public_api.PublishPostRequest,
    options?: T,
  ): Promise<public_api.PublishPostResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/interaction/post/publish');
    const method = 'POST';
    const data = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      title: _req['title'],
      content: _req['content'],
      label: _req['label'],
      resource: _req['resource'],
      mention_list: _req['mention_list'],
      conversation: _req['conversation'],
      content_text: _req['content_text'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/marketplace/interaction/comment/list
   *
   * 评论
   */
  PublicGetCommentList(
    req?: public_api.GetCommentListRequest,
    options?: T,
  ): Promise<public_api.GetCommentListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/comment/list');
    const method = 'GET';
    const params = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      cursor: _req['cursor'],
      limit: _req['limit'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/marketplace/interaction/user/ban */
  PublicBanUser(
    req?: public_api.BanUserRequest,
    options?: T,
  ): Promise<public_api.BanUserResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/user/ban');
    const method = 'POST';
    const data = { user_id: _req['user_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/marketplace/interaction/comment/reply */
  PublicReplyComment(
    req?: public_api.ReplyCommentRequest,
    options?: T,
  ): Promise<public_api.ReplyCommentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/comment/reply');
    const method = 'POST';
    const data = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      mention_list: _req['mention_list'],
      reply_content: _req['reply_content'],
      resource: _req['resource'],
      reply_to_comment_id: _req['reply_to_comment_id'],
      reply_content_text: _req['reply_content_text'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/marketplace/interaction/post/detail */
  PublicGetPostDetail(
    req?: public_api.GetPostDetailRequest,
    options?: T,
  ): Promise<public_api.GetPostDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/post/detail');
    const method = 'GET';
    const params = {
      post_id: _req['post_id'],
      is_need_latest_data: _req['is_need_latest_data'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/marketplace/interaction/comment/publish */
  PublicPublishComment(
    req?: public_api.PublishCommentRequest,
    options?: T,
  ): Promise<public_api.PublishCommentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/comment/publish');
    const method = 'POST';
    const data = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      mention_list: _req['mention_list'],
      resource: _req['resource'],
      comment_content: _req['comment_content'],
      comment_content_text: _req['comment_content_text'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/marketplace/interaction/admin/top_post/preview */
  AdminGetPreviewTopPost(
    req: admin_api.GetPreviewTopPostRequest,
    options?: T,
  ): Promise<admin_api.GetPreviewTopPostResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/top_post/preview',
    );
    const method = 'GET';
    const params = { id: _req['id'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/interaction/post_label/list */
  PublicGetPostLabelList(
    req?: public_api.GetPostLabelListRequest,
    options?: T,
  ): Promise<public_api.GetPostLabelListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/post_label/list');
    const method = 'GET';
    const params = { item_id: _req['item_id'], item_type: _req['item_type'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/post/pin */
  PublicPinPost(
    req?: public_api.PinPostRequest,
    options?: T,
  ): Promise<public_api.PinPostResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/post/pin');
    const method = 'POST';
    const data = { post_id: _req['post_id'] };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/marketplace/interaction/comment/delete */
  PublicDeleteComment(
    req?: public_api.DeleteCommentRequest,
    options?: T,
  ): Promise<public_api.DeleteCommentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/comment/delete');
    const method = 'POST';
    const data = { comment_id: _req['comment_id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/comment/reply/list */
  PublicGetReplyList(
    req?: public_api.GetReplyListRequest,
    options?: T,
  ): Promise<public_api.GetReplyListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/comment/reply/list',
    );
    const method = 'GET';
    const params = {
      comment_id: _req['comment_id'],
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      cursor: _req['cursor'],
      limit: _req['limit'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/marketplace/interaction/admin/top_post/config */
  AdminSetTopPostConfig(
    req: admin_api.SetTopPostConfigRequest,
    options?: T,
  ): Promise<admin_api.SetTopPostConfigResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/top_post/config',
    );
    const method = 'POST';
    const data = { id: _req['id'], config: _req['config'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/interaction/upload/token
   *
   * 帖子
   */
  PublicGetUploadToken(
    req?: public_api.GetUploadTokenRequest,
    options?: T,
  ): Promise<public_api.GetUploadTokenResponse> {
    const url = this.genBaseURL('/api/marketplace/interaction/upload/token');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/marketplace/interaction/post/delete */
  PublicDeletePost(
    req?: public_api.DeletePostRequest,
    options?: T,
  ): Promise<public_api.DeletePostResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/post/delete');
    const method = 'POST';
    const data = { post_id: _req['post_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/marketplace/interaction/post/pin/cancel */
  PublicCancelPinPost(
    req?: public_api.CancelPinPostRequest,
    options?: T,
  ): Promise<public_api.CancelPinPostResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/post/pin/cancel');
    const method = 'POST';
    const data = { post_id: _req['post_id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/meta */
  PublicGetMeta(
    req?: public_api.GetMetaRequest,
    options?: T,
  ): Promise<public_api.GetMetaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/meta');
    const method = 'GET';
    const params = { item_id: _req['item_id'], item_type: _req['item_type'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/reaction/do */
  PublicDoReaction(
    req?: public_api.DoReactionRequest,
    options?: T,
  ): Promise<public_api.DoReactionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/reaction/do');
    const method = 'POST';
    const data = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      reaction_type: _req['reaction_type'],
      emoji_type: _req['emoji_type'],
      is_cancel: _req['is_cancel'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/marketplace/interaction/admin/top_post/list */
  AdminGetTopPostList(
    req?: admin_api.GetTopPostListRequest,
    options?: T,
  ): Promise<admin_api.GetTopPostListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/top_post/list',
    );
    const method = 'GET';
    const params = {
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      config_type: _req['config_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/interaction/stream_reply
   *
   * ------------------------------------ HTTP SSE 接口 ------------------------------------
   *
   * @Bot 对话流式返回。接入 AGW 流式协议，thrift grpc -> http sse
   */
  StreamReply(
    req?: public_api.StreamReplyRequest,
    options?: T,
  ): Promise<public_api.StreamReplyResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/stream_reply');
    const method = 'GET';
    const params = {
      comment_id: _req['comment_id'],
      seq_start: _req['seq_start'],
      seq_end: _req['seq_end'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/interaction/admin/top_post/publish
   *
   * ------------------------------------ Admin接口 ------------------------------------
   */
  AdminPublishTopPost(
    req: admin_api.PublishTopPostRequest,
    options?: T,
  ): Promise<admin_api.PublishTopPostResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/top_post/publish',
    );
    const method = 'POST';
    const data = {
      title: _req['title'],
      content: _req['content'],
      context_text: _req['context_text'],
      resource: _req['resource'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/product/like_count */
  PublicGetProductLike(
    req?: public_api.GetProductLikeRequest,
    options?: T,
  ): Promise<public_api.GetProductLikeResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/product/like_count',
    );
    const method = 'GET';
    const params = { item_id: _req['item_id'], item_type: _req['item_type'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/admin/top_post/delete */
  AdminDeleteTopPost(
    req: admin_api.DeleteTopPostRequest,
    options?: T,
  ): Promise<admin_api.DeleteTopPostResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/top_post/delete',
    );
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/admin/discussion/global_config/get */
  AdminGetDiscussionGlobalConfig(
    req?: admin_api.GetDiscussionGlobalConfigRequest,
    options?: T,
  ): Promise<admin_api.GetDiscussionGlobalConfigResponse> {
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/discussion/global_config/get',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/marketplace/interaction/admin/discussion/list */
  AdminGetDiscussionList(
    req?: admin_api.GetDiscussionListRequest,
    options?: T,
  ): Promise<admin_api.GetDiscussionListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/discussion/list',
    );
    const method = 'GET';
    const params = {
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      item_type: _req['item_type'],
      item_show_name: _req['item_show_name'],
      item_id: _req['item_id'],
      status: _req['status'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/admin/comment/update_status */
  AdminUpdateCommentStatus(
    req: admin_api.UpdateCommentStatusRequest,
    options?: T,
  ): Promise<admin_api.UpdateCommentStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/comment/update_status',
    );
    const method = 'POST';
    const data = { ids: _req['ids'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/marketplace/interaction/admin/discussion/update_status */
  AdminUpdateDiscussionStatus(
    req: admin_api.UpdateDiscussionStatusRequest,
    options?: T,
  ): Promise<admin_api.UpdateDiscussionStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/discussion/update_status',
    );
    const method = 'POST';
    const data = { ids: _req['ids'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/marketplace/interaction/admin/post/update_status */
  AdminUpdatePostStatus(
    req: admin_api.UpdatePostStatusRequest,
    options?: T,
  ): Promise<admin_api.UpdatePostStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/post/update_status',
    );
    const method = 'POST';
    const data = { ids: _req['ids'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/admin/comment/list */
  AdminGetCommentList(
    req: admin_api.GetCommentListRequest,
    options?: T,
  ): Promise<admin_api.GetCommentListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/comment/list',
    );
    const method = 'GET';
    const params = {
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      product_id: _req['product_id'],
      comment_id: _req['comment_id'],
      author_name: _req['author_name'],
      author_type: _req['author_type'],
      author_id: _req['author_id'],
      content_keyword: _req['content_keyword'],
      post_id: _req['post_id'],
      created_at_begin: _req['created_at_begin'],
      created_at_end: _req['created_at_end'],
      status: _req['status'],
      type: _req['type'],
      root_id: _req['root_id'],
      sort_type: _req['sort_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/admin/discussion/global_config/set */
  AdminSetDiscussionGlobalConfig(
    req?: admin_api.SetDiscussionGlobalConfigRequest,
    options?: T,
  ): Promise<admin_api.SetDiscussionGlobalConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/discussion/global_config/set',
    );
    const method = 'POST';
    const data = {
      discussion_status: _req['discussion_status'],
      date_range: _req['date_range'],
      process_id: _req['process_id'],
    };
    const headers = {
      'X-Jwt-Token': _req['X-Jwt-Token'],
      'X-Bcgw-Username': _req['X-Bcgw-Username'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/marketplace/interaction/admin/post/list */
  AdminGetPostList(
    req?: admin_api.GetPostListRequest,
    options?: T,
  ): Promise<admin_api.GetPostListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/admin/post/list');
    const method = 'GET';
    const params = {
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      sort_type: _req['sort_type'],
      keyword: _req['keyword'],
      post_id: _req['post_id'],
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      author_id: _req['author_id'],
      author_name: _req['author_name'],
      publish_begin_at: _req['publish_begin_at'],
      publish_end_at: _req['publish_end_at'],
      status: _req['status'],
      label_id: _req['label_id'],
      min_evaluation_score: _req['min_evaluation_score'],
      max_evaluation_score: _req['max_evaluation_score'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/interaction/admin/post/detail */
  AdminGetPostDetail(
    req: admin_api.GetPostDetailRequest,
    options?: T,
  ): Promise<admin_api.GetPostDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/post/detail',
    );
    const method = 'GET';
    const params = { id: _req['id'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/interaction/admin/post_label/list */
  AdminGetPostLabelList(
    req?: admin_api.GetPostLabelListRequest,
    options?: T,
  ): Promise<admin_api.GetPostLabelListResponse> {
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/post_label/list',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/marketplace/interaction/img/get_url */
  PublicGetImgURL(
    req?: public_api.GetImgURLRequest,
    options?: T,
  ): Promise<public_api.GetImgURLResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/img/get_url');
    const method = 'GET';
    const params = { Key: _req['Key'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/admin/user/upsert_status */
  AdminUpsertUserStatus(
    req: admin_api.UpsertUserStatusRequest,
    options?: T,
  ): Promise<admin_api.UpsertUserStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/user/upsert_status',
    );
    const method = 'POST';
    const data = { user_id: _req['user_id'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/marketplace/interaction/self_view/set */
  PublicSetSelfView(
    req?: public_api.SetSelfViewRequest,
    options?: T,
  ): Promise<public_api.SetSelfViewResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/self_view/set');
    const method = 'POST';
    const data = { item_id: _req['item_id'], item_type: _req['item_type'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/admin/upload/token */
  AdminGetUploadToken(
    req?: admin_api.GetUploadTokenRequest,
    options?: T,
  ): Promise<admin_api.GetUploadTokenResponse> {
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/upload/token',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/marketplace/interaction/admin/img/get_url */
  AdminGetImgURL(
    req?: admin_api.GetImgURLRequest,
    options?: T,
  ): Promise<admin_api.GetImgURLResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/img/get_url',
    );
    const method = 'GET';
    const params = { Key: _req['Key'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/admin/top_post/update */
  AdminUpdateTopPost(
    req?: admin_api.UpdateTopPostRequest,
    options?: T,
  ): Promise<admin_api.UpdateTopPostResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/admin/top_post/update',
    );
    const method = 'POST';
    const data = {
      post_id: _req['post_id'],
      title: _req['title'],
      content: _req['content'],
      content_text: _req['content_text'],
      resource: _req['resource'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/interaction/user/data
   *
   * 用户主页接口
   */
  PublicGetUserInteractionData(
    req?: public_api.GetUserInteractionDataRequest,
    options?: T,
  ): Promise<public_api.GetUserInteractionDataResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/user/data');
    const method = 'GET';
    const params = {
      user_id: _req['user_id'],
      need_gain_like_count: _req['need_gain_like_count'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/user_behavior/report */
  PublicReportUserBehavior(
    req?: public_api.ReportUserBehaviorRequest,
    options?: T,
  ): Promise<public_api.ReportUserBehaviorResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/user_behavior/report',
    );
    const method = 'POST';
    const data = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      behavior_type: _req['behavior_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/like_statistics/list */
  PublicGetLikeStatisticsList(
    req?: public_api.GetLikeStatisticsListRequest,
    options?: T,
  ): Promise<public_api.GetLikeStatisticsListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/like_statistics/list',
    );
    const method = 'GET';
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      keyword: _req['keyword'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/user_behavior/delete */
  PublicDeleteUserBehaviorRecord(
    req?: public_api.DeleteUserBehaviorRecordRequest,
    options?: T,
  ): Promise<public_api.DeleteUserBehaviorRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/user_behavior/delete',
    );
    const method = 'POST';
    const data = {
      item_type: _req['item_type'],
      item_id_list: _req['item_id_list'],
      behavior_type: _req['behavior_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/marketplace/interaction/follow/follower/list */
  PublicGetFollowerList(
    req?: public_api.GetFollowerListRequest,
    options?: T,
  ): Promise<public_api.GetFollowerListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/follow/follower/list',
    );
    const method = 'GET';
    const params = { cursor: _req['cursor'], limit: _req['limit'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/interaction/follow/followee/list */
  PublicGetFolloweeList(
    req?: public_api.GetFolloweeListRequest,
    options?: T,
  ): Promise<public_api.GetFolloweeListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/follow/followee/list',
    );
    const method = 'GET';
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      keyword: _req['keyword'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/marketplace/interaction/follow/do */
  PublicDoFollow(
    req?: public_api.DoFollowRequest,
    options?: T,
  ): Promise<public_api.DoFollowResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/interaction/follow/do');
    const method = 'POST';
    const data = {
      item_id: _req['item_id'],
      item_type: _req['item_type'],
      is_cancel: _req['is_cancel'],
    };
    const headers = { Cookie: _req['Cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/marketplace/interaction/user_behavior/vistor_user/list */
  PublicGetVisitorUserList(
    req?: public_api.GetVisitorUserListRequest,
    options?: T,
  ): Promise<public_api.GetVisitorUserListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/interaction/user_behavior/vistor_user/list',
    );
    const method = 'GET';
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      begin_at: _req['begin_at'],
      end_at: _req['end_at'],
    };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
