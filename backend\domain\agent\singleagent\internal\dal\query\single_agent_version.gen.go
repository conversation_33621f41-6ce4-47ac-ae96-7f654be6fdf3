// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/agent/singleagent/internal/dal/model"
)

func newSingleAgentVersion(db *gorm.DB, opts ...gen.DOOption) singleAgentVersion {
	_singleAgentVersion := singleAgentVersion{}

	_singleAgentVersion.singleAgentVersionDo.UseDB(db, opts...)
	_singleAgentVersion.singleAgentVersionDo.UseModel(&model.SingleAgentVersion{})

	tableName := _singleAgentVersion.singleAgentVersionDo.TableName()
	_singleAgentVersion.ALL = field.NewAsterisk(tableName)
	_singleAgentVersion.ID = field.NewInt64(tableName, "id")
	_singleAgentVersion.AgentID = field.NewInt64(tableName, "agent_id")
	_singleAgentVersion.CreatorID = field.NewInt64(tableName, "creator_id")
	_singleAgentVersion.SpaceID = field.NewInt64(tableName, "space_id")
	_singleAgentVersion.Name = field.NewString(tableName, "name")
	_singleAgentVersion.Description = field.NewString(tableName, "description")
	_singleAgentVersion.IconURI = field.NewString(tableName, "icon_uri")
	_singleAgentVersion.CreatedAt = field.NewInt64(tableName, "created_at")
	_singleAgentVersion.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_singleAgentVersion.DeletedAt = field.NewField(tableName, "deleted_at")
	_singleAgentVersion.VariablesMetaID = field.NewInt64(tableName, "variables_meta_id")
	_singleAgentVersion.ModelInfo = field.NewField(tableName, "model_info")
	_singleAgentVersion.OnboardingInfo = field.NewField(tableName, "onboarding_info")
	_singleAgentVersion.Prompt = field.NewField(tableName, "prompt")
	_singleAgentVersion.Plugin = field.NewField(tableName, "plugin")
	_singleAgentVersion.Knowledge = field.NewField(tableName, "knowledge")
	_singleAgentVersion.Workflow = field.NewField(tableName, "workflow")
	_singleAgentVersion.SuggestReply = field.NewField(tableName, "suggest_reply")
	_singleAgentVersion.JumpConfig = field.NewField(tableName, "jump_config")
	_singleAgentVersion.ConnectorID = field.NewInt64(tableName, "connector_id")
	_singleAgentVersion.Version = field.NewString(tableName, "version")
	_singleAgentVersion.BackgroundImageInfoList = field.NewField(tableName, "background_image_info_list")
	_singleAgentVersion.DatabaseConfig = field.NewField(tableName, "database_config")
	_singleAgentVersion.ShortcutCommand = field.NewField(tableName, "shortcut_command")

	_singleAgentVersion.fillFieldMap()

	return _singleAgentVersion
}

// singleAgentVersion Single Agent Version Copy Table
type singleAgentVersion struct {
	singleAgentVersionDo

	ALL                     field.Asterisk
	ID                      field.Int64  // Primary Key ID
	AgentID                 field.Int64  // Agent ID
	CreatorID               field.Int64  // Creator ID
	SpaceID                 field.Int64  // Space ID
	Name                    field.String // Agent Name
	Description             field.String // Agent Description
	IconURI                 field.String // Icon URI
	CreatedAt               field.Int64  // Create Time in Milliseconds
	UpdatedAt               field.Int64  // Update Time in Milliseconds
	DeletedAt               field.Field  // delete time in millisecond
	VariablesMetaID         field.Int64  // variables meta 表 ID
	ModelInfo               field.Field  // Model Configuration Information
	OnboardingInfo          field.Field  // Onboarding Information
	Prompt                  field.Field  // Agent Prompt Configuration
	Plugin                  field.Field  // Agent Plugin Base Configuration
	Knowledge               field.Field  // Agent Knowledge Base Configuration
	Workflow                field.Field  // Agent Workflow Configuration
	SuggestReply            field.Field  // Suggested Replies
	JumpConfig              field.Field  // Jump Configuration
	ConnectorID             field.Int64  // Connector ID
	Version                 field.String // Agent Version
	BackgroundImageInfoList field.Field  // Background image
	DatabaseConfig          field.Field  // Agent Database Base Configuration
	ShortcutCommand         field.Field  // shortcut command

	fieldMap map[string]field.Expr
}

func (s singleAgentVersion) Table(newTableName string) *singleAgentVersion {
	s.singleAgentVersionDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s singleAgentVersion) As(alias string) *singleAgentVersion {
	s.singleAgentVersionDo.DO = *(s.singleAgentVersionDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *singleAgentVersion) updateTableName(table string) *singleAgentVersion {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.AgentID = field.NewInt64(table, "agent_id")
	s.CreatorID = field.NewInt64(table, "creator_id")
	s.SpaceID = field.NewInt64(table, "space_id")
	s.Name = field.NewString(table, "name")
	s.Description = field.NewString(table, "description")
	s.IconURI = field.NewString(table, "icon_uri")
	s.CreatedAt = field.NewInt64(table, "created_at")
	s.UpdatedAt = field.NewInt64(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")
	s.VariablesMetaID = field.NewInt64(table, "variables_meta_id")
	s.ModelInfo = field.NewField(table, "model_info")
	s.OnboardingInfo = field.NewField(table, "onboarding_info")
	s.Prompt = field.NewField(table, "prompt")
	s.Plugin = field.NewField(table, "plugin")
	s.Knowledge = field.NewField(table, "knowledge")
	s.Workflow = field.NewField(table, "workflow")
	s.SuggestReply = field.NewField(table, "suggest_reply")
	s.JumpConfig = field.NewField(table, "jump_config")
	s.ConnectorID = field.NewInt64(table, "connector_id")
	s.Version = field.NewString(table, "version")
	s.BackgroundImageInfoList = field.NewField(table, "background_image_info_list")
	s.DatabaseConfig = field.NewField(table, "database_config")
	s.ShortcutCommand = field.NewField(table, "shortcut_command")

	s.fillFieldMap()

	return s
}

func (s *singleAgentVersion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *singleAgentVersion) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 24)
	s.fieldMap["id"] = s.ID
	s.fieldMap["agent_id"] = s.AgentID
	s.fieldMap["creator_id"] = s.CreatorID
	s.fieldMap["space_id"] = s.SpaceID
	s.fieldMap["name"] = s.Name
	s.fieldMap["description"] = s.Description
	s.fieldMap["icon_uri"] = s.IconURI
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["variables_meta_id"] = s.VariablesMetaID
	s.fieldMap["model_info"] = s.ModelInfo
	s.fieldMap["onboarding_info"] = s.OnboardingInfo
	s.fieldMap["prompt"] = s.Prompt
	s.fieldMap["plugin"] = s.Plugin
	s.fieldMap["knowledge"] = s.Knowledge
	s.fieldMap["workflow"] = s.Workflow
	s.fieldMap["suggest_reply"] = s.SuggestReply
	s.fieldMap["jump_config"] = s.JumpConfig
	s.fieldMap["connector_id"] = s.ConnectorID
	s.fieldMap["version"] = s.Version
	s.fieldMap["background_image_info_list"] = s.BackgroundImageInfoList
	s.fieldMap["database_config"] = s.DatabaseConfig
	s.fieldMap["shortcut_command"] = s.ShortcutCommand
}

func (s singleAgentVersion) clone(db *gorm.DB) singleAgentVersion {
	s.singleAgentVersionDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s singleAgentVersion) replaceDB(db *gorm.DB) singleAgentVersion {
	s.singleAgentVersionDo.ReplaceDB(db)
	return s
}

type singleAgentVersionDo struct{ gen.DO }

type ISingleAgentVersionDo interface {
	gen.SubQuery
	Debug() ISingleAgentVersionDo
	WithContext(ctx context.Context) ISingleAgentVersionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISingleAgentVersionDo
	WriteDB() ISingleAgentVersionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISingleAgentVersionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISingleAgentVersionDo
	Not(conds ...gen.Condition) ISingleAgentVersionDo
	Or(conds ...gen.Condition) ISingleAgentVersionDo
	Select(conds ...field.Expr) ISingleAgentVersionDo
	Where(conds ...gen.Condition) ISingleAgentVersionDo
	Order(conds ...field.Expr) ISingleAgentVersionDo
	Distinct(cols ...field.Expr) ISingleAgentVersionDo
	Omit(cols ...field.Expr) ISingleAgentVersionDo
	Join(table schema.Tabler, on ...field.Expr) ISingleAgentVersionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISingleAgentVersionDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISingleAgentVersionDo
	Group(cols ...field.Expr) ISingleAgentVersionDo
	Having(conds ...gen.Condition) ISingleAgentVersionDo
	Limit(limit int) ISingleAgentVersionDo
	Offset(offset int) ISingleAgentVersionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISingleAgentVersionDo
	Unscoped() ISingleAgentVersionDo
	Create(values ...*model.SingleAgentVersion) error
	CreateInBatches(values []*model.SingleAgentVersion, batchSize int) error
	Save(values ...*model.SingleAgentVersion) error
	First() (*model.SingleAgentVersion, error)
	Take() (*model.SingleAgentVersion, error)
	Last() (*model.SingleAgentVersion, error)
	Find() ([]*model.SingleAgentVersion, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SingleAgentVersion, err error)
	FindInBatches(result *[]*model.SingleAgentVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SingleAgentVersion) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISingleAgentVersionDo
	Assign(attrs ...field.AssignExpr) ISingleAgentVersionDo
	Joins(fields ...field.RelationField) ISingleAgentVersionDo
	Preload(fields ...field.RelationField) ISingleAgentVersionDo
	FirstOrInit() (*model.SingleAgentVersion, error)
	FirstOrCreate() (*model.SingleAgentVersion, error)
	FindByPage(offset int, limit int) (result []*model.SingleAgentVersion, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISingleAgentVersionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s singleAgentVersionDo) Debug() ISingleAgentVersionDo {
	return s.withDO(s.DO.Debug())
}

func (s singleAgentVersionDo) WithContext(ctx context.Context) ISingleAgentVersionDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s singleAgentVersionDo) ReadDB() ISingleAgentVersionDo {
	return s.Clauses(dbresolver.Read)
}

func (s singleAgentVersionDo) WriteDB() ISingleAgentVersionDo {
	return s.Clauses(dbresolver.Write)
}

func (s singleAgentVersionDo) Session(config *gorm.Session) ISingleAgentVersionDo {
	return s.withDO(s.DO.Session(config))
}

func (s singleAgentVersionDo) Clauses(conds ...clause.Expression) ISingleAgentVersionDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s singleAgentVersionDo) Returning(value interface{}, columns ...string) ISingleAgentVersionDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s singleAgentVersionDo) Not(conds ...gen.Condition) ISingleAgentVersionDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s singleAgentVersionDo) Or(conds ...gen.Condition) ISingleAgentVersionDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s singleAgentVersionDo) Select(conds ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s singleAgentVersionDo) Where(conds ...gen.Condition) ISingleAgentVersionDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s singleAgentVersionDo) Order(conds ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s singleAgentVersionDo) Distinct(cols ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s singleAgentVersionDo) Omit(cols ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s singleAgentVersionDo) Join(table schema.Tabler, on ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s singleAgentVersionDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s singleAgentVersionDo) RightJoin(table schema.Tabler, on ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s singleAgentVersionDo) Group(cols ...field.Expr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s singleAgentVersionDo) Having(conds ...gen.Condition) ISingleAgentVersionDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s singleAgentVersionDo) Limit(limit int) ISingleAgentVersionDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s singleAgentVersionDo) Offset(offset int) ISingleAgentVersionDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s singleAgentVersionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISingleAgentVersionDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s singleAgentVersionDo) Unscoped() ISingleAgentVersionDo {
	return s.withDO(s.DO.Unscoped())
}

func (s singleAgentVersionDo) Create(values ...*model.SingleAgentVersion) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s singleAgentVersionDo) CreateInBatches(values []*model.SingleAgentVersion, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s singleAgentVersionDo) Save(values ...*model.SingleAgentVersion) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s singleAgentVersionDo) First() (*model.SingleAgentVersion, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentVersion), nil
	}
}

func (s singleAgentVersionDo) Take() (*model.SingleAgentVersion, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentVersion), nil
	}
}

func (s singleAgentVersionDo) Last() (*model.SingleAgentVersion, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentVersion), nil
	}
}

func (s singleAgentVersionDo) Find() ([]*model.SingleAgentVersion, error) {
	result, err := s.DO.Find()
	return result.([]*model.SingleAgentVersion), err
}

func (s singleAgentVersionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SingleAgentVersion, err error) {
	buf := make([]*model.SingleAgentVersion, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s singleAgentVersionDo) FindInBatches(result *[]*model.SingleAgentVersion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s singleAgentVersionDo) Attrs(attrs ...field.AssignExpr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s singleAgentVersionDo) Assign(attrs ...field.AssignExpr) ISingleAgentVersionDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s singleAgentVersionDo) Joins(fields ...field.RelationField) ISingleAgentVersionDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s singleAgentVersionDo) Preload(fields ...field.RelationField) ISingleAgentVersionDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s singleAgentVersionDo) FirstOrInit() (*model.SingleAgentVersion, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentVersion), nil
	}
}

func (s singleAgentVersionDo) FirstOrCreate() (*model.SingleAgentVersion, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SingleAgentVersion), nil
	}
}

func (s singleAgentVersionDo) FindByPage(offset int, limit int) (result []*model.SingleAgentVersion, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s singleAgentVersionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s singleAgentVersionDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s singleAgentVersionDo) Delete(models ...*model.SingleAgentVersion) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *singleAgentVersionDo) withDO(do gen.Dao) *singleAgentVersionDo {
	s.DO = *do.(*gen.DO)
	return s
}
