/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as eval_set from './eval_set';
import * as filter from './filter';
import * as fornaxob_domain_common from './fornaxob_domain_common';

export type Int64 = string | number;

export interface AutoEvaluateConfig {
  evaluator_version_id: Int64;
  evaluator_id: Int64;
  field_mappings: Array<FieldMapping>;
}

export interface EffectiveTime {
  /** ms timestamp */
  start_at?: Int64;
  /** ms timestamp */
  end_at?: Int64;
}

export interface FieldMapping {
  /** 数据集字段约束 */
  field_schema: eval_set.FieldSchema;
  trace_field_key: string;
  trace_field_jsonpath: string;
  eval_set_name?: string;
}

/** Rule */
export interface Rule {
  /** Span 过滤条件 */
  span_filters?: filter.SpanFilters;
  /** 采样配置 */
  sampler?: Sampler;
  /** 生效时间窗口 */
  effective_time?: EffectiveTime;
}

export interface Sampler {
  /** 采样率 */
  sample_rate?: number;
  /** 采样上限 */
  sample_size?: Int64;
  /** 是否启动任务循环 */
  is_cycle?: boolean;
  /** 采样单次上限 */
  cycle_count?: Int64;
  /** 循环间隔 */
  cycle_interval?: Int64;
  /** 循环时间单位 */
  cycle_time_unit?: string;
}

/** Task */
export interface Task {
  /** 任务 id */
  id?: Int64;
  /** 名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 所在空间 */
  workspace_id?: Int64;
  /** 类型 */
  task_type: string;
  /** 状态 */
  task_status?: string;
  /** 规则 */
  rule?: Rule;
  /** 配置 */
  task_config?: TaskConfig;
  /** 任务状态详情 */
  task_detail?: TaskDetail;
  /** 基础信息 */
  base_info?: fornaxob_domain_common.BaseInfo;
}

/** TaskConfig */
export interface TaskConfig {
  /** 配置的评测规则信息 */
  auto_evaluate_configs?: Array<AutoEvaluateConfig>;
}

/** TaskDetail */
export interface TaskDetail {
  success_count?: Int64;
  failed_count?: Int64;
}
/* eslint-enable */
