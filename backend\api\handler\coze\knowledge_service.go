/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	dataset "github.com/coze-dev/coze-studio/backend/api/model/flow/dataengine/dataset"
	application "github.com/coze-dev/coze-studio/backend/application/knowledge"
	"github.com/coze-dev/coze-studio/backend/application/upload"
)

// CreateDataset .
// @router /api/knowledge/create [POST]
func CreateDataset(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.CreateDatasetRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	resp := new(dataset.CreateDatasetResponse)
	resp, err = application.KnowledgeSVC.CreateKnowledge(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// DatasetDetail .
// @router /api/knowledge/detail [POST]
func DatasetDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.DatasetDetailRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	resp := new(dataset.DatasetDetailResponse)
	resp, err = application.KnowledgeSVC.DatasetDetail(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ListDataset .
// @router /api/knowledge/list [POST]
func ListDataset(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ListDatasetRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ListDatasetResponse)
	resp, err = application.KnowledgeSVC.ListKnowledge(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// DeleteDataset .
// @router /api/knowledge/delete [POST]
func DeleteDataset(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.DeleteDatasetRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.DeleteDatasetResponse)
	resp, err = application.KnowledgeSVC.DeleteKnowledge(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// UpdateDataset .
// @router /api/knowledge/update [POST]
func UpdateDataset(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.UpdateDatasetRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.UpdateDatasetResponse)
	resp, err = application.KnowledgeSVC.UpdateKnowledge(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// CreateDocument .
// @router /api/knowledge/document/create [POST]
func CreateDocument(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.CreateDocumentRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.CreateDocumentResponse)
	resp, err = application.KnowledgeSVC.CreateDocument(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ListDocument .
// @router /api/knowledge/document/list [POST]
func ListDocument(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ListDocumentRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ListDocumentResponse)
	resp, err = application.KnowledgeSVC.ListDocument(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// DeleteDocument .
// @router /api/knowledge/document/delete [POST]
func DeleteDocument(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.DeleteDocumentRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.DeleteDocumentResponse)
	resp, err = application.KnowledgeSVC.DeleteDocument(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// UpdateDocument .
// @router /api/knowledge/document/update [POST]
func UpdateDocument(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.UpdateDocumentRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.UpdateDocumentResponse)
	resp, err = application.KnowledgeSVC.UpdateDocument(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// GetDocumentProgress .
// @router /api/knowledge/document/progress/get [POST]
func GetDocumentProgress(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.GetDocumentProgressRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.GetDocumentProgressResponse)
	resp, err = application.KnowledgeSVC.GetDocumentProgress(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// Resegment .
// @router /api/knowledge/document/resegment [POST]
func Resegment(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ResegmentRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ResegmentResponse)
	resp, err = application.KnowledgeSVC.Resegment(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// UpdatePhotoCaption .
// @router /api/knowledge/photo/caption [POST]
func UpdatePhotoCaption(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.UpdatePhotoCaptionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.UpdatePhotoCaptionResponse)
	resp, err = application.KnowledgeSVC.UpdatePhotoCaption(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ListPhoto .
// @router /api/knowledge/photo/list [POST]
func ListPhoto(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ListPhotoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ListPhotoResponse)
	resp, err = application.KnowledgeSVC.ListPhoto(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// PhotoDetail .
// @router /api/knowledge/photo/detail [POST]
func PhotoDetail(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.PhotoDetailRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.PhotoDetailResponse)
	resp, err = application.KnowledgeSVC.PhotoDetail(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// GetTableSchema .
// @router /api/knowledge/table_schema/get [POST]
func GetTableSchema(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.GetTableSchemaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.GetTableSchemaResponse)
	resp, err = application.KnowledgeSVC.GetTableSchema(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ValidateTableSchema .
// @router /api/knowledge/table_schema/validate [POST]
func ValidateTableSchema(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ValidateTableSchemaRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ValidateTableSchemaResponse)
	resp, err = application.KnowledgeSVC.ValidateTableSchema(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// DeleteSlice .
// @router /api/knowledge/slice/delete [POST]
func DeleteSlice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.DeleteSliceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.DeleteSliceResponse)
	resp, err = application.KnowledgeSVC.DeleteSlice(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// CreateSlice .
// @router /api/knowledge/slice/create [POST]
func CreateSlice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.CreateSliceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.CreateSliceResponse)
	resp, err = application.KnowledgeSVC.CreateSlice(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// UpdateSlice .
// @router /api/knowledge/slice/update [POST]
func UpdateSlice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.UpdateSliceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.UpdateSliceResponse)
	resp, err = application.KnowledgeSVC.UpdateSlice(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ListSlice .
// @router /api/knowledge/slice/list [POST]
func ListSlice(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ListSliceRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ListSliceResponse)
	resp, err = application.KnowledgeSVC.ListSlice(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// CreateDocumentReview .
// @router /api/knowledge/review/create [POST]
func CreateDocumentReview(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.CreateDocumentReviewRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.CreateDocumentReviewResponse)
	resp, err = application.KnowledgeSVC.CreateDocumentReview(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// MGetDocumentReview .
// @router /api/knowledge/review/mget [POST]
func MGetDocumentReview(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.MGetDocumentReviewRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.MGetDocumentReviewResponse)
	resp, err = application.KnowledgeSVC.MGetDocumentReview(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// SaveDocumentReview .
// @router /api/knowledge/review/save [POST]
func SaveDocumentReview(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.SaveDocumentReviewRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.SaveDocumentReviewResponse)
	resp, err = application.KnowledgeSVC.SaveDocumentReview(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// GetIconForDataset .
// @router /api/knowledge/icon/get [POST]
func GetIconForDataset(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.GetIconRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.GetIconResponse)
	resp, err = upload.SVC.GetIconForDataset(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ExtractPhotoCaption .
// @router /api/knowledge/photo/extract_caption [POST]
func ExtractPhotoCaption(ctx context.Context, c *app.RequestContext) {
	var err error
	var req dataset.ExtractPhotoCaptionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(dataset.ExtractPhotoCaptionResponse)
	resp, err = application.KnowledgeSVC.ExtractPhotoCaption(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}
