/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum BotEnvType {
  BotEnvType_DEBUG = 0,
  BotEnvType_RELEASE = 1,
}

export enum ContentType {
  ContentType_TEXT = 0,
  ContentType_IMAGE = 1,
  ContentType_VIDEO = 2,
  ContentType_AUDIO = 3,
  ContentType_FILE = 4,
  ContentType_CARD = 5,
}

export enum StatusType {
  StatusType_SUCCESS = 0,
  StatusType_FAIL = 1,
}

export enum TagType {
  TagType_STRING = 0,
  TagType_DOUBLE = 1,
  TagType_BOOL = 2,
  TagType_LONG = 3,
  TagType_BYTES = 4,
}

export interface Content {
  /** query类型，枚举：text、image、video、audio、file、card */
  content_type?: ContentType;
  content?: ContentInfo;
}

export interface ContentInfo {
  /** 用户输入文本、audio转文本、卡片信息 */
  text?: string;
  /** 图片url */
  img_url?: string;
  /** 文件url，file、video等都以文件url的形式存储 */
  file_url?: string;
}

export interface FeedBackInfo {}

export interface IntensionInfo {}

export interface LLMInfo {
  input_tokens?: number;
  output_tokens?: number;
  total_tokens?: number;
}

/** 服务集群元信息 */
export interface MetaInfo {
  psm?: string;
  cluster?: string;
  dc?: string;
  env?: string;
  pod_name?: string;
  stage?: string;
  region?: string;
}

export interface OutputInfo {
  /** query类型，枚举：text、image、video、audio、file、card */
  messages?: Array<Content>;
}

export interface Query {
  query_id?: string;
  biz_type?: string;
  biz_sub_type?: string;
  biz_id?: string;
  trace_id?: string;
  log_id?: string;
  thread_id?: string;
  conversation_id?: string;
  start_time?: Int64;
  duration?: Int64;
  input?: Array<Content>;
  output?: Array<OutputInfo>;
  /** query 状态，根据整个query上的span状态来计算 */
  status?: StatusType;
  /** 用户相关信息 */
  user_info?: UserInfo;
  /** 脱敏等安全相关信息 */
  security_info?: SecurityInfo;
  /** query来源相关信息 */
  source_info?: SourceInfo;
  /** llm相关信息 */
  llm_info?: LLMInfo;
  /** 用户反馈相关信息 */
  feedback_info?: FeedBackInfo;
  /** 意图识别相关信息 */
  intension_info?: IntensionInfo;
  meta_info?: MetaInfo;
  tags?: Record<string, Tag>;
  query_type?: Array<ContentType>;
  start_time_first_resp?: Int64;
  latency_first_resp?: Int64;
  message_id?: string;
}

export interface SecurityInfo {
  /** 是否脱敏 */
  is_desensitized: boolean;
  /** 脱敏后的input */
  desensitized_input?: Array<Content>;
  /** 脱敏后的output */
  desensitized_output?: Array<OutputInfo>;
}

export interface SourceInfo {
  bot_id?: string;
  /** bot版本 */
  bot_version?: string;
  /** bot所属空间ID */
  bot_space_id?: string;
  /** Bot接入渠道ID */
  connector_id?: string;
  /** 渠道 */
  channel?: string;
  /** 对话场景 */
  DialogScene?: string;
  /** bot开发/正式环境 */
  BotEnv?: BotEnvType;
  /** bot名称 */
  BotName?: string;
}

export interface Tag {
  key: string;
  tag_type: TagType;
  v_str?: string;
  v_double?: number;
  v_bool?: boolean;
  v_long?: Int64;
  v_bytes?: Array<number>;
}

export interface UserInfo {
  user_id?: string;
  /** 设备ID */
  device_id?: string;
  /** 设备平台 */
  device_platform?: string;
}
/* eslint-enable */
