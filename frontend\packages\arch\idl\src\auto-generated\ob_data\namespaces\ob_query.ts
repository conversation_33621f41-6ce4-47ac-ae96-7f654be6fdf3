/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum CalFieldStatus {
  /** 字段计算中 */
  CalFieldStatusNotReady = 0,
  /** 字段生产完成，可展示 */
  CalFieldStatusOK = 1,
}

export enum Status {
  StatusSuccess = 0,
  StatusFail = 1,
}

export interface ExportQueryToCsvRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  /** 筛选参数 */
  query_filter?: QueryFilter;
}

export interface ExportQueryToCsvResponse {
  body?: Blob;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetQueryEnumsRequest {
  /** space id */
  space_id: Int64;
  bot_id: Int64;
}

export interface GetQueryEnumsResponse {
  intent?: Array<Intent>;
  /** key: connector_id, value: connector_name */
  connectors?: Array<KV>;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetQueryStatData {
  /** Trace总数量 */
  total_count?: number;
  /** 错误率，例如0.3代表30% */
  error_rate?: number;
  /** 总tokens消耗 */
  tokens?: Percentile;
  /** 总时延 */
  latency?: Percentile;
  /** 首字符回复时延 */
  latency_first_resp?: Percentile;
}

export interface GetQueryStatRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  query_filter?: QueryFilter;
}

export interface GetQueryStatResponse {
  data: GetQueryStatData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface I64Range {
  /** Greater Than or Equal (>=) */
  gte?: Int64;
  /** Less Than or Equal (<=) */
  lte?: Int64;
}

export interface Intent {
  key?: string;
  value?: string;
  children?: Array<Intent>;
}

export interface KV {
  Key?: string;
  Value?: string;
}

export interface ListQueryRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  /** 筛选参数 */
  query_filter?: QueryFilter;
  /** default 20 max 200 */
  limit?: number;
  /** 上次请求带过来的分页参数 */
  page_token?: string;
}

export interface ListQueryResponse {
  data: Array<QueryData>;
  /** 下一页的分页token，前端拉取下一页数据时回传 */
  next_page_token: string;
  /** 是否有更多数据 */
  has_more: boolean;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface Percentile {
  p50?: number;
  p99?: number;
}

export interface QueryData {
  query_id?: string;
  /** trace_id，用于查询trace细节 */
  trace_id?: string;
  /** 状态 */
  status?: Status;
  /** 用户ID */
  user_id?: string;
  /** 对话ID */
  message_id?: string;
  /** 会话ID */
  session_id?: string;
  /** 用户输入 */
  input?: string;
  /** bot输出 */
  output?: string;
  /** 模型输入长度 */
  input_tokens?: number;
  /** 模型输出长度 */
  output_tokens?: number;
  /** 发起请求时间 */
  start_time?: string;
  /** 整体耗时 */
  latency?: Int64;
  /** 首token时延 */
  latency_first_resp?: Int64;
  /** 渠道名称 */
  connector?: string;
  /** 意图标签 */
  intent?: string;
  /** 意图字段生产状态 */
  intent_status?: CalFieldStatus;
  /** session字段生产状态 */
  session_id_status?: CalFieldStatus;
  /** 会话ID（connector platform上报原始值） */
  conversation_id?: string;
}

export interface QueryFilter {
  start_time?: TimeRange;
  status?: Array<Status>;
  user_ids?: Array<string>;
  message_ids?: Array<string>;
  session_ids?: Array<string>;
  input?: string;
  output?: string;
  input_tokens?: I64Range;
  output_tokens?: I64Range;
  latency?: I64Range;
  latency_first_resp?: I64Range;
  connector_ids?: Array<string>;
  second_class_intents?: Array<string>;
}

export interface TimeRange {
  /** Greater Than or Equal (>=) ms */
  gte?: string;
  /** Less Than or Equal (<=) ms */
  lte?: string;
}
/* eslint-enable */
