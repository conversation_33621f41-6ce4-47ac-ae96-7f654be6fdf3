/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as base from './base';
import * as document from './document';
import * as dataset from './dataset';

export type Int64 = string | number;

export interface CreateDatasetOpenApiData {
  dataset_id?: string;
}

export interface CreateDatasetOpenApiRequest {
  /** 知识库名称，长度不超过100个字符 */
  name?: string;
  description?: string;
  space_id?: string;
  file_id?: string;
  format_type?: common.FormatType;
  /** 新增project ID */
  project_id?: string;
  Base?: base.Base;
}

export interface CreateDatasetOpenApiResponse {
  data?: CreateDatasetOpenApiData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface CreateDocumentV2OpenAPIRequest {
  /** 文件类型 */
  'Content-Type': string;
  /** 二进制数据 */
  Data: Blob;
  /** 知识库ID */
  dataset_id: string;
  Base?: base.Base;
}

export interface DeleteDatasetOpenApiRequest {
  dataset_id?: string;
  Base?: base.Base;
}

export interface DeleteDatasetOpenApiResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetDocumentProgressOpenApiData {
  data?: Array<document.DocumentProgress>;
}

export interface GetDocumentProgressOpenApiRequest {
  document_ids?: Array<string>;
  dataset_id?: string;
  Base?: base.Base;
}

export interface GetDocumentProgressOpenApiResponse {
  data?: GetDocumentProgressOpenApiData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListDatasetOpenApiData {
  dataset_list?: Array<dataset.Dataset>;
  total_count?: number;
}

export interface ListDatasetOpenApiRequest {
  name?: string;
  /** 类型 */
  format_type?: common.FormatType;
  page_num?: number;
  page_size?: number;
  space_id?: string;
  /** 新增project ID */
  project_id?: string;
  Base?: base.Base;
}

export interface ListDatasetOpenApiResponse {
  data?: ListDatasetOpenApiData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface ListPhotoOpenApiData {
  photo_infos?: Array<document.PhotoInfo>;
  total_count?: number;
}

export interface ListPhotoOpenApiRequest {
  dataset_id: string;
  /** 页数，从 1 开始 */
  page_num?: number;
  page_size?: number;
  /** 搜索关键字，对图片名称和图片描述进行搜索 */
  keyword?: string;
  /** 是否有描述信息 */
  has_caption?: boolean;
  Base?: base.Base;
}

export interface ListPhotoOpenApiResponse {
  data?: ListPhotoOpenApiData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface UpdateDatasetOpenApiRequest {
  dataset_id?: string;
  name?: string;
  file_id?: string;
  description?: string;
  Base?: base.Base;
}

export interface UpdateDatasetOpenApiResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface UpdatePhotoCaptionOpenApiRequest {
  document_id: string;
  /** 描述信息 */
  caption: string;
  Base?: base.Base;
}

export interface UpdatePhotoCaptionOpenApiResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}
/* eslint-enable */
