/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ConnectionStatus {
  enable = 1,
  delete = 2,
  expire = 3,
}

export enum ConnectorID {
  Notion = 101,
  GoogleDrive = 102,
  FeishuWeb = 103,
  DestinationTos = 104,
  LarkWeb = 105,
  WeChat = 109,
}

export enum DocSourceType {
  DocSourceTypeDrive = 1,
  DocSourceTypeWiki = 2,
  DocSourceTypeWeChat = 3,
}

export enum FileNodeType {
  Folder = 1,
  Document = 2,
  Sheet = 3,
  Space = 4,
}
/* eslint-enable */
