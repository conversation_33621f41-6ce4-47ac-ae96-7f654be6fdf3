/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { describe, expect, it } from 'vitest';
import { type WorkflowNodeJSON } from '@flowgram-adapter/free-layout-editor';

import { setNodePosition } from '../../src/utils';

describe('set-node-position', () => {
  it('should set node position', () => {
    const node: WorkflowNodeJSON = {
      type: 'test',
      id: '1',
    };
    setNodePosition(node, {
      x: 10,
      y: 10,
    });

    expect(node.meta?.position).toEqual({ x: 10, y: 10 });
  });
});
