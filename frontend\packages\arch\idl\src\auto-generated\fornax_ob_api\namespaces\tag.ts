/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ChangeTargetType {
  /** tag */
  Tag = 1,
  /** tag name */
  TagName = 2,
  /** tag description */
  TagDescription = 3,
  /** tag status */
  TagStatus = 4,
  /** tag type */
  TagType = 5,
  /** tag value name */
  TagValueName = 6,
  /** tag value status */
  TagValueStatus = 7,
}

export enum OperationType {
  /** 创建 */
  Create = 1,
  /** 更新 */
  Update = 2,
  /** 删除 */
  Delete = 3,
}

export enum TagStatus {
  /** 启用 */
  Active = 1,
  /** 禁用 */
  Inactive = 2,
  /** 弃用,旧版本状态 */
  Deprecated = 99,
}

export enum TagType {
  /** 标签类型 */
  Tag = 1,
  /** 单选类型 */
  Option = 2,
}
/* eslint-enable */
