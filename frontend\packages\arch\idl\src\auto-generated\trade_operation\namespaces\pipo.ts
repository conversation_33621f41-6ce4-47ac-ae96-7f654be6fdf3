/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface ChargebackCallbackRequest {
  Body?: Blob;
}

export interface ChargebackCallbackResponse {
  content?: string;
}

export interface PaymentMethodInfo {
  payment_type?: string;
  payment_method?: string;
  payment_method_id?: string;
}

export interface PipoAgreementDeductionCallbackData {
  failure_code?: string;
  failure_message?: string;
  merchant_id?: string;
  create_time?: string;
  merchant_order_id?: string;
  psp_order_id?: string;
  currency?: string;
  amount?: string;
  status?: string;
  success_time?: string;
  psp_agreement_id?: string;
  payment_method_info?: PaymentMethodInfo;
  expire_time?: string;
  description?: string;
}

export interface PipoAgreementDeductionCallbackRequest {
  event_type?: string;
  data?: PipoAgreementDeductionCallbackData;
  Body?: Blob;
}

export interface PipoAgreementDeductionCallbackResponse {
  ack?: string;
}

export interface PipoCheckoutCallbackData {
  failure_code?: string;
  failure_message?: string;
  merchant_id?: string;
  merchant_order_id?: string;
  create_time?: string;
  psp_order_id?: string;
  currency?: string;
  amount?: string;
  status?: string;
  success_time?: string;
  payment_method_info?: PaymentMethodInfo;
  expire_time?: string;
  description?: string;
}

export interface PipoCheckoutCallbackRequest {
  event_type?: string;
  data?: PipoCheckoutCallbackData;
  Body?: Blob;
}

export interface PipoCheckoutCallbackResponse {
  ack?: string;
}

export interface PipoGetPaymentMethodManagementURL {
  url?: string;
}

export interface PipoGetPaymentMethodManagementURLRequest {
  /** Coze用户 传common文件里的 ConnectorIDBotStudio */
  UserType?: Int64;
  UserID?: string;
  /** 如果是协议付/订阅场景，则填充此值 */
  scene?: common.Scene;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PipoGetPaymentMethodManagementURLResponse {
  data?: PipoGetPaymentMethodManagementURL;
  code: number;
  message: string;
}

export interface PipoGetUserPaymentMethods {
  payment_method_list?: Array<PaymentMethodInfo>;
}

export interface PipoGetUserPaymentMethodsRequest {
  /** Coze用户 传common文件里的 ConnectorIDBotStudio */
  UserType?: Int64;
  UserID?: string;
  /** 支付数额 */
  Amount?: Int64;
  /** 币种，如coze_token */
  Currency?: string;
  /** 如果是协议付/订阅场景，则填充此值 */
  scene?: common.Scene;
  'Tt-Agw-Client-Ip'?: string;
}

export interface PipoGetUserPaymentMethodsResponse {
  data?: PipoGetUserPaymentMethods;
  code: number;
  message: string;
}

export interface PipoGWAgreementDeductionCallbackRequest {
  Body?: Blob;
}

export interface PipoGWAgreementDeductionCallbackResponse {
  content?: string;
}

export interface PipoInstantPayCallbackRequest {
  Body?: Blob;
}

export interface PipoInstantPayCallbackResponse {
  content?: string;
}

export interface PipoInvoiceCallbackRequest {
  Body?: Blob;
}

export interface PipoInvoiceCallbackResponse {
  content?: string;
}

export interface RefundOrderCallbackRequest {
  Body?: Blob;
}

export interface RefundOrderCallbackResponse {
  content?: string;
}

export interface SubscriptionNotifyRequest {
  Body?: Blob;
}

export interface SubscriptionNotifyResponse {
  content?: string;
}
/* eslint-enable */
