/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useMemo } from 'react';

import { isGlobalVariableKey } from '@coze-workflow/variable';

import { useNodeAvailableVariablesWithNode } from '@/form-extensions/hooks';

import { type RelatedVariablesHookProps } from '../types';

export default function useRelatedVariable({
  variablesFormatter = v => v,
}: RelatedVariablesHookProps) {
  const availableVariables = useNodeAvailableVariablesWithNode();

  const globalVariables = useMemo(
    () =>
      variablesFormatter(
        availableVariables.filter(
          item => item.nodeId && isGlobalVariableKey(item.nodeId),
        ),
      ),
    [availableVariables, variablesFormatter],
  );

  return {
    globalVariables,
  };
}
