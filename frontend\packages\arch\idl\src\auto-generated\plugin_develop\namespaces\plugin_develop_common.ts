/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum APIDebugStatus {
  DebugWaiting = 0,
  DebugPassed = 1,
}

export enum APIListOrderBy {
  CreateTime = 1,
}

export enum APIMethod {
  GET = 1,
  POST = 2,
  PUT = 3,
  DELETE = 4,
  PATCH = 5,
}

/** 针对File类型参数的细分类型 */
export enum AssistParameterType {
  DEFAULT = 1,
  IMAGE = 2,
  DOC = 3,
  CODE = 4,
  PPT = 5,
  TXT = 6,
  EXCEL = 7,
  AUDIO = 8,
  ZIP = 9,
  VIDEO = 10,
  /** 语音 */
  VOICE = 12,
}

export enum AsyncTaskExecuteStatus {
  /** 未知状态 */
  Unknown = 0,
  /** 执行中 */
  Executing = 1,
  /** 执行成功 */
  ExecuteSucceed = 2,
  /** 执行失败 */
  ExecuteFailed = 3,
}

/** enum OauthAuthSubType {
     Standard = 0
}
enum AuthorizationSubyType {
     Service_ApiKey = 0
     Service_BytedanceZeroTrust   = 1,
     Service_OIDC = 2,
     Oauth_Standard = 3,
} */
export enum AuthorizationServiceLocation {
  Header = 1,
  Query = 2,
}

export enum AuthorizationType {
  None = 0,
  Service = 1,
  OAuth = 3,
}

export enum CardAuditStatus {
  /** 审核不通过(原因见详情) */
  Rejected = -1,
  /** 审核通过 */
  Approved = 1,
  /** 待审核 */
  Pending = 100,
}

export enum CardBizType {
  Plugin = 1,
  Workflow = 2,
}

export enum CardBusinessType {
  Plugin = 1,
  WorkFlow = 2,
}

export enum CardCategory {
  Official = 1,
  Custom = 2,
}

export enum CardDisplayType {
  /** 基础 */
  Basic = 1,
  /** 竖向列表 */
  List = 2,
  /** 自定义卡片 */
  Custom = 3,
  /** 横向列表 */
  Slide = 4,
}

export enum CardStatus {
  Draft = 0,
  Published = 1,
  UnPublish = 2,
}

export enum CreationMethod {
  COZE = 0,
  IDE = 1,
}

export enum DebugExampleStatus {
  Default = 0,
  Enable = 1,
  Disable = 2,
}

export enum DebugOperation {
  /** 调试，会保存调试状态，会校验返回值 */
  Debug = 1,
  /** 仅解析返回值结构 */
  Parse = 2,
}

/** 默认入参的设置来源 */
export enum DefaultParamSource {
  /** 默认用户输入 */
  Input = 0,
  /** 引用变量 */
  Variable = 1,
}

export enum FeedbackType {
  Unknown = 0,
  /** 未找到需要的插件 */
  NotFoundPlugin = 1,
  /** 官方插件反馈 */
  OfficialPlugin = 2,
}

export enum FieldType {
  Object = 1,
  String = 2,
  Integer = 3,
  Bool = 4,
  Array = 5,
  Number = 6,
}

export enum GrantType {
  TokenExchange = 1,
  ClientCredential = 2,
}

export enum InstallStatus {
  USING = 1,
  REMOVE = 2,
  OFFLINE = 3,
  /** 查询不传，展示用 */
  NOTINSTALL = 4,
}

/** 授权状态 */
export enum OAuthStatus {
  Authorized = 1,
  Unauthorized = 2,
}

export enum OnlineStatus {
  OFFLINE = 0,
  ONLINE = 1,
}

export enum OrderBy {
  CreateTime = 0,
  UpdateTime = 1,
  PublishTime = 2,
  Hot = 3,
}

export enum ParameterLocation {
  Path = 1,
  Query = 2,
  Body = 3,
  Header = 4,
}

export enum ParameterType {
  String = 1,
  Integer = 2,
  Number = 3,
  Object = 4,
  Array = 5,
  Bool = 6,
}

export enum PluginCardStatus {
  Latest = 1,
  /** 主卡片版本有升级 */
  NeedUpdate = 2,
  /** 插件工具出参不匹配 */
  ParamMisMatch = 3,
}

export enum PluginCurrentInfoItemType {
  source_code = 1,
  input_params = 2,
  openapi = 3,
}

export enum PluginDataFormat {
  OpenAPI = 1,
  Curl = 2,
  Postman = 3,
  Swagger = 4,
}

export enum PluginListPluginType {
  /** 不存在workflow */
  ExceptWorkflow = 0,
  Workflow = 1,
  API = 2,
  /** 仅team内plugin */
  SpaceAPI = 3,
}

/** plugin枚举值 */
export enum PluginParamTypeFormat {
  FileUrl = 0,
  ImageUrl = 1,
  DocUrl = 2,
  CodeUrl = 3,
  PptUrl = 4,
  TxtUrl = 5,
  ExcelUrl = 6,
  AudioUrl = 7,
  ZipUrl = 8,
  VideoUrl = 9,
}

export enum PluginPricingCalculationType {
  /** 按次数 */
  ByTimes = 1,
  /** 按基本单位 (例如token) */
  ByUnit = 2,
  /** 无限制 */
  Unlimited = 3,
}

export enum PluginPricingStrategy {
  /** 免费 */
  Free = 0,
  /** 用量制 */
  Quantity = 1,
  /** 订阅制 */
  Subscribe = 2,
}

export enum PluginProductStatus {
  Default = 0,
  Listed = 1,
  Unlisted = 2,
  Reviewing = 3,
}

export enum PluginReferrerScene {
  SingleAgent = 0,
  WorkflowLlmNode = 1,
}

export enum PluginStatus {
  /** 默认值 */
  Draft = 0,
  SUBMITTED = 1,
  REVIEWING = 2,
  PREPARED = 3,
  PUBLISHED = 4,
  OFFLINE = 5,
  /** 禁用 */
  BANNED = 6,
}

export enum PluginToolAuthType {
  /** 强授权 */
  Required = 0,
  /** 半匿名授权 */
  Supported = 1,
  /** 不授权 */
  Disable = 2,
}

export enum PluginType {
  PLUGIN = 1,
  APP = 2,
  FUNC = 3,
  WORKFLOW = 4,
  IMAGEFLOW = 5,
  LOCAL = 6,
}

export enum PluginTypeForFilter {
  /** 包含PLUGIN和APP */
  CloudPlugin = 1,
  /** 包含LOCAL */
  LocalPlugin = 2,
  /** 包含WORKFLOW和IMAGEFLOW */
  WorkflowPlugin = 3,
}

export enum PricingCurrencyType {
  /** 计价的货币类型 */
  USD = 0,
  CNY = 1,
}

/** Begin 插件计费信息 */
export enum PricingInterval {
  /** 计价的时间周期 */
  Second = 1,
  Minute = 2,
  Hour = 3,
  Day = 4,
  Month = 5,
  Year = 6,
}

export enum ProductDraftStatus {
  Default = 0,
  /** 审核中 */
  Pending = 1,
  /** 审核通过 */
  Approved = 2,
  /** 审核不通过 */
  Rejected = 3,
  /** 审核已废弃 */
  Abandoned = 4,
}

export enum ProductStatus {
  NeverListed = 0,
  Listed = 1,
  Unlisted = 2,
  Banned = 3,
}

export enum ProductUnlistType {
  ByAdmin = 1,
  ByUser = 2,
}

export enum ProgramLang {
  Curl = 1,
  Wget = 2,
  NodeJS = 3,
  Python = 4,
  Golang = 5,
}

export enum QueryCardStatus {
  Published = 1,
  UnPublish = 2,
}

export enum RunMode {
  DefaultToSync = 0,
  Sync = 1,
  Async = 2,
  Streaming = 3,
}

export enum ScopeType {
  /** 所有 */
  All = 0,
  /** 自己 */
  Self = 1,
}

export enum ServiceAuthSubType {
  ApiKey = 0,
  BytedanceZeroTrust = 1,
  OIDC = 2,
}

export enum SpaceRoleType {
  /** 默认 */
  Default = 0,
  /** owner */
  Owner = 1,
  /** 管理员 */
  Admin = 2,
  /** 普通成员 */
  Member = 3,
}

export enum TriggerEventType {
  /** 定时触发 */
  Time = 1,
  /** 事件触发 */
  Webhook = 2,
}

export enum TriggerExecStatus {
  /** 未执行过 */
  Default = 0,
  /** 执行成功 */
  Succeed = 1,
  /** 执行失败 */
  Failed = 2,
}

export enum TriggerTaskType {
  /** 预设任务 */
  PresetTask = 1,
  /** 用户任务 */
  UserTask = 2,
}

export enum WorkflowResponseMode {
  /** 模型总结 */
  UseLLM = 0,
  /** 不使用模型总结 */
  SkipLLM = 1,
}

export interface AgentTaskExecInfo {
  /** task id */
  task_id?: string;
  /** 触发id */
  serial_id?: string;
  /** 任务类型 */
  task_type?: TriggerTaskType;
  /** 事件类型 */
  event_type?: TriggerEventType;
  /** agent版本 */
  bot_version?: string;
  /** 用户id，仅用户触发器有 */
  trigger_user_id?: string;
  /** 渠道 */
  connector_id?: string;
  /** 执行结果 true为成功 */
  exec_status?: TriggerExecStatus;
  /** 下次运行时间，秒级时间戳 */
  next_exec_duration?: number;
  /** 生效状态，true为生效 */
  valid_status?: boolean;
  /** 配置 */
  config?: string;
}

export interface AgentTaskInfo {
  /** task id */
  id?: string;
  /** task name */
  task_name?: string;
  /** 任务类型 */
  task_type?: TriggerTaskType;
  /** 任务触发类型 */
  event_type?: TriggerEventType;
  /** 任务配置 */
  config?: string;
  /** 已发布渠道，agent仅支持飞书 */
  connector_ids?: Array<string>;
  /** 渠道 */
  connector_id?: string;
  /** 执行结果 true为成功 */
  exec_status?: TriggerExecStatus;
  /** 下次运行时间，秒级时间戳 */
  next_exec_duration?: number;
  /** 生效状态，true为生效 */
  valid_status?: boolean;
  /** agent版本 */
  bot_version?: string;
  /** 用户id，仅用户触发器有 */
  trigger_user_id?: string;
}

export interface APIExtend {
  /** tool维度授权类型 */
  auth_mode?: PluginToolAuthType;
}

export interface APIInfo {
  plugin_id?: string;
  api_id?: string;
  plugin_version?: string;
  api_name?: string;
}

export interface APIListOrder {
  order_by?: APIListOrderBy;
  desc?: boolean;
}

export interface APIParameter {
  /** for前端，无实际意义 */
  id?: string;
  name?: string;
  desc?: string;
  type?: ParameterType;
  sub_type?: ParameterType;
  location?: ParameterLocation;
  is_required?: boolean;
  sub_parameters?: Array<APIParameter>;
  global_default?: string;
  global_disable?: boolean;
  local_default?: string;
  local_disable?: boolean;
  /** 默认入参的设置来源 */
  default_param_source?: DefaultParamSource;
  /** 引用variable的key */
  variable_ref?: string;
  assist_type?: AssistParameterType;
}

export interface APIStruct {
  name?: string;
  type?: FieldType;
  children?: Array<APIStruct>;
}

export interface AsyncConfig {
  /** 插件id或workflow id */
  plugin_id?: string;
  /** 插件tool名称 */
  api_name?: string;
  /** 开关状态 */
  switch_status?: boolean;
  /** 异步插件运行时的提示信息，最大1000个字符 */
  message?: string;
}

export interface AsyncPluginTask {
  /** task id */
  task_id?: string;
  /** 任务名称 */
  task_name?: string;
  /** 任务类型 插件｜工作流｜图像流 */
  task_type?: PluginType;
  /** 用户提问 */
  query_msg?: string;
  /** 任务状态 */
  task_status?: AsyncTaskExecuteStatus;
  /** 创建时间 */
  create_time?: Int64;
  /** 插件头像url */
  plugin_icon?: string;
}

export interface BindCardsStatusData {
  bind_cards?: Array<SignleBindCardStatus>;
  publish_time?: string;
}

export interface CardBindInfo {
  card_id?: string;
  mapping_rule?: string;
  max_display_rows?: Int64;
  card_version_num?: string;
  llm_text_card?: boolean;
  category?: CardCategory;
}

export interface CardBusinessInfo {
  biz_type: CardBusinessType;
  business_id: string;
  unique_id: string;
}

export interface CheckAndLockPluginEditData {
  /** 是否已被占用 */
  Occupied?: boolean;
  /** 如果已经被占用了，返回用户ID */
  user?: Creator;
  /** 是否强占成功 */
  Seized?: boolean;
}

export interface CodeInfo {
  /** json */
  plugin_desc?: string;
  /** yaml */
  openapi_desc?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
}

export interface commonParamSchema {
  name?: string;
  value?: string;
}

export interface CozeCardInfo {
  card_id?: string;
  draft_card_id?: string;
  name?: string;
  creator_id?: string;
  card_category?: CardCategory;
  card_display_type?: CardDisplayType;
  version_num?: string;
  version_name?: string;
  description?: string;
  update_time?: string;
  creator?: Creator;
  basic_card_id?: string;
  list_card_id?: string;
  properties?: string;
  properties_hash?: string;
  thumbnail?: string;
  card_status?: CardStatus;
  publish_time?: string;
  audit_status?: CardAuditStatus;
  audit_failure_details?: Array<number>;
}

export interface CreateCardData {
  card_id?: string;
  draft_card_id?: string;
}

export interface Creator {
  id?: string;
  name?: string;
  avatar_url?: string;
  /** 是否是自己创建的 */
  self?: boolean;
  space_roly_type?: SpaceRoleType;
  /** 用户名 */
  user_unique_name?: string;
  /** 用户标签 */
  user_label?: UserLabel;
}

/** --依赖卡片实体-- */
export interface DebugExample {
  req_example?: string;
  resp_example?: string;
}

export interface DuplicateAPIInfo {
  method?: string;
  path?: string;
  count?: Int64;
}

export interface GetPlaygroundPluginListData {
  plugin_list?: Array<PluginInfoForPlayground>;
  total?: number;
}

export interface GetPluginCardsData {
  plugin_cards?: Array<PluginCards>;
}

export interface GetPluginChangelogData {
  total?: Int64;
  change_log_list?: Array<PluginChangelogRecord>;
}

export interface GetPluginListData {
  plugin_infos?: Array<PluginInfo>;
  total?: Int64;
  page?: number;
  size?: number;
}

export interface GetUserAuthorityData {
  can_edit?: boolean;
  can_read?: boolean;
  can_delete?: boolean;
  can_debug?: boolean;
  can_publish?: boolean;
  can_read_changelog?: boolean;
}

export interface GetWorkflowMessageNodesData {
  id?: string;
  plugin_id?: string;
  name?: string;
  message_nodes?: Array<NodeInfo>;
}

export interface NodeInfo {
  node_id?: string;
  node_type?: string;
  node_title?: string;
}

export interface PluginApi {
  /** operationId */
  name?: string;
  /** summary */
  desc?: string;
  parameters?: Array<PluginParameter>;
  plugin_id?: string;
  plugin_name?: string;
  /** 序号和playground保持一致 */
  api_id?: string;
  record_id?: string;
  /** 卡片绑定信息，未绑定则为nil */
  card_binding_info?: PresetCardBindingInfo;
  /** 调试api示例 */
  debug_example?: DebugExample;
  function_name?: string;
  /** 运行模式 */
  run_mode?: RunMode;
}

export interface PluginAPIInfo {
  plugin_id?: string;
  api_id?: string;
  name?: string;
  desc?: string;
  path?: string;
  method?: APIMethod;
  request_params?: Array<APIParameter>;
  response_params?: Array<APIParameter>;
  create_time?: string;
  debug_status?: APIDebugStatus;
  disabled?: boolean;
  statistic_data?: PluginStatisticData;
  /** ide创建插件展示tool的在线状态 */
  online_status?: OnlineStatus;
  api_extend?: APIExtend;
  /** 卡片绑定信息，未绑定则为nil */
  card_binding_info?: PresetCardBindingInfo;
  /** 调试示例 */
  debug_example?: DebugExample;
  /** 调试示例状态 */
  debug_example_status?: DebugExampleStatus;
  function_name?: string;
}

export interface PluginCardResp {
  type_for_model?: string;
  return_direct?: string;
  card_type?: string;
  template_url?: string;
  template_id?: string;
  template_h5_url?: string;
  template_h5?: TemplateH5UrlObj;
  gravity?: string;
  response_for_model?: string;
  content_type?: string;
  code?: string;
  data?: string;
  response_type?: string;
  setting_response?: string;
  dsl_content?: string;
  /** 卡片数据-模型的总结模式
0-未设置，线上原逻辑
1-默认大模型结合剪裁内容做总结
2-线上原逻辑 */
  card_summary_mode?: string;
  /** 展示在卡片中的结构化数据 */
  info_in_card?: string;
}

export interface PluginCards {
  card_id?: string;
  plugin_id?: string;
  api_name?: string;
  status?: PluginCardStatus;
}

export interface PluginChangelogRecord {
  changelog_id?: string;
  create_time?: string;
  creator?: Creator;
  operation?: string;
}

export interface PluginCurrentInfoItemInfo {
  item_id?: string;
  /** 1. source_code 类型 2. input 类型 3. api yaml 类型 */
  item_type?: PluginCurrentInfoItemType;
  content?: string;
}

export interface PluginIcon {
  uri?: string;
  url?: string;
}

export interface PluginInfo {
  id?: string;
  /** name_for_human */
  name?: string;
  /** description_for_human */
  desc_for_human?: string;
  plugin_icon?: string;
  plugin_type?: PluginType;
  status?: PluginStatus;
  /** json */
  plugin_desc?: string;
  /** yaml,openapi插件不返回 */
  openapi_desc?: string;
  auth?: number;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  create_time?: Int64;
  update_time?: Int64;
  ClientUrl?: string;
  Scope?: string;
  RedirectUri?: string;
  /** 查询用户和插件关系时返回
用户安装状态 */
  InstallStatus?: InstallStatus;
  /** 用户id，用户查询安装/未安装列表时选择 */
  UserID?: Int64;
  WorkFlowId?: string;
  /** 插件包含的api名称，所有类型的插件都支持 */
  api_names?: Array<string>;
  /** 创建人信息 */
  creator?: Creator;
  /** 发布状态 */
  publish_status?: boolean;
  space_id?: string;
  /** 插件统计数据 */
  statistic_data?: PluginStatisticData;
  /** 公共参数列表 */
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  /** plugin的商品上下架状态 */
  plugin_product_list_status?: ProductStatus;
  /** plugin的商品状态(组合状态)(已废弃) */
  plugin_product_status?: PluginProductStatus;
  /** 插件创建方式 */
  creation_method?: CreationMethod;
  /** plugin的商品审核状态 */
  plugin_product_draft_status?: ProductDraftStatus;
  /** 当前用户是否可以删除插件 */
  cur_user_can_del?: boolean;
}

export interface PluginInfoForPlayground {
  id?: string;
  /** name_for_human */
  name?: string;
  /** description_for_human */
  desc_for_human?: string;
  plugin_icon?: string;
  plugin_type?: PluginType;
  status?: PluginStatus;
  auth?: number;
  client_id?: string;
  client_secret?: string;
  plugin_apis?: Array<PluginApi>;
  /** 插件标签 */
  tag?: Int64;
  create_time?: string;
  update_time?: string;
  /** 创建人信息 */
  creator?: Creator;
  /** 空间id */
  space_id?: string;
  /** 插件统计数据 */
  statistic_data?: PluginStatisticData;
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  /** plugin的商品状态 */
  plugin_product_status?: ProductStatus;
  /** plugin商品下架类型 */
  plugin_product_unlist_type?: ProductUnlistType;
  /** 素材id */
  material_id?: string;
  /** 渠道id */
  channel_id?: number;
  /** 插件创建方式 */
  creation_method?: CreationMethod;
  /** 是否为官方插件 */
  is_official?: boolean;
  /** 项目id */
  project_id?: string;
  /** 版本号，毫秒时间戳 */
  version_ts?: string;
  /** 版本名称 */
  version_name?: string;
}

export interface PluginMetaInfo {
  name?: string;
  desc?: string;
  url?: string;
  icon?: PluginIcon;
  auth_type?: Array<AuthorizationType>;
  /** service */
  location?: AuthorizationServiceLocation;
  /** service */
  key?: string;
  /** service */
  service_token?: string;
  /** json序列化 */
  oauth_info?: string;
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  sub_auth_type?: number;
  auth_payload?: string;
  /** 是否固定出口ip */
  fixed_export_ip?: boolean;
  /** 私网连接id */
  private_link_id?: string;
}

export interface PluginParameter {
  name?: string;
  desc?: string;
  required?: boolean;
  type?: string;
  sub_parameters?: Array<PluginParameter>;
  /** 如果Type是数组，则有subtype */
  sub_type?: string;
  /** 如果入参的值是引用的则有fromNodeId */
  from_node_id?: string;
  /** 具体引用哪个节点的key */
  from_output?: Array<string>;
  /** 如果入参是用户手输 就放这里 */
  value?: string;
  /** 格式化参数 */
  format?: PluginParamTypeFormat;
}

export interface PluginPriceInfo {
  /** 手动填的价格
价格 */
  Price?: string;
  /** 价格对应的货币类型 */
  CurrencyType?: PricingCurrencyType;
  /** 价格对应的时间周期 */
  Interval?: PricingInterval;
  /** 每次调用消耗的基本单位的数量 */
  UnitsForOnce?: string;
  /** 基本单位的名称 (例如token) */
  UnitName?: string;
}

export interface PluginPriceLimit {
  /** 价格限制
次数限制 */
  TimesLimit?: string;
  /** 次数限制对应的时间周期 */
  TimesInterval?: PricingInterval;
  /** 基本单位的限制 */
  UnitsLimit?: string;
  /** 基本单位的限制对应的时间周期 */
  UnitsInterval?: PricingInterval;
}

export interface PluginPriceResult {
  /** 自动算的每次调用价格
价格 */
  Price?: string;
  /** 价格对应的货币类型 */
  CurrencyType?: PricingCurrencyType;
  /** 每次调用消耗的基本单位 */
  TokensForOnce?: string;
}

export interface PluginPricingRule {
  /** 为空:对整个plugin生效; 非空:对单个API生效 */
  APIName?: string;
  /** 备注信息, 比如采购方案 */
  Comment?: string;
  /** 调用限制 */
  PriceLimit?: PluginPriceLimit;
  /** 手动填的成本 */
  PriceInfo?: PluginPriceInfo;
  /** 计算出来的每次调用成本 */
  PriceResult?: PluginPriceResult;
  /** 一级规则，默认免费 */
  PricingStrategy?: PluginPricingStrategy;
  /** 二级规则 */
  PricingCalculationType?: PluginPricingCalculationType;
  /** 规则ID */
  RuleID?: string;
  /** 0: 对整个plugin生效; 非0: 对单个API生效 */
  APIID?: string;
  /** 插件信息 */
  PluginInfo?: PluginInfo;
}

/** End 插件计费信息 */
export interface PluginPublishInfo {
  /** 发布人 */
  publisher_id?: Int64;
  /** 版本，毫秒时间戳 */
  version_ts?: Int64;
  /** 版本名称 */
  version_name?: string;
  /** 版本描述 */
  version_desc?: string;
}

export interface PluginStatisticData {
  /** 为空就不展示 */
  bot_quote?: number;
}

/** 插件预置卡片绑定信息 */
export interface PresetCardBindingInfo {
  card_id?: string;
  card_version_num?: string;
  status?: PluginCardStatus;
  /** 缩略图 */
  thumbnail?: string;
}

export interface PublishedPluginListData {
  plugin_list?: Array<PluginInfoForPlayground>;
  total?: Int64;
  page?: number;
  size?: number;
}

export interface QueryCardDetailData {
  card_detail?: CozeCardInfo;
}

export interface QueryCardListData {
  card_list?: Array<CozeCardInfo>;
  total?: Int64;
}

export interface RegisterPluginData {
  plugin_id?: string;
  openapi?: string;
}

export interface ResponseStyle {
  workflow_response_mode?: WorkflowResponseMode;
}

export interface SignleBindCardStatus {
  card_id?: string;
  business_id?: string;
  unique_id?: string;
  status?: PluginCardStatus;
  business_type?: CardBusinessType;
  card_version?: string;
}

export interface TemplateH5UrlObj {
  js_url?: string;
  css_url?: string;
  json_url?: string;
}

export interface UpdatePluginData {
  res?: boolean;
  edit_version?: number;
}

export interface UpsertPluginData {
  plugin_id?: string;
}

export interface UserLabel {
  label_id?: string;
  label_name?: string;
  icon_uri?: string;
  icon_url?: string;
  jump_link?: string;
}
/* eslint-enable */
