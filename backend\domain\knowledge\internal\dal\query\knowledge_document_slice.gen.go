// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/knowledge/internal/dal/model"
)

func newKnowledgeDocumentSlice(db *gorm.DB, opts ...gen.DOOption) knowledgeDocumentSlice {
	_knowledgeDocumentSlice := knowledgeDocumentSlice{}

	_knowledgeDocumentSlice.knowledgeDocumentSliceDo.UseDB(db, opts...)
	_knowledgeDocumentSlice.knowledgeDocumentSliceDo.UseModel(&model.KnowledgeDocumentSlice{})

	tableName := _knowledgeDocumentSlice.knowledgeDocumentSliceDo.TableName()
	_knowledgeDocumentSlice.ALL = field.NewAsterisk(tableName)
	_knowledgeDocumentSlice.ID = field.NewInt64(tableName, "id")
	_knowledgeDocumentSlice.KnowledgeID = field.NewInt64(tableName, "knowledge_id")
	_knowledgeDocumentSlice.DocumentID = field.NewInt64(tableName, "document_id")
	_knowledgeDocumentSlice.Content = field.NewString(tableName, "content")
	_knowledgeDocumentSlice.Sequence = field.NewFloat64(tableName, "sequence")
	_knowledgeDocumentSlice.CreatedAt = field.NewInt64(tableName, "created_at")
	_knowledgeDocumentSlice.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_knowledgeDocumentSlice.DeletedAt = field.NewField(tableName, "deleted_at")
	_knowledgeDocumentSlice.CreatorID = field.NewInt64(tableName, "creator_id")
	_knowledgeDocumentSlice.SpaceID = field.NewInt64(tableName, "space_id")
	_knowledgeDocumentSlice.Status = field.NewInt32(tableName, "status")
	_knowledgeDocumentSlice.FailReason = field.NewString(tableName, "fail_reason")
	_knowledgeDocumentSlice.Hit = field.NewInt64(tableName, "hit")

	_knowledgeDocumentSlice.fillFieldMap()

	return _knowledgeDocumentSlice
}

// knowledgeDocumentSlice 知识库文件切片表
type knowledgeDocumentSlice struct {
	knowledgeDocumentSliceDo

	ALL         field.Asterisk
	ID          field.Int64   // 主键ID
	KnowledgeID field.Int64   // knowledge id
	DocumentID  field.Int64   // document id
	Content     field.String  // 切片内容
	Sequence    field.Float64 // 切片顺序号, 从1开始
	CreatedAt   field.Int64   // Create Time in Milliseconds
	UpdatedAt   field.Int64   // Update Time in Milliseconds
	DeletedAt   field.Field   // Delete Time in Milliseconds
	CreatorID   field.Int64   // 创建者ID
	SpaceID     field.Int64   // 空间ID
	Status      field.Int32   // 状态
	FailReason  field.String  // 失败原因
	Hit         field.Int64   // 命中次数

	fieldMap map[string]field.Expr
}

func (k knowledgeDocumentSlice) Table(newTableName string) *knowledgeDocumentSlice {
	k.knowledgeDocumentSliceDo.UseTable(newTableName)
	return k.updateTableName(newTableName)
}

func (k knowledgeDocumentSlice) As(alias string) *knowledgeDocumentSlice {
	k.knowledgeDocumentSliceDo.DO = *(k.knowledgeDocumentSliceDo.As(alias).(*gen.DO))
	return k.updateTableName(alias)
}

func (k *knowledgeDocumentSlice) updateTableName(table string) *knowledgeDocumentSlice {
	k.ALL = field.NewAsterisk(table)
	k.ID = field.NewInt64(table, "id")
	k.KnowledgeID = field.NewInt64(table, "knowledge_id")
	k.DocumentID = field.NewInt64(table, "document_id")
	k.Content = field.NewString(table, "content")
	k.Sequence = field.NewFloat64(table, "sequence")
	k.CreatedAt = field.NewInt64(table, "created_at")
	k.UpdatedAt = field.NewInt64(table, "updated_at")
	k.DeletedAt = field.NewField(table, "deleted_at")
	k.CreatorID = field.NewInt64(table, "creator_id")
	k.SpaceID = field.NewInt64(table, "space_id")
	k.Status = field.NewInt32(table, "status")
	k.FailReason = field.NewString(table, "fail_reason")
	k.Hit = field.NewInt64(table, "hit")

	k.fillFieldMap()

	return k
}

func (k *knowledgeDocumentSlice) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := k.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (k *knowledgeDocumentSlice) fillFieldMap() {
	k.fieldMap = make(map[string]field.Expr, 13)
	k.fieldMap["id"] = k.ID
	k.fieldMap["knowledge_id"] = k.KnowledgeID
	k.fieldMap["document_id"] = k.DocumentID
	k.fieldMap["content"] = k.Content
	k.fieldMap["sequence"] = k.Sequence
	k.fieldMap["created_at"] = k.CreatedAt
	k.fieldMap["updated_at"] = k.UpdatedAt
	k.fieldMap["deleted_at"] = k.DeletedAt
	k.fieldMap["creator_id"] = k.CreatorID
	k.fieldMap["space_id"] = k.SpaceID
	k.fieldMap["status"] = k.Status
	k.fieldMap["fail_reason"] = k.FailReason
	k.fieldMap["hit"] = k.Hit
}

func (k knowledgeDocumentSlice) clone(db *gorm.DB) knowledgeDocumentSlice {
	k.knowledgeDocumentSliceDo.ReplaceConnPool(db.Statement.ConnPool)
	return k
}

func (k knowledgeDocumentSlice) replaceDB(db *gorm.DB) knowledgeDocumentSlice {
	k.knowledgeDocumentSliceDo.ReplaceDB(db)
	return k
}

type knowledgeDocumentSliceDo struct{ gen.DO }

type IKnowledgeDocumentSliceDo interface {
	gen.SubQuery
	Debug() IKnowledgeDocumentSliceDo
	WithContext(ctx context.Context) IKnowledgeDocumentSliceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IKnowledgeDocumentSliceDo
	WriteDB() IKnowledgeDocumentSliceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IKnowledgeDocumentSliceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IKnowledgeDocumentSliceDo
	Not(conds ...gen.Condition) IKnowledgeDocumentSliceDo
	Or(conds ...gen.Condition) IKnowledgeDocumentSliceDo
	Select(conds ...field.Expr) IKnowledgeDocumentSliceDo
	Where(conds ...gen.Condition) IKnowledgeDocumentSliceDo
	Order(conds ...field.Expr) IKnowledgeDocumentSliceDo
	Distinct(cols ...field.Expr) IKnowledgeDocumentSliceDo
	Omit(cols ...field.Expr) IKnowledgeDocumentSliceDo
	Join(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentSliceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentSliceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentSliceDo
	Group(cols ...field.Expr) IKnowledgeDocumentSliceDo
	Having(conds ...gen.Condition) IKnowledgeDocumentSliceDo
	Limit(limit int) IKnowledgeDocumentSliceDo
	Offset(offset int) IKnowledgeDocumentSliceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeDocumentSliceDo
	Unscoped() IKnowledgeDocumentSliceDo
	Create(values ...*model.KnowledgeDocumentSlice) error
	CreateInBatches(values []*model.KnowledgeDocumentSlice, batchSize int) error
	Save(values ...*model.KnowledgeDocumentSlice) error
	First() (*model.KnowledgeDocumentSlice, error)
	Take() (*model.KnowledgeDocumentSlice, error)
	Last() (*model.KnowledgeDocumentSlice, error)
	Find() ([]*model.KnowledgeDocumentSlice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.KnowledgeDocumentSlice, err error)
	FindInBatches(result *[]*model.KnowledgeDocumentSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.KnowledgeDocumentSlice) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IKnowledgeDocumentSliceDo
	Assign(attrs ...field.AssignExpr) IKnowledgeDocumentSliceDo
	Joins(fields ...field.RelationField) IKnowledgeDocumentSliceDo
	Preload(fields ...field.RelationField) IKnowledgeDocumentSliceDo
	FirstOrInit() (*model.KnowledgeDocumentSlice, error)
	FirstOrCreate() (*model.KnowledgeDocumentSlice, error)
	FindByPage(offset int, limit int) (result []*model.KnowledgeDocumentSlice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IKnowledgeDocumentSliceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (k knowledgeDocumentSliceDo) Debug() IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Debug())
}

func (k knowledgeDocumentSliceDo) WithContext(ctx context.Context) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.WithContext(ctx))
}

func (k knowledgeDocumentSliceDo) ReadDB() IKnowledgeDocumentSliceDo {
	return k.Clauses(dbresolver.Read)
}

func (k knowledgeDocumentSliceDo) WriteDB() IKnowledgeDocumentSliceDo {
	return k.Clauses(dbresolver.Write)
}

func (k knowledgeDocumentSliceDo) Session(config *gorm.Session) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Session(config))
}

func (k knowledgeDocumentSliceDo) Clauses(conds ...clause.Expression) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Clauses(conds...))
}

func (k knowledgeDocumentSliceDo) Returning(value interface{}, columns ...string) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Returning(value, columns...))
}

func (k knowledgeDocumentSliceDo) Not(conds ...gen.Condition) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Not(conds...))
}

func (k knowledgeDocumentSliceDo) Or(conds ...gen.Condition) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Or(conds...))
}

func (k knowledgeDocumentSliceDo) Select(conds ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Select(conds...))
}

func (k knowledgeDocumentSliceDo) Where(conds ...gen.Condition) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Where(conds...))
}

func (k knowledgeDocumentSliceDo) Order(conds ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Order(conds...))
}

func (k knowledgeDocumentSliceDo) Distinct(cols ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Distinct(cols...))
}

func (k knowledgeDocumentSliceDo) Omit(cols ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Omit(cols...))
}

func (k knowledgeDocumentSliceDo) Join(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Join(table, on...))
}

func (k knowledgeDocumentSliceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.LeftJoin(table, on...))
}

func (k knowledgeDocumentSliceDo) RightJoin(table schema.Tabler, on ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.RightJoin(table, on...))
}

func (k knowledgeDocumentSliceDo) Group(cols ...field.Expr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Group(cols...))
}

func (k knowledgeDocumentSliceDo) Having(conds ...gen.Condition) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Having(conds...))
}

func (k knowledgeDocumentSliceDo) Limit(limit int) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Limit(limit))
}

func (k knowledgeDocumentSliceDo) Offset(offset int) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Offset(offset))
}

func (k knowledgeDocumentSliceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Scopes(funcs...))
}

func (k knowledgeDocumentSliceDo) Unscoped() IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Unscoped())
}

func (k knowledgeDocumentSliceDo) Create(values ...*model.KnowledgeDocumentSlice) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Create(values)
}

func (k knowledgeDocumentSliceDo) CreateInBatches(values []*model.KnowledgeDocumentSlice, batchSize int) error {
	return k.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (k knowledgeDocumentSliceDo) Save(values ...*model.KnowledgeDocumentSlice) error {
	if len(values) == 0 {
		return nil
	}
	return k.DO.Save(values)
}

func (k knowledgeDocumentSliceDo) First() (*model.KnowledgeDocumentSlice, error) {
	if result, err := k.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentSlice), nil
	}
}

func (k knowledgeDocumentSliceDo) Take() (*model.KnowledgeDocumentSlice, error) {
	if result, err := k.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentSlice), nil
	}
}

func (k knowledgeDocumentSliceDo) Last() (*model.KnowledgeDocumentSlice, error) {
	if result, err := k.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentSlice), nil
	}
}

func (k knowledgeDocumentSliceDo) Find() ([]*model.KnowledgeDocumentSlice, error) {
	result, err := k.DO.Find()
	return result.([]*model.KnowledgeDocumentSlice), err
}

func (k knowledgeDocumentSliceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.KnowledgeDocumentSlice, err error) {
	buf := make([]*model.KnowledgeDocumentSlice, 0, batchSize)
	err = k.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (k knowledgeDocumentSliceDo) FindInBatches(result *[]*model.KnowledgeDocumentSlice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return k.DO.FindInBatches(result, batchSize, fc)
}

func (k knowledgeDocumentSliceDo) Attrs(attrs ...field.AssignExpr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Attrs(attrs...))
}

func (k knowledgeDocumentSliceDo) Assign(attrs ...field.AssignExpr) IKnowledgeDocumentSliceDo {
	return k.withDO(k.DO.Assign(attrs...))
}

func (k knowledgeDocumentSliceDo) Joins(fields ...field.RelationField) IKnowledgeDocumentSliceDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Joins(_f))
	}
	return &k
}

func (k knowledgeDocumentSliceDo) Preload(fields ...field.RelationField) IKnowledgeDocumentSliceDo {
	for _, _f := range fields {
		k = *k.withDO(k.DO.Preload(_f))
	}
	return &k
}

func (k knowledgeDocumentSliceDo) FirstOrInit() (*model.KnowledgeDocumentSlice, error) {
	if result, err := k.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentSlice), nil
	}
}

func (k knowledgeDocumentSliceDo) FirstOrCreate() (*model.KnowledgeDocumentSlice, error) {
	if result, err := k.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.KnowledgeDocumentSlice), nil
	}
}

func (k knowledgeDocumentSliceDo) FindByPage(offset int, limit int) (result []*model.KnowledgeDocumentSlice, count int64, err error) {
	result, err = k.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = k.Offset(-1).Limit(-1).Count()
	return
}

func (k knowledgeDocumentSliceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = k.Count()
	if err != nil {
		return
	}

	err = k.Offset(offset).Limit(limit).Scan(result)
	return
}

func (k knowledgeDocumentSliceDo) Scan(result interface{}) (err error) {
	return k.DO.Scan(result)
}

func (k knowledgeDocumentSliceDo) Delete(models ...*model.KnowledgeDocumentSlice) (result gen.ResultInfo, err error) {
	return k.DO.Delete(models)
}

func (k *knowledgeDocumentSliceDo) withDO(do gen.Dao) *knowledgeDocumentSliceDo {
	k.DO = *do.(*gen.DO)
	return k
}
