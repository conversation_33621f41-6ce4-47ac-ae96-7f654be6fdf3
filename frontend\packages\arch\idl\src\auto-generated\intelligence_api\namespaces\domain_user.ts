/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AccountStatus {
  Available = 1,
  /** 账户付费不可用 */
  Unavailable = 2,
}

export enum BenefitType {
  /** RPM 限流 */
  RateLimitRPM = 60,
  /** 模型 Input TPM 限流 */
  RateLimitModelInputTPM = 61,
  /** 模型 Output TPM 限流 */
  RateLimitModelOutputTPM = 62,
  /** 基础模型 Input TPM 限流 */
  RateLimitModelInputTPMBasic = 63,
  /** 基础模型 Output TPM 限流 */
  RateLimitModelOutputTPMBasic = 64,
}

export enum CommonUnit {
  /** 通用
资源点 */
  ResourcePoint = 1,
  /** 次 */
  Times = 2,
  /** 时间
年 */
  Year = 100,
  /** 月 */
  Month = 101,
  /** 日 */
  Day = 102,
  /** 小时 */
  Hour = 103,
  /** 分钟 */
  Min = 104,
  /** 秒 */
  Sec = 105,
  /** 毫秒 */
  MillSec = 106,
  /** 存储
字节 */
  Byte = 200,
  /** 千字节 */
  KB = 201,
  /** 兆字节 */
  MB = 202,
  /** 吉字节 */
  GB = 203,
}

export enum CozeAccountType {
  CozeAccountTypeUnknown = 0,
  CozeAccountTypePersonal = 1,
  CozeAccountTypeOrganization = 2,
}

export enum InstanceStatus {
  /** 创建中, 理论上不会返回该状态 */
  InstanceStatusCreating = 0,
  /** 运行中 */
  Running = 1,
  /** 创建失败, 理论上不会返回该状态 */
  InstanceStatusFailed = 2,
  /** 退订回收 */
  UnsubsRecycled = 3,
  /** 到期关停 */
  ExpiredClosed = 4,
  /** 到期回收 */
  ExpiredRecycled = 5,
  /** 欠费关停 */
  InstanceStatusOverdueShutdown = 6,
  /** 欠费回收 */
  InstanceStatusOverdueRecycled = 7,
  /** 退订关停 */
  InstanceStatusTerminatedShutdown = 8,
}

export enum ResourceUsageStrategy {
  /** 无限制 */
  UnLimit = 1,
  /** 限制 */
  Forbidden = 2,
  /** 通过额度校验 */
  ByQuota = 3,
}

export enum UserLevel {
  /** 免费版 */
  Free = 0,
  /** 海外 */
  PremiumLite = 10,
  /** Premium */
  Premium = 15,
  PremiumPlus = 20,
  /** 国内 */
  V1ProInstance = 100,
  /** 个人旗舰版 */
  ProPersonal = 110,
  /** 团队版 */
  Team = 120,
  /** 企业版 */
  Enterprise = 130,
}

export enum VolcanoUserType {
  Unknown = 0,
  RootUser = 1,
  BasicUser = 2,
}
/* eslint-enable */
