/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation from './annotation';
import * as filter from './filter';
import * as span from './span';
import * as common from './common';
import * as view from './view';

export type Int64 = string | number;

export interface BatchGetTracesAdvanceInfoRequest {
  workspace_id: string;
  traces: Array<TraceQueryParams>;
  platform_type?: string;
}

export interface BatchGetTracesAdvanceInfoResponse {
  traces_advance_info: Array<TraceAdvanceInfo>;
}

export interface CreateManualAnnotationRequest {
  annotation: annotation.Annotation;
  platform_type?: string;
}

export interface CreateManualAnnotationResponse {
  annotation_id?: string;
}

export interface CreateViewRequest {
  enterprise_id?: string;
  workspace_id: string;
  view_name: string;
  platform_type: string;
  span_list_type: string;
  filters: string;
}

export interface CreateViewResponse {
  id: string;
}

export interface DeleteManualAnnotationRequest {
  annotation_id: string;
  workspace_id: string;
  trace_id: string;
  span_id: string;
  start_time: string;
  annotation_key: string;
  platform_type?: string;
}

export interface DeleteManualAnnotationResponse {}

export interface DeleteViewRequest {
  view_id: string;
  workspace_id: string;
}

export interface DeleteViewResponse {}

export interface FieldMeta {
  value_type: string;
  filter_types: Array<string>;
  field_options?: filter.FieldOptions;
  support_customizable_option?: boolean;
}

export interface GetTraceRequest {
  workspace_id: string;
  trace_id: string;
  /** ms */
  start_time: string;
  /** ms */
  end_time: string;
  platform_type?: string;
}

export interface GetTraceResponse {
  spans: Array<span.OutputSpan>;
  traces_advance_info?: TraceAdvanceInfo;
}

export interface GetTracesMetaInfoRequest {
  platform_type?: string;
  span_list_type?: string;
  /** required */
  workspace_id?: string;
}

export interface GetTracesMetaInfoResponse {
  field_metas: Record<string, FieldMeta>;
}

export interface ListAnnotationsRequest {
  workspace_id: string;
  span_id: string;
  trace_id: string;
  start_time: string;
  platform_type?: string;
  desc_by_updated_at?: boolean;
}

export interface ListAnnotationsResponse {
  annotations: Array<annotation.Annotation>;
}

export interface ListSpansRequest {
  workspace_id: string;
  /** ms */
  start_time: string;
  /** ms */
  end_time: string;
  filters?: filter.FilterFields;
  page_size?: number;
  order_bys?: Array<common.OrderBy>;
  page_token?: string;
  platform_type?: string;
  /** default root span */
  span_list_type?: string;
}

export interface ListSpansResponse {
  spans: Array<span.OutputSpan>;
  next_page_token: string;
  has_more: boolean;
}

export interface ListViewsRequest {
  enterprise_id?: string;
  workspace_id: string;
  view_name?: string;
}

export interface ListViewsResponse {
  views: Array<view.View>;
}

export interface TokenCost {
  input: string;
  output: string;
}

export interface TraceAdvanceInfo {
  trace_id: string;
  tokens: TokenCost;
}

export interface TraceQueryParams {
  trace_id: string;
  start_time: string;
  end_time: string;
}

export interface UpdateManualAnnotationRequest {
  annotation_id: string;
  annotation: annotation.Annotation;
  platform_type?: string;
}

export interface UpdateManualAnnotationResponse {}

export interface UpdateViewRequest {
  view_id: string;
  workspace_id: string;
  view_name?: string;
  platform_type?: string;
  span_list_type?: string;
  filters?: string;
}

export interface UpdateViewResponse {}
/* eslint-enable */
