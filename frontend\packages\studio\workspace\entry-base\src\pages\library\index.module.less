.selected-params {
  border:1px solid var(--semi-color-focus-border);
  outline:0
}

.layout-content {
  height: calc(100%);

  :global(.coz-layout-header) {
    padding-bottom: 16px;
  }
}

.dropdown-options-6 {
  :global(.semi-cascader-option-lists) {
    height: 208px;
  }
}

.dropdown-options-7 {
  :global(.semi-cascader-option-lists) {
    height: 242px;
  }
}

.dropdown-options-8 {
  :global(.semi-cascader-option-lists) {
    height: 276px;
  }
}

.dropdown-options-9 {
  :global(.semi-cascader-option-lists) {
    height: 310px;
  }
}

.layout-header {
  .select {
    @apply shrink-0 !important;

    min-width: 128px;
  }

  .cascader{
    @apply shrink-0 !important;

    min-width: 128px;
  }
}
