/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

/** CPU scale settings for a cluster */
export interface ClusterCPUScaleSettings {
  /** Cluster name */
  cluster_name?: string;
  /** CPU scale settings for the cluster */
  cpu_scale_settings?: common.FuncCPUScaleSettings;
  /** CPU scale settings for specific zones */
  zone_scale_settings?: Record<string, common.FuncCPUScaleSettings>;
}

export interface EmergencyScaleRequest {
  /** Service ID */
  service_id: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Minimum replicas during emergency scale */
  min_replicas: Record<string, number>;
  /** Scale duration in minutes */
  scale_duration_minutes: number;
}

export interface EmergencyScaleResponse {
  /** Response code */
  code: number;
  /** Result of the emergency scale request */
  data: EmergencyScaleResult;
  /** Error message, if any */
  error: string;
}

/** Result for emergency scale request */
export interface EmergencyScaleResult {
  /** Function ID */
  function_id: string;
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** Minimum replicas during emergency scale */
  min_replicas: Record<string, number>;
  /** Expected start time for keeping minimum replicas */
  expect_keep_min_begin_at: string;
  /** Expected end time for keeping minimum replicas */
  expect_keep_min_end_at: string;
}

export interface FuncScaleSettingApiResponse {
  /** Response code */
  code: number;
  /** Response data containing function scale setting */
  data: FuncScaleSettingResponse;
  /** Error message, if any */
  error: string;
}

/** Function scale setting response */
export interface FuncScaleSettingResponse {
  /** Function ID */
  function_id: string;
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** Scale threshold set for the function */
  scale_threshold_set: ScaleThresholdsSet;
  /** Lag scale set name */
  lag_scale_set: string;
  /** Overload fast scale setting */
  overload_fast_scale_set: OverloadFastScaleSetting;
}

export interface GetFunctionScaleThresholdsSettingRequest {
  /** Function ID */
  service_id: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
}

export interface GetMQTriggerScaleThresholdSetRequest {
  /** Service ID */
  service_id: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Trigger ID of the MQ Trigger */
  trigger_id: string;
}

export interface GetMQTriggerScaleThresholdSetResponse {
  /** Response code */
  code: number;
  /** Data of the MQ Trigger scale threshold set */
  data: MQTriggerScaleThresholdData;
  /** Error message, if any */
  error: string;
}

export interface ListFuncScaleSettingApiRequest {
  /** Service ID */
  service_id: string;
  /** Optional region filter */
  region?: string;
  /** Optional cluster filter */
  cluster?: string;
  /** Optional offset for pagination */
  offset?: string;
  /** Optional limit for pagination */
  limit?: string;
}

export interface ListFuncScaleSettingApiResponse {
  /** Response code */
  code: number;
  /** List of function scale setting results */
  data: Array<ListFuncScaleSettingResult>;
  /** Error message, if any */
  error: string;
}

/** Result for listing function scale settings */
export interface ListFuncScaleSettingResult {
  /** Function ID */
  function_id: string;
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** Scale threshold set for the function */
  scale_threshold_set: ScaleThresholdsSet;
  /** Lag scale set name */
  lag_scale_set: string;
  /** Overload fast scale setting */
  overload_fast_scale_set: OverloadFastScaleSetting;
  /** List of cron scale strategies */
  cron_scale_strategies: Array<common.ScaleStrategy>;
}

/** Data for listing MQ Trigger scale settings */
export interface ListMQTriggerScaleSettingData {
  /** MQ Trigger ID */
  mqtrigger_id: string;
  /** Type of the trigger，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Name of the trigger */
  trigger_name: string;
  /** Cluster where the trigger is located */
  cluster: string;
  /** Service ID associated with the trigger */
  service_id: string;
  /** Function ID associated with the trigger */
  function_id: string;
  /** Scale threshold set for the trigger */
  scale_threshold_set: ScaleThresholdsSet;
  /** Lag scale set name */
  lag_scale_set: string;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled: boolean;
  /** List of cron scale strategies */
  cron_scale_strategies: Array<common.ScaleStrategy>;
  /** Region where the trigger is located */
  region: string;
}

export interface ListMQTriggerScaleThresholdsSettingRequest {
  /** Service ID */
  service_id: string;
  /** Optional offset for pagination */
  offset?: string;
  /** Optional limit for pagination */
  limit?: string;
  /** Optional search term */
  search?: string;
  /** Optional region filter */
  region?: string;
  /** Optional cluster filter */
  cluster?: string;
}

export interface ListMQTriggerScaleThresholdsSettingResponse {
  /** Response code */
  code: number;
  /** List of MQ Trigger scale setting data */
  data: Array<ListMQTriggerScaleSettingData>;
  /** Error message, if any */
  error: string;
}

export interface MQTriggerEmergencyScaleRequest {
  /** Service ID */
  service_id: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Trigger ID of the MQ Trigger */
  trigger_id: string;
  /** Minimum replicas during emergency scale */
  min_replicas: Record<string, number>;
  /** Scale duration in minutes */
  scale_duration_minutes: number;
}

export interface MQTriggerEmergencyScaleResponse {
  /** Response code */
  code: number;
  /** Result of the MQ Trigger emergency scale request */
  data: MQTriggerEmergencyScaleResult;
  /** Error message, if any */
  error: string;
}

/** Emergency scale result for MQ Trigger */
export interface MQTriggerEmergencyScaleResult {
  /** Function ID */
  function_id: string;
  /** Cluster name */
  cluster: string;
  /** Region name */
  region: string;
  /** Minimum replicas during emergency scale */
  min_replicas: Record<string, number>;
  /** Expected start time for keeping minimum replicas */
  expect_keep_min_begin_at: string;
  /** Expected end time for keeping minimum replicas */
  expect_keep_min_end_at: string;
  /** MQ Trigger ID */
  mqtrigger_id: string;
}

/** MQ Trigger scale threshold data */
export interface MQTriggerScaleThresholdData {
  /** MQ Trigger ID */
  mqtrigger_id: string;
  /** Type of the trigger，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Name of the trigger */
  trigger_name: string;
  /** Cluster where the trigger is located */
  cluster: string;
  /** Service ID associated with the trigger */
  service_id: string;
  /** Function ID associated with the trigger */
  function_id: string;
  /** Scale threshold set for the trigger */
  scale_threshold_set: ScaleThresholdsSet;
  /** Lag scale set name */
  lag_scale_set: string;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled: boolean;
  /** Region where the trigger is located */
  region: string;
}

/** Overload fast scale setting */
export interface OverloadFastScaleSetting {
  /** Whether online revision fast scale setting is enabled */
  online_revision_setting_enabled: boolean;
  /** Whether latest revision fast scale setting is enabled */
  latest_setting_enabled: boolean;
}

export interface PatchMQTriggerScaleThresholdSetRequest {
  /** Service ID */
  service_id: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Trigger ID of the MQ Trigger */
  trigger_id: string;
  /** Optional scale set name */
  scale_set_name?: string;
  /** Optional lag scale set name */
  lag_scale_set_name?: string;
  /** Optional vertical scale enable flag */
  vertical_scale_enabled?: boolean;
}

export interface PatchMQTriggerScaleThresholdSetResponse {
  /** Response code */
  code: number;
  /** Data of the patched MQ Trigger scale threshold set */
  data: MQTriggerScaleThresholdData;
  /** Error message, if any */
  error: string;
}

export interface ScaleThresholdOptionsApiResponse {
  /** Response code */
  code: number;
  /** Response data containing scale threshold options */
  data: ScaleThresholdOptionsResult;
  /** Error message, if any */
  error: string;
}

export interface ScaleThresholdOptionsRequest {
  /** Service ID to query */
  service_id: string;
  /** Scale target type (functions/mqtriggers) */
  target: string;
  /** MQ trigger ID (if applicable) */
  mqtrigger_id?: string;
}

export interface ScaleThresholdOptionsRequestV2 {
  /** Service ID to query */
  service_id: string;
  /** Scale target type (functions/mqtriggers) */
  target: string;
  /** MQ trigger ID (if applicable) */
  mqtrigger_id?: string;
  /** Region to query */
  region: string;
  /** Cluster to query */
  cluster: string;
}

/** Result containing available scale threshold options */
export interface ScaleThresholdOptionsResult {
  /** List of scale threshold options */
  scale_threshold_options: Array<ScaleThresholdsSet>;
  /** List of lag scale options */
  lag_scale_options: Array<string>;
}

/** Scale threshold setting for a function */
export interface ScaleThresholdSetting {
  /** Name of the scaling strategy */
  strategy_name: string;
  /** Threshold to scale up */
  scale_up_threshold: number;
  /** Target value for scaling */
  scale_target: number;
  /** Threshold to scale down */
  scale_down_threshold: number;
}

/** A set of scale thresholds for a function */
export interface ScaleThresholdsSet {
  /** Name of the scale set */
  scale_set_name: string;
  /** List of strategy settings */
  strategy_settings: Array<ScaleThresholdSetting>;
}

export interface UpdateScaleThresholdSetRequest {
  /** Function ID */
  service_id: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Name of the scale threshold set to update */
  scale_set_name?: string;
  /** Whether overload fast scale is enabled */
  overload_fast_scale_enabled?: boolean;
  /** Name of the lag scale set */
  lag_scale_set_name?: string;
}

export interface UpdateServiceScaleSettingsRequest {
  /** ID of service */
  service_id: string;
  /** Optional CPU scale settings for the service */
  service_cpu_scale_settings?: common.FuncCPUScaleSettings;
  /** Optional list of cluster CPU scale settings */
  cluster_cpu_scale_settings?: Array<ClusterCPUScaleSettings>;
}
/* eslint-enable */
