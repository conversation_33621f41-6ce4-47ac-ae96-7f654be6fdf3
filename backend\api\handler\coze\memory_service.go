/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	"github.com/coze-dev/coze-studio/backend/api/model/knowledge/document"
	"github.com/coze-dev/coze-studio/backend/api/model/kvmemory"
	"github.com/coze-dev/coze-studio/backend/api/model/project_memory"
	table "github.com/coze-dev/coze-studio/backend/api/model/table"
	appApplication "github.com/coze-dev/coze-studio/backend/application/app"
	"github.com/coze-dev/coze-studio/backend/application/knowledge"
	"github.com/coze-dev/coze-studio/backend/application/memory"
	"github.com/coze-dev/coze-studio/backend/pkg/lang/conv"
)

// GetSysVariableConf .
// @router /api/memory/sys_variable_conf [GET]
func GetSysVariableConf(ctx context.Context, c *app.RequestContext) {
	var err error
	var req kvmemory.GetSysVariableConfRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := memory.VariableApplicationSVC.GetSysVariableConf(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetProjectVariableList .
// @router /api/memory/project/variable/meta_list [GET]
func GetProjectVariableList(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project_memory.GetProjectVariableListReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.ProjectID == "" {
		invalidParamRequestResponse(c, "project_id is empty")
		return
	}

	pID, err := conv.StrToInt64(req.ProjectID)
	if err != nil {
		invalidParamRequestResponse(c, "project_id is not int")
		return
	}

	pInfo, err := appApplication.APPApplicationSVC.DomainSVC.GetDraftAPP(ctx, pID)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	resp, err := memory.VariableApplicationSVC.GetProjectVariablesMeta(ctx, pInfo.OwnerID, &req)
	if err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// UpdateProjectVariable .
// @router /api/memory/project/variable/meta_update [POST]
func UpdateProjectVariable(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project_memory.UpdateProjectVariableReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.ProjectID == "" {
		invalidParamRequestResponse(c, "project_id is empty")
		return
	}

	key2Var := make(map[string]*project_memory.Variable)
	for _, v := range req.VariableList {
		if v.Keyword == "" {
			invalidParamRequestResponse(c, "variable name is empty")
			return
		}

		if key2Var[v.Keyword] != nil {
			invalidParamRequestResponse(c, "variable keyword is duplicate")
			return
		}

		key2Var[v.Keyword] = v
	}

	resp, err := memory.VariableApplicationSVC.UpdateProjectVariable(ctx, req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// SetKvMemory .
// @router /api/memory/variable/upsert [POST]
func SetKvMemory(ctx context.Context, c *app.RequestContext) {
	var err error
	var req kvmemory.SetKvMemoryReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.BotID == 0 && req.GetProjectID() == "" {
		invalidParamRequestResponse(c, "bot_id and project_id are both empty")
		return
	}

	if len(req.Data) == 0 {
		invalidParamRequestResponse(c, "data is empty")
		return
	}

	resp, err := memory.VariableApplicationSVC.SetVariableInstance(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetMemoryVariableMeta .
// @router /api/memory/variable/get_meta [POST]
func GetMemoryVariableMeta(ctx context.Context, c *app.RequestContext) {
	var err error
	var req project_memory.GetMemoryVariableMetaReq
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp, err := memory.VariableApplicationSVC.GetVariableMeta(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// DelProfileMemory .
// @router /api/memory/variable/delete [POST]
func DelProfileMemory(ctx context.Context, c *app.RequestContext) {
	var err error
	var req kvmemory.DelProfileMemoryRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.BotID == 0 && req.GetProjectID() == "" {
		invalidParamRequestResponse(c, "bot_id and project_id are both empty")
		return
	}

	resp, err := memory.VariableApplicationSVC.DeleteVariableInstance(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetPlayGroundMemory .
// @router /api/memory/variable/get [POST]
func GetPlayGroundMemory(ctx context.Context, c *app.RequestContext) {
	var err error
	var req kvmemory.GetProfileMemoryRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.BotID == 0 && req.GetProjectID() == "" {
		invalidParamRequestResponse(c, "bot_id and project_id are both empty")
		return
	}

	resp, err := memory.VariableApplicationSVC.GetPlayGroundMemory(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}

// GetDocumentTableInfo .
// @router /api/memory/doc_table_info [GET]
func GetDocumentTableInfo(ctx context.Context, c *app.RequestContext) {
	var err error
	var req document.GetDocumentTableInfoRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	resp := new(document.GetDocumentTableInfoResponse)
	resp, err = knowledge.KnowledgeSVC.GetDocumentTableInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// GetModeConfig .
// @router /api/memory/table_mode_config [GET]
func GetModeConfig(ctx context.Context, c *app.RequestContext) {
	var err error
	var req table.GetModeConfigRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}

	if req.BotID == 0 {
		invalidParamRequestResponse(c, "bot_id is zero")
		return
	}

	resp, err := memory.DatabaseApplicationSVC.GetModeConfig(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}

	c.JSON(consts.StatusOK, resp)
}
