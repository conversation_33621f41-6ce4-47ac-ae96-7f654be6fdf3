/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as cluster from './cluster';
import * as admin from './admin';

export type Int64 = string | number;

export interface CreateServiceRequest {
  /** 管理员
Administrators of the service (comma-separated user IDs) */
  admins?: string;
  /** 仅对管理员开放的受限访问
If true, restricts access to administrators only */
  async_mode?: boolean;
  /** 授权人
Authorizers for the service (comma-separated user IDs) */
  authorizers?: string;
  /** 基础镜像
Base image used for the service container */
  base_image?: string;
  /** 服务类型
Category of the service */
  category: string;
  /** 仅用于引用结构体类型，值为 JSON 字符串
List of dependencies required by the service */
  dependency?: Array<common.Dependency>;
  /** 部署方式
Deployment method for the service. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** 服务描述, 原来的函数描述
Description of the service */
  description: string;
  /** 多环境标识
Environment name */
  env_name?: string;
  /** 服务名称, 原来的函数名称
Name of the service */
  name: string;
  /** 是否需要审核
Whether the service requires approval */
  need_approve?: boolean;
  /** 服务的来源，除了 faas 也有可能是来自轻服务等
Origin of the service */
  origin?: string;
  /** 服务的 Owner
Owner of the service */
  owner: string;
  /** 服务协议，如 TTHeader 等
Protocol used by the service */
  protocol: string;
  /** 服务唯一标识
PSM (Product-Service-Module) unique identifier for the service */
  psm: string;
  /** 服务树父节点
Parent ID of the PSM, used only during creation */
  psm_parent_id: number;
  /** 运行时语言. 可选值: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1
Runtime language for the service */
  runtime?: string;
  /** 服务等级
Service level */
  service_level: string;
  /** 服务用途
Purpose of the service */
  service_purpose: string;
  /** 源码
Source code of the service */
  source?: string;
  /** 源码类型
Type of the source code */
  source_type?: string;
  /** 订阅人
List of subscribers to the service */
  subscribers?: Array<string>;
  /** 基于代码模板创建
Template name used to create the service code */
  template_name?: string;
  /** 是否为在线模式
If true, the service is in online mode */
  online_mode?: boolean;
  /** scm 路径信息
SCM (Source Code Management) plugin path information */
  plugin_scm_path?: string;
  /** 代码包大小, 单位 MB, 需要管理员权限
Size of the code file in MB (admin permission required) */
  code_file_size_mb?: number;
  /** 泳道函数关闭报警, 默认为 false
If true, disables alarm for canary (env) functions */
  disable_ppe_alarm?: boolean;
  /** 运行时语言
Programming language used by the service when the runtime is wasm/v1. Enums: c++, golang, rust, assemblyscript, javascript */
  language?: string;
  /** 运行命令
Command to run the service */
  run_cmd?: string;
  /** 镜像懒加载
If true, enables lazy loading of the image */
  image_lazy_load?: boolean;
  /** 插件名称
Name of the plugin associated with the service */
  plugin_name?: string;
  /** 运行时容器端口
Port used by the runtime container */
  runtime_container_port?: number;
  /** 运行时调试容器端口
Debug port for the runtime container */
  runtime_debug_container_port?: number;
  /** 健康检查路径
Path used for health checks */
  health_check_path?: string;
  /** 健康检查失败阈值
Threshold for health check failures */
  health_check_failure_threshold?: number;
  /** 健康检查周期
Period (in seconds) for health checks */
  health_check_period?: number;
  /** 运行时其他容器端口
List of additional ports for the runtime container */
  runtime_other_container_ports?: Array<number>;
  /** 是否启用过载保护
If true, enables overload protection for the service */
  overload_protect_enabled?: boolean;
  /** 网络队列
Network queue name for the service */
  net_queue?: string;
  /** 微服务平台服务元参数
Meta parameters for MS platform (not stored in DB) */
  ms_service_meta_params?: MSServiceMetaParams;
  /** 挂载信息
List of mount information for the service */
  mount_info?: Array<string>;
  /** 是否禁用构建安装
If true, disables build and install steps */
  disable_build_install?: boolean;
  /** 是否启用懒加载
If true, enables lazy loading for the service */
  lazyload?: boolean;
  /** 审核权限点 [cluster_create, cluster_update, cluster_delete, code_release]
List of approval scopes for the service */
  approval_scope?: Array<string>;
  /** 消息队列类型
Type of message queue used by the service, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** 是否依赖 GPU，所有集群必须使用 GPU 资源
If true, the service depends on GPU resources */
  use_gpu?: boolean;
  /** 要创建的 git group
Git group to be created for the service */
  git_group?: string;
  /** 要创建的 git repo
Git repository to be created for the service */
  git_repo?: string;
  /** 要创建的 scm repo
SCM repository to be created for the service */
  scm_repo?: string;
  /** enable privileged in pod */
  privileged?: boolean;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface CreateServiceResponse {
  code?: number;
  data?: cluster.ServiceResponse;
  error?: string;
}

export interface CrossRegionMigrationMeta {
  /** PSM
PSM (Product-Service-Module) identifier for migration */
  psm: string;
  /** 是否启用迁移
If true, migration is enabled */
  migration_enabled: boolean;
  /** 跨区域 Vefaas 集群列表
List of cross-region Vefaas clusters */
  vefaas_clusters: Array<CrossRegionVefaasCluster>;
}

export interface CrossRegionVefaasCluster {
  /** 函数 ID
Function ID for the cross-region cluster */
  function_id: string;
  /** 区域
Region of the cluster */
  region: string;
  /** 集群名称
Name of the cluster */
  cluster_name: string;
}

export interface DeleteServiceRequest {
  /** 服务 ID
ID of the service to delete */
  service_id: string;
  /** 如果为 true，则为软删除
If true, perform a soft delete */
  soft?: boolean;
  /** 软删除原因
Reason for soft deletion */
  reason?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface DeleteServiceResponse {
  code?: number;
  data?: cluster.ServiceResponse;
  error?: string;
}

export interface FunctionResponseData {
  /** 函数 ID
Function ID */
  id?: string;
  /** 服务 ID
Service ID associated with the function */
  service_id?: string;
  /** 函数名称
Name of the function */
  name?: string;
  /** 函数描述
Description of the function */
  description?: string;
  /** 管理员
Administrators of the function (comma-separated user IDs) */
  admins?: string;
  /** Owner
Owner of the function */
  owner?: string;
  /** PSM
PSM (Product-Service-Module) identifier */
  psm?: string;
  /** 运行时
Runtime language of the function */
  runtime?: string;
  /** 语言
Programming language of the function */
  language?: string;
  /** 运行命令
Command to run the function */
  run_cmd?: string;
  /** 基础镜像
Base image for the function */
  base_image?: string;
  /** 来源
Origin of the function */
  origin?: string;
  /** 服务类型
Category of the function */
  category?: string;
  /** 是否关闭报警
If true, disables alarm for the function */
  disable_ppe_alarm?: boolean;
  /** 初始化函数耗时（秒）
Time taken by the initializer (in seconds) */
  initializer_sec?: number;
  /** 延迟（秒）
Latency in seconds */
  latency_sec?: number;
  /** 冷启动耗时（秒）
Cold start time in seconds */
  cold_start_sec?: number;
  /** 是否禁用冷启动
If true, disables cold start */
  cold_start_disabled?: boolean;
  /** 是否需要审核
If true, approval is required */
  need_approve?: boolean;
  /** 是否启用鉴权
If true, authentication is enabled */
  auth_enable?: boolean;
  /** 是否启用链路追踪
If true, tracing is enabled */
  trace_enable?: boolean;
  /** 授权人
Authorizers for the function (comma-separated user IDs) */
  authorizers?: string;
  /** 订阅人
List of subscribers to the function */
  subscribers?: Array<string>;
  /** 环境变量
Environment variables for the function (key-value pairs) */
  envs?: Record<string, Record<string, string>>;
  /** 格式化环境变量
Formatted environment variables */
  format_envs?: Record<string, Array<common.FormatEnvs>>;
  /** 内存大小（MB）
Memory size in MB */
  memory_mb?: number;
  /** 代码包大小（MB）
Code file size in MB */
  code_file_size_mb?: number;
  /** 最大并发数
Maximum concurrency allowed */
  max_concurrency?: number;
  /** 自适应并发模式
Adaptive concurrency mode */
  adaptive_concurrency_mode?: string;
  /** 是否独占模式
If true, exclusive mode is enabled */
  exclusive_mode?: boolean;
  /** 是否异步模式
If true, async mode is enabled */
  async_mode?: boolean;
  /** 是否启用 CORS
If true, CORS is enabled */
  cors_enable?: boolean;
  /** 是否禁用构建安装
If true, disables build and install steps */
  disable_build_install?: boolean;
  /** 最大修订版本号
Maximum revision number */
  max_revision_number?: number;
  /** 微服务注册是否成功
If true, MS registration succeeded */
  ms_register_suc?: boolean;
  /** 是否启用运行时文件日志
If true, runtime file logging is enabled */
  enable_runtime_file_log?: boolean;
  /** 是否启用运行时控制台日志
If true, runtime console logging is enabled */
  enable_runtime_console_log?: boolean;
  /** 是否启用运行时流日志
If true, runtime stream logging is enabled */
  enable_runtime_stream_log?: boolean;
  /** 是否启用运行时 ES 日志
If true, runtime Elasticsearch logging is enabled */
  enable_runtime_es_log?: boolean;
  /** 是否启用运行时 JSON 日志
If true, runtime JSON logging is enabled */
  enable_runtime_json_log?: boolean;
  /** 是否启用系统流日志
If true, system stream logging is enabled */
  enable_system_stream_log?: boolean;
  /** 是否启用系统 ES 日志
If true, system Elasticsearch logging is enabled */
  enable_system_es_log?: boolean;
  /** 运行时流日志速率限制（字节/秒）
Runtime stream log bytes per second */
  runtime_stream_log_bytes_per_sec?: number;
  /** 系统流日志速率限制（字节/秒）
System stream log bytes per second */
  system_stream_log_bytes_per_sec?: number;
  /** 日志节流速率（字节/秒）
Throttle log bytes per second */
  throttle_log_bytes_per_sec?: number;
  /** 标准输出日志节流速率（字节/秒）
Throttle stdout log bytes per second */
  throttle_stdout_log_bytes_per_sec?: number;
  /** 标准错误日志节流速率（字节/秒）
Throttle stderr log bytes per second */
  throttle_stderr_log_bytes_per_sec?: number;
  /** 是否启用懒加载
If true, lazy loading is enabled */
  lazyload?: boolean;
  /** 插件名称
Name of the plugin associated with the function */
  plugin_name?: string;
  /** 插件 SCM ID
Plugin SCM ID */
  plugin_scm_id?: number;
  /** 环境名称
Environment name */
  env_name?: string;
  /** 副本限制
Replica limits for the function (per environment/cluster) */
  replica_limit?: Record<string, Record<string, common.PodReplicaLimit>>;
  /** 资源限制
Resource limits for the function */
  resource_limit?: common.Resource;
  /** 是否启用自动伸缩
If true, scaling is enabled */
  scale_enabled?: boolean;
  /** 伸缩阈值
Threshold for scaling */
  scale_threshold?: number;
  /** 伸缩类型
Type of scaling */
  scale_type?: number;
  /** 是否启用伸缩优化
If true, scale optimization is enabled */
  enable_scale_optimise?: boolean;
  /** 是否启用伸缩策略
If true, scale strategy is enabled */
  enable_scale_strategy?: boolean;
  /** 源码类型
Type of the source code */
  source_type?: string;
  /** 源码
Source code of the function */
  source?: string;
  /** 依赖列表
List of dependencies for the function */
  dependency?: Array<common.Dependency>;
  /** 全局 KV 命名空间 ID 列表
List of global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** 本地缓存命名空间 ID 列表
List of local cache namespace IDs */
  local_cache_namespace_ids?: Array<string>;
  /** 协议
Protocol used by the function */
  protocol?: string;
  /** Argos 链接
Argos link for the function */
  argos_link?: string;
  /** 创建时间
Creation time of the function */
  created_at?: string;
  /** 更新时间
Last update time of the function */
  updated_at?: string;
  /** 修订版本 ID
Revision ID of the function */
  revision_id?: string;
  /** 网络队列
Network queue name */
  net_queue?: string;
  /** 挂载信息
List of mount information */
  mount_info?: Array<string>;
  /** 审核权限点 [cluster_create, cluster_update, cluster_delete, code_release]
List of approval scopes for the function */
  approval_scope?: Array<string>;
}

export interface GetAllServiceByPsmRequest {
  /** 服务的 PSM
PSM (Product-Service-Module) identifier of the service */
  psm: string;
  /** 不包含鉴权信息
If set, do not include authentication info */
  no_auth_info?: string;
}

export interface GetAllServiceByPsmResponse {
  code?: number;
  data?: Array<cluster.ServiceResponse>;
  error?: string;
}

export interface GetCrossRegionMigrationRequest {
  /** PSM
PSM (Product-Service-Module) identifier for migration query */
  psm: string;
}

export interface GetCrossRegionMigrationResponse {
  code: number;
  data: CrossRegionMigrationMeta;
  error: string;
}

export interface GetServiceByPsmAndEnvRequest {
  /** 通过 psm 和 env 获取服务信息
Environment name to filter the service */
  env_name: string;
  /** 服务的 PSM
PSM (Product-Service-Module) identifier of the service */
  psm: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetServiceByPsmAndEnvResponse {
  code?: number;
  data?: cluster.ServiceResponse;
  error?: string;
}

export interface GetServiceInspectionListRequest {
  /** 服务 ID
ID of the service to inspect */
  service_id: string;
}

export interface GetServiceInspectionListResponse {
  code?: number;
  data?: Array<InspectionResponseData>;
  error?: string;
}

export interface GetServiceRequest {
  /** 区域
Region of the service */
  region?: string;
  /** 服务 ID
ID of the service to retrieve */
  service_id: string;
  /** 如果为 true，获取详细信息（包括集群信息）
If true, include detailed information with clusters */
  verbose?: boolean;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetServiceResponse {
  code?: number;
  data?: cluster.ServiceResponse;
  error?: string;
}

export interface GetServicesListRequest {
  /** 是否获取全部，默认为 false
If set, retrieves all services (default: false) */
  all?: string;
  /** 环境名称，可选值: prod/ppe/boe_feature
Environment name to filter services */
  env?: string;
  /** 按服务 ID 搜索
Search by service ID */
  id?: string;
  /** 分页限制数量
Limit for pagination */
  limit?: number;
  /** 按名称搜索
Search by service name */
  name?: string;
  /** 不包含 worker 函数
If true, exclude worker functions */
  no_worker?: boolean;
  /** 分页偏移量
Offset for pagination */
  offset?: number;
  /** 按 owner 搜索
Search by owner */
  owner?: string;
  /** 按 PSM 前缀匹配，仅在 all 不为空时生效
Search by PSM prefix (works only if 'all' is set) */
  psm?: string;
  /** 多字段前缀搜索，缓存
Prefix search across multiple fields */
  search?: string;
  /** 搜索类型: all/admin/own/subscribe
Type of search */
  search_type?: string;
  /** 按服务模型字段排序
Field to sort by in the service model */
  sort_by?: string;
  /** 支持的搜索字段: cluster_id/id/name/psm
Supported search fields (comma-separated) */
  search_fields?: string;
  /** 过滤软删除服务
If true, filter for soft-deleted services */
  soft_deleted?: boolean;
}

export interface GetServicesListResponse {
  code?: number;
  data?: Array<cluster.ServiceResponse>;
  error?: string;
}

export interface InspectionLabel {
  /** 服务等级
Service level label */
  service_level?: string;
  /** 服务 ID
Service ID label */
  service_id?: string;
  /** PSM
PSM label */
  psm?: string;
  /** Owner
Owner label */
  owner?: string;
  /** 集群
Cluster label */
  cluster?: string;
  /** 区域
Region label */
  region?: string;
}

export interface InspectionResponseData {
  /** 检查项名称
Name of the inspection item */
  name?: string;
  /** 标签信息
Labels associated with the inspection */
  labels?: InspectionLabel;
  /** 检查结果
Result of the inspection */
  result?: string;
  /** 任务名称
Name of the inspection task */
  task_name?: string;
  /** 规则名称
Name of the rule applied during inspection */
  rule_name?: string;
  /** 目标 ID
Target ID for the inspection */
  target_id?: string;
  /** 目标类型
Type of the inspection target */
  target_type?: string;
  /** 风险等级
Risk level identified during inspection */
  risk_level?: string;
  /** 检查值
Value found during inspection */
  value?: string;
  /** 检查开始时间
Start time of the inspection */
  start_time?: string;
  /** 检查结束时间
End time of the inspection */
  end_time?: string;
  /** 创建时间
Creation time of the inspection record */
  created_at?: string;
  /** 更新时间
Last update time of the inspection record */
  updated_at?: string;
}

/** this params is used to create/update service meta(ms platform), will not store in db */
export interface MSServiceMetaParams {
  /** List of endpoints for the service */
  endpoints?: Array<string>;
  /** Framework used by the service */
  framework?: string;
  /** Programming language used by the service */
  language?: string;
}

export interface RecoverDeletedClusterRequest {
  /** 服务 ID
ID of the service whose cluster is to be recovered */
  service_id: string;
}

export interface RecoverDeletedClusterResponse {
  code?: number;
  data?: admin.BasicCluster;
  error?: string;
}

export interface RestoreServiceRequest {
  /** 服务 ID
ID of the service to restore */
  service_id: string;
}

export interface SearchFunctionsBySCMRequest {
  /** 分页查询，每页数量
Limit for pagination (number of items per page) */
  limit?: number;
  /** 分页查询，偏移量，默认为 0
Offset for pagination (default: 0) */
  offset?: number;
  /** 服务引用的 scm 名称
SCM name referenced by the service */
  scm: string;
}

export interface SearchFunctionsBySCMResponse {
  code?: number;
  data?: cluster.ServiceResponse;
  error?: string;
}

export interface UpdateCodeByServiceIDRequest {
  /** 仅用于引用结构体类型，值为 JSON 字符串
List of dependencies for the service */
  dependency?: Array<common.Dependency>;
  /** 部署方式
Deployment method for the service. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** 是否禁用构建安装
If true, disables build and install steps */
  disable_build_install?: boolean;
  /** 入口函数
Handler function for the service */
  handler?: string;
  /** 初始化函数
Initializer function for the service */
  initializer?: string;
  /** 是否启用懒加载
If true, enables lazy loading */
  lazyload?: boolean;
  /** 运行命令
Command to run the service */
  run_cmd?: string;
  /** 运行时. 可选值: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1
Runtime language for the service */
  runtime?: string;
  /** 运行时容器端口
Port for the runtime container */
  runtime_container_port?: number;
  /** 运行时调试容器端口
Debug port for the runtime container */
  runtime_debug_container_port?: number;
  /** 服务 ID
ID of the service to update */
  service_id: string;
  /** 源码
Source code for the service */
  source?: string;
  /** 源码类型
Type of the source code */
  source_type?: string;
  /** 代码资源的二进制数据
Binary data of the code resource (zip file) */
  zip_file?: UpdateCodeByServiceIDRequestZipFileMessage2;
  /** 代码包大小
Size of the zip file (in bytes) */
  zip_file_size?: number;
  /** 是否开启镜像懒加载
If true, enables image lazy loading */
  open_image_lazyload?: boolean;
  /** 运行时其他容器端口
List of additional ports for the runtime container */
  runtime_other_container_ports?: Array<number>;
  /** 健康检查失败阈值
Threshold for health check failures */
  health_check_failure_threshold?: number;
  /** 健康检查周期
Period (in seconds) for health checks */
  health_check_period?: number;
  /** 健康检查路径
Path used for health checks */
  health_check_path?: string;
}

export interface UpdateCodeByServiceIDRequestZipFileMessage2 {}

export interface UpdateCodeByServiceIDResponse {
  code?: number;
  data?: common.CodeRevision;
  error?: string;
}

export interface UpdateServiceInfoByServiceIDRequest {
  /** 管理员
Administrators of the service (comma-separated user IDs) */
  admins?: string;
  /** 授权人
Authorizers for the service (comma-separated user IDs) */
  authorizers?: string;
  /** 基础镜像
Base image for the service */
  base_image?: string;
  /** 服务类型
Category of the service */
  category?: string;
  /** 服务描述, 原来的函数描述
Description of the service */
  description?: string;
  /** 服务名称, 原来的函数名称
Name of the service */
  name?: string;
  /** 是否需要审核
If true, approval is required */
  need_approve?: boolean;
  /** 服务的来源，除了 faas 也有可能是来自轻服务等
Origin of the service */
  origin?: string;
  /** 服务的 Owner
Owner of the service */
  owner?: string;
  /** 绑定的 lego 插件函数名称
Name of the plugin function bound to the service */
  plugin_name?: string;
  /** 运行时语言. 可选值: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1
Runtime language for the service */
  runtime?: string;
  /** 服务 ID
ID of the service to update */
  service_id: string;
  /** 服务等级
Service level */
  service_level?: string;
  /** 服务用途
Purpose of the service */
  service_purpose?: string;
  /** 订阅人
List of subscribers to the service */
  subscribers?: Array<string>;
  /** 代码包上限大小
Maximum code file size in MB */
  code_file_size_mb?: number;
  /** PSM
PSM (Product-Service-Module) identifier */
  psm?: string;
  /** PSM 父节点 ID
Parent ID of the PSM */
  psm_parent_id?: Int64;
  /** 是否支持集群级别 run_cmd
If true, enables cluster-level run command */
  enable_cluster_run_cmd?: boolean;
  /** 是否关闭报警
If true, disables alarm for the service */
  disable_ppe_alarm?: boolean;
  /** 网络队列
Network queue name */
  net_queue?: string;
  /** 微服务平台服务元参数
Meta parameters for MS platform (not stored in DB) */
  ms_service_meta_params?: MSServiceMetaParams;
  /** 语言
Programming language used in the service */
  language?: string;
  /** 挂载信息
List of mount information for the service */
  mount_info?: Array<string>;
  /** 审核权限点 [cluster_create, cluster_update, cluster_delete, code_release]
List of approval scopes for the service */
  approval_scope?: Array<string>;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface UpdateServiceInfoByServiceIDResponse {
  code?: number;
  data?: FunctionResponseData;
  error?: string;
}
/* eslint-enable */
