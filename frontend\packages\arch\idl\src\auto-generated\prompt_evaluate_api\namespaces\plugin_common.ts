/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AsyncTaskStatusForPlugin {
  /** 未知状态 */
  Unknown = 0,
  /** 创建成功 */
  CreateSucceed = 1,
  /** 运行 */
  Executing = 2,
  /** 推送中 */
  Pushing = 3,
  /** 执行成功 */
  ExecuteSucceed = 10,
  /** 执行失败 */
  ExecuteFailed = 11,
}

export enum AsyncTaskStatusForUser {
  /** 未知状态 */
  Unknown = 0,
  /** 执行中 */
  Executing = 1,
  /** 执行成功 */
  ExecuteSucceed = 2,
  /** 执行失败 */
  ExecuteFailed = 3,
}

export enum AsyncType {
  /** 未知类型 */
  Unknown = 0,
  /** 插件异步任务 */
  Workflow = 1,
  /** 原生异步插件 */
  NativeAsyncPlugin = 2,
  /** 同步转异步插件 */
  SyncToAsyncPlugin = 3,
}

export enum AttributeValueType {
  Unknown = 0,
  String = 1,
  Boolean = 2,
  StringList = 11,
  BooleanList = 12,
}

export enum CozeReleaseChannel {
  Inhouse = 0,
  Release = 1,
}

export enum DebugExampleStatus {
  Default = 0,
  Enable = 1,
  Disable = 2,
}

export enum FeedbackType {
  Unknown = 0,
  /** 未找到需要的插件 */
  NotFoundPlugin = 1,
  /** 官方插件反馈 */
  OfficialPlugin = 2,
}

export enum GrantType {
  TokenExchange = 1,
  ClientCredential = 2,
}

/** 操作类型 */
export enum OperateType {
  Add = 0,
  Delete = 1,
}

/** 新增channel id */
export enum PluginChannel {
  /** 所有渠道 */
  All = 0,
  /** 素材 */
  Material = 1,
  /** 商店 */
  Store = 2,
}

/** 融合plugin product上下架和审核状态 给前端使用 */
export enum PluginProductStatus {
  Default = 0,
  Listed = 1,
  Unlisted = 2,
  Reviewing = 3,
}

/** plugin update 类型 */
export enum PluginUpdateEventType {
  /** 转移空间 */
  TransferSpace = 1,
  /** 转移owner */
  TransferOwner = 2,
}

export enum PrivacyAuthStatus {
  Unauthorized = 1,
  Authorized = 2,
}

/** plugin插件授权类型 */
export enum PrivacyAuthType {
  /** 询问 */
  Ask = 0,
  /** 仅本次允许 */
  Once = 1,
  /** 始终允许 */
  Always = 2,
}

/** ==================== Project IDE Begin ====================
 authz 接口 */
export enum ResourceType {
  Account = 1,
  Workspace = 2,
  App = 3,
  Bot = 4,
  Plugin = 5,
  Workflow = 6,
  Knowledge = 7,
  PersonalAccessToken = 8,
  Connector = 9,
  Card = 10,
  CardTemplate = 11,
  Conversation = 12,
  File = 13,
  ServicePrincipal = 14,
  Enterprise = 15,
  MigrateTask = 16,
  Prompt = 17,
  UI = 18,
  Project = 19,
}

export enum ServiceAuthSubType {
  ApiKey = 0,
  BytedanceZeroTrust = 1,
  OIDC = 2,
}

export enum TcsAuditStatus {
  /** 默认状态 */
  Default = 0,
  /** 审核通过 */
  AuditPass = 1,
  /** 审核不通过 */
  AuditReject = 2,
}

export interface AsyncPluginTaskFinishMessage {
  /** 任务id */
  serial_id?: string;
  /** 插件id */
  plugin_id?: Int64;
  /** 插件apiName */
  api_name?: string;
  /** 扩展字段 */
  ext?: Record<string, string>;
  /** task是否成功 */
  is_succeed?: boolean;
  /** 消息发送时间 */
  event_time?: string;
}

export interface AttributeValue {
  Type: AttributeValueType;
  Value: string;
}

export interface CardBindInfo {
  CardID?: Int64;
  MappingRule?: string;
  MaxDisplayRows?: Int64;
  CardVersion?: Int64;
  LlmTextCard?: boolean;
  /** 出参是否和MappingRule匹配，部分接口会设置该值 */
  OutputParamMatch?: boolean;
}

export interface ExecuteDisplayName {
  /** 执行中的展示名称，nil则代表未设置 */
  name_executing?: string;
  /** 已执行的展示名称，nil则代表未设置 */
  name_executed?: string;
  /** 执行失败的展示名称，nil则代表未设置 */
  name_execute_failed?: string;
}

export interface OIDCPayload {
  grant_type?: GrantType;
  endpoint_url?: string;
  audience?: string;
  oidc_scope?: string;
  oidc_client_id?: string;
}

export interface PluginPrivacyAuth {
  /** 插件id */
  plugin_id?: Int64;
  /** 插件名称 */
  plugin_name?: string;
  /** 授权类型 */
  auth_type?: PrivacyAuthType;
}

export interface PluginPublishInfo {
  /** 发布人 */
  PublisherID?: Int64;
  /** 版本，毫秒时间戳 */
  VersionTs?: Int64;
  /** 版本名称 */
  VersionName?: string;
  /** 版本描述 */
  VersionDesc?: string;
}

export interface PluginUnauthorizedInfo {
  /** 插件id */
  plugin_id?: Int64;
  /** 插件名称 */
  plugin_name?: string;
}

export interface PluginUpdateEvent {
  /** 渠道插件id */
  channel_plugin_id?: Int64;
  /** 素材id */
  material_id?: Int64;
  /** 事件类型 */
  event_type?: PluginUpdateEventType;
  /** 消息发送时间 */
  event_time?: Int64;
  /** 源用户id */
  source_user_id?: Int64;
  /** 目标用户id */
  target_user_id?: Int64;
  /** 目标空间id */
  target_space_id?: Int64;
}

export interface PrivacyAuthStatusInfo {
  PluginId?: Int64;
  /** 授权状态 */
  Status?: PrivacyAuthStatus;
}

export interface ResourceCopyResultInfo {
  /** 如果插件被复制了, 记录工具ID映射 */
  ApiIdMapping?: Record<Int64, Int64>;
}

export interface ResourceIdentifier {
  /** 资源类型 */
  Type: ResourceType;
  /** 资源Id */
  Id: string;
}
/* eslint-enable */
