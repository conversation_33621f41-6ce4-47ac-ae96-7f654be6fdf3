# 硅基流动模型ID分配表

## ID范围规划 (3000-3999)

### 通用模型 (3001-3050)
- 3001: SiliconFlow-通用模型 (模板)
- 3002-3050: 预留给其他通用模型

### Qwen系列 (3051-3100)
- 3051: Qwen/Qwen2.5-72B-Instruct
- 3052: Qwen/Qwen2.5-32B-Instruct  
- 3053: Qwen/Qwen2.5-14B-Instruct
- 3054: Qwen/Qwen2.5-7B-Instruct
- 3055: Qwen/Qwen2.5-3B-Instruct
- 3056: Qwen/Qwen2.5-1.5B-Instruct
- 3057: Qwen/Qwen2.5-0.5B-Instruct
- 3058: Qwen/Qwen2.5-Coder-32B-Instruct
- 3059: Qwen/Qwen2.5-Coder-14B-Instruct
- 3060: Qwen/Qwen2.5-Coder-7B-Instruct
- 3061-3100: 预留给其他Qwen模型

### DeepSeek系列 (3101-3150)
- 3101: deepseek-ai/DeepSeek-V3
- 3102: deepseek-ai/DeepSeek-V2.5
- 3103: deepseek-ai/DeepSeek-Coder-V2-Instruct
- 3104: deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct
- 3105: deepseek-ai/deepseek-math-7b-instruct
- 3106-3150: 预留给其他DeepSeek模型

### Llama系列 (3151-3200)
- 3151: meta-llama/Llama-3.1-405B-Instruct
- 3152: meta-llama/Llama-3.1-70B-Instruct
- 3153: meta-llama/Llama-3.1-8B-Instruct
- 3154: meta-llama/Llama-3.2-90B-Vision-Instruct
- 3155: meta-llama/Llama-3.2-11B-Vision-Instruct
- 3156: meta-llama/Llama-3.2-3B-Instruct
- 3157: meta-llama/Llama-3.2-1B-Instruct
- 3158-3200: 预留给其他Llama模型

### ChatGLM系列 (3201-3250)
- 3201: THUDM/glm-4-9b-chat
- 3202: THUDM/chatglm3-6b
- 3203-3250: 预留给其他ChatGLM模型

### Yi系列 (3251-3300)
- 3251: 01-ai/Yi-1.5-34B-Chat
- 3252: 01-ai/Yi-1.5-9B-Chat
- 3253: 01-ai/Yi-1.5-6B-Chat
- 3254: 01-ai/Yi-Coder-9B-Chat
- 3255-3300: 预留给其他Yi模型

### 其他开源模型 (3301-3400)
- 3301: internlm/internlm2_5-7b-chat
- 3302: internlm/internlm2_5-20b-chat
- 3303: microsoft/Phi-3.5-mini-instruct
- 3304: google/gemma-2-9b-it
- 3305: google/gemma-2-27b-it
- 3306: mistralai/Mistral-7B-Instruct-v0.3
- 3307: mistralai/Mixtral-8x7B-Instruct-v0.1
- 3308: mistralai/Mixtral-8x22B-Instruct-v0.1
- 3309-3400: 预留给其他开源模型

### 多模态模型 (3401-3450)
- 3401: Qwen/Qwen2-VL-72B-Instruct
- 3402: Qwen/Qwen2-VL-7B-Instruct
- 3403: Qwen/Qwen2-VL-2B-Instruct
- 3404: meta-llama/Llama-3.2-90B-Vision-Instruct
- 3405: meta-llama/Llama-3.2-11B-Vision-Instruct
- 3406-3450: 预留给其他多模态模型

### 特殊用途模型 (3451-3500)
- 3451: 嵌入模型
- 3452: 重排序模型
- 3453-3500: 预留给特殊用途模型

## 使用说明

1. **ID唯一性**: 每个模型必须有唯一的ID，不能重复
2. **范围管理**: 严格按照上述范围分配，避免冲突
3. **预留空间**: 每个系列都预留了足够的ID空间供扩展
4. **命名规范**: 建议使用 `平台-模型系列-规格` 的命名方式

## 配置示例

```yaml
# Qwen2.5-72B配置示例
id: 3051
name: Qwen2.5-72B (SiliconFlow)
meta:
  conn_config:
    model: "Qwen/Qwen2.5-72B-Instruct"
```

```yaml
# DeepSeek-V3配置示例  
id: 3101
name: DeepSeek-V3 (SiliconFlow)
meta:
  conn_config:
    model: "deepseek-ai/DeepSeek-V3"
```
