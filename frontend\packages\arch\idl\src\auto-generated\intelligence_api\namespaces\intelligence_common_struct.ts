/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum CacheType {
  /** 缓存关闭 */
  CacheClosed = 0,
  /** 前缀缓存 */
  PrefixCache = 1,
}

/** *
和前端交互的视图结构体 */
export enum IntelligenceStatus {
  Using = 1,
  Deleted = 2,
  Banned = 3,
  /** 迁移失败 */
  MoveFailed = 4,
  /** 复制中 */
  Copying = 5,
  /** 复制失败 */
  CopyFailed = 6,
}

export enum IntelligenceType {
  Bot = 1,
  Project = 2,
  DouyinAvatarBot = 3,
}

export interface IntelligenceBasicInfo {
  id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  icon_url?: string;
  space_id?: string;
  owner_id?: string;
  create_time?: string;
  update_time?: string;
  status?: IntelligenceStatus;
  publish_time?: string;
  enterprise_id?: string;
  organization_id?: Int64;
  cache_type?: CacheType;
}
/* eslint-enable */
