/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_common from './bot_common';
import * as plugin_common from './plugin_common';
import * as base from './base';

export type Int64 = string | number;

export enum AsyncStatus {
  /** 创建 */
  Create = 1,
  /** 成功 */
  Finish = 2,
  /** 失败 */
  Failed = 3,
}

export enum BotDeleteStatus {
  Deleted = 1,
}

export enum BotExploreStatus {
  Online = 1,
  Offline = 2,
}

export enum BotMode {
  SingleMode = 0,
  MultiMode = 1,
}

export enum ConnectorApiKeyStatus {
  Available = 1,
  Delete = 2,
  StopUse = 3,
}

export enum ConnectorId {
  doubao = 482431,
  cici = 489823,
  bot = 10000010,
  feishu = 10000011,
  obric = 10000012,
  discord = 10000028,
}

/** 上下文允许传输的类型 */
export enum ContextContentType {
  /** 无任何处理版 */
  USER_RES = 0,
  USER_LLM_RES = 1,
  USER_LLM_APILEN_RES = 2,
  USER_LLM_API_RES = 3,
}

export enum DraftBotStatus {
  Deleted = 0,
  Using = 1,
}

export enum FileBizType {
  BIZ_LABEL_ICON = 1,
}

export enum GrantType {
  WaitListOff = 1,
  AdminOpt = 2,
  WhiteList = 3,
}

export enum IconType {
  Bot = 1,
  User = 2,
  Plugin = 3,
  Dataset = 4,
}

export enum IntelligenceType {
  Bot = 1,
  Project = 2,
  DouyinAvatarBot = 3,
}

export enum IntentCaseType {
  /** 正常 */
  None = 0,
  /** 拒答 */
  Reject = 1,
  /** 欠召回 */
  FalseNegative = 2,
  /** 误召回 */
  FalsePositive = 4,
}

export enum OrderBy {
  CreateTime = 0,
  UpdateTime = 1,
  Hot = 3,
}

export enum PluginProductStatus {
  /** 默认状态 */
  Default = 0,
  /** 已上架市场 */
  Listed = 1,
  /** 已下架市场 */
  Unlisted = 2,
  /** 待上架市场 */
  Reviewing = 3,
}

export enum PluginPublishStatus {
  /** 未发布 */
  UnPublished = 1,
  /** 有更新，待发布 */
  PendingPublished = 2,
  /** 已发布，无更新 */
  Published = 3,
}

export enum PluginStatus {
  /** 默认值 */
  Draft = 0,
  SUBMITTED = 1,
  REVIEWING = 2,
  PREPARED = 3,
  PUBLISHED = 4,
  OFFLINE = 5,
  /** 运营封禁 */
  BANNED = 6,
}

export enum PluginType {
  PLUGIN = 1,
  APP = 2,
  FUNC = 3,
  WORKFLOW = 4,
}

export enum Publish {
  NoPublish = 0,
  HadPublished = 1,
}

export enum PublishStatus {
  All = 0,
  Publish = 1,
  NoPublish = 2,
}

export enum QueryType {
  QueryByID = 0,
  QueryByName = 1,
}

export enum SpaceType {
  /** 个人 */
  Personal = 1,
  /** 小组 */
  Team = 2,
}

export enum VisibilityType {
  /** 不可见 */
  Invisible = 0,
  /** 可见 */
  Visible = 1,
}

export enum WaitStatus {
  Wait = 1,
  Failed = 2,
  Grant = 3,
}

export interface ApiKeyInfo {
  space_id?: string;
  space_name?: string;
  creator_name?: string;
  env?: string;
  api_key?: string;
  status?: string;
  creator_time?: string;
  last_used_time?: string;
  creator_type?: string;
  api_key_id?: string;
}

export interface AppIDInfo {
  id?: string;
  name?: string;
  icon?: string;
}

export interface BasicInfo {
  id?: string;
  name?: string;
}

export interface BatchUpdateExploreBot {
  id: string;
  index?: number;
  category_id?: Array<string>;
}

export interface BatchUpdateExploreBotData {
  failed_explore_bot_id?: Array<string>;
}

export interface BatchUpdateExploreBotRequest {
  explore_status: BotExploreStatus;
  explore_bot?: Array<BatchUpdateExploreBot>;
}

export interface BatchUpdateExploreBotResponse {
  code?: Int64;
  msg?: string;
  data?: BatchUpdateExploreBotData;
}

export interface BotInfoCompareData {
  draft_bot?: DraftBot;
  bot_version?: DraftBot;
  flow_bot?: DraftBot;
}

export interface BotInfoCompareRequest {
  bot_id?: string;
  conn_id?: ConnectorId;
}

export interface BotInfoCompareResponse {
  code?: Int64;
  msg?: string;
  data?: BotInfoCompareData;
}

export interface BotSpace {
  /** 空间id，新建为0 */
  id?: string;
  /** 发布平台 */
  app_ids?: Array<AppIDInfo>;
  /** 空间名称 */
  name?: string;
  /** 空间描述 */
  description?: string;
  /** 图标url */
  icon_url?: string;
  /** 空间类型 */
  space_type?: SpaceType;
}

export interface CancelPublishOfficialPluginRequest {
  /** 官方插件id */
  plugin_id: string;
}

export interface CancelPublishOfficialPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface CategoryListData {
  categorys?: Array<ExploreBotCategory>;
}

export interface ChainEvent {
  root_id?: string;
  parent_run_id?: string;
  id?: string;
  message_id?: string;
  name?: string;
  run_type?: string;
  start_time?: string;
  duration?: string;
  question?: string;
  answer?: string;
  a_id?: string;
  inputs?: string;
  outputs?: string;
  q_create_time?: string;
  q_update_time?: string;
  a_create_time?: string;
  a_update_time?: string;
  a_status?: string;
  a_broken_position?: string;
  log_id?: string;
  extra?: string;
}

export interface CommonFileInfo {
  /** 文件类型，后缀 */
  file_type?: string;
  /** 业务类型 */
  biz_type?: FileBizType;
}

export interface ConnectorInfo {
  id?: string;
  name?: string;
  icon?: string;
}

export interface ConnInfo {
  conn_id?: string;
  name?: string;
}

export interface Creator {
  id?: string;
  name?: string;
  avatar_url?: string;
  /** 是否是自己创建的 */
  self?: boolean;
}

/** DeleteOfficialPlugin */
export interface DeleteOfficialPluginRequest {
  /** 官方插件id */
  plugin_id: string;
}

export interface DeleteOfficialPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteUserLabelRequest {
  label_id?: string;
}

export interface DeleteUserLabelResponse {
  code?: Int64;
  msg?: string;
}

export interface DraftBot {
  /** draftid */
  id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  icon_url?: string;
  visibility?: VisibilityType;
  has_published?: Publish;
  app_ids?: Array<AppIDInfo>;
  create_time?: string;
  update_time?: string;
  creator_id?: string;
  space_id?: string;
  model_info?: ModelInfo;
  creator?: Creator;
  index?: string;
  bot_explore_status?: BotExploreStatus;
  space_name?: string;
  /** explore_id */
  explore_id?: string;
  last_online_time?: string;
  explore_bot_update_time?: string;
  /** bot 模式 */
  bot_mode?: BotMode;
  /** 是否更新过热度 */
  need_backlog?: boolean;
  /** 分类名 */
  category_name?: string;
  /** 分类ID */
  category_id?: string;
  work_info?: WorkInfo;
  intelligence_type?: IntelligenceType;
}

export interface DuplicateExploreBotRequest {
  bot_id?: string;
}

export interface DuplicateExploreBotResponse {
  code?: Int64;
  msg?: string;
  data?: DuplicateExploreBotResponseData;
}

export interface DuplicateExploreBotResponseData {
  bot_id?: string;
}

export interface ExploreBotCategory {
  id?: string;
  index?: number;
  name?: string;
  name_key?: string;
  bot_count?: number;
}

export interface ExportIntentCaseRequest {
  /** 毫秒时间戳 */
  from_date?: string;
  to_date?: string;
  /** 筛选dora数据 */
  is_dora?: number;
  /** 1:badcase 2: 随机session */
  data_source?: number;
  /** 导出多少session */
  session_num?: number;
  /** 筛选agent/插件名称 */
  plugin_name?: Array<string>;
  /** 欠召2 or 误召4 */
  intent_case_type?: Array<number>;
}

export interface ExportIntentCaseResponse {
  code?: Int64;
  msg?: string;
  url?: string;
}

export interface FetchChainEventListData {
  events?: Array<ChainEvent>;
  offset?: Int64;
  limit?: Int64;
}

export interface FetchChainEventListResponse {
  code?: Int64;
  msg?: string;
  data?: FetchChainEventListData;
}

export interface FetchChainEventRequest {
  start_time?: Int64;
  end_time?: Int64;
  /** 3:   optional string message_id */
  offset?: number;
  limit?: number;
  root_id?: string;
  log_id?: string;
}

export interface FetchMsgListData {
  messages?: Array<Msg>;
  page_index?: number;
  page_size?: number;
  total?: number;
}

export interface FetchMsgListRequest {
  start_time?: string;
  end_time?: string;
  content?: string;
  message_id?: string;
  device_id?: string;
  page_index?: number;
  page_size?: number;
  bot_id?: string;
  connector_id?: ConnectorId;
  user_id?: string;
  log_id?: string;
  conn_id?: string;
}

export interface FetchMsgListResponse {
  code?: Int64;
  msg?: string;
  data?: FetchMsgListData;
}

export interface GetAllUserLabelRequest {}

export interface GetAllUserLabelResponse {
  code?: Int64;
  msg?: string;
  user_labels?: Array<bot_common.UserLabel>;
}

/** -------展示api_key列表------- */
export interface GetApiKeyListRequest {
  space_id?: string;
  page_index?: number;
  page_size?: number;
}

export interface GetApiKeyListResponse {
  code?: Int64;
  msg?: string;
  data?: GetApiKeyListResponseData;
}

export interface GetApiKeyListResponseData {
  api_keys?: Array<ApiKeyInfo>;
  total?: number;
}

export interface GetBotMigrateProgressData {
  async_task?: Array<MigBotSpaceAsyncTask>;
  total?: number;
}

export interface GetBotMigrateProgressRequest {
  target_space_id?: string;
  bot_id?: string;
  page?: number;
  size?: number;
  final_status?: AsyncStatus;
}

export interface GetBotMigrateProgressResponse {
  code?: Int64;
  msg?: string;
  data: GetBotMigrateProgressData;
}

export interface GetCategoryListRequest {}

export interface GetCategoryListResponse {
  code?: Int64;
  msg?: string;
  data?: CategoryListData;
}

export interface GetConnIdData {
  conn_info?: Array<ConnInfo>;
}

export interface GetConnIdRequest {}

export interface GetConnIdResponse {
  code?: Int64;
  msg?: string;
  data?: GetConnIdData;
}

export interface GetIconRequest {
  icon_type?: IconType;
}

export interface GetIconResponse {
  code?: Int64;
  msg?: string;
  data?: GetIconResponseData;
}

export interface GetIconResponseData {
  icon_list?: Array<Icon>;
}

export interface GetIntentCaseDetailRequest {
  /** 具体格式，前端定好 */
  from_date?: string;
  to_date?: string;
  agent_id?: string;
  case_types?: Array<IntentCaseType>;
  page?: number;
  page_size?: number;
}

export interface GetIntentCaseDetailResponse {
  code?: Int64;
  msg?: string;
  data?: Array<IntentCaseDetail>;
  total?: number;
}

export interface GetIntentCaseStatsRequest {
  /** 具体格式，前端定好 */
  from_date?: string;
  to_date?: string;
  agent_id?: string;
}

export interface GetIntentCaseStatsResponse {
  code?: Int64;
  msg?: string;
  stats?: Array<IntentCaseStats>;
}

export interface GetLabelledUserRequest {
  /** page>=1 */
  page?: number;
  /** 0<size<=50 */
  size?: number;
  /** 按照id查询 */
  user_id?: string;
  /** 按照用户名查询 */
  user_unique_name?: string;
}

export interface GetLabelledUserResponse {
  code?: Int64;
  msg?: string;
  user_info?: Array<UserLabelInfo>;
  count?: number;
}

export interface GetOpAllBotListData {
  /** 结果 */
  bot_draft_list?: Array<DraftBot>;
  /** 总个数 */
  total?: number;
}

export interface GetOpAllBotListRequest {
  /** 空间id */
  space_id?: string;
  /** bot_name 搜索 */
  bot_name?: string;
  /** 发布平台 */
  publish_platform?: Array<string>;
  /** 分页 */
  page_index?: number;
  /** 分页大小 */
  page_size?: number;
  /** 是否已发布 */
  is_publish?: PublishStatus;
  /** bot_id */
  bot_id?: string;
  /** 批量查询botid */
  batch_bot_id?: Array<string>;
  connector_id?: ConnectorId;
}

export interface GetOpAllBotListResponse {
  code?: Int64;
  msg?: string;
  data?: GetOpAllBotListData;
}

export interface GetOpExploreBotListData {
  /** 结果 */
  bot_draft_list?: Array<DraftBot>;
  /** 总个数 */
  total?: number;
}

export interface GetOpExploreBotListRequest {
  /** 是否显示 */
  bot_status?: BotExploreStatus;
  /** bot名模糊搜 */
  key_word?: string;
  /** 分页 */
  page_index?: number;
  /** 分页大小 */
  page_size?: number;
  /** 分类id */
  category_id?: Array<string>;
  /** true表示未分类 */
  uncategorized?: boolean;
}

export interface GetOpExploreBotListResponse {
  code?: Int64;
  msg?: string;
  data?: GetOpExploreBotListData;
}

export interface GetOpSpaceListData {
  bot_space_list?: Array<BotSpace>;
}

export interface GetOpSpaceListRequest {
  /** space_name */
  name?: string;
}

export interface GetOpSpaceListResponse {
  code?: Int64;
  msg?: string;
  get_op_space_list_data?: GetOpSpaceListData;
}

export interface GetPluginListData {
  plugin_list?: Array<PluginData>;
  total?: Int64;
}

export interface GetPluginListRequest {
  page?: number;
  size?: number;
  status?: PluginStatus;
  plugin_ids?: Array<string>;
  space_id?: string;
  plugin_name?: string;
  order_by?: OrderBy;
}

export interface GetPluginListResponse {
  code?: Int64;
  msg?: string;
  data?: GetPluginListData;
}

export interface GetSSOUserInfoData {
  /** 姓名 */
  username?: string;
  /** region */
  region?: string;
  /** 工号 */
  employee_id?: Int64;
  /** 邮箱 */
  email?: string;
  /** 头像地址 */
  avatar_url?: string;
  /** 开发者平台的userid */
  user_id?: string;
}

export interface GetSSOUserInfoRequest {}

export interface GetSSOUserInfoResponse {
  code?: Int64;
  msg?: string;
  data?: GetSSOUserInfoData;
}

export interface GetWaitListStatisticalRequest {}

export interface GetWaitListStatisticalResponse {
  code?: Int64;
  msg?: string;
  data: StatisticalInfo;
}

export interface GrantBotQualificationRequest {
  total?: Int64;
  timestamp?: Int64;
  user_ids?: Array<string>;
}

export interface GrantBotQualificationResponse {
  code?: Int64;
  msg?: string;
}

export interface HisData {
  name?: string;
  desc?: string;
  time?: string;
}

export interface Icon {
  url?: string;
  uri?: string;
}

export interface IntentCaseDetail {
  user_query?: string;
  answer?: string;
  system_prompt?: string;
  chat_history?: string;
  /** 错误类型 */
  case_type?: IntentCaseType;
  /** 当前选择的Agent_id */
  current_agent_id?: string;
  /** 意图分析后，应该跳转的Agent_id */
  analysis_agent_id?: string;
  date?: string;
  id?: string;
  /** 模型给出的分析原因 */
  analysis_reason?: string;
}

export interface IntentCaseStats {
  date?: string;
  /** 正确召回的数量 */
  true_positive_cnt?: number;
  /** 拒答率：reject_cnt/true_positive_cnt */
  reject_cnt?: number;
  /** 误召回率：false_positive_cnt/(false_positive_cnt+true_positive_cnt) */
  false_positive_cnt?: number;
  /** 欠召回率：false_negative_cnt/(true_positive_cnt+false_negative_cnt) */
  false_negative_cnt?: number;
}

export interface ListWaitingQueueRequest {
  page_size: number;
  page_no: number;
  wait_status?: WaitStatus;
  email?: string;
  user_id?: string;
  mobile?: string;
}

export interface ListWaitingQueueResponse {
  code?: Int64;
  msg?: string;
  data: WaitQueueData;
}

export interface LogoutRequest {}

export interface LogoutResponse {}

export interface MGetUserLabelInfoRequest {
  query_condition?: Array<string>;
  query_type?: QueryType;
}

export interface MGetUserLabelInfoResponse {
  code?: Int64;
  msg?: string;
  user_info?: Array<UserLabelInfo>;
}

export interface MGetUserUIDRequest {
  uid_list?: string;
}

export interface MGetUserUIDResponse {
  code?: Int64;
  msg?: string;
  data?: MGetUserUIDResponseData;
}

export interface MGetUserUIDResponseData {
  user_list?: Record<string, User>;
}

export interface MigBotSpaceAsyncTask {
  id?: string;
  primary_id?: string;
  task_name?: string;
  status?: AsyncStatus;
  task_info?: MigBotSpaceTaskInfo;
  sub_task_status?: MigBotSpaceSubTaskStatus;
  operator_id?: string;
  create_time?: string;
  bot_list?: Array<DraftBot>;
  trans_space?: TransBotSpace;
}

export interface MigBotSpaceSubTaskStatus {
  transfer_draft_bot_space?: boolean;
  transfer_plugin_space?: boolean;
  transfer_work_flow_space?: boolean;
  transfer_dataset_space?: boolean;
  transfer_card_space?: boolean;
  trans_fail_plugin?: Array<TransferFailResource>;
  trans_fail_dataset?: Array<TransferFailResource>;
}

export interface MigBotSpaceTaskInfo {
  target_space_id?: string;
  user_id?: string;
  bot_ids?: Array<string>;
}

export interface MigrateBotRequest {
  bot_ids?: Array<string>;
  target_space_id?: string;
}

export interface MigrateBotResponse {
  code?: Int64;
  msg?: string;
}

export interface ModelInfo {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  ShortMemPolicy?: ShortMemPolicy;
  prompt_id?: number;
  card_ids?: Array<number>;
  model_name?: string;
}

export interface MSetPluginSpaceByIDsRequest {
  plugin_ids?: Array<string>;
  target_space_id: string;
}

export interface MSetPluginSpaceByIDsResponse {
  code?: Int64;
  msg?: string;
  repeat_plugin_list?: Array<BasicInfo>;
}

export interface Msg {
  message_id?: string;
  chat_start_time?: string;
  group_name?: string;
  user_id?: string;
  span_count?: number;
  llm_span_count?: number;
  tool_span_count?: number;
  tool_names?: Array<string>;
  content?: string;
  device_id?: string;
  connector_id?: string;
  log_id?: string;
}

export interface OfflineOfficialPluginRequest {
  /** 官方插件id */
  plugin_id: string;
}

export interface OfflineOfficialPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface OnlineOfficialPluginRequest {
  /** 官方插件id */
  plugin_id: string;
}

export interface OnlineOfficialPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface PluginApi {
  /** operationId */
  name?: string;
  /** summary */
  desc?: string;
  parameters?: Array<PluginParameter>;
  plugin_id?: string;
  plugin_name?: string;
  /** 序号和playground保持一致 */
  api_id?: string;
  record_id?: string;
  /** path */
  path?: string;
  response?: Array<PluginParameter>;
}

export interface PluginData {
  id?: string;
  name?: string;
  /** description_for_human */
  desc_for_human?: string;
  plugin_icon?: string;
  plugin_type?: PluginType;
  status?: PluginStatus;
  /** json */
  plugin_desc?: string;
  update_time?: Int64;
  creator?: string;
  space_id?: string;
  space_name?: string;
  /** 引用数 */
  bot_quote?: number;
  /** 插件商品状态 */
  plugin_product_status?: PluginProductStatus;
  /** 发布状态 */
  publish_status?: PluginPublishStatus;
  /** 插件渠道 */
  channel_id?: plugin_common.PluginChannel;
  /** 插件素材id */
  material_id?: string;
  /** tools */
  plugin_apis?: Array<PluginApi>;
  /** server url */
  server_url?: string;
}

export interface PluginParameter {
  name?: string;
  desc?: string;
  required?: boolean;
  type?: string;
  sub_parameters?: Array<PluginParameter>;
  /** 如果Type是数组，则有subtype */
  sub_type?: string;
  /** 如果入参的值是引用的则有fromNodeId */
  from_node_id?: string;
  /** 具体引用哪个节点的key */
  from_output?: Array<string>;
  /** 如果入参是用户手输 就放这里 */
  value?: string;
}

export interface PublishOfficialPluginRequest {
  /** 官方插件id */
  plugin_id: string;
}

export interface PublishOfficialPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface RetryMigTaskRequest {
  task_id?: string;
  Base?: base.Base;
}

export interface RetryMigTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface SaveCategoryRequest {
  categorys?: Array<ExploreBotCategory>;
}

export interface SaveCategoryResponse {
  code?: Int64;
  msg?: string;
}

export interface SaveUserLabelRequest {
  /** 传ID则更新，不传ID则新建 */
  user_label?: bot_common.UserLabel;
}

export interface SaveUserLabelResponse {
  code?: Int64;
  msg?: string;
}

export interface SearchUserUIDRequest {
  /** 用户名称，不是 nickname，需要精确搜索 */
  username?: string;
}

export interface SearchUserUIDResponse {
  code?: Int64;
  msg?: string;
  data?: SearchUserUIDResponseData;
}

export interface SearchUserUIDResponseData {
  user_list?: Array<User>;
}

export interface ShortMemPolicy {
  ContextContentType?: ContextContentType;
  HistoryRound?: number;
}

export interface StatisticalInfo {
  today_add_wait_grant_count?: number;
  today_add_finish_grant_count?: number;
}

export interface TransBotSpace {
  SpaceId?: string;
  SpaceName?: string;
  OriSpaceId?: string;
  OriSpaceName?: string;
}

export interface TransferFailResource {
  id?: string;
  name?: string;
}

export interface UpdateAllowDomainRequest {
  /** 域名:端口 */
  domain?: string;
}

export interface UpdateAllowDomainResponse {
  code?: Int64;
  msg?: string;
}

/** -------修改api_key------- */
export interface UpdateApiKeyRequest {
  api_key_id?: string;
  operate_type?: number;
}

export interface UpdateApiKeyResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateExploreBotData {}

export interface UpdateExploreBotRequest {
  explore_bot_id: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  index?: number;
  /** 上线下线 */
  explore_status?: BotExploreStatus;
  /** 删除 */
  del_status?: BotDeleteStatus;
}

export interface UpdateExploreBotResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdatePluginStatusRequest {
  plugin_id: string;
  status: PluginStatus;
}

export interface UpdatePluginStatusResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateUserLabelRequest {
  user_ids?: Array<string>;
  label_id?: string;
}

export interface UpdateUserLabelResponse {
  code?: Int64;
  msg?: string;
}

export interface UploadFileData {
  /** 文件url */
  upload_url?: string;
  /** 文件uri，提交使用这个 */
  upload_uri?: string;
}

export interface UploadFileRequest {
  /** 文件相关描述 */
  file_head?: CommonFileInfo;
  /** 文件数据 */
  data?: string;
}

export interface UploadFileResponse {
  code?: Int64;
  msg?: string;
  /** 数据 */
  data?: UploadFileData;
}

export interface User {
  /** 用户 uid */
  uid?: string;
  /** 昵称 */
  name?: string;
  /** 头像 */
  avatar_url?: string;
}

export interface UserLabelInfo {
  user_id?: string;
  /** 用户名 */
  user_unique_name?: string;
  user_label?: bot_common.UserLabel;
}

export interface WaitData {
  id?: string;
  uid?: string;
  mail?: string;
  using_for?: string;
  hear_from?: string;
  ext_message?: string;
  ip_region?: string;
  register_time?: string;
  grant_time?: string;
  grant_type?: GrantType;
  mobile?: string;
}

export interface WaitQueueData {
  waiting_count: number;
  waiting_list?: Array<WaitData>;
  page_size: number;
  page_no: number;
}

/** 工作区间各个模块的信息 */
export interface WorkInfo {
  message_info?: string;
  /** 2:optional string   prompt */
  variable?: string;
  other_info?: string;
  history_info?: string;
  tools?: string;
  system_info_all?: string;
  dataset?: string;
  onboarding?: string;
  profile_memory?: string;
  table_info?: string;
  workflow?: string;
  task?: string;
  suggest_reply?: string;
}
/* eslint-enable */
