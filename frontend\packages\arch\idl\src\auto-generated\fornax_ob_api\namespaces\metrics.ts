/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 聚合方式 */
export enum AggregateType {
  Unknown = 1,
  Avg = 2,
  P50 = 3,
  P90 = 4,
  P99 = 5,
  Max = 6,
  Min = 7,
  Sum = 8,
}

/** 降采样间隔 */
export enum DownsampleInterval {
  Unknown = 1,
  /** 30 second */
  DI30S = 2,
  /** 1 minute */
  DI1M = 3,
  /** 2 minute */
  DI2M = 4,
  /** 5 minute */
  DI5M = 5,
  /** 10 minute */
  DI10M = 6,
  /** 20 minute */
  DI20M = 7,
  /** 30 minute */
  DI30M = 8,
}
/* eslint-enable */
