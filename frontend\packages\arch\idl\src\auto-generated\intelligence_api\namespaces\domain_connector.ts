/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ConnectorAuditStatus {
  /** 未知、无审核 */
  Unknown = 0,
  /** 审核中 */
  Progress = 1,
  /** 审核通过 */
  Audited = 2,
  /** 审核拒绝 */
  Reject = 3,
}

export enum ConnectorBindStatus {
  Invalid = 0,
  Valid = 1,
  SaveNotPublish = 2,
  /** Token发生变化 */
  Disconnected = 3,
  /** 目前的配置不合法，需要重新配置。目前仅飞书多维表格使用 */
  NeedReconfigure = 4,
}

export enum ConnectorBindType {
  /** 无需绑定 */
  NoBindRequired = 1,
  /** Auth绑定 */
  AuthBind = 2,
  /** Kv绑定 */
  KvBind = 3,
  /** Kv并Auth授权 */
  KvAuthBind = 4,
  /** api渠道绑定 */
  ApiBind = 5,
  /** WebSDK绑定 */
  WebSDKBind = 6,
  /** 商店绑定 */
  StoreBind = 7,
  /** 授权和配置各一个按钮 */
  AuthAndConfig = 8,
  /** 模板渠道绑定 */
  TemplateBind = 9,
}

export enum ConnectorClassification {
  /** api或sdk */
  APIOrSDK = 1,
  /** 社交平台 */
  SocialPlatform = 2,
  /** Coze商店/模板 */
  Coze = 3,
  /** 小程序 */
  MiniProgram = 4,
  /** MCP扩展库 */
  CozeSpaceExtensionLibrary = 5,
}

export enum ConnectorStatus {
  /** 正常 */
  Normal = 0,
  /** 审核中 */
  InReview = 1,
  /** 已下线 */
  Offline = 2,
}
/* eslint-enable */
