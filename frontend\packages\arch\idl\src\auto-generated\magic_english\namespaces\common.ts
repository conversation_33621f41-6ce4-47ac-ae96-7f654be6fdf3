/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ChatRole {
  User = 1,
  Assistant = 2,
}

export enum ConversationStatus {
  /** 对话中 */
  InProgress = 0,
  /** 已完成 */
  Finished = 1,
}

export enum EnglishExerciseType {
  /** 原句 */
  OriginalSentence = 1,
  /** 词汇匹配 */
  VocabularyMatching = 2,
  /** 听音重组句子 */
  SentencesByListening = 3,
  /** 口语跟读 */
  OralShadowing = 4,
  /** 听力素材，需要支持马赛克 */
  ListeningMaterial = 5,
  /** 题目描述，不需要出AI老师提示 */
  QuestionDescription = 6,
  /** 多选 */
  MultiChoice = 7,
  /** 简答题，需要出AI提示 */
  ShortAnswer = 8,
  /** 事件描述 */
  EventDescription = 9,
  /** 图表描述 */
  ChartDescription = 10,
  /** 事件复述 */
  EventRetelling = 11,
}

export enum ExerciseType {
  NORMAL = 1,
  TEST = 2,
}

export enum MeetingCategoryType {
  BizLine = 1,
  JobRole = 2,
  Scene = 3,
}

export enum MeetingLearingRecordStatus {
  Answering = 1,
  AIChating = 2,
  Finished = 3,
  Exited = 4,
}

export enum MeetingLearningStatus {
  Unfinished = 0,
  Finished = 1,
}

export enum MeetingType {
  /** 通用教材 */
  General = 1,
  /** 个性化会议 */
  Personalization = 2,
  /** 口语练习 */
  OralPractice = 3,
  /** 听力练习 */
  ListeningPractice = 4,
}

/** ======================= 枚举 ======================= */
export enum MessageRole {
  /** 模型 */
  Assistant = 1,
  /** 用户 */
  User = 2,
}

export enum NodeStatus {
  Locked = 0,
  InProgress = 1,
  Finished = 2,
}

export enum OralScoreType {
  A1_1 = 1,
  A1_2 = 2,
  A2_1 = 3,
  A2_2 = 4,
  B1_1 = 5,
  B1_2 = 6,
  B2_1 = 7,
  B2_2 = 8,
  C1_1 = 9,
  C1_2 = 10,
  C2 = 11,
}

export enum PhraseStatus {
  UNUSED = 0,
  USED = 1,
}

export enum ReportStatus {
  /** 无效 */
  Expired = 0,
  /** 有效 */
  Normal = 1,
}

export enum StudyModuleType {
  /** 魔鬼口语教练 */
  MagicOral = 1,
  /** 原味2.0 AI会议 */
  AiMeeting = 2,
  /** 刷题（口语+听力） */
  PracticeExam = 3,
}

export enum TaskStatus {
  UNFINISHED = 0,
  FINISHED = 1,
}

export enum TopicType {
  BIZ = 1,
  INTEREST = 2,
  REALTIME = 3,
}

export enum UserExerciseStatus {
  /** 练习中 */
  InProgress = 0,
  /** 已完成 */
  Finished = 1,
}

export interface AISuggestionOutput {
  suggestions?: Array<string>;
}

export interface AudioDetail {
  /** 音频id */
  audio_id?: Int64;
  /** 音频uri */
  audio_url?: string;
  /** 字时间戳 */
  words_info?: Array<TTSWord>;
  created_at?: Int64;
  updated_at?: Int64;
}

export interface Conversation {
  id: Int64;
  user_id?: Int64;
  exercise_id?: Int64;
  status?: ConversationStatus;
  report?: ConversationReport;
  /** 用户对话轮数 */
  user_message_count?: number;
  message_list?: Array<ConversationMessage>;
  task_list?: Array<ExerciseTask>;
  phrase_list?: Array<Phrase>;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface ConversationMessage {
  id?: Int64;
  role?: MessageRole;
  content?: string;
  audio_id?: string;
  duration?: number;
  has_eval?: boolean;
  accuracy_score?: number;
  fluency_score?: number;
  answer_recombined?: string;
  revised_answer?: string;
  revise_reason?: string;
  optimized_answer?: string;
  translation?: string;
  optimized_translation?: string;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface ConversationReport {
  id?: Int64;
  conversation_id?: Int64;
  total_score?: number;
  accuracy_score?: number;
  fluency_score?: number;
  task_score?: number;
  phrase_score?: number;
  detail_score?: number;
  summary?: string;
  status?: ReportStatus;
  created_at?: Int64;
  updated_at?: Int64;
}

export interface Exercise {
  exercise_id: Int64;
  exercise_title?: string;
  exercise_description?: string;
  exercise_type?: ExerciseType;
  status?: NodeStatus;
  topic_id?: Int64;
}

export interface ExerciseTask {
  task_id: string;
  title?: string;
  description?: string;
  status?: TaskStatus;
}

export interface HistoryMessage {
  role?: MessageRole;
  role_name?: string;
  role_content?: string;
}

export interface JSSDKConfig {
  app_id?: string;
  nonce_str?: string;
  signature?: string;
  timestamp?: string;
}

export interface MessageEvalData {
  message_id?: string;
  used_phrase_list?: Array<Phrase>;
  finished_task_list?: Array<ExerciseTask>;
  accuracy_score?: number;
  fluency_score?: number;
  answer_recombined?: string;
  revised_answer?: string;
  revise_reason?: string;
  optimized_answer?: string;
  optimized_translation?: string;
}

/** ======================= 模型 ======================= */
export interface Phase {
  phase_id: Int64;
  phase_title?: string;
  phase_description?: string;
  phase_detail?: string;
  section_list?: Array<Section>;
  status?: NodeStatus;
  disabled?: boolean;
}

export interface Phrase {
  phrase_id: string;
  zh_text?: string;
  en_text?: string;
  usage_list?: Array<PhraseUsage>;
  example_list?: Array<PhraseExample>;
  status?: PhraseStatus;
}

export interface PhraseExample {
  zh_text?: string;
  en_text?: string;
  video_url?: string;
}

export interface PhraseUsage {
  zh_text?: string;
  en_text?: string;
}

export interface Section {
  section_id: Int64;
  section_title?: string;
  section_description?: string;
  topic_list?: Array<Topic>;
  status?: NodeStatus;
  phase_id?: Int64;
}

export interface StreamChunk {
  content?: string;
  is_finished?: boolean;
}

export interface Topic {
  topic_id: Int64;
  topic_title?: string;
  topic_description?: string;
  topic_type?: TopicType;
  exercise_list?: Array<Exercise>;
  status?: NodeStatus;
  section_id?: Int64;
}

export interface TTSWord {
  word?: string;
  start_time?: number;
  end_time?: number;
}

export interface UserLevelData {
  level?: string;
  is_new_user?: boolean;
  phase_list?: Array<Phase>;
  recommend_phase?: Phase;
}

export interface VoiceToken {
  token: string;
}
/* eslint-enable */
