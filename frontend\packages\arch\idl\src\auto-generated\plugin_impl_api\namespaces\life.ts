/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface GetCityRankListRequest {
  city: string;
  poi_type: string;
  sub_poi_type: string;
  district: string;
  rank_type?: string;
  longitude?: number;
  latitude?: number;
}

export interface GetCityRankListResponse {
  code?: number;
  msg?: string;
  response_for_model?: string;
}

export interface GetCityRankListTypeRequest {
  city_code?: string;
  city?: string;
  longitude?: number;
  latitude?: number;
}

export interface GetCityRankListTypeResponse {
  code?: number;
  msg?: string;
  response_for_model?: string;
}

export interface GetPoiDetailRequest {
  poi_id: string;
  longitude?: number;
  latitude?: number;
}

export interface GetPoiDetailResponse {
  code?: number;
  msg?: string;
  response_for_model?: string;
}

export interface GetPoiRateFeedRequest {
  poi_id: string;
}

export interface GetPoiRateFeedResponse {
  code?: number;
  msg?: string;
  response_for_model?: string;
}

export interface GetSearchListRequest {
  query: string;
  city?: string;
  longitude?: number;
  latitude?: number;
}

export interface GetSearchListResponse {
  code?: number;
  msg?: string;
  response_for_model?: string;
}
/* eslint-enable */
