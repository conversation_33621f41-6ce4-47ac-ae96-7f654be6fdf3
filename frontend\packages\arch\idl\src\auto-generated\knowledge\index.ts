/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as common from './namespaces/common';
import * as connector from './namespaces/connector';
import * as connector_common from './namespaces/connector_common';
import * as dataset from './namespaces/dataset';
import * as document from './namespaces/document';
import * as openapi from './namespaces/openapi';
import * as opensearch from './namespaces/opensearch';
import * as resource_common from './namespaces/resource_common';
import * as review from './namespaces/review';
import * as slice from './namespaces/slice';
import * as url from './namespaces/url';
import * as volcano_dataset from './namespaces/volcano_dataset';

export {
  base,
  common,
  connector,
  connector_common,
  dataset,
  document,
  openapi,
  opensearch,
  resource_common,
  review,
  slice,
  url,
  volcano_dataset,
};
export * from './namespaces/base';
export * from './namespaces/common';
export * from './namespaces/connector';
export * from './namespaces/connector_common';
export * from './namespaces/dataset';
export * from './namespaces/document';
export * from './namespaces/openapi';
export * from './namespaces/opensearch';
export * from './namespaces/resource_common';
export * from './namespaces/review';
export * from './namespaces/slice';
export * from './namespaces/url';
export * from './namespaces/volcano_dataset';

export type Int64 = string | number;

export default class KnowledgeService<T> {
  private request: any = () => {
    throw new Error('KnowledgeService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/knowledge/slice/delete
   *
   * 切片 - 通用 *
   */
  DeleteSlice(
    req?: slice.DeleteSliceRequest,
    options?: T,
  ): Promise<slice.DeleteSliceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/slice/delete');
    const method = 'POST';
    const data = { slice_ids: _req['slice_ids'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/slice/update */
  UpdateSlice(
    req: slice.UpdateSliceRequest,
    options?: T,
  ): Promise<slice.UpdateSliceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/slice/update');
    const method = 'POST';
    const data = {
      slice_id: _req['slice_id'],
      document_id: _req['document_id'],
      status: _req['status'],
      raw_text: _req['raw_text'],
      table_unit_text: _req['table_unit_text'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/slice/create */
  CreateSlice(
    req: slice.CreateSliceRequest,
    options?: T,
  ): Promise<slice.CreateSliceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/slice/create');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      raw_text: _req['raw_text'],
      sequence: _req['sequence'],
      extra: _req['extra'],
      tree_node_id: _req['tree_node_id'],
      front_tree_node_id: _req['front_tree_node_id'],
      parent_tree_node_id: _req['parent_tree_node_id'],
      dataset_id: _req['dataset_id'],
      document_id_new: _req['document_id_new'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/create */
  CreateDocument(
    req?: document.CreateDocumentRequest,
    options?: T,
  ): Promise<document.CreateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/create');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      format_type: _req['format_type'],
      document_bases: _req['document_bases'],
      chunk_strategy: _req['chunk_strategy'],
      sink_strategy: _req['sink_strategy'],
      is_append: _req['is_append'],
      parsing_strategy: _req['parsing_strategy'],
      index_strategy: _req['index_strategy'],
      storage_strategy: _req['storage_strategy'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/table_schema/get
   *
   * 表格解析 *
   */
  GetTableSchema(
    req?: document.GetTableSchemaRequest,
    options?: T,
  ): Promise<document.GetTableSchemaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/table_schema/get');
    const method = 'POST';
    const data = {
      table_sheet: _req['table_sheet'],
      table_data_type: _req['table_data_type'],
      document_id: _req['document_id'],
      source_file: _req['source_file'],
      origin_table_meta: _req['origin_table_meta'],
      preview_table_meta: _req['preview_table_meta'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/slice/list */
  ListSlice(
    req?: slice.ListSliceRequest,
    options?: T,
  ): Promise<slice.ListSliceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/slice/list');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      sequence: _req['sequence'],
      keyword: _req['keyword'],
      dataset_id: _req['dataset_id'],
      page_no: _req['page_no'],
      page_size: _req['page_size'],
      sort_field: _req['sort_field'],
      is_asc: _req['is_asc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/list */
  ListDocument(
    req: document.ListDocumentRequest,
    options?: T,
  ): Promise<document.ListDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/document/list');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_ids: _req['document_ids'],
      page: _req['page'],
      size: _req['size'],
      keyword: _req['keyword'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/resegment */
  Resegment(
    req?: document.ResegmentRequest,
    options?: T,
  ): Promise<document.ResegmentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/resegment');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_ids: _req['document_ids'],
      chunk_strategy: _req['chunk_strategy'],
      review_ids: _req['review_ids'],
      parsing_strategy: _req['parsing_strategy'],
      index_strategy: _req['index_strategy'],
      filter_strategy: _req['filter_strategy'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/delete */
  DeleteDocument(
    req?: document.DeleteDocumentRequest,
    options?: T,
  ): Promise<document.DeleteDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/delete');
    const method = 'POST';
    const data = {
      document_ids: _req['document_ids'],
      document_ids_new: _req['document_ids_new'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/progress/get */
  GetDocumentProgress(
    req?: document.GetDocumentProgressRequest,
    options?: T,
  ): Promise<document.GetDocumentProgressResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/progress/get');
    const method = 'POST';
    const data = { document_ids: _req['document_ids'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/web_url/submit
   *
   * web 获取 *
   */
  SubmitWebUrl(
    req?: document.SubmitWebUrlRequest,
    options?: T,
  ): Promise<document.SubmitWebUrlResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/web_url/submit');
    const method = 'POST';
    const data = {
      web_url: _req['web_url'],
      subpages_count: _req['subpages_count'],
      format_type: _req['format_type'],
      title: _req['title'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/web_url/get */
  GetWebInfo(
    req?: document.GetWebInfoRequest,
    options?: T,
  ): Promise<document.GetWebInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/web_url/get');
    const method = 'POST';
    const data = {
      web_ids: _req['web_ids'],
      include_content: _req['include_content'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/update */
  UpdateDocument(
    req?: document.UpdateDocumentRequest,
    options?: T,
  ): Promise<document.UpdateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/update');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      status: _req['status'],
      document_name: _req['document_name'],
      update_rule: _req['update_rule'],
      table_meta: _req['table_meta'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/table_schema/validate */
  ValidateTableSchema(
    req?: document.ValidateTableSchemaRequest,
    options?: T,
  ): Promise<document.ValidateTableSchemaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/table_schema/validate');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      document_id: _req['document_id'],
      source_file: _req['source_file'],
      table_sheet: _req['table_sheet'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/create
   *
   * 知识库 - 通用 *
   */
  CreateDataset(
    req?: dataset.CreateDatasetRequest,
    options?: T,
  ): Promise<dataset.CreateDatasetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      space_id: _req['space_id'],
      icon_uri: _req['icon_uri'],
      format_type: _req['format_type'],
      biz_id: _req['biz_id'],
      project_id: _req['project_id'],
      storage_location: _req['storage_location'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/list */
  ListDataset(
    req?: dataset.ListDatasetRequest,
    options?: T,
  ): Promise<dataset.ListDatasetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/list');
    const method = 'POST';
    const data = {
      filter: _req['filter'],
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      order_field: _req['order_field'],
      order_type: _req['order_type'],
      space_auth: _req['space_auth'],
      biz_id: _req['biz_id'],
      need_ref_bots: _req['need_ref_bots'],
      project_id: _req['project_id'],
      storage_location: _req['storage_location'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/detail */
  DatasetDetail(
    req?: dataset.DatasetDetailRequest,
    options?: T,
  ): Promise<dataset.DatasetDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/detail');
    const method = 'POST';
    const data = {
      dataset_ids: _req['dataset_ids'],
      space_id: _req['space_id'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/update */
  UpdateDataset(
    req?: dataset.UpdateDatasetRequest,
    options?: T,
  ): Promise<dataset.UpdateDatasetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/update');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      name: _req['name'],
      icon_uri: _req['icon_uri'],
      description: _req['description'],
      status: _req['status'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/delete */
  DeleteDataset(
    req?: dataset.DeleteDatasetRequest,
    options?: T,
  ): Promise<dataset.DeleteDatasetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/delete');
    const method = 'POST';
    const data = { dataset_id: _req['dataset_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/web_url/batch_submit */
  BatchSubmitWebUrl(
    req?: document.BatchSubmitWebUrlRequest,
    options?: T,
  ): Promise<document.BatchSubmitWebUrlResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/web_url/batch_submit');
    const method = 'POST';
    const data = { web_urls: _req['web_urls'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/icon/get
   *
   * 为前端提供查询支持库图标
   */
  GetIcon(
    req?: dataset.GetIconRequest,
    options?: T,
  ): Promise<dataset.GetIconResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/icon/get');
    const method = 'POST';
    const data = { format_type: _req['format_type'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /open_api/knowledge/document/update */
  UpdateDocumentOpenAPI(
    req?: document.UpdateDocumentRequest,
    options?: T,
  ): Promise<document.UpdateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/open_api/knowledge/document/update');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      status: _req['status'],
      document_name: _req['document_name'],
      update_rule: _req['update_rule'],
      table_meta: _req['table_meta'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open_api/knowledge/document/create
   *
   * 文档 - OpenAPI *
   */
  CreateDocumentOpenAPI(
    req?: document.CreateDocumentRequest,
    options?: T,
  ): Promise<document.CreateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/open_api/knowledge/document/create');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      format_type: _req['format_type'],
      document_bases: _req['document_bases'],
      chunk_strategy: _req['chunk_strategy'],
      sink_strategy: _req['sink_strategy'],
      is_append: _req['is_append'],
      parsing_strategy: _req['parsing_strategy'],
      index_strategy: _req['index_strategy'],
      storage_strategy: _req['storage_strategy'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /open_api/knowledge/document/delete */
  DeleteDocumentAPI(
    req?: document.DeleteDocumentRequest,
    options?: T,
  ): Promise<document.DeleteDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/open_api/knowledge/document/delete');
    const method = 'POST';
    const data = {
      document_ids: _req['document_ids'],
      document_ids_new: _req['document_ids_new'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /open_api/knowledge/document/list */
  ListDocumentOpenAPI(
    req: document.ListDocumentRequest,
    options?: T,
  ): Promise<document.ListDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL('/open_api/knowledge/document/list');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_ids: _req['document_ids'],
      page: _req['page'],
      size: _req['size'],
      keyword: _req['keyword'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/photo/extract_caption */
  ExtractPhotoCaption(
    req: document.ExtractPhotoCaptionRequest,
    options?: T,
  ): Promise<document.ExtractPhotoCaptionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/photo/extract_caption');
    const method = 'POST';
    const data = { document_id: _req['document_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/photo/list
   *
   * for 图片知识库 *
   */
  ListPhoto(
    req: document.ListPhotoRequest,
    options?: T,
  ): Promise<document.ListPhotoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/photo/list');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      page: _req['page'],
      size: _req['size'],
      filter: _req['filter'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/photo/caption */
  UpdatePhotoCaption(
    req: document.UpdatePhotoCaptionRequest,
    options?: T,
  ): Promise<document.UpdatePhotoCaptionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/photo/caption');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      caption: _req['caption'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/photo/detail */
  PhotoDetail(
    req: document.PhotoDetailRequest,
    options?: T,
  ): Promise<document.PhotoDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/photo/detail');
    const method = 'POST';
    const data = {
      document_ids: _req['document_ids'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/connector/file_tree_doc_list
   *
   * connector相关API *
   */
  GetFileTreeDocList(
    req: connector.GetFileTreeDocListRequest,
    options?: T,
  ): Promise<connector.GetFileTreeDocListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/connector/file_tree_doc_list');
    const method = 'POST';
    const data = {
      auth_id: _req['auth_id'],
      file_type_list: _req['file_type_list'],
      folder_id: _req['folder_id'],
      page_token: _req['page_token'],
      space_id: _req['space_id'],
      doc_source_type: _req['doc_source_type'],
      time_filter: _req['time_filter'],
      search_keywords: _req['search_keywords'],
      force_get_latest: _req['force_get_latest'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/connector/search_document */
  SearchDocument(
    req: connector.SearchDocumentRequest,
    options?: T,
  ): Promise<connector.SearchDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/connector/search_document');
    const method = 'POST';
    const data = {
      auth_id: _req['auth_id'],
      search_query: _req['search_query'],
      file_type_list: _req['file_type_list'],
      doc_source_type: _req['doc_source_type'],
      page_token: _req['page_token'],
      offset: _req['offset'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/document/refresh_document
   *
   * RefreshDocument 从 source 拉取最新的内容，重新分片，生成新的 document
   */
  RefreshDocument(
    req?: document.RefreshDocumentRequest,
    options?: T,
  ): Promise<document.RefreshDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/refresh_document');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_id: _req['document_id'],
      chunk_strategy: _req['chunk_strategy'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/batch_fetch */
  FetchWebUrl(
    req?: document.FetchWebUrlRequest,
    options?: T,
  ): Promise<document.FetchWebUrlResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/batch_fetch');
    const method = 'POST';
    const data = { document_ids: _req['document_ids'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/ref_bots */
  GetDatasetRefBots(
    req?: dataset.GetDatasetRefBotsRequest,
    options?: T,
  ): Promise<dataset.GetDatasetRefBotsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/ref_bots');
    const method = 'POST';
    const data = { dataset_id: _req['dataset_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/batch_update */
  BatchUpdateDocument(
    req?: document.BatchUpdateDocumentRequest,
    options?: T,
  ): Promise<document.BatchUpdateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/batch_update');
    const method = 'POST';
    const data = {
      document_ids: _req['document_ids'],
      update_rule: _req['update_rule'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/list_model */
  ListModel(
    req?: document.ListModelRequest,
    options?: T,
  ): Promise<document.ListModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/document/list_model');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/review/create
   *
   * 预分片相关 *
   */
  CreateDocumentReview(
    req?: review.CreateDocumentReviewRequest,
    options?: T,
  ): Promise<review.CreateDocumentReviewResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/review/create');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      reviews: _req['reviews'],
      chunk_strategy: _req['chunk_strategy'],
      parsing_strategy: _req['parsing_strategy'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/get_tree_chunk_rec
   *
   * 获取是否推荐层级分配方式，传入多个本地文件的存储tos
   */
  GetTreeChunkRec(
    req?: dataset.GetTreeChunkRecRequest,
    options?: T,
  ): Promise<dataset.GetTreeChunkRecResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/get_tree_chunk_rec');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      tos_uris: _req['tos_uris'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/review/save */
  SaveDocumentReview(
    req?: review.SaveDocumentReviewRequest,
    options?: T,
  ): Promise<review.SaveDocumentReviewResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/review/save');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      review_id: _req['review_id'],
      doc_tree_json: _req['doc_tree_json'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/review/mget */
  MGetDocumentReview(
    req?: review.MGetDocumentReviewRequest,
    options?: T,
  ): Promise<review.MGetDocumentReviewResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/review/mget');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      review_ids: _req['review_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v1/datasets */
  ListDatasetOpenAPI(
    req?: openapi.ListDatasetOpenApiRequest,
    options?: T,
  ): Promise<openapi.ListDatasetOpenApiResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/datasets');
    const method = 'GET';
    const params = {
      name: _req['name'],
      format_type: _req['format_type'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      space_id: _req['space_id'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v1/datasets
   *
   * 知识库 - OpenAPI *
   */
  CreateDatasetOpenAPI(
    req?: openapi.CreateDatasetOpenApiRequest,
    options?: T,
  ): Promise<openapi.CreateDatasetOpenApiResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/datasets');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      space_id: _req['space_id'],
      file_id: _req['file_id'],
      format_type: _req['format_type'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /v1/datasets/:dataset_id */
  DeleteDatasetOpenAPI(
    req?: openapi.DeleteDatasetOpenApiRequest,
    options?: T,
  ): Promise<openapi.DeleteDatasetOpenApiResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/datasets/${_req['dataset_id']}`);
    const method = 'DELETE';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** PUT /v1/datasets/:dataset_id */
  UpdateDatasetOpenAPI(
    req?: openapi.UpdateDatasetOpenApiRequest,
    options?: T,
  ): Promise<openapi.UpdateDatasetOpenApiResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/datasets/${_req['dataset_id']}`);
    const method = 'PUT';
    const data = {
      name: _req['name'],
      file_id: _req['file_id'],
      description: _req['description'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v1/datasets/:dataset_id/documents_v2
   *
   * 预留接口，create document支持form方式上传 *
   */
  CreatePhotoDocumentV2OpenAPI(
    req: openapi.CreateDocumentV2OpenAPIRequest,
    options?: T,
  ): Promise<document.CreateDocumentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v1/datasets/${_req['dataset_id']}/documents_v2`,
    );
    const method = 'POST';
    const data = { Data: _req['Data'], Base: _req['Base'] };
    const headers = { 'Content-Type': _req['Content-Type'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v1/datasets/:dataset_id/process */
  GetDocumentProgressOpenAPI(
    req?: openapi.GetDocumentProgressOpenApiRequest,
    options?: T,
  ): Promise<openapi.GetDocumentProgressOpenApiResponse> {
    const _req = req || {};
    const url = this.genBaseURL(`/v1/datasets/${_req['dataset_id']}/process`);
    const method = 'POST';
    const data = { document_ids: _req['document_ids'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /v1/datasets/:dataset_id/images/:document_id
   *
   * 图片 - OpenAPI *
   */
  UpdatePhotoCaptionOpenAPI(
    req: openapi.UpdatePhotoCaptionOpenApiRequest,
    options?: T,
  ): Promise<openapi.UpdatePhotoCaptionOpenApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v1/datasets/:dataset_id/images/${_req['document_id']}`,
    );
    const method = 'PUT';
    const data = { caption: _req['caption'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v1/datasets/:dataset_id/images */
  ListPhotoDocumentOpenAPI(
    req: openapi.ListPhotoOpenApiRequest,
    options?: T,
  ): Promise<openapi.ListPhotoOpenApiResponse> {
    const _req = req;
    const url = this.genBaseURL(`/v1/datasets/${_req['dataset_id']}/images`);
    const method = 'GET';
    const params = {
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      keyword: _req['keyword'],
      has_caption: _req['has_caption'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/knowledge/document/set_append_frequency
   *
   * 追加频率
   */
  SetAppendFrequency(
    req: document.SetAppendFrequencyRequest,
    options?: T,
  ): Promise<document.SetAppendFrequencyResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/document/set_append_frequency');
    const method = 'POST';
    const data = {
      auth_frequency_info: _req['auth_frequency_info'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/document/get_append_frequency */
  GetAppendFrequency(
    req: document.GetAppendFrequencyRequest,
    options?: T,
  ): Promise<document.GetAppendFrequencyResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/document/get_append_frequency');
    const method = 'POST';
    const data = { dataset_id: _req['dataset_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/opensearch/instances */
  GetInstances(
    req: opensearch.GetInstancesRequest,
    options?: T,
  ): Promise<opensearch.GetInstancesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/opensearch/instances');
    const method = 'POST';
    const data = { region: _req['region'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/opensearch/set_config */
  SetConfig(
    req: opensearch.SetConfigRequest,
    options?: T,
  ): Promise<opensearch.SetConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/opensearch/set_config');
    const method = 'POST';
    const data = {
      storage_config_id: _req['storage_config_id'],
      config: _req['config'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/opensearch/get_config */
  GetConfig(
    req: opensearch.GetConfigRequest,
    options?: T,
  ): Promise<opensearch.GetConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/opensearch/get_config');
    const method = 'POST';
    const data = {
      storage_config_id: _req['storage_config_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/opensearch/connection
   *
   * 火山云搜索相关接口
   */
  TestConnection(
    req: opensearch.TestConnectionRequest,
    options?: T,
  ): Promise<opensearch.TestConnectionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/opensearch/connection');
    const method = 'POST';
    const data = { config: _req['config'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/opensearch/open_public_address */
  OpenPublicAddress(
    req: opensearch.OpenPublicAddressRequest,
    options?: T,
  ): Promise<opensearch.OpenPublicAddressResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/knowledge/opensearch/open_public_address',
    );
    const method = 'POST';
    const data = { config: _req['config'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/user/benefit */
  GetUserKnowledgeBenefit(
    req?: dataset.KnowledgeBenefitCheckRequest,
    options?: T,
  ): Promise<dataset.KnowledgeBenefitCheckResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/knowledge/user/benefit');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      SpaceID: _req['SpaceID'],
      CozeAccountType: _req['CozeAccountType'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/volcano_dataset/dataset_list */
  GetVolcanoDatasetList(
    req: volcano_dataset.GetVolcanoDatasetListRequest,
    options?: T,
  ): Promise<volcano_dataset.GetVolcanoDatasetListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/volcano_dataset/dataset_list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      project_name: _req['project_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/volcano_dataset/batch_create */
  BatchCreateVolcanoDataset(
    req: volcano_dataset.BatchCreateVolcanoDatasetRequest,
    options?: T,
  ): Promise<volcano_dataset.BatchCreateVolcanoDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/volcano_dataset/batch_create');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      project_id: _req['project_id'],
      volcano_dataset_id_list: _req['volcano_dataset_id_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/knowledge/volcano_dataset/dataset_service_list */
  GetVolcanoDatasetServiceList(
    req?: volcano_dataset.GetVolcanoDatasetServiceListRequest,
    options?: T,
  ): Promise<volcano_dataset.GetVolcanoDatasetServiceListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/knowledge/volcano_dataset/dataset_service_list',
    );
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      volcano_dataset_service_ids: _req['volcano_dataset_service_ids'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/knowledge/volcano_dataset/project_list
   *
   * 火山知识库相关
   */
  GetVolcanoDatasetProjectList(
    req: volcano_dataset.GetVolcanoDatasetProjectListRequest,
    options?: T,
  ): Promise<volcano_dataset.GetVolcanoDatasetProjectListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/knowledge/volcano_dataset/project_list');
    const method = 'POST';
    const data = { space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
