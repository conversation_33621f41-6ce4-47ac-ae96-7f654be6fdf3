/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

/** Alarm parameters for MQ trigger */
export interface AlarmParameters {
  /** Lag alarm threshold for MQ trigger */
  lag_alarm_threshold?: number;
}

export interface CreateMqTriggerByTypeRequest {
  /** Batch size for message consumption */
  batch_size?: number;
  /** Batch flush duration in milliseconds */
  batch_flush_duration_milliseconds?: number;
  /** Description of the trigger */
  description?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Environment variables for the trigger */
  envs?: Record<string, string>;
  /** Function ID associated with the trigger */
  function_id?: string;
  /** Cell name for the trigger */
  cell?: string;
  /** Unique ID for the trigger */
  id?: string;
  /** Image version for the trigger */
  image_version?: string;
  /** SDK version for the trigger */
  sdk_version?: string;
  /** Image alias for the trigger */
  image_alias?: string;
  /** List of alarm IDs for monitoring */
  ms_alarm_id?: Array<string>;
  /** MQ type for the trigger, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** Maximum retries from function status */
  max_retries_from_function_status?: number;
  /** Message channel length */
  msg_chan_length?: number;
  /** Name of the trigger */
  name?: string;
  /** Whether auto sharding is needed */
  need_auto_sharding?: boolean;
  /** Number of MQ pods to one function pod */
  num_of_mq_pod_to_one_func_pod?: number;
  /** Trigger options */
  options?: common.TriggerOptions;
  /** QPS limit for the trigger */
  qps_limit?: number;
  /** Region for the trigger */
  region?: string;
  /** MQ region for the trigger */
  mq_region?: string;
  /** Whether to enable runtime agent mode */
  runtime_agent_mode?: boolean;
  /** Whether to enable dynamic worker thread */
  dynamic_worker_thread?: boolean;
  /** Maximum replica limits per key */
  replica_max_limit?: Record<string, number>;
  /** Minimum replica limits per key */
  replica_min_limit?: Record<string, number>;
  /** Number of replicas */
  replicas?: number;
  /** Resource configuration for the trigger */
  resource?: common.Resource;
  /** Whether scaling is enabled */
  scale_enabled?: boolean;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled?: boolean;
  /** Whether static membership is enabled */
  enable_static_membership?: boolean;
  /** Number of workers per pod */
  workers_per_pod?: number;
  /** Alarm parameters for the trigger */
  alarm_params?: AlarmParameters;
  /** Request timeout in milliseconds */
  request_timeout?: number;
  /** Whether to disable infinite retry for timeout, true means disable */
  disable_infinite_retry_for_timeout?: boolean;
  /** Initial offset start position */
  initial_offset_start_from?: string;
  /** Whether to enable MQ debug */
  enable_mq_debug?: boolean;
  /** MQ logger limit size */
  mq_logger_limit_size?: number;
  /** Whether to enable backoff */
  enable_backoff?: boolean;
  /** Whether to disable backoff, true means disable */
  disable_backoff?: boolean;
  /** Number of worker v2 per half core */
  worker_v2_num_per_half_core?: number;
  /** Whether to enable concurrency filter */
  enable_concurrency_filter?: boolean;
  /** Whether to enable IPC mode */
  enable_ipc_mode?: boolean;
  /** Whether to enable traffic priority scheduling */
  enable_traffic_priority_scheduling?: boolean;
  /** Whether to enable pod colocate scheduling */
  enable_pod_colocate_scheduling?: boolean;
  /** Whether to enable global rate limiter */
  enable_global_rate_limiter?: boolean;
  /** Whether to enable congestion control */
  enable_congestion_control?: boolean;
  /** Whether to allow bytesuite debug */
  allow_bytesuite_debug?: boolean;
  /** Whether to enable dynamic load balance */
  enable_dynamic_load_balance?: boolean;
  /** Whether to disable smooth WRR, true means disable */
  disable_smooth_wrr?: boolean;
  /** Type of dynamic load balance, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Whether replica must meet partition */
  replica_force_meet_partition?: boolean;
  /** Scale settings for MQ event */
  scale_settings?: common.MQEventScaleSettings;
  /** Whether to enable hot reload */
  hot_reload?: boolean;
  /** MQ message type, enums: native (to be deprecated), faas_native, cloudEvent_0_2 */
  mq_msg_type?: string;
  /** Status of the trigger, enums: changing, pending, ready, failed, resetting, reset_failed */
  status?: string;
  /** Whether the trigger is in releasing state */
  in_releasing?: boolean;
  /** Mirror region filter */
  mirror_region_filter?: string;
  /** Whether to enable GC tuner */
  enable_gctuner?: boolean;
  /** GC tuner percent */
  gctuner_percent?: number;
  /** Retry strategy for the trigger, enums: fixed_interval, exponential */
  retry_strategy?: string;
  /** Maximum retry time */
  max_retry_time?: number;
  /** QPS limit time ranges */
  qps_limit_time_ranges?: Array<common.QPSLimitTimeRanges>;
  /** Limit for disaster scenario */
  limit_disaster_scenario?: number;
  /** Whether to enable step rate limit */
  enable_step_rate_limit?: boolean;
  /** Rate limit step settings */
  rate_limit_step_settings?: common.RateLimitStepSettings;
  /** Maximum dwell time in minutes */
  max_dwell_time_minute?: number;
  /** QPS auto limit settings */
  qps_auto_limit?: common.ConsumeMigrateAutoLimit;
  /** Plugin function parameters */
  plugin_function_param?: common.PluginFunctionParam;
  /** Whether to enable plugin function */
  enable_plugin_function?: boolean;
  /** Whether to enable canary update */
  enable_canary_update?: boolean;
  /** Traffic configuration */
  traffic_config?: Record<string, number>;
  /** Whether authentication info is updated */
  is_auth_info_updated?: boolean;
  /** Pod type for the trigger */
  pod_type?: string;
  /** Package name for the trigger */
  package?: string;
  /** Whether to enable filter congestion control */
  enable_filter_congestion_control?: boolean;
  /** Whether to enable congestion control cache */
  enable_congestion_control_cache?: boolean;
  /** Image version number for the trigger */
  host_uniq?: common.HostUniq;
  /** Whether the trigger is in cell migration */
  in_cell_migration?: boolean;
  /** Pipeline parameters for the trigger */
  pipeline_params?: common.PipelineParams;
  /** Caller identifier in query */
  caller?: string;
  /** Service ID in the request path */
  service_id: string;
  /** Cluster in the request path */
  cluster: string;
  /** Trigger type in the request path，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface CreateMqTriggerByTypeResponse {
  /** Response code */
  code?: number;
  /** Response data for created MQ trigger by type */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface CreateMQTriggerResponse {
  /** Response code */
  code?: number;
  /** Response data for created MQ trigger */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface DeleteMqTriggerByTypeRequest {
  /** Cluster name of the service */
  cluster: string;
  /** Region name of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Trigger ID to delete */
  trigger_id: string;
  /** Trigger type to delete，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Caller identifier in query */
  caller?: string;
  /** Consumer group name in query */
  consumer_group?: string;
  /** Event bus name in query */
  eventbus_name?: string;
  /** Pipeline template ID for the operation */
  pipeline_template_id?: string;
  /** Pipeline template type for the operation */
  pipeline_template_type?: string;
  /** User who approved the operation */
  approved_by?: string;
  /** User type of the approver. Enums: "person_account","service_account" */
  approved_by_usertype?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface DeleteMqTriggerByTypeResponse {
  /** Response code */
  code?: number;
  /** Response data for deleted MQ trigger by type */
  data?: common.EmptyObject;
  /** Error message, if any */
  error?: string;
}

export interface GetMQeventAdvancedConfigRequest {
  /** FaaS cluster region in query */
  region?: string;
}

export interface GetMQeventAdvancedConfigResponse {
  /** Response code */
  code?: number;
  /** Error message, if any */
  error?: string;
  /** List of advanced config data for MQ event */
  data?: Array<MQEventAdvancedConfigData>;
}

export interface GetMqTriggerByTypeRequest {
  /** Cluster name of the service */
  cluster: string;
  /** Region name of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Trigger ID to get */
  trigger_id: string;
  /** Trigger type to get，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetMqTriggerByTypeResponse {
  /** Response code */
  code?: number;
  /** MQ trigger response data for the specified type */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface GetMQTriggerRequest {
  /** Cluster name of the service */
  cluster: string;
  /** Filter for plugin function enabled triggers */
  enable_plugin_function?: string;
  /** Region name of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Filter by plugin function version */
  plugin_function_version?: string;
}

export interface GetMQTriggerResponse {
  /** Response code */
  code?: number;
  /** List of MQ trigger response data */
  data?: Array<common.GlobalMQEventTriggerResponseData>;
  /** Error message, if any */
  error?: string;
}

export interface GetMQTriggersListWithPaginationRequest {
  /** Cluster name in query */
  cluster?: string;
  /** Limit per page */
  limit?: number;
  /** Offset for pagination */
  offset?: number;
  /** Region name in query */
  region?: string;
  /** Fuzzy search in cluster id and name */
  search?: string;
  /** Service ID in path */
  service_id: string;
}

export interface GetMQTriggersListWithPaginationResponse {
  /** Response code */
  code?: number;
  /** List of MQ trigger response data with pagination */
  data?: Array<common.GlobalMQEventTriggerResponseData>;
  /** Error message, if any */
  error?: string;
}

/** Canary update parameters for MQ trigger */
export interface MQCanaryUpdateParams {
  /** Whether canary update is enabled */
  is_canary_update?: boolean;
  /** Grey percentage for canary update */
  grey_percentage?: number;
}

/** Advanced configuration data for MQ event */
export interface MQEventAdvancedConfigData {
  /** 中文名称
Name in Chinese */
  name_zh: string;
  /** Name in English */
  name_en: string;
  /** 中文描述
Description in Chinese */
  des_zh: string;
  /** Description in English */
  des_en: string;
  /** Field name */
  field: string;
  /** Value type */
  value_type: string;
  /** Expected value map */
  expect_value: Record<string, string>;
  /** Show condition */
  show_condition: string;
}

export interface MQTriggerRestartRequest {
  /** Service ID */
  service_id: string;
  /** Region name of the service */
  region: string;
  /** Cluster name of the service */
  cluster: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Trigger ID */
  trigger_id: string;
  /** Maximum surge percent for restart */
  max_surge_percent?: number;
}

export interface MQTriggerRestartResponse {
  /** Response code */
  code?: number;
  /** Restarted MQ trigger response data */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface PatchMqEventReplicaLimitRequest {
  /** Service ID */
  service_id: string;
  /** Region name of the service (also in body) */
  region: string;
  /** Cluster name of the service */
  cluster: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Trigger ID */
  trigger_id: string;
  /** Maximum replica limits per key */
  replica_max_limit: Record<string, number>;
  /** Minimum replica limits per key */
  replica_min_limit: Record<string, number>;
}

export interface PatchMqEventReplicaLimitResponse {
  /** Response code */
  code?: number;
  /** Patched MQ event replica limit response data */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface PatchMqTriggerByTypeRequest {
  /** Alarm parameters for the trigger */
  alarm_params?: PatchMqTriggerByTypeRequestAlarmParamsMessage2;
  /** Whether to allow bytesuite debug */
  allow_bytesuite_debug?: boolean;
  /** Batch size for message consumption */
  batch_size?: number;
  /** Cell name for the trigger */
  cell?: string;
  /** Cluster name of the service */
  cluster: string;
  /** Whether deployment is inactive */
  deployment_inactive?: boolean;
  /** Description of the trigger */
  description?: string;
  /** Whether to disable backoff, true means disable */
  disable_backoff?: boolean;
  /** Whether to disable smooth WRR, true means disable */
  disable_smooth_wrr?: boolean;
  /** Type of dynamic load balance, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Whether to enable dynamic worker thread */
  dynamic_worker_thread?: boolean;
  /** Whether to enable backoff */
  enable_backoff?: boolean;
  /** Whether to enable concurrency filter */
  enable_concurrency_filter?: boolean;
  /** Whether to enable congestion control */
  enable_congestion_control?: boolean;
  /** Whether to enable dynamic load balance */
  enable_dynamic_load_balance?: boolean;
  /** Whether to enable global rate limiter */
  enable_global_rate_limiter?: boolean;
  /** Whether to enable IPC mode */
  enable_ipc_mode?: boolean;
  /** Whether to enable MQ debug */
  enable_mq_debug?: boolean;
  /** Whether to enable pod colocate scheduling */
  enable_pod_colocate_scheduling?: boolean;
  /** Whether to enable static membership */
  enable_static_membership?: boolean;
  /** Whether to enable traffic priority scheduling */
  enable_traffic_priority_scheduling?: boolean;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Environment variables for the trigger */
  envs?: Record<string, string>;
  /** Function ID associated with the trigger */
  function_id?: string;
  /** Whether to enable hot reload */
  hot_reload?: boolean;
  /** Unique ID for the trigger */
  id?: string;
  /** Image alias for the trigger */
  image_alias?: string;
  /** Image version for the trigger */
  image_version?: string;
  /** Initial offset start position */
  initial_offset_start_from?: string;
  /** Whether authentication info is updated */
  is_auth_info_updated?: boolean;
  /** Maximum retries from function status */
  max_retries_from_function_status?: number;
  /** MQ logger limit size */
  mq_logger_limit_size?: number;
  /** MQ message type, enums: native (to be deprecated), faas_native, cloudEvent_0_2 */
  mq_msg_type?: string;
  /** MQ region for the trigger */
  mq_region?: string;
  /** MQ type for the trigger, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** List of alarm IDs for monitoring */
  ms_alarm_id?: Array<string>;
  /** Message channel length */
  msg_chan_length?: number;
  /** Name of the trigger */
  name?: string;
  /** Whether auto sharding is needed */
  need_auto_sharding?: boolean;
  /** Number of MQ pods to one function pod */
  num_of_mq_pod_to_one_func_pod?: number;
  /** Trigger options */
  options?: common.TriggerOptions;
  /** Plugin function parameters */
  plugin_function_param?: common.PluginFunctionParam;
  /** QPS limit for the trigger */
  qps_limit?: number;
  /** Region name of the service */
  region: string;
  /** Maximum replica limit */
  replica_max_limit?: number;
  /** Minimum replica limit */
  replica_min_limit?: number;
  /** Number of replicas */
  replicas?: number;
  /** Request timeout in milliseconds */
  request_timeout?: number;
  /** Resource limit for the trigger */
  resource?: common.ResourceLimit;
  /** Whether to enable runtime agent mode */
  runtime_agent_mode?: boolean;
  /** Whether scaling is enabled */
  scale_enabled?: boolean;
  /** Scale settings for MQ event */
  scale_settings?: common.MQEventScaleSettings;
  /** SDK version for the trigger */
  sdk_version?: string;
  /** Service ID */
  service_id: string;
  /** Trigger ID */
  trigger_id: string;
  /** trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled?: boolean;
  /** Number of worker v2 per half core */
  worker_v2_num_per_half_core?: number;
  /** Number of workers per pod */
  workers_per_pod?: number;
  /** Whether to enable plugin function */
  enable_plugin_function?: boolean;
  /** Whether to disable infinite retry for timeout, true means disable */
  disable_infinite_retry_for_timeout?: boolean;
  /** Mirror region filter */
  mirror_region_filter?: string;
  /** Whether to enable GC tuner */
  enable_gctuner?: boolean;
  /** GC tuner percent */
  gctuner_percent?: number;
  /** Retry strategy for the trigger, enums: fixed_interval, exponential */
  retry_strategy?: string;
  /** Maximum retry time */
  max_retry_time?: number;
  /** QPS limit time ranges */
  qps_limit_time_ranges?: Array<common.QPSLimitTimeRanges>;
  /** Rate limit step settings */
  rate_limit_step_settings?: common.RateLimitStepSettings;
  /** Whether to enable step rate limit */
  enable_step_rate_limit?: boolean;
  /** Whether to enable filter congestion control */
  enable_filter_congestion_control?: boolean;
  /** Whether to enable congestion control cache */
  enable_congestion_control_cache?: boolean;
}

/** Alarm parameters for patching an MQ trigger */
export interface PatchMqTriggerByTypeRequestAlarmParamsMessage2 {
  /** Lag alarm threshold for MQ trigger */
  lag_alarm_threshold?: number;
}

export interface PatchMqTriggerByTypeResponse {
  /** Response code */
  code?: number;
  /** Patched MQ trigger response data */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface PatchMqTriggerRestrictedMetaByTypeRequest {
  /** Service ID */
  service_id: string;
  /** Trigger ID */
  trigger_id: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Target cluster name for migration */
  cluster?: string;
  /** Region name of the function */
  region: string;
}

export interface SyncMqTriggerDataRequest {
  /** Cluster name of the service */
  cluster: string;
  /** Region name of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Trigger ID */
  trigger_id: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
}

export interface SyncMqTriggerDataResponse {
  /** Response code */
  code?: number;
  /** Synced MQ trigger response data */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface UpdateMqTriggerByTypeRequest {
  /** Batch size for message consumption */
  batch_size?: number;
  /** Batch flush duration in milliseconds */
  batch_flush_duration_milliseconds?: number;
  /** Description of the trigger */
  description?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Environment variables for the trigger */
  envs?: Record<string, string>;
  /** Function ID associated with the trigger */
  function_id?: string;
  /** Cell name for the trigger */
  cell?: string;
  /** Unique ID for the trigger */
  id?: string;
  /** Image version for the trigger */
  image_version?: string;
  /** SDK version for the trigger */
  sdk_version?: string;
  /** Image alias for the trigger */
  image_alias?: string;
  /** List of alarm IDs for monitoring */
  ms_alarm_id?: Array<string>;
  /** MQ type for the trigger, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** Maximum retries from function status */
  max_retries_from_function_status?: number;
  /** Message channel length */
  msg_chan_length?: number;
  /** Name of the trigger */
  name?: string;
  /** Whether auto sharding is needed */
  need_auto_sharding?: boolean;
  /** Number of MQ pods to one function pod */
  num_of_mq_pod_to_one_func_pod?: number;
  /** Trigger options */
  options?: common.TriggerOptions;
  /** QPS limit for the trigger */
  qps_limit?: number;
  /** Region for the trigger */
  region?: string;
  /** MQ region for the trigger */
  mq_region?: string;
  /** Whether to enable runtime agent mode */
  runtime_agent_mode?: boolean;
  /** Whether to enable dynamic worker thread */
  dynamic_worker_thread?: boolean;
  /** Maximum replica limits per key */
  replica_max_limit?: Record<string, number>;
  /** Minimum replica limits per key */
  replica_min_limit?: Record<string, number>;
  /** Number of replicas */
  replicas?: number;
  /** Resource configuration for the trigger */
  resource?: common.Resource;
  /** Whether scaling is enabled */
  scale_enabled?: boolean;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled?: boolean;
  /** Whether to enable static membership */
  enable_static_membership?: boolean;
  /** Number of workers per pod */
  workers_per_pod?: number;
  /** Alarm parameters for the trigger */
  alarm_params?: AlarmParameters;
  /** Request timeout in milliseconds */
  request_timeout?: number;
  /** Whether to disable infinite retry for timeout, true means disable */
  disable_infinite_retry_for_timeout?: boolean;
  /** Initial offset start position */
  initial_offset_start_from?: string;
  /** Whether to enable MQ debug mode */
  enable_mq_debug?: boolean;
  /** MQ logger limit size */
  mq_logger_limit_size?: number;
  /** Whether to enable backoff */
  enable_backoff?: boolean;
  /** Whether to disable backoff, true means disable */
  disable_backoff?: boolean;
  /** Number of worker v2 per half core */
  worker_v2_num_per_half_core?: number;
  /** Whether to enable concurrency filter */
  enable_concurrency_filter?: boolean;
  /** Whether to enable IPC mode */
  enable_ipc_mode?: boolean;
  /** Whether to enable traffic priority scheduling */
  enable_traffic_priority_scheduling?: boolean;
  /** Whether to enable pod colocate scheduling */
  enable_pod_colocate_scheduling?: boolean;
  /** Whether to enable global rate limiter */
  enable_global_rate_limiter?: boolean;
  /** Whether to enable congestion control */
  enable_congestion_control?: boolean;
  /** Whether to allow bytesuite debug mode */
  allow_bytesuite_debug?: boolean;
  /** Whether to enable dynamic load balancing */
  enable_dynamic_load_balance?: boolean;
  /** Whether to disable smooth weighted round robin, true means disable */
  disable_smooth_wrr?: boolean;
  /** Type of dynamic load balancing, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Whether replica must meet partition requirement */
  replica_force_meet_partition?: boolean;
  /** Scale settings for MQ event */
  scale_settings?: common.MQEventScaleSettings;
  /** Whether to enable hot reload */
  hot_reload?: boolean;
  /** MQ message type, enums: native (to be deprecated), faas_native, cloudEvent_0_2 */
  mq_msg_type?: string;
  /** Status of the trigger, enums: changing, pending, ready, failed, resetting, reset_failed */
  status?: string;
  /** Whether the trigger is in releasing state */
  in_releasing?: boolean;
  /** Mirror region filter */
  mirror_region_filter?: string;
  /** Whether to enable GC tuner */
  enable_gctuner?: boolean;
  /** GC tuner percent */
  gctuner_percent?: number;
  /** Retry strategy for the trigger, enums: fixed_interval, exponential */
  retry_strategy?: string;
  /** Maximum retry time */
  max_retry_time?: number;
  /** QPS limit time ranges */
  qps_limit_time_ranges?: Array<common.QPSLimitTimeRanges>;
  /** Limit for disaster scenario */
  limit_disaster_scenario?: number;
  /** Whether to enable step rate limit */
  enable_step_rate_limit?: boolean;
  /** Rate limit step settings */
  rate_limit_step_settings?: common.RateLimitStepSettings;
  /** Maximum dwell time in minutes */
  max_dwell_time_minute?: number;
  /** QPS auto limit settings for consumption migration */
  qps_auto_limit?: common.ConsumeMigrateAutoLimit;
  /** Plugin function parameters */
  plugin_function_param?: common.PluginFunctionParam;
  /** Whether to enable plugin function */
  enable_plugin_function?: boolean;
  /** Whether to enable canary update */
  enable_canary_update?: boolean;
  /** Traffic configuration mapping */
  traffic_config?: Record<string, number>;
  /** Whether authentication info is updated */
  is_auth_info_updated?: boolean;
  /** Pod type for the trigger */
  pod_type?: string;
  /** Package name for the trigger */
  package?: string;
  /** Whether to enable filter congestion control */
  enable_filter_congestion_control?: boolean;
  /** Whether to enable congestion control cache */
  enable_congestion_control_cache?: boolean;
  /** Image version number for the trigger */
  image_version_number?: string;
  /** Host unique identifier */
  host_uniq?: common.HostUniq;
  /** Whether the trigger is in cell migration */
  in_cell_migration?: boolean;
  /** Canary update parameters for the MQ trigger */
  mq_canary_update_params?: MQCanaryUpdateParams;
  /** Pipeline parameters for the trigger */
  pipeline_params?: common.PipelineParams;
  /** Service ID in path */
  service_id: string;
  /** Cluster name of the service */
  cluster: string;
  /** Trigger ID */
  trigger_id: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Whether to skip image upgrade */
  skip_image_upgrade?: string;
  /** Caller identifier */
  caller?: string;
  /** Whether to skip alarm updates */
  not_update_alarm?: string;
  /** Whether migration was triggered by CLI */
  migrated_by_cli?: string;
  /** Check parameter for validation */
  check?: string;
  /** Force update flag for MQ event */
  'X-Bytefaas-Mqevent-Force-Update'?: string;
  /** Confirmation parameter */
  confirm?: string;
  /** Flag to update MQ image */
  'X-ByteFaas-Update-MQ-Image'?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface UpdateMqTriggerByTypeResponse {
  /** Response code */
  code?: number;
  /** Updated MQ trigger response data by type */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}

export interface UpdateMQTriggerRequest {
  /** Alarm parameters for the MQ trigger */
  alarm_params?: UpdateMQTriggerRequestAlarmParamsMessage2;
  /** Whether to allow bytesuite debug mode */
  allow_bytesuite_debug?: boolean;
  /** Batch size for message consumption */
  batch_size?: number;
  /** Cell name for the trigger */
  cell?: string;
  /** Cluster name of the service */
  cluster: string;
  /** Whether the deployment is inactive */
  deployment_inactive?: boolean;
  /** Description of the trigger */
  description?: string;
  /** Whether to disable backoff, true means disable */
  disable_backoff?: boolean;
  /** Whether to disable smooth weighted round robin, true means disable */
  disable_smooth_wrr?: boolean;
  /** Type of dynamic load balancing, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Whether to enable dynamic worker thread */
  dynamic_worker_thread?: boolean;
  /** Whether to enable backoff */
  enable_backoff?: boolean;
  /** Whether to enable concurrency filter */
  enable_concurrency_filter?: boolean;
  /** Whether to enable congestion control */
  enable_congestion_control?: boolean;
  /** Whether to enable dynamic load balancing */
  enable_dynamic_load_balance?: boolean;
  /** Whether to enable global rate limiter */
  enable_global_rate_limiter?: boolean;
  /** Whether to enable IPC mode */
  enable_ipc_mode?: boolean;
  /** Whether to enable MQ debug mode */
  enable_mq_debug?: boolean;
  /** Whether to enable pod colocate scheduling */
  enable_pod_colocate_scheduling?: boolean;
  /** Whether to enable static membership */
  enable_static_membership?: boolean;
  /** Whether to enable traffic priority scheduling */
  enable_traffic_priority_scheduling?: boolean;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Environment variables for the trigger */
  envs?: Record<string, string>;
  /** Function ID associated with the trigger */
  function_id?: string;
  /** Whether to enable hot reload */
  hot_reload?: boolean;
  /** Unique ID for the trigger */
  id?: string;
  /** Image alias for the trigger */
  image_alias?: string;
  /** Image version for the trigger */
  image_version?: string;
  /** Initial offset start position */
  initial_offset_start_from?: string;
  /** Whether authentication info is updated */
  is_auth_info_updated?: boolean;
  /** Maximum retries from function status */
  max_retries_from_function_status?: number;
  /** MQ logger limit size */
  mq_logger_limit_size?: number;
  /** MQ message type, enums: native (to be deprecated), faas_native, cloudEvent_0_2 */
  mq_msg_type?: string;
  /** MQ region for the trigger */
  mq_region?: string;
  /** MQ type for the trigger, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** List of alarm IDs for monitoring */
  ms_alarm_id?: Array<string>;
  /** Message channel length */
  msg_chan_length?: number;
  /** Name of the trigger */
  name?: string;
  /** Whether auto sharding is needed */
  need_auto_sharding?: boolean;
  /** Number of MQ pods to one function pod */
  num_of_mq_pod_to_one_func_pod?: number;
  /** Trigger options */
  options?: common.TriggerOptions;
  /** Plugin function parameters */
  plugin_function_param?: common.PluginFunctionParam;
  /** QPS limit for the trigger */
  qps_limit?: number;
  /** Region name of the service */
  region: string;
  /** Maximum replica limit */
  replica_max_limit?: number;
  /** Minimum replica limit */
  replica_min_limit?: number;
  /** Number of replicas */
  replicas?: number;
  /** Request timeout in milliseconds */
  request_timeout?: number;
  /** Resource limit for the trigger */
  resource?: common.ResourceLimit;
  /** Whether to enable runtime agent mode */
  runtime_agent_mode?: boolean;
  /** Whether scaling is enabled */
  scale_enabled?: boolean;
  /** Scale settings for MQ event */
  scale_settings?: common.MQEventScaleSettings;
  /** SDK version for the trigger */
  sdk_version?: string;
  /** Service ID */
  service_id: string;
  /** MQ trigger ID */
  trigger_id: string;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled?: boolean;
  /** Number of worker v2 per half core */
  worker_v2_num_per_half_core?: number;
  /** Number of workers per pod */
  workers_per_pod?: number;
  /** Whether to enable plugin function */
  enable_plugin_function?: boolean;
  /** Whether to disable infinite retry for timeout, true means disable */
  disable_infinite_retry_for_timeout?: boolean;
  /** Mirror region filter */
  mirror_region_filter?: string;
  /** Whether to enable GC tuner */
  enable_gctuner?: boolean;
  /** GC tuner percent */
  gctuner_percent?: number;
  /** Retry strategy for the trigger, enums: fixed_interval, exponential */
  retry_strategy?: string;
  /** Maximum retry time */
  max_retry_time?: number;
  /** QPS limit time ranges */
  qps_limit_time_ranges?: Array<common.QPSLimitTimeRanges>;
  /** Rate limit step settings */
  rate_limit_step_settings?: common.RateLimitStepSettings;
  /** Whether to enable step rate limit */
  enable_step_rate_limit?: boolean;
  /** Whether to enable filter congestion control */
  enable_filter_congestion_control?: boolean;
  /** Whether to enable congestion control cache */
  enable_congestion_control_cache?: boolean;
  /** Image version number for the trigger */
  image_version_number?: string;
  /** Host unique identifier */
  host_uniq?: common.HostUniq;
  /** Whether the trigger is in cell migration */
  in_cell_migration?: boolean;
  /** Canary update parameters for the MQ trigger */
  mq_canary_update_params?: MQCanaryUpdateParams;
  /** Pipeline parameters for the trigger */
  pipeline_params?: common.PipelineParams;
}

/** Alarm parameters for updating MQ trigger */
export interface UpdateMQTriggerRequestAlarmParamsMessage2 {
  /** Lag alarm threshold for MQ trigger */
  lag_alarm_threshold?: number;
}

export interface UpdateMQTriggerResponse {
  /** Response code */
  code?: number;
  /** Updated MQ trigger response data */
  data?: common.GlobalMQEventTriggerResponseData;
  /** Error message, if any */
  error?: string;
}
/* eslint-enable */
