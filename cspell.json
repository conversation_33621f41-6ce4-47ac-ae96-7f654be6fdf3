{"version": "0.2", "ignorePaths": [], "dictionaryDefinitions": [], "dictionaries": [], "caseSensitive": false, "words": ["8xlarge", "ahooks", "autoinstaller", "BLOCKWISE", "btns", "byted", "Bytedance", "BYTEGATE", "chatflow", "cozeloop", "Datas", "Datasource", "ddim", "<PERSON><PERSON><PERSON><PERSON>", "easings", "edenx", "endregion", "faas", "feishu", "Filebox", "gfdatav", "gulux", "hglt", "hocs", "idllint", "idllintrc", "imageflow", "immer", "inhouse", "inversify", "Metas", "mkpl", "<PERSON><PERSON><PERSON>", "Nclc", "oceanos", "pascalize", "Plasmo", "pnpmfile", "Popconfirm", "<PERSON><PERSON><PERSON>", "RESEGMENT", "reslint", "rsbuild", "rspack", "rushstack", "rushx", "Seperator", "<PERSON><PERSON>", "s<PERSON>ar", "stylelint", "tailwindcss", "tanstack", "teamspace", "testid", "testset", "uikit", "Vmok", "webp", "workflowstore", "zoomin", "zoomout", "zustand"], "ignoreWords": [], "import": []}