id: 3002
name: DeepSeek-V3 (SiliconFlow)
icon_uri: default_icon/deepseek.png
icon_url: ""
description:
    zh: 通过硅基流动平台提供的DeepSeek-V3模型，具有强大的推理能力和代码生成能力，支持长上下文处理。
    en: DeepSeek-V3 model provided through SiliconFlow platform, featuring powerful reasoning and code generation capabilities with long context support.
default_parameters:
    - name: temperature
      label:
        zh: 生成随机性
        en: Temperature
      desc:
        zh: '- **temperature**: 调高温度会使得模型的输出更多样性和创新性，反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与"Top p"同时调整。'
        en: '**Temperature**:\n\n- When you increase this value, the model outputs more diverse and innovative content; when you decrease it, the model outputs less diverse content that strictly follows the given instructions.\n- It is recommended not to adjust this value with "Top p" at the same time.'
      type: float
      min: "0"
      max: "2"
      default_val:
        balance: "0.7"
        creative: "1.0"
        default_val: "0.7"
        precise: "0.1"
      precision: 2
      options: []
      style:
        widget: slider
        label:
            zh: 生成多样性
            en: Generation diversity
    - name: max_tokens
      label:
        zh: 最大回复长度
        en: Response max length
      desc:
        zh: 控制模型输出的Tokens 长度上限。DeepSeek-V3支持最大8192个输出tokens。
        en: Controls the maximum length of tokens output by the model. DeepSeek-V3 supports up to 8192 output tokens.
      type: int
      min: "1"
      max: "8192"
      default_val:
        default_val: "4096"
      options: []
      style:
        widget: slider
        label:
            zh: 输入及输出设置
            en: Input and output settings
    - name: top_p
      label:
        zh: Top P
        en: Top P
      desc:
        zh: '- **Top p 为累计概率**: 模型在生成输出时会从概率最高的词汇开始选择，直到这些词汇的总概率累积达到Top p 值。这样可以限制模型只选择这些高概率的词汇，从而控制输出内容的多样性。建议不要与"生成随机性"同时调整。'
        en: '**Top P**:\n\n- An alternative to sampling with temperature, where only tokens within the top p probability mass are considered. For example, 0.1 means only the top 10% probability mass tokens are considered.\n- We recommend altering this or temperature, but not both.'
      type: float
      min: "0"
      max: "1"
      default_val:
        default_val: "0.9"
      precision: 2
      options: []
      style:
        widget: slider
        label:
            zh: 生成多样性
            en: Generation diversity
    - name: response_format
      label:
        zh: 输出格式
        en: Response format
      desc:
        zh: '- **文本**: 使用普通文本格式回复\n- **JSON**: 将引导模型使用JSON格式输出'
        en: '**Response Format**:\n\n- **Text**: Replies in plain text format\n- **JSON**: Uses JSON format for replies'
      type: int
      min: ""
      max: ""
      default_val:
        default_val: "0"
      options:
        - label: Text
          value: "0"
        - label: JSON
          value: "1"
      style:
        widget: radio_buttons
        label:
            zh: 输入及输出设置
            en: Input and output settings
meta:
    name: deepseek-v3
    protocol: openai
    capability:
        function_call: true
        input_modal:
            - text
        input_tokens: 65536
        json_mode: true
        max_tokens: 8192
        output_modal:
            - text
        output_tokens: 8192
        prefix_caching: false
        reasoning: true
        prefill_response: false
    conn_config:
        base_url: "https://api.siliconflow.cn/v1"
        api_key: ""
        timeout: 120s
        model: "deepseek-ai/DeepSeek-V3"
        temperature: 0.7
        frequency_penalty: 0
        presence_penalty: 0
        max_tokens: 4096
        top_p: 0.9
        top_k: 0
        stop: []
        openai:
            by_azure: false
            api_version: ""
            response_format:
                type: text
                jsonschema: null
        claude: null
        ark: null
        deepseek: null
        qwen: null
        gemini: null
        custom: {}
    status: 0
