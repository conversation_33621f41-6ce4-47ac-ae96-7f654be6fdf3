/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as resource_resource_common from './resource_resource_common';

export type Int64 = string | number;

export interface LibraryResourceListRequest {
  /** 是否由当前用户创建，0-不筛选，1-当前用户 */
  user_filter?: number;
  /** [4,1]   0代表不筛选 */
  res_type_filter?: Array<number>;
  /** 名称 */
  name?: string;
  /** 发布状态，0-不筛选，1-未发布，2-已发布 */
  publish_status_filter?: number;
  /** 用户所在空间ID */
  space_id: string;
  /** 6 : optional i32 page, // 页数，首页是1。默认1。
一次读取的数据条数，默认10，最大100. */
  size?: number;
  /** 8 : optional i32 offset, // 数据记录偏移，含义是从第(offset+1)条记录开始读
游标，用于分页，默认0，第一次请求可以不传，后续请求需要带上上次返回的cursor */
  cursor?: string;
  /** 用来指定自定义搜索的字段 不填默认只name匹配，eg []string{name,自定} 匹配name和自定义字段full_text */
  search_keys?: Array<string>;
  /** 当res_type_filter为[2 workflow]时，是否需要返回图片流 */
  is_get_imageflow?: boolean;
  Base?: base.Base;
}

export interface LibraryResourceListResponse {
  code?: Int64;
  msg?: string;
  resource_list?: Array<resource_resource_common.ResourceInfo>;
  /** 4  : i32 total,
游标，用于下次请求的cursor */
  cursor?: string;
  /** 是否还有数据待拉取 */
  has_more?: boolean;
  BaseResp: base.BaseResp;
}

export interface ProjectResourceListRequest {
  /** 项目ID */
  project_id: string;
  /** 用户所在space id */
  space_id?: string;
  /** 指定获取某个版本的project的资源 */
  project_version?: string;
  Base?: base.Base;
}

export interface ProjectResourceListResponse {
  code?: Int64;
  msg?: string;
  resource_groups?: Array<resource_resource_common.ProjectResourceGroup>;
  BaseResp: base.BaseResp;
}

export interface ResourceCopyCancelRequest {
  /** 复制任务id, 用于查询任务状态或取消、重试任务 */
  task_id?: string;
  Base?: base.Base;
}

export interface ResourceCopyCancelResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface ResourceCopyDetailRequest {
  /** 复制任务id, 用于查询任务状态或取消、重试任务 */
  task_id?: string;
  Base?: base.Base;
}

export interface ResourceCopyDetailResponse {
  code?: Int64;
  msg?: string;
  task_detail?: resource_resource_common.ResourceCopyTaskDetail;
  BaseResp: base.BaseResp;
}

export interface ResourceCopyDispatchRequest {
  /** 场景，只支持单资源的操作 */
  scene?: resource_resource_common.ResourceCopyScene;
  /** 被用户选择复制/移动的资源ID */
  res_id?: string;
  res_type?: resource_resource_common.ResType;
  /** 所在项目ID */
  project_id?: string;
  res_name?: string;
  /** 跨空间复制的目标space id */
  target_space_id?: string;
  Base?: base.Base;
}

export interface ResourceCopyDispatchResponse {
  code?: Int64;
  msg?: string;
  /** 复制任务id, 用于查询任务状态或取消、重试任务 */
  task_id?: string;
  /** 不可以进行操作的原因，返回多语言文本 */
  failed_reasons?: Array<resource_resource_common.ResourceCopyFailedReason>;
  BaseResp: base.BaseResp;
}

export interface ResourceCopyRetryRequest {
  /** 复制任务id, 用于查询任务状态或取消、重试任务 */
  task_id?: string;
  Base?: base.Base;
}

export interface ResourceCopyRetryResponse {
  code?: Int64;
  msg?: string;
  /** 不可以进行操作的原因，返回多语言文本 */
  failed_reasons?: Array<resource_resource_common.ResourceCopyFailedReason>;
  BaseResp: base.BaseResp;
}
/* eslint-enable */
