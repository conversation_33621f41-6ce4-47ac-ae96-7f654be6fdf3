/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ModelCacheType {
  PrefixCache = 1,
}

export enum ModelParamType {
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum ModelProvider {
  GptOpenApi = 1,
  GptEngine = 2,
  MaaS = 3,
  QianFan = 4,
  BytedLLMServer = 5,
}

export enum ModelTagKeyID {
  /** 用户权益 */
  UserRights = 1,
  /** 模型类型 */
  ModelType = 2,
  /** 模型特色 */
  ModelFeatures = 3,
  /** 模型支持功能 */
  ModelCapabilities = 4,
  /** 模型应用场景 */
  ModelScene = 5,
  /** 自定义标签 */
  CustomTag = 6,
  /** 模型厂商 */
  ModelManufacturer = 7,
  /** 参数量 */
  ParameterQuantity = 8,
  /** 火山方舟标签 */
  MaasTag = 9,
  /** 模型系列 */
  ModelSeries = 10,
  /** 下线日期 */
  OfflineDate = 11,
  /** 替换模型 */
  ReplaceModel = 12,
  /** 更新日志 */
  UpdateLog = 13,
  /** 一句话描述 */
  BriefDescription = 14,
  /** 付费标签 */
  PaidTag = 15,
  /** 模型运行时能力 */
  ModelAbility = 16,
}

export interface ModelEntity {
  /** 模型 id */
  ModelID?: string;
  /** 模型名称 */
  ModelName?: string;
  /** 模型分流规则 */
  Targets?: Array<ModelRuleTarget>;
  biz?: number;
  /** 创建者 */
  CreaterEmail?: string;
  /** 最后修改人 */
  UpdaterEmail?: string;
  /** 模型创建时间 */
  CreateTime?: Int64;
  /** 模型修改时间 */
  UpdateTime?: Int64;
}

export interface ModelParameter {
  /** 配置字段，如max_tokens */
  name?: string;
  /** 类型 */
  type?: ModelParamType;
  /** 是否必填 */
  is_required?: boolean;
  /** 数值类型参数，允许设置的最小值 */
  min?: string;
  /** 数值类型参数，允许设置的最大值 */
  max?: string;
  /** float类型参数的精度 */
  precision?: number;
  /** 不同风格的参数默认值 */
  default_value?: Record<string, string>;
  /** 枚举值，如response_format支持text,markdown,json */
  options?: Array<string>;
  /** 是否自动修正该参数到[min, max]范围内， 默认为false */
  auto_fix?: boolean;
}

export interface ModelRuleTarget {
  /** 要打到的模型元数据ID */
  ModelMetaID?: string;
  /** 要命中的流量比例区间开始 */
  RangeStart?: Int64;
  /** 要命中的流量比例区间结束 */
  RangeEnd?: Int64;
  /** 逗号拼接的要命中的用户名列表 */
  ConnectorUIDs?: Array<string>;
}
/* eslint-enable */
