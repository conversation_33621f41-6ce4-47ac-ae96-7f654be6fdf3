/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface Annotation {
  id?: string;
  span_id?: string;
  trace_id?: string;
  workspace_id?: string;
  start_time?: string;
  type?: string;
  key?: string;
  value_type?: string;
  value?: string;
  status?: string;
  reasoning?: string;
  base_info?: common.BaseInfo;
  auto_evaluate?: AutoEvaluate;
  manual_feedback?: ManualFeedback;
}

export interface AutoEvaluate {
  evaluator_version_id: string;
  evaluator_name: string;
  evaluator_version: string;
  evaluator_result?: EvaluatorResult;
  record_id: string;
  task_id: string;
}

export interface Correction {
  score?: number;
  explain?: string;
  base_info?: common.BaseInfo;
}

export interface EvaluatorResult {
  score?: number;
  correction?: Correction;
  reasoning?: string;
}

export interface ManualFeedback {
  tag_key_id: string;
  tag_key_name: string;
  tag_value_id?: string;
  tag_value?: string;
}
/* eslint-enable */
