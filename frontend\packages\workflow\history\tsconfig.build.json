{"extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"baseUrl": "./", "paths": {}, "types": [], "jsx": "react", "isolatedModules": true, "preserveSymlinks": false, "strictPropertyInitialization": false, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../base/tsconfig.build.json"}, {"path": "../../common/flowgram-adapter/common/tsconfig.build.json"}, {"path": "../../common/flowgram-adapter/free-layout-editor/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../render/tsconfig.build.json"}], "$schema": "https://json.schemastore.org/tsconfig"}