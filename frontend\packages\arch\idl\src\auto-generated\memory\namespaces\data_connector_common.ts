/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DataSourceType {
  Notion = 1,
  GoogleDrive = 2,
  FeishuWeb = 103,
  LarkWeb = 104,
  WeChat = 109,
}

export enum FileNodeType {
  Folder = 1,
  Document = 2,
  Sheet = 3,
  Space = 4,
}

export enum FileStatus {
  Initialized = 1,
  Processing = 2,
  Success = 3,
  Failed = 4,
  UnAssociated = 5,
}

export enum SourceFileType {
  Markdown = 1,
  Excel = 2,
}

export enum UserPolicyAction {
  Agree = 0,
  Disagree = 1,
}
/* eslint-enable */
