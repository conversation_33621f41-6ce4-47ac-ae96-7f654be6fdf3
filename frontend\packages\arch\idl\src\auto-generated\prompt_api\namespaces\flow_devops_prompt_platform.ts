/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_prompt_common from './flow_devops_prompt_common';
import * as base from './base';

export type Int64 = string | number;

export interface AddSpaceMemberRequest {
  /** 空间ID */
  space_id: Int64;
  /** 添加空间成员 */
  space_members?: Array<flow_devops_prompt_common.SpaceMember>;
  base?: base.Base;
}

export interface AddSpaceMemberResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface AuthComponentSDKRequest {
  /** 一个随机字符串，由数字、字母组成 */
  noncestr?: string;
  /** 时间戳（毫秒） */
  timestamp?: Int64;
  /** 组件页面url */
  url?: string;
  base?: base.Base;
}

export interface AuthComponentSDKResponse {
  signature?: string;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface CreateSpaceRequest {
  /** 空间名称 */
  name: string;
  /** 空间描述 */
  description?: string;
  space_type?: flow_devops_prompt_common.SpaceType;
  base?: base.Base;
}

export interface CreateSpaceResponse {
  /** 创建空间 */
  space?: flow_devops_prompt_common.Space;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetSessionInfoRequest {
  base?: base.Base;
}

export interface GetSessionInfoResponse {
  /** 登录用户信息 */
  user_info?: flow_devops_prompt_common.UserInfo;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetSpaceRequest {
  /** 空间ID */
  space_id: Int64;
  base?: base.Base;
}

export interface GetSpaceResponse {
  /** 空间 */
  space?: flow_devops_prompt_common.Space;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetUserInfoRequest {
  /** 选填用户ID */
  user_id?: string;
  /** 选填用户名 */
  user_name?: string;
  base?: base.Base;
}

export interface GetUserInfoResponse {
  /** 用户信息 */
  user_info?: flow_devops_prompt_common.UserInfo;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetUserSpaceRolesRequest {
  /** 空间ID */
  space_id: Int64;
  base?: base.Base;
}

export interface GetUserSpaceRolesResponse {
  roles?: Array<flow_devops_prompt_common.AuthRole>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface LarkCardActionCallbackRequest {
  challenge?: string;
  schema?: string;
  header?: flow_devops_prompt_common.LarkCallbackHeader;
  event?: flow_devops_prompt_common.LarkCallbackEvent;
}

export interface LarkCardActionCallbackResponse {
  challenge?: string;
  toast?: flow_devops_prompt_common.LarkToast;
  card?: flow_devops_prompt_common.LarkCard;
  base_resp?: base.BaseResp;
}

export interface ListUserSpaceRequest {
  base?: base.Base;
}

export interface ListUserSpaceResponse {
  /** 空间列表 */
  spaces?: Array<flow_devops_prompt_common.Space>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface LoginRequest {
  /** 登录授权码 */
  code?: string;
  /** 登录流程重定向uri */
  state?: string;
  /** 指定 sessionID */
  session_id?: string;
  base?: base.Base;
}

export interface LoginResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface LogoutRequest {
  base?: base.Base;
}

export interface LogoutResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface MCheckPermissionRequest {
  /** 鉴权三元组列表 */
  auths?: Array<flow_devops_prompt_common.SubjectActionObjects>;
  /** 应用 ID，用于区别内外场
应用ID */
  app_id?: number;
  base?: base.Base;
}

export interface MCheckPermissionResponse {
  auth_res?: Array<flow_devops_prompt_common.SubjectActionObjectAuthRes>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface MGetUserInfoRequest {
  /** 飞书UserID列表 */
  user_ids?: Array<string>;
  /** SsoUserName列表 */
  user_names?: Array<string>;
  base?: base.Base;
}

export interface MGetUserInfoResponse {
  /** 用户信息列表 */
  user_infos?: Array<flow_devops_prompt_common.UserInfo>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface QuerySpaceMemberRequest {
  /** 空间ID */
  space_id: Int64;
  role_type?: flow_devops_prompt_common.SpaceRoleType;
  page: number;
  page_size: number;
  base?: base.Base;
}

export interface QuerySpaceMemberResponse {
  /** 空间成员 */
  space_members?: Array<flow_devops_prompt_common.SpaceMember>;
  total?: number;
  /** 成员租户分布，去重 */
  member_tenant?: Array<flow_devops_prompt_common.TenantType>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface QueryUserInfoRequest {
  /** 用户名模糊搜索 */
  name_like: string;
  /** 分页大小，默认为20 */
  page_size: number;
  /** 分页Token */
  page_token: string;
  base?: base.Base;
}

export interface QueryUserInfoResponse {
  /** 用户信息列表 */
  user_infos?: Array<flow_devops_prompt_common.UserInfo>;
  /** 分页Token */
  page_token?: string;
  /** 是否还有下一页 */
  has_more?: boolean;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface RemoveSpaceMemberRequest {
  /** 空间ID */
  space_id: Int64;
  /** 移除空间成员 */
  space_members?: Array<flow_devops_prompt_common.SpaceMember>;
  base?: base.Base;
}

export interface RemoveSpaceMemberResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface UpdateSpaceRequest {
  /** 空间ID */
  space_id: Int64;
  /** 空间名称 */
  name?: string;
  /** 空间描述 */
  description?: string;
  /** 发布审批配置 */
  release_approval_config?: flow_devops_prompt_common.ReleaseApprovalConfig;
  base?: base.Base;
}

export interface UpdateSpaceResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}
/* eslint-enable */
