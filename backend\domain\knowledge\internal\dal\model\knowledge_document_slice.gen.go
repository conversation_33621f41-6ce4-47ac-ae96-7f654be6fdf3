// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameKnowledgeDocumentSlice = "knowledge_document_slice"

// KnowledgeDocumentSlice 知识库文件切片表
type KnowledgeDocumentSlice struct {
	ID          int64          `gorm:"column:id;primaryKey;comment:主键ID" json:"id"`                                      // 主键ID
	KnowledgeID int64          `gorm:"column:knowledge_id;not null;comment:knowledge id" json:"knowledge_id"`            // knowledge id
	DocumentID  int64          `gorm:"column:document_id;not null;comment:document id" json:"document_id"`               // document id
	Content     string         `gorm:"column:content;comment:切片内容" json:"content"`                                       // 切片内容
	Sequence    float64        `gorm:"column:sequence;not null;type:decimal(20,5);comment:切片顺序号, 从1开始" json:"sequence"`                     // 切片顺序号, 从1开始
	CreatedAt   int64          `gorm:"column:created_at;not null;comment:Create Time in Milliseconds" json:"created_at"` // Create Time in Milliseconds
	UpdatedAt   int64          `gorm:"column:updated_at;not null;comment:Update Time in Milliseconds" json:"updated_at"` // Update Time in Milliseconds
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;comment:Delete Time in Milliseconds" json:"deleted_at"`          // Delete Time in Milliseconds
	CreatorID   int64          `gorm:"column:creator_id;not null;comment:创建者ID" json:"creator_id"`                       // 创建者ID
	SpaceID     int64          `gorm:"column:space_id;not null;comment:空间ID" json:"space_id"`                            // 空间ID
	Status      int32          `gorm:"column:status;not null;comment:状态" json:"status"`                                  // 状态
	FailReason  string         `gorm:"column:fail_reason;comment:失败原因" json:"fail_reason"`                               // 失败原因
	Hit         int64          `gorm:"column:hit;not null;comment:命中次数" json:"hit"`                                      // 命中次数
}

// TableName KnowledgeDocumentSlice's table name
func (*KnowledgeDocumentSlice) TableName() string {
	return TableNameKnowledgeDocumentSlice
}
