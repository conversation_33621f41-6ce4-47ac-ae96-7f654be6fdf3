/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum PublishResult {
  /** 未发布 */
  Default = 0,
  /** 审核中 */
  Auditing = 1,
  /** 发布成功 */
  Successful = 2,
  /** 发布失败 */
  Failed = 3,
}

export enum PublishStatus {
  /** 打包中 */
  Packing = 0,
  /** 打包失败 */
  PackFailed = 1,
  /** 审核中 */
  Auditing = 2,
  /** 审核未通过 */
  AuditNotPass = 3,
  /** 渠道发布中 */
  ConnectorPublishing = 4,
  /** 发布完成 */
  PublishDone = 5,
}
/* eslint-enable */
