/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** Function scale record item */
export interface FunctionScaleRecordListItem {
  /** Unique scale record ID */
  record_id: string;
  /** Request ID for the scaling operation */
  request_id: string;
  /** Function ID being scaled */
  function_id: string;
  /** Cluster where scaling occurred */
  cluster: string;
  /** Region where scaling occurred */
  region: string;
  /** Cell where scaling occurred */
  cell: string;
  /** Zone where scaling occurred */
  zone: string;
  /** Name of the deployment */
  deploy_name: string;
  /** Strategy used for scaling */
  effect_strategy: string;
  /** Time when scaling took effect */
  scale_at: string;
  /** Type of scaling operation */
  scale_operation: string;
  /** Number of replicas before scaling */
  replicas_from: Int64;
  /** Number of replicas after scaling */
  replicas_to: Int64;
  /** Reason for scaling */
  detail_reason: string;
  /** Status of the scaling record */
  status: string;
  /** Record creation time */
  created_at: string;
  /** Record last update time */
  updated_at: string;
  /** Environment name */
  env_name: string;
  /** PSM of the function */
  psm: string;
  /** MQ app replica counts before scaling */
  mq_app_replicas_from: Record<string, Int64>;
  /** MQ app replica counts after scaling */
  mq_app_replicas_to: Record<string, Int64>;
}

export interface GetFunctionScaleRecordListReq {
  /** Service ID to query scale records for */
  service_id: string;
  /** Region to query scale records for */
  region: string;
  /** Pagination offset */
  offset?: string;
  /** Pagination limit */
  limit?: string;
  /** Start time (second level timestamp) */
  start_time?: string;
  /** End time (second level timestamp) */
  end_time?: string;
  /** Cluster to filter records */
  cluster?: string;
  /** Scaling strategy to filter records */
  strategy?: string;
}

export interface GetFunctionScaleRecordListRes {
  code: number;
  data: Array<FunctionScaleRecordListItem>;
  error: string;
}

export interface GetMQTriggerScaleRecordListReq {
  /** Service ID to query scale records for */
  service_id: string;
  /** Region to query scale records for */
  region: string;
  /** Pagination offset */
  offset?: string;
  /** Pagination limit */
  limit?: string;
  /** Start time (second level timestamp) */
  start_time?: string;
  /** End time (second level timestamp) */
  end_time?: string;
  /** Cluster to filter records */
  cluster?: string;
  /** Scaling strategy to filter records */
  strategy?: string;
  /** Search keyword */
  search?: string;
}

export interface GetMQTriggerScaleRecordListRes {
  code: number;
  data: Array<MQTriggerScaleRecordListItem>;
  error: string;
}

export interface MQTriggerScaleRecordListItem {
  /** Unique scale record ID */
  record_id: string;
  /** Request ID for the scaling operation */
  request_id: string;
  /** Function ID being scaled */
  function_id: string;
  /** Cluster where scaling occurred */
  cluster: string;
  /** Region where scaling occurred */
  region: string;
  /** Cell where scaling occurred */
  cell: string;
  /** MQ event ID */
  mqevent_id: string;
  /** Name of the deployment */
  deploy_name: string;
  /** Strategy used for scaling */
  effect_strategy: string;
  /** Time when scaling took effect */
  scale_at: string;
  /** Type of scaling operation */
  scale_operation: string;
  /** Replica counts before scaling (per MQ app) */
  replicas_from: Record<string, Int64>;
  /** Replica counts after scaling (per MQ app) */
  replicas_to: Record<string, Int64>;
  /** Resource allocation before scaling */
  resource_from: Record<string, Record<string, Int64>>;
  /** Resource allocation after scaling */
  resource_to: Record<string, Record<string, Int64>>;
  /** Reason for scaling */
  detail_reason: string;
  /** Status of the scaling record */
  status: string;
  /** Record creation time */
  created_at: string;
  /** Record last update time */
  updated_at: string;
  /** Environment name */
  env_name: string;
  /** PSM of the function */
  psm: string;
  /** Name of the trigger */
  trigger_name: string;
}
/* eslint-enable */
