// Code generated by hertz generator.

package coze

import (
	"context"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"

	upload "github.com/coze-dev/coze-studio/backend/api/model/file/upload"
	uploadSVC "github.com/coze-dev/coze-studio/backend/application/upload"

	"github.com/coze-dev/coze-studio/backend/pkg/lang/ptr"
)

// CommonUpload .
// @router /api/common/upload/*tos_uri [POST]
func CommonUpload(ctx context.Context, c *app.RequestContext) {
	var err error
	var req upload.CommonUploadRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	fullUrl := string(c.Request.URI().FullURI())

	resp, err := uploadSVC.SVC.UploadFileCommon(ctx, &req, fullUrl)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, resp)
}

// ApplyUploadAction .
// @router /api/common/upload/apply_upload_action [GET]
func ApplyUploadAction(ctx context.Context, c *app.RequestContext) {
	var err error
	var req upload.ApplyUploadActionRequest
	err = c.BindAndValidate(&req)
	if err != nil {
		c.String(consts.StatusBadRequest, err.Error())
		return
	}
	resp := new(upload.ApplyUploadActionResponse)
	host := c.Request.Host()
	if ptr.From(req.Action) == "ApplyImageUpload" {
		resp, err = uploadSVC.SVC.ApplyImageUpload(ctx, &req, string(host))
		if err != nil {
			internalServerErrorResponse(ctx, c, err)
			return
		}
	} else if ptr.From(req.Action) == "CommitImageUpload" {
		resp, err = uploadSVC.SVC.CommitImageUpload(ctx, &req, string(host))
		if err != nil {
			internalServerErrorResponse(ctx, c, err)
			return
		}
	}

	c.JSON(consts.StatusOK, resp)
}
