// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package common

import (
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

/****************************** audit **********************************/
type AuditStatus int64

const (
	// 审核中
	AuditStatus_Auditing AuditStatus = 0
	// 审核通过
	AuditStatus_Success AuditStatus = 1
	// 审核失败
	AuditStatus_Failed AuditStatus = 2
)

func (p AuditStatus) String() string {
	switch p {
	case AuditStatus_Auditing:
		return "Auditing"
	case AuditStatus_Success:
		return "Success"
	case AuditStatus_Failed:
		return "Failed"
	}
	return "<UNSET>"
}

func AuditStatusFromString(s string) (AuditStatus, error) {
	switch s {
	case "Auditing":
		return AuditStatus_Auditing, nil
	case "Success":
		return AuditStatus_Success, nil
	case "Failed":
		return AuditStatus_Failed, nil
	}
	return AuditStatus(0), fmt.<PERSON><PERSON><PERSON>("not a valid AuditStatus string")
}

func AuditStatusPtr(v AuditStatus) *AuditStatus { return &v }
func (p *AuditStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = AuditStatus(result.Int64)
	return
}

func (p *AuditStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

/****************************** publish **********************************/
type ConnectorDynamicStatus int64

const (
	ConnectorDynamicStatus_Normal          ConnectorDynamicStatus = 0
	ConnectorDynamicStatus_Offline         ConnectorDynamicStatus = 1
	ConnectorDynamicStatus_TokenDisconnect ConnectorDynamicStatus = 2
)

func (p ConnectorDynamicStatus) String() string {
	switch p {
	case ConnectorDynamicStatus_Normal:
		return "Normal"
	case ConnectorDynamicStatus_Offline:
		return "Offline"
	case ConnectorDynamicStatus_TokenDisconnect:
		return "TokenDisconnect"
	}
	return "<UNSET>"
}

func ConnectorDynamicStatusFromString(s string) (ConnectorDynamicStatus, error) {
	switch s {
	case "Normal":
		return ConnectorDynamicStatus_Normal, nil
	case "Offline":
		return ConnectorDynamicStatus_Offline, nil
	case "TokenDisconnect":
		return ConnectorDynamicStatus_TokenDisconnect, nil
	}
	return ConnectorDynamicStatus(0), fmt.Errorf("not a valid ConnectorDynamicStatus string")
}

func ConnectorDynamicStatusPtr(v ConnectorDynamicStatus) *ConnectorDynamicStatus { return &v }
func (p *ConnectorDynamicStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ConnectorDynamicStatus(result.Int64)
	return
}

func (p *ConnectorDynamicStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type ResourceType int64

const (
	ResourceType_Plugin    ResourceType = 1
	ResourceType_Workflow  ResourceType = 2
	ResourceType_Imageflow ResourceType = 3
	ResourceType_Knowledge ResourceType = 4
	ResourceType_UI        ResourceType = 5
	ResourceType_Prompt    ResourceType = 6
	ResourceType_Database  ResourceType = 7
	ResourceType_Variable  ResourceType = 8
)

func (p ResourceType) String() string {
	switch p {
	case ResourceType_Plugin:
		return "Plugin"
	case ResourceType_Workflow:
		return "Workflow"
	case ResourceType_Imageflow:
		return "Imageflow"
	case ResourceType_Knowledge:
		return "Knowledge"
	case ResourceType_UI:
		return "UI"
	case ResourceType_Prompt:
		return "Prompt"
	case ResourceType_Database:
		return "Database"
	case ResourceType_Variable:
		return "Variable"
	}
	return "<UNSET>"
}

func ResourceTypeFromString(s string) (ResourceType, error) {
	switch s {
	case "Plugin":
		return ResourceType_Plugin, nil
	case "Workflow":
		return ResourceType_Workflow, nil
	case "Imageflow":
		return ResourceType_Imageflow, nil
	case "Knowledge":
		return ResourceType_Knowledge, nil
	case "UI":
		return ResourceType_UI, nil
	case "Prompt":
		return ResourceType_Prompt, nil
	case "Database":
		return ResourceType_Database, nil
	case "Variable":
		return ResourceType_Variable, nil
	}
	return ResourceType(0), fmt.Errorf("not a valid ResourceType string")
}

func ResourceTypePtr(v ResourceType) *ResourceType { return &v }
func (p *ResourceType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = ResourceType(result.Int64)
	return
}

func (p *ResourceType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type OrderByType int64

const (
	OrderByType_Asc  OrderByType = 1
	OrderByType_Desc OrderByType = 2
)

func (p OrderByType) String() string {
	switch p {
	case OrderByType_Asc:
		return "Asc"
	case OrderByType_Desc:
		return "Desc"
	}
	return "<UNSET>"
}

func OrderByTypeFromString(s string) (OrderByType, error) {
	switch s {
	case "Asc":
		return OrderByType_Asc, nil
	case "Desc":
		return OrderByType_Desc, nil
	}
	return OrderByType(0), fmt.Errorf("not a valid OrderByType string")
}

func OrderByTypePtr(v OrderByType) *OrderByType { return &v }
func (p *OrderByType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = OrderByType(result.Int64)
	return
}

func (p *OrderByType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type PermissionType int64

const (
	//不能查看详情
	PermissionType_NoDetail PermissionType = 1
	//可以查看详情
	PermissionType_Detail PermissionType = 2
	//可以查看和操作
	PermissionType_Operate PermissionType = 3
)

func (p PermissionType) String() string {
	switch p {
	case PermissionType_NoDetail:
		return "NoDetail"
	case PermissionType_Detail:
		return "Detail"
	case PermissionType_Operate:
		return "Operate"
	}
	return "<UNSET>"
}

func PermissionTypeFromString(s string) (PermissionType, error) {
	switch s {
	case "NoDetail":
		return PermissionType_NoDetail, nil
	case "Detail":
		return PermissionType_Detail, nil
	case "Operate":
		return PermissionType_Operate, nil
	}
	return PermissionType(0), fmt.Errorf("not a valid PermissionType string")
}

func PermissionTypePtr(v PermissionType) *PermissionType { return &v }
func (p *PermissionType) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = PermissionType(result.Int64)
	return
}

func (p *PermissionType) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type SpaceStatus int64

const (
	SpaceStatus_Valid   SpaceStatus = 1
	SpaceStatus_Invalid SpaceStatus = 2
)

func (p SpaceStatus) String() string {
	switch p {
	case SpaceStatus_Valid:
		return "Valid"
	case SpaceStatus_Invalid:
		return "Invalid"
	}
	return "<UNSET>"
}

func SpaceStatusFromString(s string) (SpaceStatus, error) {
	switch s {
	case "Valid":
		return SpaceStatus_Valid, nil
	case "Invalid":
		return SpaceStatus_Invalid, nil
	}
	return SpaceStatus(0), fmt.Errorf("not a valid SpaceStatus string")
}

func SpaceStatusPtr(v SpaceStatus) *SpaceStatus { return &v }
func (p *SpaceStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = SpaceStatus(result.Int64)
	return
}

func (p *SpaceStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type UserLabel struct {
	LabelID   string `thrift:"label_id,1" form:"label_id" json:"label_id" query:"label_id"`
	LabelName string `thrift:"label_name,2" form:"label_name" json:"label_name" query:"label_name"`
	IconURI   string `thrift:"icon_uri,3" form:"icon_uri" json:"icon_uri" query:"icon_uri"`
	IconURL   string `thrift:"icon_url,4" form:"icon_url" json:"icon_url" query:"icon_url"`
	JumpLink  string `thrift:"jump_link,5" form:"jump_link" json:"jump_link" query:"jump_link"`
}

func NewUserLabel() *UserLabel {
	return &UserLabel{}
}

func (p *UserLabel) InitDefault() {
}

func (p *UserLabel) GetLabelID() (v string) {
	return p.LabelID
}

func (p *UserLabel) GetLabelName() (v string) {
	return p.LabelName
}

func (p *UserLabel) GetIconURI() (v string) {
	return p.IconURI
}

func (p *UserLabel) GetIconURL() (v string) {
	return p.IconURL
}

func (p *UserLabel) GetJumpLink() (v string) {
	return p.JumpLink
}

var fieldIDToName_UserLabel = map[int16]string{
	1: "label_id",
	2: "label_name",
	3: "icon_uri",
	4: "icon_url",
	5: "jump_link",
}

func (p *UserLabel) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UserLabel[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UserLabel) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LabelID = _field
	return nil
}
func (p *UserLabel) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.LabelName = _field
	return nil
}
func (p *UserLabel) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURI = _field
	return nil
}
func (p *UserLabel) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.IconURL = _field
	return nil
}
func (p *UserLabel) ReadField5(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.JumpLink = _field
	return nil
}

func (p *UserLabel) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UserLabel"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UserLabel) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("label_id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LabelID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UserLabel) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("label_name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.LabelName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UserLabel) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_uri", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURI); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UserLabel) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon_url", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.IconURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UserLabel) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("jump_link", thrift.STRING, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.JumpLink); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UserLabel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserLabel(%+v)", *p)

}

type User struct {
	UserID int64 `thrift:"user_id,1" form:"user_id" json:"user_id,string" query:"user_id"`
	// 用户昵称
	Nickname string `thrift:"nickname,2" form:"nickname" json:"nickname" query:"nickname"`
	// 用户头像
	AvatarURL string `thrift:"avatar_url,3" form:"avatar_url" json:"avatar_url" query:"avatar_url"`
	// 用户名
	UserUniqueName string `thrift:"user_unique_name,4" form:"user_unique_name" json:"user_unique_name" query:"user_unique_name"`
	// 用户标签
	UserLabel *UserLabel `thrift:"user_label,5" form:"user_label" json:"user_label" query:"user_label"`
}

func NewUser() *User {
	return &User{}
}

func (p *User) InitDefault() {
}

func (p *User) GetUserID() (v int64) {
	return p.UserID
}

func (p *User) GetNickname() (v string) {
	return p.Nickname
}

func (p *User) GetAvatarURL() (v string) {
	return p.AvatarURL
}

func (p *User) GetUserUniqueName() (v string) {
	return p.UserUniqueName
}

var User_UserLabel_DEFAULT *UserLabel

func (p *User) GetUserLabel() (v *UserLabel) {
	if !p.IsSetUserLabel() {
		return User_UserLabel_DEFAULT
	}
	return p.UserLabel
}

var fieldIDToName_User = map[int16]string{
	1: "user_id",
	2: "nickname",
	3: "avatar_url",
	4: "user_unique_name",
	5: "user_label",
}

func (p *User) IsSetUserLabel() bool {
	return p.UserLabel != nil
}

func (p *User) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_User[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *User) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserID = _field
	return nil
}
func (p *User) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Nickname = _field
	return nil
}
func (p *User) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AvatarURL = _field
	return nil
}
func (p *User) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.UserUniqueName = _field
	return nil
}
func (p *User) ReadField5(iprot thrift.TProtocol) error {
	_field := NewUserLabel()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.UserLabel = _field
	return nil
}

func (p *User) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("User"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *User) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.UserID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *User) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("nickname", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Nickname); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *User) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("avatar_url", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AvatarURL); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *User) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_unique_name", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.UserUniqueName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *User) writeField5(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("user_label", thrift.STRUCT, 5); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.UserLabel.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *User) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("User(%+v)", *p)

}

type AuditInfo struct {
	AuditStatus   *AuditStatus `thrift:"audit_status,1,optional" form:"audit_status" json:"audit_status,omitempty" query:"audit_status"`
	PublishID     *string      `thrift:"publish_id,2,optional" form:"publish_id" json:"publish_id,omitempty" query:"publish_id"`
	CommitVersion *string      `thrift:"commit_version,3,optional" form:"commit_version" json:"commit_version,omitempty" query:"commit_version"`
}

func NewAuditInfo() *AuditInfo {
	return &AuditInfo{}
}

func (p *AuditInfo) InitDefault() {
}

var AuditInfo_AuditStatus_DEFAULT AuditStatus

func (p *AuditInfo) GetAuditStatus() (v AuditStatus) {
	if !p.IsSetAuditStatus() {
		return AuditInfo_AuditStatus_DEFAULT
	}
	return *p.AuditStatus
}

var AuditInfo_PublishID_DEFAULT string

func (p *AuditInfo) GetPublishID() (v string) {
	if !p.IsSetPublishID() {
		return AuditInfo_PublishID_DEFAULT
	}
	return *p.PublishID
}

var AuditInfo_CommitVersion_DEFAULT string

func (p *AuditInfo) GetCommitVersion() (v string) {
	if !p.IsSetCommitVersion() {
		return AuditInfo_CommitVersion_DEFAULT
	}
	return *p.CommitVersion
}

var fieldIDToName_AuditInfo = map[int16]string{
	1: "audit_status",
	2: "publish_id",
	3: "commit_version",
}

func (p *AuditInfo) IsSetAuditStatus() bool {
	return p.AuditStatus != nil
}

func (p *AuditInfo) IsSetPublishID() bool {
	return p.PublishID != nil
}

func (p *AuditInfo) IsSetCommitVersion() bool {
	return p.CommitVersion != nil
}

func (p *AuditInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AuditInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AuditInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field *AuditStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := AuditStatus(v)
		_field = &tmp
	}
	p.AuditStatus = _field
	return nil
}
func (p *AuditInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PublishID = _field
	return nil
}
func (p *AuditInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CommitVersion = _field
	return nil
}

func (p *AuditInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AuditInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AuditInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err = oprot.WriteFieldBegin("audit_status", thrift.I32, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.AuditStatus)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AuditInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishID() {
		if err = oprot.WriteFieldBegin("publish_id", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.PublishID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *AuditInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCommitVersion() {
		if err = oprot.WriteFieldBegin("commit_version", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CommitVersion); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *AuditInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuditInfo(%+v)", *p)

}

// 审核结果
type AuditData struct {
	// true：机审校验不通过
	CheckNotPass bool `thrift:"check_not_pass,1" form:"check_not_pass" json:"check_not_pass" query:"check_not_pass"`
	// 机审校验不通过文案
	CheckNotPassMsg *string `thrift:"check_not_pass_msg,2,optional" form:"check_not_pass_msg" json:"check_not_pass_msg,omitempty" query:"check_not_pass_msg"`
}

func NewAuditData() *AuditData {
	return &AuditData{}
}

func (p *AuditData) InitDefault() {
}

func (p *AuditData) GetCheckNotPass() (v bool) {
	return p.CheckNotPass
}

var AuditData_CheckNotPassMsg_DEFAULT string

func (p *AuditData) GetCheckNotPassMsg() (v string) {
	if !p.IsSetCheckNotPassMsg() {
		return AuditData_CheckNotPassMsg_DEFAULT
	}
	return *p.CheckNotPassMsg
}

var fieldIDToName_AuditData = map[int16]string{
	1: "check_not_pass",
	2: "check_not_pass_msg",
}

func (p *AuditData) IsSetCheckNotPassMsg() bool {
	return p.CheckNotPassMsg != nil
}

func (p *AuditData) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_AuditData[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *AuditData) ReadField1(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.CheckNotPass = _field
	return nil
}
func (p *AuditData) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CheckNotPassMsg = _field
	return nil
}

func (p *AuditData) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("AuditData"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *AuditData) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("check_not_pass", thrift.BOOL, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.CheckNotPass); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *AuditData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCheckNotPassMsg() {
		if err = oprot.WriteFieldBegin("check_not_pass_msg", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CheckNotPassMsg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *AuditData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuditData(%+v)", *p)

}

type ConnectorInfo struct {
	ID              string                 `thrift:"id,1" form:"id" json:"id" query:"id"`
	Name            string                 `thrift:"name,2" form:"name" json:"name" query:"name"`
	Icon            string                 `thrift:"icon,3" form:"icon" json:"icon" query:"icon"`
	ConnectorStatus ConnectorDynamicStatus `thrift:"connector_status,4" form:"connector_status" json:"connector_status" query:"connector_status"`
	ShareLink       *string                `thrift:"share_link,5,optional" form:"share_link" json:"share_link,omitempty" query:"share_link"`
}

func NewConnectorInfo() *ConnectorInfo {
	return &ConnectorInfo{}
}

func (p *ConnectorInfo) InitDefault() {
}

func (p *ConnectorInfo) GetID() (v string) {
	return p.ID
}

func (p *ConnectorInfo) GetName() (v string) {
	return p.Name
}

func (p *ConnectorInfo) GetIcon() (v string) {
	return p.Icon
}

func (p *ConnectorInfo) GetConnectorStatus() (v ConnectorDynamicStatus) {
	return p.ConnectorStatus
}

var ConnectorInfo_ShareLink_DEFAULT string

func (p *ConnectorInfo) GetShareLink() (v string) {
	if !p.IsSetShareLink() {
		return ConnectorInfo_ShareLink_DEFAULT
	}
	return *p.ShareLink
}

var fieldIDToName_ConnectorInfo = map[int16]string{
	1: "id",
	2: "name",
	3: "icon",
	4: "connector_status",
	5: "share_link",
}

func (p *ConnectorInfo) IsSetShareLink() bool {
	return p.ShareLink != nil
}

func (p *ConnectorInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConnectorInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ConnectorInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *ConnectorInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *ConnectorInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Icon = _field
	return nil
}
func (p *ConnectorInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field ConnectorDynamicStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = ConnectorDynamicStatus(v)
	}
	p.ConnectorStatus = _field
	return nil
}
func (p *ConnectorInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ShareLink = _field
	return nil
}

func (p *ConnectorInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ConnectorInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConnectorInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ConnectorInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ConnectorInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("icon", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Icon); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ConnectorInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connector_status", thrift.I32, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.ConnectorStatus)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ConnectorInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetShareLink() {
		if err = oprot.WriteFieldBegin("share_link", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.ShareLink); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *ConnectorInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConnectorInfo(%+v)", *p)

}

type IntelligencePublishInfo struct {
	PublishTime  string           `thrift:"publish_time,1" form:"publish_time" json:"publish_time" query:"publish_time"`
	HasPublished bool             `thrift:"has_published,2" form:"has_published" json:"has_published" query:"has_published"`
	Connectors   []*ConnectorInfo `thrift:"connectors,3" form:"connectors" json:"connectors" query:"connectors"`
}

func NewIntelligencePublishInfo() *IntelligencePublishInfo {
	return &IntelligencePublishInfo{}
}

func (p *IntelligencePublishInfo) InitDefault() {
}

func (p *IntelligencePublishInfo) GetPublishTime() (v string) {
	return p.PublishTime
}

func (p *IntelligencePublishInfo) GetHasPublished() (v bool) {
	return p.HasPublished
}

func (p *IntelligencePublishInfo) GetConnectors() (v []*ConnectorInfo) {
	return p.Connectors
}

var fieldIDToName_IntelligencePublishInfo = map[int16]string{
	1: "publish_time",
	2: "has_published",
	3: "connectors",
}

func (p *IntelligencePublishInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_IntelligencePublishInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *IntelligencePublishInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.PublishTime = _field
	return nil
}
func (p *IntelligencePublishInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = v
	}
	p.HasPublished = _field
	return nil
}
func (p *IntelligencePublishInfo) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ConnectorInfo, 0, size)
	values := make([]ConnectorInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Connectors = _field
	return nil
}

func (p *IntelligencePublishInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("IntelligencePublishInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *IntelligencePublishInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("publish_time", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.PublishTime); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *IntelligencePublishInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("has_published", thrift.BOOL, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteBool(p.HasPublished); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *IntelligencePublishInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("connectors", thrift.LIST, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Connectors)); err != nil {
		return err
	}
	for _, v := range p.Connectors {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *IntelligencePublishInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IntelligencePublishInfo(%+v)", *p)

}

type Space struct {
	ID      int64       `thrift:"id,1" form:"id" json:"id" query:"id"`
	OwnerID int64       `thrift:"owner_id,2" form:"owner_id" json:"owner_id" query:"owner_id"`
	Status  SpaceStatus `thrift:"status,3" form:"status" json:"status" query:"status"`
	Name    string      `thrift:"name,4" form:"name" json:"name" query:"name"`
}

func NewSpace() *Space {
	return &Space{}
}

func (p *Space) InitDefault() {
}

func (p *Space) GetID() (v int64) {
	return p.ID
}

func (p *Space) GetOwnerID() (v int64) {
	return p.OwnerID
}

func (p *Space) GetStatus() (v SpaceStatus) {
	return p.Status
}

func (p *Space) GetName() (v string) {
	return p.Name
}

var fieldIDToName_Space = map[int16]string{
	1: "id",
	2: "owner_id",
	3: "status",
	4: "name",
}

func (p *Space) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_Space[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *Space) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *Space) ReadField2(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.OwnerID = _field
	return nil
}
func (p *Space) ReadField3(iprot thrift.TProtocol) error {

	var _field SpaceStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = SpaceStatus(v)
	}
	p.Status = _field
	return nil
}
func (p *Space) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}

func (p *Space) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Space"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *Space) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *Space) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("owner_id", thrift.I64, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.OwnerID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *Space) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *Space) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *Space) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Space(%+v)", *p)

}
