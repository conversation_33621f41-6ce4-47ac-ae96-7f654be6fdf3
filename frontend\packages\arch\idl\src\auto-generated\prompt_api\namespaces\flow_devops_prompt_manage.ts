/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_prompt_common from './flow_devops_prompt_common';
import * as release from './release';

export type Int64 = string | number;

export enum CheckPublishPromptConflictType {
  NoConflict = 0,
  /** prompt有更新 */
  ExistPromptUpdated = 1,
}

/** prompt生成方法 */
export enum GeneratePromptType {
  Undefined = 0,
  /** 结构化模板框架 */
  StructuredPromptGenerate = 1,
  /** 一步优化 */
  OneStepOptimize = 2,
  /** SP快捷反思优化 */
  FeedbackOptimize = 3,
}

/** 结构化模板框架类型 */
export enum StructuredPromptType {
  Undefined = 0,
  /** 基础模板框架 */
  Basic = 1,
  /** CRISPE模板框架 */
  CRISPE = 2,
}

export interface CancelReleaseTaskRequest {
  /** 发布工单id */
  release_task_id: Int64;
  base?: base.Base;
}

export interface CancelReleaseTaskResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ChatWithPromptBotRequest {
  /** 用户输入 */
  message?: flow_devops_prompt_common.Message;
  /** 历史对话记录 */
  contexts?: Array<flow_devops_prompt_common.Message>;
  /** 开场白/下一轮迭代引导 */
  get_next_guide?: boolean;
  /** 上一次生成的prompt */
  last_generated_prompt?: string;
  /** prompt名称 */
  prompt_name?: string;
  /** prompt描述 */
  prompt_desc?: string;
  base?: base.Base;
}

export interface ChatWithPromptBotResponse {
  item?: flow_devops_prompt_common.ReplyItem;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface CheckPublishPromptRequest {
  /** Prompt ID */
  prompt_id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 草稿关联版本 */
  draft_base_version?: string;
  base?: base.Base;
}

export interface CheckPublishPromptResponse {
  /** 冲突 */
  conflict_type?: CheckPublishPromptConflictType;
  /** 更新的prompt */
  prompt?: flow_devops_prompt_common.Prompt;
  /** 新候选版本号 */
  candidate_version?: string;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ClonePromptRequest {
  /** 空间ID */
  space_id?: Int64;
  /** 源PromptID */
  prompt_id?: Int64;
  /** 源版本 */
  version?: string;
  /** 新的PromptKey */
  dest_prompt_key?: string;
  /** 新的展示名称 */
  dest_display_name?: string;
  /** 新的描述 */
  dest_description?: string;
  base?: base.Base;
}

export interface ClonePromptResponse {
  dest_prompt_id?: Int64;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface CreatePromptRequest {
  /** 空间ID */
  space_id?: Int64;
  /** 提示词类型 */
  prompt_type?: flow_devops_prompt_common.PromptType;
  /** Prompt名称 */
  display_name: string;
  /** Prompt唯一键 */
  prompt_key: string;
  /** Prompt描述 */
  description?: string;
  /** 模型 */
  model_config?: flow_devops_prompt_common.ModelConfig;
  /** PromptText */
  prompt_text?: flow_devops_prompt_common.PromptText;
  /** Prompt输入 */
  prompt_input?: flow_devops_prompt_common.PromptInput;
  /** 标签 */
  labels?: Array<Int64>;
  /** 工具定义 */
  tools?: Array<flow_devops_prompt_common.ToolCombine>;
  /** 密级标签 */
  security_level?: flow_devops_prompt_common.SecurityLevel;
  tool_call_config?: flow_devops_prompt_common.ToolCallConfig;
  /** 模版引擎的类型 */
  template_type?: string;
  /** 元数据 */
  metadata?: Record<string, string>;
  /** 标签id */
  tag_ids?: Array<Int64>;
  /** MCP服务列表 */
  mcp_servers?: Array<flow_devops_prompt_common.MCPServerCombine>;
  base?: base.Base;
}

export interface CreatePromptResponse {
  /** 创建成功返回Prompt */
  prompt?: flow_devops_prompt_common.Prompt;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface CreateReleaseTaskRequest {
  /** 空间id */
  space_id: Int64;
  /** 发布Prompt列表(暂不支持批量) */
  release_resources?: Array<flow_devops_prompt_common.ReleaseResource>;
  /** 发布环境 */
  env: flow_devops_prompt_common.Env;
  /** 发布泳道 */
  feature?: string;
  /** 发布配置 */
  release_config?: flow_devops_prompt_common.ReleaseConfig;
  /** 发布备注 */
  comment?: string;
  /** 发布label */
  label?: string;
  base?: base.Base;
}

export interface CreateReleaseTaskResponse {
  /** 发布任务id */
  release_task_id?: Int64;
  /** 变更管控校验结果 */
  release_check_result?: release.CheckResult;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface DeletePromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 空间ID */
  space_id: Int64;
  base?: base.Base;
}

export interface DeletePromptResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface FulldosePromptGrayReleaseRequest {
  /** 发布单id */
  release_id: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** Prompt ID */
  prompt_id?: Int64;
  base?: base.Base;
}

export interface FulldosePromptGrayReleaseResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GeneratePromptRequest {
  /** prompt生成方法 */
  generate_prompt_type?: GeneratePromptType;
  /** 原始prompt */
  original_prompt?: string;
  /** prompt名称 */
  prompt_name?: string;
  /** prompt描述 */
  prompt_desc?: string;
  /** PromptKey */
  prompt_key?: string;
  /** 空间ID */
  space_id?: Int64;
  /** Prompt ID */
  prompt_id?: Int64;
  /** 是否重试 */
  is_retry?: boolean;
  /** 结构化模板框架额外参数
结构化模板框架类型 */
  structured_prompt_type?: StructuredPromptType;
  /** 结构化表单（JSON） */
  structured_context?: string;
  /** SP 快捷反思优化额外参数
用户输入 */
  user_message?: flow_devops_prompt_common.Message;
  /** 模型输出 */
  assistant_message?: flow_devops_prompt_common.Message;
  /** 参数内容 */
  variables?: Array<flow_devops_prompt_common.Variable>;
  /** 用户反馈问题 */
  feedback?: string;
  base?: base.Base;
}

export interface GeneratePromptResponse {
  /** 流式返回生成的prompt */
  item?: flow_devops_prompt_common.ReplyItem;
  /** 生成记录ID */
  record_id?: Int64;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetClipboardRequest {
  prompt_id?: Int64;
  space_id?: Int64;
  content_key?: string;
  /** 要粘贴的目标版本 */
  version?: string;
  base?: base.Base;
}

export interface GetClipboardResponse {
  content?: string;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetPromptReleaseRequest {
  /** 发布单id */
  release_id: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** Prompt ID */
  prompt_id?: Int64;
  base?: base.Base;
}

export interface GetPromptReleaseResponse {
  /** 发布配置信息 */
  release_info?: flow_devops_prompt_common.ReleaseInfo;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetPromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 版本 */
  version?: string;
  /** 空间ID */
  space_id?: Int64;
  /** 获取个人草稿 */
  personal_draft?: boolean;
  base?: base.Base;
}

export interface GetPromptResponse {
  /** 返回Prompt */
  prompt?: flow_devops_prompt_common.Prompt;
  /** 子Prompt列表 */
  sub_prompts?: Record<string, flow_devops_prompt_common.Prompt>;
  /** 展开后的完整PromptText */
  full_prompt_text?: flow_devops_prompt_common.PromptText;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetReleaseTaskDetailRequest {
  /** 发布工单id */
  release_task_id: Int64;
  base?: base.Base;
}

export interface GetReleaseTaskDetailResponse {
  /** 发布工单详情（没有变更时不返回） */
  release_task?: flow_devops_prompt_common.ReleaseTask;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListAvailableModelRequest {
  base?: base.Base;
}

export interface ListAvailableModelResponse {
  models?: Array<flow_devops_prompt_common.Model>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListLabelRequest {
  label_type?: string;
  base?: base.Base;
}

export interface ListLabelResponse {
  labels?: Array<flow_devops_prompt_common.Label>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListOfficialPromptRequest {
  page: number;
  page_size: number;
  /** name/key前缀匹配 */
  key_word?: string;
  /** 标签筛选 */
  labels?: Array<Int64>;
  base?: base.Base;
}

export interface ListOfficialPromptResponse {
  /** Prompt列表 */
  prompts?: Array<flow_devops_prompt_common.Prompt>;
  total?: number;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListPromptBasicOApiRequest {
  page: number;
  page_size: number;
  /** name/key前缀匹配 */
  key_word?: string;
  /** 创建人 */
  creator?: string;
  /** 过滤初始创建的PromptType类型 */
  filter_prompt_types?: Array<flow_devops_prompt_common.PromptType>;
  /** 用于fornax鉴权 */
  Authorization?: string;
  base?: base.Base;
}

export interface ListPromptBasicOApiResponse {
  /** Prompt列表 */
  prompts?: Array<flow_devops_prompt_common.PromptBasic>;
  total?: number;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListPromptReleaseInfoRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 版本 */
  version?: string;
  /** 环境 */
  env?: flow_devops_prompt_common.Env;
  /** 泳道 */
  feature?: string;
  /** 发布状态 */
  status?: flow_devops_prompt_common.ReleaseStatus;
  /** 发布label */
  label?: string;
  /** 版本信息模糊查询 */
  version_like?: string;
  /** 起始为空，滚动传入Response里的NextCursor */
  cursor?: Int64;
  page_size: Int64;
  base?: base.Base;
}

export interface ListPromptReleaseInfoResponse {
  infos?: Array<flow_devops_prompt_common.ReleaseInfo>;
  next_cursor?: Int64;
  has_more?: boolean;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListPromptRequest {
  /** 空间ID */
  space_id?: Int64;
  page: number;
  page_size: number;
  /** name/key前缀匹配 */
  key_word?: string;
  /** 创建人 */
  creator?: string;
  /** 过滤初始创建的PromptType类型 */
  filter_prompt_types?: Array<flow_devops_prompt_common.PromptType>;
  /** 发布状态 */
  publish_statuses?: Array<flow_devops_prompt_common.PublishStatus>;
  /** 标签筛选 */
  tag_ids?: Array<Int64>;
  /** 排序参数 */
  order_param?: flow_devops_prompt_common.OrderParam;
  /** 创建人列表筛选 */
  creator_list?: Array<string>;
  base?: base.Base;
}

export interface ListPromptResponse {
  /** Prompt列表 */
  prompts?: Array<flow_devops_prompt_common.Prompt>;
  total?: number;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListPromptVersionRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 起始为空，滚动传入Response里的NextCursor */
  cursor?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 过滤草稿 */
  filter_draft?: boolean;
  /** 分页数量，默认20 */
  page_size?: Int64;
  /** 版本信息模糊查询 */
  version_like?: string;
  base?: base.Base;
}

export interface ListPromptVersionResponse {
  /** Prompt列表 */
  prompts?: Array<flow_devops_prompt_common.Prompt>;
  next_cursor?: Int64;
  has_more?: boolean;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListReleaseApproverRequest {
  /** 空间id */
  space_id?: Int64;
  base?: base.Base;
}

export interface ListReleaseApproverResponse {
  /** 审批人列表 */
  approvers?: Array<flow_devops_prompt_common.UserInfo>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListReleaseTaskRequest {
  /** 空间id */
  space_id?: Int64;
  /** PromptID */
  prompt_id?: Int64;
  /** 发布环境 */
  env?: flow_devops_prompt_common.Env;
  /** 发布泳道 */
  feature?: string;
  /** 页码(从1开始) */
  page?: number;
  /** 每页数量 */
  page_size?: number;
  /** 发布label */
  label?: string;
  base?: base.Base;
}

export interface ListReleaseTaskResponse {
  /** 发布工单列表 */
  release_tasks?: Array<flow_devops_prompt_common.ReleaseTask>;
  /** 总数 */
  total?: Int64;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface MConvertURI2URLRequest {
  /** 空间ID */
  space_id?: Int64;
  /** URI列表 */
  uris?: Array<string>;
  base?: base.Base;
}

export interface MConvertURI2URLResponse {
  /** URL列表 */
  url_map?: Record<string, string>;
  base_resp?: base.BaseResp;
}

export interface MGetPromptQuery {
  /** Prompt ID */
  prompt_id?: Int64;
  /** 版本 */
  version?: string;
  /** PromptKey */
  prompt_key?: string;
  /** 发布label */
  release_label?: string;
}

export interface NotifyReleaseApproverRequest {
  /** 发布工单id */
  release_task_id: Int64;
  /** 通知审批人列表 */
  notify_approvers?: Array<string>;
  base?: base.Base;
}

export interface NotifyReleaseApproverResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface OfflinePromptGrayReleaseRequest {
  /** 发布单id */
  release_id: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** Prompt ID */
  prompt_id?: Int64;
  base?: base.Base;
}

export interface OfflinePromptGrayReleaseResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface OfflinePromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 当前版本 */
  version: string;
  /** 环境 */
  env: flow_devops_prompt_common.Env;
  /** 泳道 */
  feature?: string;
  /** 空间ID */
  space_id?: Int64;
  /** 发布label */
  label?: string;
  base?: base.Base;
}

export interface OfflinePromptResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface PublishPromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 版本 */
  version: string;
  /** 发布描述 */
  publish_description?: string;
  /** 空间ID */
  space_id: Int64;
  /** 提交个人草稿 */
  personal_draft?: boolean;
  /** 草稿关联版本 */
  draft_base_version?: string;
  base?: base.Base;
}

export interface PublishPromptResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ReleasePromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 版本 */
  version: string;
  /** 环境 */
  env: flow_devops_prompt_common.Env;
  /** 泳道 */
  feature?: string;
  /** 空间ID */
  space_id?: Int64;
  /** 灰度发布配置(废弃，不再生效) */
  gray_release_config?: flow_devops_prompt_common.GrayReleaseConfig;
  base?: base.Base;
}

export interface ReleasePromptResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ReportEventRequest {
  report_event?: flow_devops_prompt_common.ReportEvent;
  base?: base.Base;
}

export interface ReportEventResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface RevertPromptByVersionRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 版本 */
  version: string;
  /** 空间ID */
  space_id: Int64;
  /** 回滚个人草稿 */
  personal_draft?: boolean;
  base?: base.Base;
}

export interface RevertPromptByVersionResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface RollBackPreVersionPromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 当前版本 */
  version: string;
  /** 环境 */
  env: flow_devops_prompt_common.Env;
  /** 泳道 */
  feature?: string;
  /** 空间ID */
  space_id?: Int64;
  base?: base.Base;
}

export interface RollBackPreVersionPromptResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface RollbackReleaseTaskRequest {
  /** 发布工单id */
  release_task_id: Int64;
  base?: base.Base;
}

export interface RollbackReleaseTaskResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface SaveClipboardRequest {
  prompt_id?: Int64;
  space_id?: Int64;
  content?: string;
  /** 复制的源版本 */
  version?: string;
  base?: base.Base;
}

export interface SaveClipboardResponse {
  /** 剪切板key，格式为：_fornax_clipboard:xxx */
  content_key?: string;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface SavePromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** 模型 */
  model_config?: flow_devops_prompt_common.ModelConfig;
  /** PromptText */
  prompt_text?: flow_devops_prompt_common.PromptText;
  /** Prompt输入 */
  prompt_input?: flow_devops_prompt_common.PromptInput;
  /** 老逻辑指修改目标版本 */
  version?: string;
  /** 空间ID */
  space_id: Int64;
  /** Prompt类型 */
  prompt_type?: flow_devops_prompt_common.PromptType;
  /** 新逻辑都要传草稿关联版本，合并时需要传入合并目标版本 */
  draft_base_version?: string;
  /** 工具定义 */
  tools?: Array<flow_devops_prompt_common.ToolCombine>;
  tool_call_config?: flow_devops_prompt_common.ToolCallConfig;
  /** 模版引擎的类型 */
  template_type?: string;
  /** 元数据 */
  metadata?: Record<string, string>;
  /** MCP服务列表 */
  mcp_servers?: Array<flow_devops_prompt_common.MCPServerCombine>;
  base?: base.Base;
}

export interface SavePromptResponse {
  /** 保存成功返回Prompt */
  prompt?: flow_devops_prompt_common.Prompt;
  /** 子Prompt列表 */
  sub_prompts?: Record<string, flow_devops_prompt_common.Prompt>;
  /** 展开后的完整PromptText */
  full_prompt_text?: flow_devops_prompt_common.PromptText;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface TriggerReleaseSubtaskRequest {
  /** 发布工单id */
  release_task_id: Int64;
  /** 子任务key */
  subtask_key: string;
  /** 触发操作 */
  trigger_operation: flow_devops_prompt_common.TriggerOperation;
  /** 备注（审批驳回时需要） */
  comment?: string;
  base?: base.Base;
}

export interface TriggerReleaseSubtaskResponse {
  /** 变更管控校验结果 */
  release_check_result?: release.CheckResult;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface UpdateGenerateRecordRequest {
  /** 生成记录ID */
  record_id?: Int64;
  /** PromptID */
  prompt_id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 是否点赞 */
  is_liked?: boolean;
  /** 是否点踩 */
  is_disliked?: boolean;
  /** 是否采纳 */
  is_accepted?: boolean;
  /** 是否中断取消 */
  is_canceled?: boolean;
  base?: base.Base;
}

export interface UpdateGenerateRecordResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface UpdatePromptReleaseRequest {
  /** 发布单id */
  release_id: Int64;
  /** 灰度发布配置 */
  gray_release_config?: flow_devops_prompt_common.GrayReleaseConfig;
  /** 空间ID */
  space_id?: Int64;
  /** Prompt ID */
  prompt_id?: Int64;
  base?: base.Base;
}

export interface UpdatePromptReleaseResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface UpdatePromptRequest {
  /** Prompt ID */
  prompt_id: Int64;
  /** Prompt名称 */
  display_name?: string;
  /** Prompt描述 */
  description?: string;
  /** 空间ID */
  space_id?: Int64;
  /** 标签 */
  labels?: Array<Int64>;
  /** 密级标签 */
  security_level?: flow_devops_prompt_common.SecurityLevel;
  /** 密级标签降级理由 */
  downgrade_reason?: string;
  /** 标签id列表 */
  tag_ids?: Array<Int64>;
  base?: base.Base;
}

export interface UpdatePromptResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}
/* eslint-enable */
