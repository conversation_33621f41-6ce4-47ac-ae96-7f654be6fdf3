/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AttributeValueType {
  Unknown = 0,
  String = 1,
  Boolean = 2,
  StringList = 11,
  BooleanList = 12,
}

export enum ResourceType {
  Account = 1,
  Workspace = 2,
  App = 3,
  Bot = 4,
  Plugin = 5,
  Workflow = 6,
  Knowledge = 7,
  PersonalAccessToken = 8,
  Connector = 9,
  Card = 10,
  CardTemplate = 11,
  Conversation = 12,
  File = 13,
  ServicePrincipal = 14,
  Enterprise = 15,
  MigrateTask = 16,
  Prompt = 17,
  UI = 18,
  Project = 19,
  EvaluationDataset = 20,
  EvaluationTask = 21,
  Evaluator = 22,
  Database = 23,
  OceanProject = 24,
  FinetuneTask = 25,
  LoopPrompt = 26,
  LoopEvaluationExperiment = 27,
  LoopEvaluationSet = 28,
  LoopEvaluator = 29,
  LoopEvaluationTarget = 30,
  LoopTraceView = 31,
  LoopModel = 32,
}

export interface AttributeValue {
  type: AttributeValueType;
  value: string;
}

export interface ResourceIdentifier {
  /** 资源类型 */
  type: ResourceType;
  /** 资源Id */
  id: string;
}
/* eslint-enable */
