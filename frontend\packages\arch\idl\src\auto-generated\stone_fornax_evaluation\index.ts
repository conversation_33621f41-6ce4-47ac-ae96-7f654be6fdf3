/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as authz from './namespaces/authz';
import * as base from './namespaces/base';
import * as common from './namespaces/common';
import * as datasetv2 from './namespaces/datasetv2';
import * as datasetv2similarity from './namespaces/datasetv2similarity';
import * as eval_set from './namespaces/eval_set';
import * as eval_target from './namespaces/eval_target';
import * as evaluator from './namespaces/evaluator';
import * as stone_fornax_evaluation_eval_set from './namespaces/stone_fornax_evaluation_eval_set';
import * as stone_fornax_evaluation_eval_target from './namespaces/stone_fornax_evaluation_eval_target';
import * as stone_fornax_evaluation_evaluator from './namespaces/stone_fornax_evaluation_evaluator';
import * as stone_fornax_evaluation_expt from './namespaces/stone_fornax_evaluation_expt';
import * as tag from './namespaces/tag';

export {
  authz,
  base,
  common,
  datasetv2,
  datasetv2similarity,
  eval_set,
  eval_target,
  evaluator,
  stone_fornax_evaluation_eval_set,
  stone_fornax_evaluation_eval_target,
  stone_fornax_evaluation_evaluator,
  stone_fornax_evaluation_expt,
  tag,
};
export * from './namespaces/authz';
export * from './namespaces/base';
export * from './namespaces/common';
export * from './namespaces/datasetv2';
export * from './namespaces/datasetv2similarity';
export * from './namespaces/eval_set';
export * from './namespaces/eval_target';
export * from './namespaces/evaluator';
export * from './namespaces/stone_fornax_evaluation_eval_set';
export * from './namespaces/stone_fornax_evaluation_eval_target';
export * from './namespaces/stone_fornax_evaluation_evaluator';
export * from './namespaces/stone_fornax_evaluation_expt';
export * from './namespaces/tag';

export type Int64 = string | number;

export default class StoneFornaxEvaluationService<T> {
  private request: any = () => {
    throw new Error('StoneFornaxEvaluationService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/evaluation/v2/evaluator/list
   *
   * 按查询条件查询evaluator
   */
  ListEvaluator(
    req: stone_fornax_evaluation_evaluator.ListEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.ListEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      search_name: _req['search_name'],
      creator_ids: _req['creator_ids'],
      evaluator_type: _req['evaluator_type'],
      page_size: _req['page_size'],
      page_num: _req['page_num'],
      order_bys: _req['order_bys'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/:evaluator_id/submit
   *
   * 提交evaluator版本
   */
  SubmitEvaluatorVersion(
    req: stone_fornax_evaluation_evaluator.SubmitEvaluatorVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.SubmitEvaluatorVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluator/${_req['evaluator_id']}/submit`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      version: _req['version'],
      description: _req['description'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/get_batch
   *
   * 按id批量查询evaluator
   */
  BatchGetEvaluator(
    req: stone_fornax_evaluation_evaluator.BatchGetEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.BatchGetEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/get_batch');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      evaluator_ids: _req['evaluator_ids'],
      include_deleted: _req['include_deleted'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/create
   *
   * 创建evaluator
   */
  CreateEvaluator(
    req: stone_fornax_evaluation_evaluator.CreateEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.CreateEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/create');
    const method = 'POST';
    const data = {
      evaluator: _req['evaluator'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/evaluation/v2/evaluator/list_builtin_template
   *
   * 获取内置评估器模板列表（不含具体内容）
   */
  ListBuiltinTemplate(
    req: stone_fornax_evaluation_evaluator.ListBuiltinTemplateRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.ListBuiltinTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/evaluator/list_builtin_template',
    );
    const method = 'GET';
    const params = {
      builtin_template_type: _req['builtin_template_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/evaluation/v2/evaluator/get_builtin_template_info
   *
   * 按key单个查询内置评估器模板详情
   */
  GetBuiltinEvaluatorTemplate(
    req: stone_fornax_evaluation_evaluator.GetBuiltinTemplateInfoRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.GetBuiltinTemplateInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/evaluator/get_builtin_template_info',
    );
    const method = 'GET';
    const params = {
      builtin_template_type: _req['builtin_template_type'],
      builtin_template_key: _req['builtin_template_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/evaluation/v2/evaluator/:evaluator_id
   *
   * 按id单个查询evaluator
   */
  GetEvaluator(
    req: stone_fornax_evaluation_evaluator.GetEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.GetEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluator/${_req['evaluator_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      include_deleted: _req['include_deleted'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/evaluation/v2/experiment/result
   *
   * MGetExperimentResult 获取实验结果
   */
  MGetExperimentResult(
    req: stone_fornax_evaluation_expt.MGetExperimentResultRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.MGetExperimentResultResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment/result');
    const method = 'POST';
    const data = {
      experiment_ids: _req['experiment_ids'],
      baseline_experiment_id: _req['baseline_experiment_id'],
      filters: _req['filters'],
      Base: _req['Base'],
    };
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /api/evaluation/v2/experiment/aggr_result */
  MGetExperimentAggrResult(
    req: stone_fornax_evaluation_expt.MGetExperimentAggrResultRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.MGetExperimentAggrResultResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment/aggr_result');
    const method = 'POST';
    const data = { experiment_ids: _req['experiment_ids'], Base: _req['Base'] };
    const params = { space_id: _req['space_id'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/version/get_batch
   *
   * 按版本id批量查询evaluator version
   */
  BatchGetEvaluatorVersion(
    req: stone_fornax_evaluation_evaluator.BatchGetEvaluatorVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.BatchGetEvaluatorVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/evaluator/version/get_batch',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      evaluator_version_ids: _req['evaluator_version_ids'],
      include_deleted: _req['include_deleted'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/evaluation/v2/evaluator/version/:evaluator_version_id
   *
   * 按版本id单个查询evaluator version
   */
  GetEvaluatorVersion(
    req: stone_fornax_evaluation_evaluator.GetEvaluatorVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.GetEvaluatorVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluator/version/${_req['evaluator_version_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      include_deleted: _req['include_deleted'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/evaluation/v2/evaluator_record/:evaluator_record_id
   *
   * 按id查询单个evaluator运行结果
   */
  GetEvaluatorRecord(
    req: stone_fornax_evaluation_evaluator.GetEvaluatorRecordRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.GetEvaluatorRecordResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluator_record/${_req['evaluator_record_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      include_deleted: _req['include_deleted'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator_record/get_batch
   *
   * 按id批量查询evaluator运行结果
   */
  BatchGetEvaluatorRecord(
    req: stone_fornax_evaluation_evaluator.BatchGetEvaluatorRecordRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.BatchGetEvaluatorRecordResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/evaluator_record/get_batch',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      evaluator_record_ids: _req['evaluator_record_ids'],
      include_deleted: _req['include_deleted'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/evaluation/v2/evaluator_record/correct
   *
   * 修正evaluator运行分数
   */
  CorrectEvaluatorRecord(
    req: stone_fornax_evaluation_evaluator.CorrectEvaluatorRecordRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.CorrectEvaluatorRecordResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator_record/correct');
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      evaluator_record_id: _req['evaluator_record_id'],
      correction: _req['correction'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/evaluation_sets/:evaluation_set_id/items/batch_delete */
  BatchDeleteEvaluationSetItems(
    req: stone_fornax_evaluation_eval_set.BatchDeleteEvaluationSetItemsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.BatchDeleteEvaluationSetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/items/batch_delete`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      item_ids: _req['item_ids'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /api/evaluation/v2/evaluation_sets/:evaluation_set_id */
  DeleteEvaluationSet(
    req: stone_fornax_evaluation_eval_set.DeleteEvaluationSetReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.DeleteEvaluationSetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}`,
    );
    const method = 'DELETE';
    const params = { space_id: _req['space_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/evaluation/v2/evaluation_sets/list */
  ListEvaluationSets(
    req: stone_fornax_evaluation_eval_set.ListEvaluationSetsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.ListEvaluationSetsResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluation_sets/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      creators: _req['creators'],
      evaluation_set_ids: _req['evaluation_set_ids'],
      page: _req['page'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PUT /api/evaluation/v2/evaluation_sets/:evaluation_set_id */
  UpdateEvaluationSet(
    req: stone_fornax_evaluation_eval_set.UpdateEvaluationSetReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.UpdateEvaluationSetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/evaluation/v2/evaluation_sets/versions/:version_id */
  GetEvaluationSetVersion(
    req: stone_fornax_evaluation_eval_set.GetEvaluationSetVersionReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.GetEvaluationSetVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/versions/${_req['version_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      evaluation_set_id: _req['evaluation_set_id'],
      deleted_at: _req['deleted_at'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluation_sets/:evaluation_set_id/items/batch
   *
   * 数据管理
   */
  BatchCreateEvaluationSetItems(
    req: stone_fornax_evaluation_eval_set.BatchCreateEvaluationSetItemsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.BatchCreateEvaluationSetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/items/batch`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      items: _req['items'],
      skip_invalid_items: _req['skip_invalid_items'],
      allow_partial_add: _req['allow_partial_add'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PUT /api/evaluation/v2/evaluation_sets/:evaluation_set_id/items/:item_id */
  UpdateEvaluationSetItem(
    req: stone_fornax_evaluation_eval_set.UpdateEvaluationSetItemReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.UpdateEvaluationSetItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/items/${_req['item_id']}`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      turns: _req['turns'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/evaluation/v2/evaluation_sets/:evaluation_set_id */
  GetEvaluationSet(
    req: stone_fornax_evaluation_eval_set.GetEvaluationSetReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.GetEvaluationSetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      deleted_at: _req['deleted_at'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluation_sets/:evaluation_set_id/versions
   *
   * 版本管理
   */
  CreateEvaluationSetVersion(
    req: stone_fornax_evaluation_eval_set.CreateEvaluationSetVersionReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.CreateEvaluationSetVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/versions`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      version: _req['version'],
      desc: _req['desc'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluation_sets
   *
   * 基本信息管理
   */
  CreateEvaluationSet(
    req: stone_fornax_evaluation_eval_set.CreateEvaluationSetReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.CreateEvaluationSetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluation_sets');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      evaluation_set_schema: _req['evaluation_set_schema'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/evaluation_sets/:evaluation_set_id/versions/list */
  ListEvaluationSetVersions(
    req: stone_fornax_evaluation_eval_set.ListEvaluationSetVersionsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.ListEvaluationSetVersionsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/versions/list`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      version_like: _req['version_like'],
      page: _req['page'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/evaluation/v2/evaluation_sets/:evaluation_set_id/schema
   *
   * 字段管理
   */
  UpdateEvaluationSetSchema(
    req: stone_fornax_evaluation_eval_set.UpdateEvaluationSetSchemaReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.UpdateEvaluationSetSchemaResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/schema`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      fields: _req['fields'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/version/list
   *
   * 按evaluator id查询evaluator version
   */
  ListEvaluatorVersion(
    req: stone_fornax_evaluation_evaluator.ListEvaluatorVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.ListEvaluatorVersionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/version/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      evaluator_id: _req['evaluator_id'],
      query_versions: _req['query_versions'],
      page_size: _req['page_size'],
      page_num: _req['page_num'],
      order_bys: _req['order_bys'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PUT /api/evaluation/v2/experiment */
  UpdateExperiment(
    req: stone_fornax_evaluation_expt.UpdateExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.UpdateExperimentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment');
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      expt_id: _req['expt_id'],
      name: _req['name'],
      desc: _req['desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/experiment/submit
   *
   * SubmitExperiment 创建并提交运行
   */
  SubmitExperiment(
    req: stone_fornax_evaluation_expt.SubmitExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.SubmitExperimentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment/submit');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      eval_set_version_id: _req['eval_set_version_id'],
      TargetVersionID: _req['TargetVersionID'],
      evaluator_version_ids: _req['evaluator_version_ids'],
      name: _req['name'],
      desc: _req['desc'],
      eval_set_id: _req['eval_set_id'],
      TargetID: _req['TargetID'],
      target_field_mapping: _req['target_field_mapping'],
      evaluator_field_mapping: _req['evaluator_field_mapping'],
      item_concur_num: _req['item_concur_num'],
      evaluators_concur_num: _req['evaluators_concur_num'],
      create_eval_target_param: _req['create_eval_target_param'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/experiment/kill */
  KillExperiment(
    req?: stone_fornax_evaluation_expt.KillExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.KillExperimentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/evaluation/v2/experiment/kill');
    const method = 'POST';
    const data = {
      expt_id: _req['expt_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/experiment/retry */
  RetryExperiment(
    req?: stone_fornax_evaluation_expt.RetryExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.RetryExperimentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/evaluation/v2/experiment/retry');
    const method = 'POST';
    const data = {
      retry_mode: _req['retry_mode'],
      space_id: _req['space_id'],
      expt_id: _req['expt_id'],
      item_ids: _req['item_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /api/evaluation/v2/experiment */
  DeleteExperiment(
    req: stone_fornax_evaluation_expt.DeleteExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.DeleteExperimentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment');
    const method = 'DELETE';
    const data = { space_id: _req['space_id'], expt_id: _req['expt_id'] };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /api/evaluation/v2/experiment/clone */
  CloneExperiment(
    req?: stone_fornax_evaluation_expt.CloneExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.CloneExperimentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/evaluation/v2/experiment/clone');
    const method = 'POST';
    const data = {
      expt_id: _req['expt_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/evaluation_sets/:evaluation_set_id/items/list */
  ListEvaluationSetItems(
    req: stone_fornax_evaluation_eval_set.ListEvaluationSetItemsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.ListEvaluationSetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/items/list`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      version_id: _req['version_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/evaluation/v2/evaluator/delete
   *
   * 批量删除evaluator
   */
  DeleteEvaluator(
    req: stone_fornax_evaluation_evaluator.DeleteEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.DeleteEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/delete');
    const method = 'DELETE';
    const data = {
      evaluator_ids: _req['evaluator_ids'],
      space_id: _req['space_id'],
    };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /api/evaluation/v2/evaluation_sets/:evaluation_set_id/items/batch_get */
  BatchGetEvaluationSetItems(
    req: stone_fornax_evaluation_eval_set.BatchGetEvaluationSetItemsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.BatchGetEvaluationSetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/evaluation_sets/${_req['evaluation_set_id']}/items/batch_get`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      version_id: _req['version_id'],
      item_ids: _req['item_ids'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/evaluation/v2/evaluator/update_meta
   *
   * 修改evaluator元信息
   */
  UpdateEvaluatorMeta(
    req: stone_fornax_evaluation_evaluator.UpdateEvaluatorMetaRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.UpdateEvaluatorMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/update_meta');
    const method = 'PUT';
    const data = {
      evaluator_id: _req['evaluator_id'],
      space_id: _req['space_id'],
      evaluator_type: _req['evaluator_type'],
      name: _req['name'],
      description: _req['description'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/evaluation/v2/evaluator/update_draft
   *
   * 修改evaluator草稿
   */
  UpdateEvaluatorDraft(
    req: stone_fornax_evaluation_evaluator.UpdateEvaluatorDraftRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.UpdateEvaluatorDraftResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/update_draft');
    const method = 'PUT';
    const data = {
      evaluator_id: _req['evaluator_id'],
      space_id: _req['space_id'],
      evaluator_content: _req['evaluator_content'],
      evaluator_type: _req['evaluator_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/eval_targets/execute
   *
   * 执行
   */
  ExecuteEvalTarget(
    req: stone_fornax_evaluation_eval_target.ExecuteEvalTargetRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.ExecuteEvalTargetResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/eval_targets/execute');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      eval_target_id: _req['eval_target_id'],
      eval_target_version_id: _req['eval_target_version_id'],
      input_data: _req['input_data'],
      experiment_run_id: _req['experiment_run_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/evaluation/v2/eval_targets/records/:eval_target_record_id */
  GetEvalTargetRecord(
    req: stone_fornax_evaluation_eval_target.GetEvalTargetRecordRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.GetEvalTargetRecordResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/eval_targets/records/${_req['eval_target_record_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/evaluation/v2/eval_targets/records/batch_get */
  BatchGetEvalTargetRecord(
    req: stone_fornax_evaluation_eval_target.BatchGetEvalTargetRecordRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.BatchGetEvalTargetRecordResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/eval_targets/records/batch_get',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      eval_target_record_ids: _req['eval_target_record_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/eval_targets
   *
   * 创建评测对象
   */
  CreateEvalTarget(
    req: stone_fornax_evaluation_eval_target.CreateEvalTargetRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.CreateEvalTargetResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/eval_targets');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      param: _req['param'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/run
   *
   * evaluator 运行
   */
  RunEvaluator(
    req: stone_fornax_evaluation_evaluator.RunEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.RunEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/run');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      evaluator_version_id: _req['evaluator_version_id'],
      input_data: _req['input_data'],
      experiment_id: _req['experiment_id'],
      experiment_run_id: _req['experiment_run_id'],
      item_id: _req['item_id'],
      turn_id: _req['turn_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/debug
   *
   * evaluator 调试
   */
  DebugEvaluator(
    req: stone_fornax_evaluation_evaluator.DebugEvaluatorRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.DebugEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/debug');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      evaluator_content: _req['evaluator_content'],
      input_data: _req['input_data'],
      evaluator_type: _req['evaluator_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/eval_targets/list_source_version
   *
   * Source评测对象版本列表
   */
  ListSourceEvalTargetVersion(
    req: stone_fornax_evaluation_eval_target.ListSourceEvalTargetVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.ListSourceEvalTargetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/eval_targets/list_source_version',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_target_id: _req['source_target_id'],
      target_type: _req['target_type'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/eval_targets/list_source
   *
   * Source评测对象列表
   */
  ListSourceEvalTarget(
    req: stone_fornax_evaluation_eval_target.ListSourceEvalTargetRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.ListSourceEvalTargetResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/eval_targets/list_source');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      target_type: _req['target_type'],
      name: _req['name'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/experiment/list */
  PullExperiments(
    req: stone_fornax_evaluation_expt.PullExperimentsRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.PullExperimentsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      filter_option: _req['filter_option'],
      order_bys: _req['order_bys'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/experiments */
  MGetExperiments(
    req: stone_fornax_evaluation_expt.MGetExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.MGetExperimentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiments');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      expt_ids: _req['expt_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/evaluation/v2/evaluation_sets/versions/batch_get */
  BatchGetEvaluationSetVersions(
    req: stone_fornax_evaluation_eval_set.BatchGetEvaluationSetVersionsReq,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_set.BatchGetEvaluationSetVersionsResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/evaluation_sets/versions/batch_get',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      version_ids: _req['version_ids'],
      deleted_at: _req['deleted_at'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/eval_targets/versions/batch_get
   *
   * 批量获取+版本
   */
  BatchGetEvalTargetVersion(
    req: stone_fornax_evaluation_eval_target.BatchGetEvalTargetVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.BatchGetEvalTargetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/eval_targets/versions/batch_get',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      eval_target_version_ids: _req['eval_target_version_ids'],
      need_source_info: _req['need_source_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/evaluation/v2/eval_targets/versions/:eval_target_version_id
   *
   * 获取评测对象+版本
   */
  GetEvalTargetVersion(
    req: stone_fornax_evaluation_eval_target.GetEvalTargetVersionRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.GetEvalTargetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v2/eval_targets/versions/${_req['eval_target_version_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** DELETE /api/evaluation/v2/experiments */
  MDeleteExperiment(
    req: stone_fornax_evaluation_expt.MDeleteExperimentRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.MDeleteExperimentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiments');
    const method = 'DELETE';
    const data = { space_id: _req['space_id'], expt_ids: _req['expt_ids'] };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /api/evaluation/v2/experiment/check/name */
  CheckExperimentName(
    req: stone_fornax_evaluation_expt.CheckExperimentNameRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_expt.CheckExperimentNameResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/experiment/check/name');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/evaluation/v2/evaluator/default_prompt_evaluator_tools
   *
   * 获取prompt evaluator tools
   */
  GetDefaultPromptEvaluatorTools(
    req?: stone_fornax_evaluation_evaluator.GetDefaultPromptEvaluatorToolsRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.GetDefaultPromptEvaluatorToolsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/evaluation/v2/evaluator/default_prompt_evaluator_tools',
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/evaluation/v2/eval_targets/batch_get_by_source
   *
   * 根据source target获取评测对象信息
   */
  BatchGetEvalTargetBySource(
    req: stone_fornax_evaluation_eval_target.BatchGetEvalTargetBySourceRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_eval_target.BatchGetEvalTargetBySourceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v2/eval_targets/batch_get_by_source',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_target_ids: _req['source_target_ids'],
      eval_target_type: _req['eval_target_type'],
      need_source_info: _req['need_source_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v2/evaluator/check_name
   *
   * 校验evaluator名称是否重复
   */
  CheckEvaluatorName(
    req: stone_fornax_evaluation_evaluator.CheckEvaluatorNameRequest,
    options?: T,
  ): Promise<stone_fornax_evaluation_evaluator.CheckEvaluatorNameResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v2/evaluator/check_name');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      evaluator_id: _req['evaluator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
