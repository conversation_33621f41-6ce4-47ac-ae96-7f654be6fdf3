// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/openauth/openapiauth/internal/dal/model"
)

func newAPIKey(db *gorm.DB, opts ...gen.DOOption) aPIKey {
	_aPIKey := aPIKey{}

	_aPIKey.aPIKeyDo.UseDB(db, opts...)
	_aPIKey.aPIKeyDo.UseModel(&model.APIKey{})

	tableName := _aPIKey.aPIKeyDo.TableName()
	_aPIKey.ALL = field.NewAsterisk(tableName)
	_aPIKey.ID = field.NewInt64(tableName, "id")
	_aPIKey.APIKey = field.NewString(tableName, "api_key")
	_aPIKey.Name = field.NewString(tableName, "name")
	_aPIKey.Status = field.NewInt32(tableName, "status")
	_aPIKey.UserID = field.NewInt64(tableName, "user_id")
	_aPIKey.ExpiredAt = field.NewInt64(tableName, "expired_at")
	_aPIKey.CreatedAt = field.NewInt64(tableName, "created_at")
	_aPIKey.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_aPIKey.LastUsedAt = field.NewInt64(tableName, "last_used_at")

	_aPIKey.fillFieldMap()

	return _aPIKey
}

// aPIKey api key table
type aPIKey struct {
	aPIKeyDo

	ALL        field.Asterisk
	ID         field.Int64  // Primary Key ID
	APIKey     field.String // API Key hash
	Name       field.String // API Key Name
	Status     field.Int32  // 0 normal, 1 deleted
	UserID     field.Int64  // API Key Owner
	ExpiredAt  field.Int64  // API Key Expired Time
	CreatedAt  field.Int64  // Create Time in Milliseconds
	UpdatedAt  field.Int64  // Update Time in Milliseconds
	LastUsedAt field.Int64  // Used Time in Milliseconds

	fieldMap map[string]field.Expr
}

func (a aPIKey) Table(newTableName string) *aPIKey {
	a.aPIKeyDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a aPIKey) As(alias string) *aPIKey {
	a.aPIKeyDo.DO = *(a.aPIKeyDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *aPIKey) updateTableName(table string) *aPIKey {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.APIKey = field.NewString(table, "api_key")
	a.Name = field.NewString(table, "name")
	a.Status = field.NewInt32(table, "status")
	a.UserID = field.NewInt64(table, "user_id")
	a.ExpiredAt = field.NewInt64(table, "expired_at")
	a.CreatedAt = field.NewInt64(table, "created_at")
	a.UpdatedAt = field.NewInt64(table, "updated_at")
	a.LastUsedAt = field.NewInt64(table, "last_used_at")

	a.fillFieldMap()

	return a
}

func (a *aPIKey) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *aPIKey) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["id"] = a.ID
	a.fieldMap["api_key"] = a.APIKey
	a.fieldMap["name"] = a.Name
	a.fieldMap["status"] = a.Status
	a.fieldMap["user_id"] = a.UserID
	a.fieldMap["expired_at"] = a.ExpiredAt
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["last_used_at"] = a.LastUsedAt
}

func (a aPIKey) clone(db *gorm.DB) aPIKey {
	a.aPIKeyDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a aPIKey) replaceDB(db *gorm.DB) aPIKey {
	a.aPIKeyDo.ReplaceDB(db)
	return a
}

type aPIKeyDo struct{ gen.DO }

type IAPIKeyDo interface {
	gen.SubQuery
	Debug() IAPIKeyDo
	WithContext(ctx context.Context) IAPIKeyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAPIKeyDo
	WriteDB() IAPIKeyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAPIKeyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAPIKeyDo
	Not(conds ...gen.Condition) IAPIKeyDo
	Or(conds ...gen.Condition) IAPIKeyDo
	Select(conds ...field.Expr) IAPIKeyDo
	Where(conds ...gen.Condition) IAPIKeyDo
	Order(conds ...field.Expr) IAPIKeyDo
	Distinct(cols ...field.Expr) IAPIKeyDo
	Omit(cols ...field.Expr) IAPIKeyDo
	Join(table schema.Tabler, on ...field.Expr) IAPIKeyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAPIKeyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAPIKeyDo
	Group(cols ...field.Expr) IAPIKeyDo
	Having(conds ...gen.Condition) IAPIKeyDo
	Limit(limit int) IAPIKeyDo
	Offset(offset int) IAPIKeyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIKeyDo
	Unscoped() IAPIKeyDo
	Create(values ...*model.APIKey) error
	CreateInBatches(values []*model.APIKey, batchSize int) error
	Save(values ...*model.APIKey) error
	First() (*model.APIKey, error)
	Take() (*model.APIKey, error)
	Last() (*model.APIKey, error)
	Find() ([]*model.APIKey, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.APIKey, err error)
	FindInBatches(result *[]*model.APIKey, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.APIKey) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAPIKeyDo
	Assign(attrs ...field.AssignExpr) IAPIKeyDo
	Joins(fields ...field.RelationField) IAPIKeyDo
	Preload(fields ...field.RelationField) IAPIKeyDo
	FirstOrInit() (*model.APIKey, error)
	FirstOrCreate() (*model.APIKey, error)
	FindByPage(offset int, limit int) (result []*model.APIKey, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAPIKeyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a aPIKeyDo) Debug() IAPIKeyDo {
	return a.withDO(a.DO.Debug())
}

func (a aPIKeyDo) WithContext(ctx context.Context) IAPIKeyDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a aPIKeyDo) ReadDB() IAPIKeyDo {
	return a.Clauses(dbresolver.Read)
}

func (a aPIKeyDo) WriteDB() IAPIKeyDo {
	return a.Clauses(dbresolver.Write)
}

func (a aPIKeyDo) Session(config *gorm.Session) IAPIKeyDo {
	return a.withDO(a.DO.Session(config))
}

func (a aPIKeyDo) Clauses(conds ...clause.Expression) IAPIKeyDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a aPIKeyDo) Returning(value interface{}, columns ...string) IAPIKeyDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a aPIKeyDo) Not(conds ...gen.Condition) IAPIKeyDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a aPIKeyDo) Or(conds ...gen.Condition) IAPIKeyDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a aPIKeyDo) Select(conds ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a aPIKeyDo) Where(conds ...gen.Condition) IAPIKeyDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a aPIKeyDo) Order(conds ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a aPIKeyDo) Distinct(cols ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a aPIKeyDo) Omit(cols ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a aPIKeyDo) Join(table schema.Tabler, on ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a aPIKeyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a aPIKeyDo) RightJoin(table schema.Tabler, on ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a aPIKeyDo) Group(cols ...field.Expr) IAPIKeyDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a aPIKeyDo) Having(conds ...gen.Condition) IAPIKeyDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a aPIKeyDo) Limit(limit int) IAPIKeyDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a aPIKeyDo) Offset(offset int) IAPIKeyDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a aPIKeyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAPIKeyDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a aPIKeyDo) Unscoped() IAPIKeyDo {
	return a.withDO(a.DO.Unscoped())
}

func (a aPIKeyDo) Create(values ...*model.APIKey) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a aPIKeyDo) CreateInBatches(values []*model.APIKey, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a aPIKeyDo) Save(values ...*model.APIKey) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a aPIKeyDo) First() (*model.APIKey, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.APIKey), nil
	}
}

func (a aPIKeyDo) Take() (*model.APIKey, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.APIKey), nil
	}
}

func (a aPIKeyDo) Last() (*model.APIKey, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.APIKey), nil
	}
}

func (a aPIKeyDo) Find() ([]*model.APIKey, error) {
	result, err := a.DO.Find()
	return result.([]*model.APIKey), err
}

func (a aPIKeyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.APIKey, err error) {
	buf := make([]*model.APIKey, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a aPIKeyDo) FindInBatches(result *[]*model.APIKey, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a aPIKeyDo) Attrs(attrs ...field.AssignExpr) IAPIKeyDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a aPIKeyDo) Assign(attrs ...field.AssignExpr) IAPIKeyDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a aPIKeyDo) Joins(fields ...field.RelationField) IAPIKeyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a aPIKeyDo) Preload(fields ...field.RelationField) IAPIKeyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a aPIKeyDo) FirstOrInit() (*model.APIKey, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.APIKey), nil
	}
}

func (a aPIKeyDo) FirstOrCreate() (*model.APIKey, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.APIKey), nil
	}
}

func (a aPIKeyDo) FindByPage(offset int, limit int) (result []*model.APIKey, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a aPIKeyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a aPIKeyDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a aPIKeyDo) Delete(models ...*model.APIKey) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *aPIKeyDo) withDO(do gen.Dao) *aPIKeyDo {
	a.DO = *do.(*gen.DO)
	return a
}
