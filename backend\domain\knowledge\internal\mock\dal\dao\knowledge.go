// Code generated by MockGen. DO NOT EDIT.
// Source: knowledge.go
//
// Generated by this command:
//
//	mockgen -destination ../../mock/dal/dao/knowledge.go --package dao -source knowledge.go
//

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"

	"github.com/coze-dev/coze-studio/backend/domain/knowledge/entity"
	model "github.com/coze-dev/coze-studio/backend/domain/knowledge/internal/dal/model"
)

// MockKnowledgeRepo is a mock of KnowledgeRepo interface.
type MockKnowledgeRepo struct {
	ctrl     *gomock.Controller
	recorder *MockKnowledgeRepoMockRecorder
}

// MockKnowledgeRepoMockRecorder is the mock recorder for MockKnowledgeRepo.
type MockKnowledgeRepoMockRecorder struct {
	mock *MockKnowledgeRepo
}

// NewMockKnowledgeRepo creates a new mock instance.
func NewMockKnowledgeRepo(ctrl *gomock.Controller) *MockKnowledgeRepo {
	mock := &MockKnowledgeRepo{ctrl: ctrl}
	mock.recorder = &MockKnowledgeRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKnowledgeRepo) EXPECT() *MockKnowledgeRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockKnowledgeRepo) Create(ctx context.Context, knowledge *model.Knowledge) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, knowledge)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockKnowledgeRepoMockRecorder) Create(ctx, knowledge any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockKnowledgeRepo)(nil).Create), ctx, knowledge)
}

// Delete mocks base method.
func (m *MockKnowledgeRepo) Delete(ctx context.Context, id int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockKnowledgeRepoMockRecorder) Delete(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKnowledgeRepo)(nil).Delete), ctx, id)
}

// FilterEnableKnowledge mocks base method.
func (m *MockKnowledgeRepo) FilterEnableKnowledge(ctx context.Context, ids []int64) ([]*model.Knowledge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterEnableKnowledge", ctx, ids)
	ret0, _ := ret[0].([]*model.Knowledge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterEnableKnowledge indicates an expected call of FilterEnableKnowledge.
func (mr *MockKnowledgeRepoMockRecorder) FilterEnableKnowledge(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterEnableKnowledge", reflect.TypeOf((*MockKnowledgeRepo)(nil).FilterEnableKnowledge), ctx, ids)
}

// FindKnowledgeByCondition mocks base method.
func (m *MockKnowledgeRepo) FindKnowledgeByCondition(ctx context.Context, opts *entity.WhereKnowledgeOption) ([]*model.Knowledge, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindKnowledgeByCondition", ctx, opts)
	ret0, _ := ret[0].([]*model.Knowledge)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// FindKnowledgeByCondition indicates an expected call of FindKnowledgeByCondition.
func (mr *MockKnowledgeRepoMockRecorder) FindKnowledgeByCondition(ctx, opts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindKnowledgeByCondition", reflect.TypeOf((*MockKnowledgeRepo)(nil).FindKnowledgeByCondition), ctx, opts)
}

// GetByID mocks base method.
func (m *MockKnowledgeRepo) GetByID(ctx context.Context, id int64) (*model.Knowledge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.Knowledge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockKnowledgeRepoMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockKnowledgeRepo)(nil).GetByID), ctx, id)
}

// InitTx mocks base method.
func (m *MockKnowledgeRepo) InitTx() (*gorm.DB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitTx")
	ret0, _ := ret[0].(*gorm.DB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitTx indicates an expected call of InitTx.
func (mr *MockKnowledgeRepoMockRecorder) InitTx() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitTx", reflect.TypeOf((*MockKnowledgeRepo)(nil).InitTx))
}

// MGetByID mocks base method.
func (m *MockKnowledgeRepo) MGetByID(ctx context.Context, ids []int64) ([]*model.Knowledge, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGetByID", ctx, ids)
	ret0, _ := ret[0].([]*model.Knowledge)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGetByID indicates an expected call of MGetByID.
func (mr *MockKnowledgeRepoMockRecorder) MGetByID(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGetByID", reflect.TypeOf((*MockKnowledgeRepo)(nil).MGetByID), ctx, ids)
}

// Update mocks base method.
func (m *MockKnowledgeRepo) Update(ctx context.Context, knowledge *model.Knowledge) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, knowledge)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockKnowledgeRepoMockRecorder) Update(ctx, knowledge any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockKnowledgeRepo)(nil).Update), ctx, knowledge)
}

// UpdateWithTx mocks base method.
func (m *MockKnowledgeRepo) UpdateWithTx(ctx context.Context, tx *gorm.DB, knowledgeID int64, updateMap map[string]any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWithTx", ctx, tx, knowledgeID, updateMap)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWithTx indicates an expected call of UpdateWithTx.
func (mr *MockKnowledgeRepoMockRecorder) UpdateWithTx(ctx, tx, knowledgeID, updateMap any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWithTx", reflect.TypeOf((*MockKnowledgeRepo)(nil).UpdateWithTx), ctx, tx, knowledgeID, updateMap)
}
