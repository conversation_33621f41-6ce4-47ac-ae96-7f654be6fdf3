/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum SpanCategory {
  Unknown = 1,
  Start = 2,
  Agent = 3,
  LLMCall = 4,
  Workflow = 5,
  WorkflowStart = 6,
  WorkflowEnd = 7,
  Plugin = 8,
  Knowledge = 9,
  Code = 10,
  Condition = 11,
  Card = 12,
  Message = 13,
  Variable = 14,
  Database = 15,
  LongTermMemory = 16,
  Hook = 17,
  Batch = 18,
  Loop = 19,
  <PERSON>llel = 20,
  Script = 21,
  CallFlow = 22,
  Connector = 23,
  WorkflowHTTP = 24,
}

export enum SpanStatus {
  Unknown = 1,
  Success = 2,
  Error = 3,
  Broken = 4,
}

export enum SpanType {
  Unknown = 1,
  UserInput = 2,
  ThirdParty = 3,
  ScheduledTasks = 4,
  OpenDialog = 5,
  InvokeAgent = 6,
  RestartAgent = 7,
  SwitchAgent = 8,
  LLMCall = 9,
  LLMBatchCall = 10,
  Workflow = 11,
  WorkflowStart = 12,
  WorkflowEnd = 13,
  PluginTool = 14,
  PluginToolBatch = 15,
  Knowledge = 16,
  Code = 17,
  CodeBatch = 18,
  Condition = 19,
  Chain = 20,
  Card = 21,
  WorkflowMessage = 22,
  WorkflowLLMCall = 23,
  WorkflowLLMBatchCall = 24,
  WorkflowCode = 25,
  WorkflowCodeBatch = 26,
  WorkflowCondition = 27,
  WorkflowPluginTool = 28,
  WorkflowPluginToolBatch = 29,
  WorkflowKnowledge = 30,
  WorkflowVariable = 31,
  WorkflowDatabase = 32,
  Variable = 33,
  Database = 34,
  LongTermMemory = 35,
  Hook = 36,
  BWStart = 37,
  BWEnd = 38,
  BWBatch = 39,
  BWLoop = 40,
  BWCondition = 41,
  BWLLM = 42,
  BWParallel = 43,
  BWScript = 44,
  BWVariable = 45,
  BWCallFlow = 46,
  BWConnector = 47,
  UserInputV2 = 48,
  WorkflowHttp = 49,
  WorkflowHttp_end = 50,
}

export interface AttrBWBatch {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrBWCallFlow {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 若被调用流程是pkg内的流程，则会填充此字段 */
  call_flow_pkg_name: string;
  /** 被调用的流程名称 */
  call_flow_name: string;
}

export interface AttrBWCondition {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 最终选择的分支名称 */
  branch_name: string;
}

export interface AttrBWConnector {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 具体的连接器节点的类型 */
  node_type: string;
}

export interface AttrBWEnd {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrBWLLM {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 大模型类型，仅当选用OpenAIChatModel才会具备有效值 */
  model: string;
  /** temperature */
  temperature: number;
}

export interface AttrBWLoop {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrBWParallel {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrBWScript {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrBWStart {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrBWVariable {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrCard {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 卡片ID */
  card_id: string;
  /** 卡片类型，枚举：workflow、plugin、workflow_message */
  card_sub_type: string;
  /** 卡片对应节点的名称 */
  card_node_name?: string;
}

/** Deprecated */
export interface AttrChain {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrCode {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
}

export interface AttrCodeBatch {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 当前任务序号 */
  task_index: number;
  /** 批处理任务总数 */
  task_total: number;
}

export interface AttrCondition {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
}

export interface AttrDatabase {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
}

export interface AttrHook {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** hook服务类型，枚举值有 rpc、http、workflow、plugin、retriever */
  hook_type: string;
  /** 对应 hook 服务 URI */
  hook_uri: string;
  /** 调用该 Hook 的 agent id（若是 single agent 模式，则为空） */
  agent_id: string;
  /** hook 响应码 */
  hook_resp_code: string;
  /** 是否是流式输入输出 */
  is_stream: boolean;
}

export interface AttrInvokeAgent {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** LLM模型，例如GPT-4(8K) */
  model: string;
  /** Temperature参数 */
  temperature: number;
  /** 最大回复字符长度 */
  max_length_resp: number;
  /** 上下文模式 */
  context_mode: string;
  /** 记忆的会话轮数 */
  dialog_round: number;
  /** Agent模式类型 */
  agent_type: string;
  /** AgentFlow状态机中，当前Agent的ID */
  agent_id: string;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
}

export interface AttrKnowledge {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** Knowledge 唯一标识 */
  knowledge_id: string;
}

export interface AttrLLMBatchCall {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 调用类型 */
  call_type: string;
  /** LLM模型，例如GPT-4(8K) */
  model: string;
  /** Temperature参数 */
  temperature: number;
  /** 最大回复字符长度 */
  max_length_resp: number;
  /** 当前任务序号 */
  task_index: number;
  /** 批处理任务总数 */
  task_total: number;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
}

export interface AttrLLMCall {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 调用类型 */
  call_type: string;
  /** LLM模型，例如GPT-4(8K) */
  model: string;
  /** Temperature参数 */
  temperature: number;
  /** 最大回复字符长度 */
  max_length_resp: number;
  /** 该节点的提示词 */
  prompt: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
}

export interface AttrLongTermMemory {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
}

export interface AttrPluginTool {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 插件唯一标识 */
  plugin_id: string;
  /** 插件中Tool的唯一标识 */
  tool_id: string;
  /** Tool名称 */
  tool_name: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
}

export interface AttrPluginToolBatch {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 插件唯一标识 */
  plugin_id: string;
  /** 插件中Tool的唯一标识 */
  tool_id: string;
  /** Tool名称 */
  tool_name: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 当前任务序号 */
  task_index: number;
  /** 批处理任务总数 */
  task_total: number;
}

export interface AttrRestartAgent {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrSwitchAgent {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
}

export interface AttrUserInput {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** query类型: text/file/voice ... */
  query_type: string;
  /** Query输入方式: 手动输入 ... */
  query_input_method: string;
  /** 是否是重新生成 */
  is_regenerate: boolean;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 客户端类型 */
  client_type: string;
  /** 用户的操作系统，如 iOS 设备、Android 设备 */
  os: string;
  /** 用户操作系统版本，如iOS 版本 */
  os_version: string;
  /** 浏览器类型 */
  browser_type: string;
  /** 浏览器版本 */
  browser_version: string;
  /** 调试信息中的pushUuid */
  push_uuid: string;
  /** 建议消息 */
  suggestions: string;
  /** 对话场景，仅Coze平台上报，取值homebot, draftbot, bot_detailpage */
  dialog_scene: string;
  /** 消息唯一标识 */
  message_id: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
  /** 本节点的简洁输入 */
  simple_input?: string;
}

export interface AttrUserInputV2 {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  connector_id: string;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
  /** 消息唯一标识 */
  message_id: string;
  /** 客户端类型 */
  conversation_id: string;
  /** 用户的操作系统，如 iOS 设备、Android 设备 */
  section_id: string;
  /** 用户操作系统版本，如iOS 版本 */
  resource_type: string;
  /** 事件发生的Bot ID */
  app_id: string;
  /** 流程唯一标识 */
  workflow_id: string;
  /** 调试信息中的pushUuid */
  workflow_type: string;
  /** 对外版本 */
  version: string;
  /** 资源版本号 */
  workflow_version: string;
  /** 该资源是否直接在 Project 中被创建 */
  in_project_create: boolean;
  /** 是否是触发器触发 */
  is_trigger: boolean;
  /** 触发器ID */
  trigger_id: string;
  /** 执行ID，例如：触发器执行id、工作流执行id */
  execute_id: string;
}

export interface AttrVariable {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 调用方法 */
  action: string;
}

export interface AttrWorkflow {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 流程唯一标识 */
  workflow_id: string;
  /** 流程执行ID */
  execute_id: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 在workflow中的节点ID */
  workflow_node_id?: string;
  /** workflow类型：1——基础工作流，2——BlockWise工作流 */
  workflow_schema_type?: number;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowCode {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowCodeBatch {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 当前任务序号 */
  task_index: number;
  /** 批处理任务总数 */
  task_total: number;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowCondition {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowDatabase {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowEnd {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 输出模式 */
  output_mode: string;
  /** 应答内容 */
  answer_content: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowHttp {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
  /** 流程节点名 */
  node_name?: string;
}

export interface AttrWorkflowHttpEnd {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowKnowledge {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** Knowledge 唯一标识 */
  knowledge_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowLLMBatchCall {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 调用类型 */
  call_type: string;
  /** LLM模型，例如GPT-4(8K) */
  model: string;
  /** Temperature参数 */
  temperature: number;
  /** 最大回复字符长度 */
  max_length_resp: number;
  /** 当前任务序号 */
  task_index: number;
  /** 批处理任务总数 */
  task_total: number;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowLLMCall {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 首字母回复时间戳，单位毫秒 */
  start_time_first_resp: string;
  /** 调用类型 */
  call_type: string;
  /** LLM模型，例如GPT-4(8K) */
  model: string;
  /** Temperature参数 */
  temperature: number;
  /** 最大回复字符长度 */
  max_length_resp: number;
  /** 该节点的提示词 */
  prompt: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 首字母回复时延，单位毫秒 */
  latency_first_resp: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowMessage {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流式输出，枚举：open、close */
  streaming_output: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowPluginTool {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 插件唯一标识 */
  plugin_id: string;
  /** 插件中Tool的唯一标识 */
  tool_id: string;
  /** Tool名称 */
  tool_name: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowPluginToolBatch {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 插件唯一标识 */
  plugin_id: string;
  /** 插件中Tool的唯一标识 */
  tool_id: string;
  /** Tool名称 */
  tool_name: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 当前任务序号 */
  task_index: number;
  /** 批处理任务总数 */
  task_total: number;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

export interface AttrWorkflowVariable {
  /** argos日志ID */
  log_id: string;
  /** 触发事件的用户ID */
  user_id: string;
  /** 事件发生的Bot ID */
  bot_id: string;
  /** Bot 的名称快照 */
  bot_name: string;
  /** bot开发/正式环境 */
  bot_env: string;
  /** bot版本 */
  bot_version: string;
  /** bot所属空间ID */
  bot_space_id: string;
  /** Bot 的下发渠道，如豆包、飞书等 */
  channel: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 当前span实际输入消耗token数 */
  input_tokens: number;
  /** 当前span实际输出消耗token数 */
  output_tokens: number;
  /** 当前span的状态码，0表示成功，非0表示失败 */
  status_code: string;
  /** 调用类型 */
  call_type: string;
  /** 在workflow中的节点ID */
  workflow_node_id: string;
  /** 流程唯一标识 */
  workflow_id?: string;
  /** 流程执行ID */
  execute_id?: string;
  /** 流程版本号 */
  workflow_version?: string;
  /** 流程执行子ID */
  sub_execute_id?: string;
}

/** A Span represents a single operation performed by a single component of the system. */
export interface Span {
  /** A unique identifier for a trace. All spans from the same trace share the same `trace_id`.
This field is required. */
  trace_id: string;
  /** A unique identifier for a span within a trace, assigned when the span
is created.
This field is required. */
  id: string;
  /** The `span id` of this span's parent span. If this is a root span, then the value of this
field must be empty.
❌ "parent_id": null
✅ "parent_id": "" */
  parent_id: string;
  /** A description of the span's operation.

For example, the name can be a qualified method name or a file name
and a line number where the operation is called. A best practice is to use
the same display name at the same call point in an application.
This makes it easier to correlate spans in different traces.

This field is semantically required to be set to non-empty string.
Empty value is equivalent to an unknown span name.

This field is required. */
  name: string;
  /** Type is the type of span. */
  type: SpanType;
  /** StartTime(ms) is the start time of the span. On the client side, this is the time
kept by the local machine where the span execution starts. On the server side,
this is the time when the server's application handler starts running.
Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970. */
  start_time: string;
  /** Latency(ms) */
  latency: string;
  /** An final status for this span. */
  status: SpanStatus;
  /** Attributes is a collection of key/value pairs. examples of attributes:

"log_id": "20240101123456"
"input_tokens": 8888

Attribute keys MUST be unique (it is not allowed to have more than one
attribute with the same key). */
  attr_user_input?: AttrUserInput;
  attr_invoke_agent?: AttrInvokeAgent;
  attr_restart_agent?: AttrRestartAgent;
  attr_switch_agent?: AttrSwitchAgent;
  attr_llm_call?: AttrLLMCall;
  attr_llm_batch_call?: AttrLLMBatchCall;
  attr_workflow?: AttrWorkflow;
  attr_workflow_end?: AttrWorkflowEnd;
  attr_code?: AttrCode;
  attr_code_batch?: AttrCodeBatch;
  attr_condition?: AttrCondition;
  attr_plugin_tool?: AttrPluginTool;
  attr_plugin_tool_batch?: AttrPluginToolBatch;
  attr_knowledge?: AttrKnowledge;
  /** Deprecated */
  attr_chain?: AttrChain;
  attr_card?: AttrCard;
  attr_workflow_message?: AttrWorkflowMessage;
  attr_workflow_llm_call?: AttrWorkflowLLMCall;
  attr_workflow_llm_batch_call?: AttrWorkflowLLMBatchCall;
  attr_workflow_code?: AttrWorkflowCode;
  attr_workflow_code_batch?: AttrWorkflowCodeBatch;
  attr_workflow_condition?: AttrWorkflowCondition;
  attr_workflow_plugin_tool?: AttrWorkflowPluginTool;
  attr_workflow_plugin_tool_batch?: AttrWorkflowPluginToolBatch;
  attr_workflow_knowledge?: AttrWorkflowKnowledge;
  attr_workflow_variable?: AttrWorkflowVariable;
  attr_workflow_database?: AttrWorkflowDatabase;
  attr_variable?: AttrVariable;
  attr_database?: AttrDatabase;
  attr_long_term_memory?: AttrLongTermMemory;
  attr_hook?: AttrHook;
  attr_bw_start?: AttrBWStart;
  attr_bw_end?: AttrBWEnd;
  attr_bw_batch?: AttrBWBatch;
  attr_bw_loop?: AttrBWLoop;
  attr_bw_condition?: AttrBWCondition;
  attr_bw_llm?: AttrBWLLM;
  attr_bw_parallel?: AttrBWParallel;
  attr_bw_variable?: AttrBWVariable;
  attr_bw_call_flow?: AttrBWCallFlow;
  attr_bw_connector?: AttrBWConnector;
  attr_user_input_v2?: AttrUserInputV2;
  attr_workflow_http?: AttrWorkflowHttp;
  attr_workflow_http_end?: AttrWorkflowHttpEnd;
  attr_bw_script?: AttrBWScript;
}
/* eslint-enable */
