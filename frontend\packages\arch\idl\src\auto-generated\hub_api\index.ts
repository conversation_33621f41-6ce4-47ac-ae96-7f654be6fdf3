/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export enum DeployType {
  CANARY = 1,
  ONLINE = 2,
}

export interface CheckHealthRequest {}

export interface CheckHealthResponse {
  status?: string;
  message?: string;
}

export interface GetDeployListRequest {
  projectId?: string;
  page?: number;
  pageSize?: number;
}

export interface GetDeployListResponse {
  list?: Array<OceanDeploy>;
  total?: number;
  code?: number;
  message?: string;
}

export interface OceanDeploy {
  deployId?: string;
  pkgName?: string;
  version?: string;
  uid?: string;
  pkgUrl?: string;
  createTime?: number;
  resourceUrls?: Record<string, string>;
  deployType?: DeployType;
}

export default class HubApiService<T> {
  private request: any = () => {
    throw new Error('HubApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /api/hub/ocean/deploy-list */
  getDeployList(
    req?: GetDeployListRequest,
    options?: T,
  ): Promise<GetDeployListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/hub/ocean/deploy-list');
    const method = 'GET';
    const params = {
      projectId: _req['projectId'],
      page: _req['page'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/hub/check-health */
  checkHealth(
    req?: CheckHealthRequest,
    options?: T,
  ): Promise<CheckHealthResponse> {
    const url = this.genBaseURL('/api/hub/check-health');
    const method = 'GET';
    return this.request({ url, method }, options);
  }
}
/* eslint-enable */
