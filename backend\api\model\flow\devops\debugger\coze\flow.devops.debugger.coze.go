// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package coze

import (
	"github.com/coze-dev/coze-studio/backend/api/model/base"
	"github.com/coze-dev/coze-studio/backend/api/model/flow/devops/debugger/domain/infra"
	"github.com/coze-dev/coze-studio/backend/api/model/flow/devops/debugger/domain/testcase"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

// ========== TestCase =========== //
type SaveCaseDataReq struct {
	// 业务信息
	BizCtx              *infra.BizCtx           `thrift:"bizCtx,1,optional" form:"bizCtx" json:"bizCtx,omitempty" query:"bizCtx"`
	BizComponentSubject *infra.ComponentSubject `thrift:"bizComponentSubject,2,optional" form:"bizComponentSubject" json:"bizComponentSubject,omitempty" query:"bizComponentSubject"`
	// case基本数据
	CaseBase *testcase.CaseDataBase `thrift:"caseBase,3,optional" form:"caseBase" json:"caseBase,omitempty" query:"caseBase"`
	Base     *base.Base             `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewSaveCaseDataReq() *SaveCaseDataReq {
	return &SaveCaseDataReq{}
}

func (p *SaveCaseDataReq) InitDefault() {
}

var SaveCaseDataReq_BizCtx_DEFAULT *infra.BizCtx

func (p *SaveCaseDataReq) GetBizCtx() (v *infra.BizCtx) {
	if !p.IsSetBizCtx() {
		return SaveCaseDataReq_BizCtx_DEFAULT
	}
	return p.BizCtx
}

var SaveCaseDataReq_BizComponentSubject_DEFAULT *infra.ComponentSubject

func (p *SaveCaseDataReq) GetBizComponentSubject() (v *infra.ComponentSubject) {
	if !p.IsSetBizComponentSubject() {
		return SaveCaseDataReq_BizComponentSubject_DEFAULT
	}
	return p.BizComponentSubject
}

var SaveCaseDataReq_CaseBase_DEFAULT *testcase.CaseDataBase

func (p *SaveCaseDataReq) GetCaseBase() (v *testcase.CaseDataBase) {
	if !p.IsSetCaseBase() {
		return SaveCaseDataReq_CaseBase_DEFAULT
	}
	return p.CaseBase
}

var SaveCaseDataReq_Base_DEFAULT *base.Base

func (p *SaveCaseDataReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return SaveCaseDataReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_SaveCaseDataReq = map[int16]string{
	1:   "bizCtx",
	2:   "bizComponentSubject",
	3:   "caseBase",
	255: "Base",
}

func (p *SaveCaseDataReq) IsSetBizCtx() bool {
	return p.BizCtx != nil
}

func (p *SaveCaseDataReq) IsSetBizComponentSubject() bool {
	return p.BizComponentSubject != nil
}

func (p *SaveCaseDataReq) IsSetCaseBase() bool {
	return p.CaseBase != nil
}

func (p *SaveCaseDataReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *SaveCaseDataReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SaveCaseDataReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SaveCaseDataReq) ReadField1(iprot thrift.TProtocol) error {
	_field := infra.NewBizCtx()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizCtx = _field
	return nil
}
func (p *SaveCaseDataReq) ReadField2(iprot thrift.TProtocol) error {
	_field := infra.NewComponentSubject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizComponentSubject = _field
	return nil
}
func (p *SaveCaseDataReq) ReadField3(iprot thrift.TProtocol) error {
	_field := testcase.NewCaseDataBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CaseBase = _field
	return nil
}
func (p *SaveCaseDataReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *SaveCaseDataReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SaveCaseDataReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SaveCaseDataReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizCtx() {
		if err = oprot.WriteFieldBegin("bizCtx", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizCtx.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SaveCaseDataReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizComponentSubject() {
		if err = oprot.WriteFieldBegin("bizComponentSubject", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizComponentSubject.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *SaveCaseDataReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaseBase() {
		if err = oprot.WriteFieldBegin("caseBase", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CaseBase.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *SaveCaseDataReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SaveCaseDataReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveCaseDataReq(%+v)", *p)

}

type SaveCaseDataResp struct {
	CaseDetail *testcase.CaseDataDetail `thrift:"caseDetail,1,optional" form:"caseDetail" json:"caseDetail,omitempty" query:"caseDetail"`
	Code       *int32                   `thrift:"code,253,optional" form:"code" json:"code,omitempty" query:"code"`
	Msg        *string                  `thrift:"msg,254,optional" form:"msg" json:"msg,omitempty" query:"msg"`
	BaseResp   *base.BaseResp           `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewSaveCaseDataResp() *SaveCaseDataResp {
	return &SaveCaseDataResp{}
}

func (p *SaveCaseDataResp) InitDefault() {
}

var SaveCaseDataResp_CaseDetail_DEFAULT *testcase.CaseDataDetail

func (p *SaveCaseDataResp) GetCaseDetail() (v *testcase.CaseDataDetail) {
	if !p.IsSetCaseDetail() {
		return SaveCaseDataResp_CaseDetail_DEFAULT
	}
	return p.CaseDetail
}

var SaveCaseDataResp_Code_DEFAULT int32

func (p *SaveCaseDataResp) GetCode() (v int32) {
	if !p.IsSetCode() {
		return SaveCaseDataResp_Code_DEFAULT
	}
	return *p.Code
}

var SaveCaseDataResp_Msg_DEFAULT string

func (p *SaveCaseDataResp) GetMsg() (v string) {
	if !p.IsSetMsg() {
		return SaveCaseDataResp_Msg_DEFAULT
	}
	return *p.Msg
}

var SaveCaseDataResp_BaseResp_DEFAULT *base.BaseResp

func (p *SaveCaseDataResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return SaveCaseDataResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_SaveCaseDataResp = map[int16]string{
	1:   "caseDetail",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *SaveCaseDataResp) IsSetCaseDetail() bool {
	return p.CaseDetail != nil
}

func (p *SaveCaseDataResp) IsSetCode() bool {
	return p.Code != nil
}

func (p *SaveCaseDataResp) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *SaveCaseDataResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *SaveCaseDataResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SaveCaseDataResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SaveCaseDataResp) ReadField1(iprot thrift.TProtocol) error {
	_field := testcase.NewCaseDataDetail()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.CaseDetail = _field
	return nil
}
func (p *SaveCaseDataResp) ReadField253(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *SaveCaseDataResp) ReadField254(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Msg = _field
	return nil
}
func (p *SaveCaseDataResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *SaveCaseDataResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SaveCaseDataResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SaveCaseDataResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaseDetail() {
		if err = oprot.WriteFieldBegin("caseDetail", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.CaseDetail.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *SaveCaseDataResp) writeField253(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *SaveCaseDataResp) writeField254(oprot thrift.TProtocol) (err error) {
	if p.IsSetMsg() {
		if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Msg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *SaveCaseDataResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *SaveCaseDataResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveCaseDataResp(%+v)", *p)

}

type DeleteCaseDataReq struct {
	// 业务信息
	BizCtx *infra.BizCtx `thrift:"bizCtx,1,optional" form:"bizCtx" json:"bizCtx,omitempty" query:"bizCtx"`
	// 单次上限20个
	CaseIDs []int64    `thrift:"caseIDs,2,optional" form:"caseIDs" json:"caseIDs,omitempty" query:"caseIDs"`
	Base    *base.Base `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewDeleteCaseDataReq() *DeleteCaseDataReq {
	return &DeleteCaseDataReq{}
}

func (p *DeleteCaseDataReq) InitDefault() {
}

var DeleteCaseDataReq_BizCtx_DEFAULT *infra.BizCtx

func (p *DeleteCaseDataReq) GetBizCtx() (v *infra.BizCtx) {
	if !p.IsSetBizCtx() {
		return DeleteCaseDataReq_BizCtx_DEFAULT
	}
	return p.BizCtx
}

var DeleteCaseDataReq_CaseIDs_DEFAULT []int64

func (p *DeleteCaseDataReq) GetCaseIDs() (v []int64) {
	if !p.IsSetCaseIDs() {
		return DeleteCaseDataReq_CaseIDs_DEFAULT
	}
	return p.CaseIDs
}

var DeleteCaseDataReq_Base_DEFAULT *base.Base

func (p *DeleteCaseDataReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return DeleteCaseDataReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_DeleteCaseDataReq = map[int16]string{
	1:   "bizCtx",
	2:   "caseIDs",
	255: "Base",
}

func (p *DeleteCaseDataReq) IsSetBizCtx() bool {
	return p.BizCtx != nil
}

func (p *DeleteCaseDataReq) IsSetCaseIDs() bool {
	return p.CaseIDs != nil
}

func (p *DeleteCaseDataReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *DeleteCaseDataReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteCaseDataReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteCaseDataReq) ReadField1(iprot thrift.TProtocol) error {
	_field := infra.NewBizCtx()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizCtx = _field
	return nil
}
func (p *DeleteCaseDataReq) ReadField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.CaseIDs = _field
	return nil
}
func (p *DeleteCaseDataReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *DeleteCaseDataReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteCaseDataReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteCaseDataReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizCtx() {
		if err = oprot.WriteFieldBegin("bizCtx", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizCtx.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteCaseDataReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaseIDs() {
		if err = oprot.WriteFieldBegin("caseIDs", thrift.LIST, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.CaseIDs)); err != nil {
			return err
		}
		for _, v := range p.CaseIDs {
			if err := oprot.WriteI64(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *DeleteCaseDataReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteCaseDataReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCaseDataReq(%+v)", *p)

}

type DeleteCaseDataResp struct {
	DeletedCaseIDS []int64        `thrift:"deletedCaseIDS,1,optional" form:"deletedCaseIDS" json:"deletedCaseIDS,omitempty" query:"deletedCaseIDS"`
	Code           *int32         `thrift:"code,253,optional" form:"code" json:"code,omitempty" query:"code"`
	Msg            *string        `thrift:"msg,254,optional" form:"msg" json:"msg,omitempty" query:"msg"`
	BaseResp       *base.BaseResp `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewDeleteCaseDataResp() *DeleteCaseDataResp {
	return &DeleteCaseDataResp{}
}

func (p *DeleteCaseDataResp) InitDefault() {
}

var DeleteCaseDataResp_DeletedCaseIDS_DEFAULT []int64

func (p *DeleteCaseDataResp) GetDeletedCaseIDS() (v []int64) {
	if !p.IsSetDeletedCaseIDS() {
		return DeleteCaseDataResp_DeletedCaseIDS_DEFAULT
	}
	return p.DeletedCaseIDS
}

var DeleteCaseDataResp_Code_DEFAULT int32

func (p *DeleteCaseDataResp) GetCode() (v int32) {
	if !p.IsSetCode() {
		return DeleteCaseDataResp_Code_DEFAULT
	}
	return *p.Code
}

var DeleteCaseDataResp_Msg_DEFAULT string

func (p *DeleteCaseDataResp) GetMsg() (v string) {
	if !p.IsSetMsg() {
		return DeleteCaseDataResp_Msg_DEFAULT
	}
	return *p.Msg
}

var DeleteCaseDataResp_BaseResp_DEFAULT *base.BaseResp

func (p *DeleteCaseDataResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return DeleteCaseDataResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_DeleteCaseDataResp = map[int16]string{
	1:   "deletedCaseIDS",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *DeleteCaseDataResp) IsSetDeletedCaseIDS() bool {
	return p.DeletedCaseIDS != nil
}

func (p *DeleteCaseDataResp) IsSetCode() bool {
	return p.Code != nil
}

func (p *DeleteCaseDataResp) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *DeleteCaseDataResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *DeleteCaseDataResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteCaseDataResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DeleteCaseDataResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]int64, 0, size)
	for i := 0; i < size; i++ {

		var _elem int64
		if v, err := iprot.ReadI64(); err != nil {
			return err
		} else {
			_elem = v
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.DeletedCaseIDS = _field
	return nil
}
func (p *DeleteCaseDataResp) ReadField253(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *DeleteCaseDataResp) ReadField254(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Msg = _field
	return nil
}
func (p *DeleteCaseDataResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *DeleteCaseDataResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteCaseDataResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteCaseDataResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeletedCaseIDS() {
		if err = oprot.WriteFieldBegin("deletedCaseIDS", thrift.LIST, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.DeletedCaseIDS)); err != nil {
			return err
		}
		for _, v := range p.DeletedCaseIDS {
			if err := oprot.WriteI64(v); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteCaseDataResp) writeField253(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *DeleteCaseDataResp) writeField254(oprot thrift.TProtocol) (err error) {
	if p.IsSetMsg() {
		if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Msg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *DeleteCaseDataResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *DeleteCaseDataResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteCaseDataResp(%+v)", *p)

}

type CheckCaseDuplicateReq struct {
	BizCtx *infra.BizCtx `thrift:"bizCtx,1,optional" form:"bizCtx" json:"bizCtx,omitempty" query:"bizCtx"`
	// case名称
	CaseName            *string                 `thrift:"caseName,2,optional" form:"caseName" json:"caseName,omitempty" query:"caseName"`
	BizComponentSubject *infra.ComponentSubject `thrift:"bizComponentSubject,3,optional" form:"bizComponentSubject" json:"bizComponentSubject,omitempty" query:"bizComponentSubject"`
	Base                *base.Base              `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewCheckCaseDuplicateReq() *CheckCaseDuplicateReq {
	return &CheckCaseDuplicateReq{}
}

func (p *CheckCaseDuplicateReq) InitDefault() {
}

var CheckCaseDuplicateReq_BizCtx_DEFAULT *infra.BizCtx

func (p *CheckCaseDuplicateReq) GetBizCtx() (v *infra.BizCtx) {
	if !p.IsSetBizCtx() {
		return CheckCaseDuplicateReq_BizCtx_DEFAULT
	}
	return p.BizCtx
}

var CheckCaseDuplicateReq_CaseName_DEFAULT string

func (p *CheckCaseDuplicateReq) GetCaseName() (v string) {
	if !p.IsSetCaseName() {
		return CheckCaseDuplicateReq_CaseName_DEFAULT
	}
	return *p.CaseName
}

var CheckCaseDuplicateReq_BizComponentSubject_DEFAULT *infra.ComponentSubject

func (p *CheckCaseDuplicateReq) GetBizComponentSubject() (v *infra.ComponentSubject) {
	if !p.IsSetBizComponentSubject() {
		return CheckCaseDuplicateReq_BizComponentSubject_DEFAULT
	}
	return p.BizComponentSubject
}

var CheckCaseDuplicateReq_Base_DEFAULT *base.Base

func (p *CheckCaseDuplicateReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return CheckCaseDuplicateReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_CheckCaseDuplicateReq = map[int16]string{
	1:   "bizCtx",
	2:   "caseName",
	3:   "bizComponentSubject",
	255: "Base",
}

func (p *CheckCaseDuplicateReq) IsSetBizCtx() bool {
	return p.BizCtx != nil
}

func (p *CheckCaseDuplicateReq) IsSetCaseName() bool {
	return p.CaseName != nil
}

func (p *CheckCaseDuplicateReq) IsSetBizComponentSubject() bool {
	return p.BizComponentSubject != nil
}

func (p *CheckCaseDuplicateReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *CheckCaseDuplicateReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckCaseDuplicateReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckCaseDuplicateReq) ReadField1(iprot thrift.TProtocol) error {
	_field := infra.NewBizCtx()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizCtx = _field
	return nil
}
func (p *CheckCaseDuplicateReq) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CaseName = _field
	return nil
}
func (p *CheckCaseDuplicateReq) ReadField3(iprot thrift.TProtocol) error {
	_field := infra.NewComponentSubject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizComponentSubject = _field
	return nil
}
func (p *CheckCaseDuplicateReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *CheckCaseDuplicateReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CheckCaseDuplicateReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckCaseDuplicateReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizCtx() {
		if err = oprot.WriteFieldBegin("bizCtx", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizCtx.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckCaseDuplicateReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCaseName() {
		if err = oprot.WriteFieldBegin("caseName", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.CaseName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckCaseDuplicateReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizComponentSubject() {
		if err = oprot.WriteFieldBegin("bizComponentSubject", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizComponentSubject.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CheckCaseDuplicateReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckCaseDuplicateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckCaseDuplicateReq(%+v)", *p)

}

type CheckCaseDuplicateResp struct {
	IsPass *bool `thrift:"isPass,1,optional" form:"isPass" json:"isPass,omitempty" query:"isPass"`
	// 当pass=false时，给出具体的校验不通过的原因
	FailReason *string        `thrift:"failReason,2,optional" form:"failReason" json:"failReason,omitempty" query:"failReason"`
	FailCode   *int32         `thrift:"failCode,3,optional" form:"failCode" json:"failCode,omitempty" query:"failCode"`
	Code       *int32         `thrift:"code,253,optional" form:"code" json:"code,omitempty" query:"code"`
	Msg        *string        `thrift:"msg,254,optional" form:"msg" json:"msg,omitempty" query:"msg"`
	BaseResp   *base.BaseResp `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewCheckCaseDuplicateResp() *CheckCaseDuplicateResp {
	return &CheckCaseDuplicateResp{}
}

func (p *CheckCaseDuplicateResp) InitDefault() {
}

var CheckCaseDuplicateResp_IsPass_DEFAULT bool

func (p *CheckCaseDuplicateResp) GetIsPass() (v bool) {
	if !p.IsSetIsPass() {
		return CheckCaseDuplicateResp_IsPass_DEFAULT
	}
	return *p.IsPass
}

var CheckCaseDuplicateResp_FailReason_DEFAULT string

func (p *CheckCaseDuplicateResp) GetFailReason() (v string) {
	if !p.IsSetFailReason() {
		return CheckCaseDuplicateResp_FailReason_DEFAULT
	}
	return *p.FailReason
}

var CheckCaseDuplicateResp_FailCode_DEFAULT int32

func (p *CheckCaseDuplicateResp) GetFailCode() (v int32) {
	if !p.IsSetFailCode() {
		return CheckCaseDuplicateResp_FailCode_DEFAULT
	}
	return *p.FailCode
}

var CheckCaseDuplicateResp_Code_DEFAULT int32

func (p *CheckCaseDuplicateResp) GetCode() (v int32) {
	if !p.IsSetCode() {
		return CheckCaseDuplicateResp_Code_DEFAULT
	}
	return *p.Code
}

var CheckCaseDuplicateResp_Msg_DEFAULT string

func (p *CheckCaseDuplicateResp) GetMsg() (v string) {
	if !p.IsSetMsg() {
		return CheckCaseDuplicateResp_Msg_DEFAULT
	}
	return *p.Msg
}

var CheckCaseDuplicateResp_BaseResp_DEFAULT *base.BaseResp

func (p *CheckCaseDuplicateResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return CheckCaseDuplicateResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_CheckCaseDuplicateResp = map[int16]string{
	1:   "isPass",
	2:   "failReason",
	3:   "failCode",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *CheckCaseDuplicateResp) IsSetIsPass() bool {
	return p.IsPass != nil
}

func (p *CheckCaseDuplicateResp) IsSetFailReason() bool {
	return p.FailReason != nil
}

func (p *CheckCaseDuplicateResp) IsSetFailCode() bool {
	return p.FailCode != nil
}

func (p *CheckCaseDuplicateResp) IsSetCode() bool {
	return p.Code != nil
}

func (p *CheckCaseDuplicateResp) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *CheckCaseDuplicateResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *CheckCaseDuplicateResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CheckCaseDuplicateResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CheckCaseDuplicateResp) ReadField1(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.IsPass = _field
	return nil
}
func (p *CheckCaseDuplicateResp) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FailReason = _field
	return nil
}
func (p *CheckCaseDuplicateResp) ReadField3(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.FailCode = _field
	return nil
}
func (p *CheckCaseDuplicateResp) ReadField253(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *CheckCaseDuplicateResp) ReadField254(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Msg = _field
	return nil
}
func (p *CheckCaseDuplicateResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *CheckCaseDuplicateResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CheckCaseDuplicateResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CheckCaseDuplicateResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsPass() {
		if err = oprot.WriteFieldBegin("isPass", thrift.BOOL, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.IsPass); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CheckCaseDuplicateResp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailReason() {
		if err = oprot.WriteFieldBegin("failReason", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.FailReason); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CheckCaseDuplicateResp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFailCode() {
		if err = oprot.WriteFieldBegin("failCode", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.FailCode); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CheckCaseDuplicateResp) writeField253(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *CheckCaseDuplicateResp) writeField254(oprot thrift.TProtocol) (err error) {
	if p.IsSetMsg() {
		if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Msg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *CheckCaseDuplicateResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *CheckCaseDuplicateResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckCaseDuplicateResp(%+v)", *p)

}

type GetSchemaByIDReq struct {
	// 业务信息
	BizCtx              *infra.BizCtx           `thrift:"bizCtx,1,optional" form:"bizCtx" json:"bizCtx,omitempty" query:"bizCtx"`
	BizComponentSubject *infra.ComponentSubject `thrift:"bizComponentSubject,2,optional" form:"bizComponentSubject" json:"bizComponentSubject,omitempty" query:"bizComponentSubject"`
	Base                *base.Base              `thrift:"Base,255,optional" form:"Base" json:"Base,omitempty" query:"Base"`
}

func NewGetSchemaByIDReq() *GetSchemaByIDReq {
	return &GetSchemaByIDReq{}
}

func (p *GetSchemaByIDReq) InitDefault() {
}

var GetSchemaByIDReq_BizCtx_DEFAULT *infra.BizCtx

func (p *GetSchemaByIDReq) GetBizCtx() (v *infra.BizCtx) {
	if !p.IsSetBizCtx() {
		return GetSchemaByIDReq_BizCtx_DEFAULT
	}
	return p.BizCtx
}

var GetSchemaByIDReq_BizComponentSubject_DEFAULT *infra.ComponentSubject

func (p *GetSchemaByIDReq) GetBizComponentSubject() (v *infra.ComponentSubject) {
	if !p.IsSetBizComponentSubject() {
		return GetSchemaByIDReq_BizComponentSubject_DEFAULT
	}
	return p.BizComponentSubject
}

var GetSchemaByIDReq_Base_DEFAULT *base.Base

func (p *GetSchemaByIDReq) GetBase() (v *base.Base) {
	if !p.IsSetBase() {
		return GetSchemaByIDReq_Base_DEFAULT
	}
	return p.Base
}

var fieldIDToName_GetSchemaByIDReq = map[int16]string{
	1:   "bizCtx",
	2:   "bizComponentSubject",
	255: "Base",
}

func (p *GetSchemaByIDReq) IsSetBizCtx() bool {
	return p.BizCtx != nil
}

func (p *GetSchemaByIDReq) IsSetBizComponentSubject() bool {
	return p.BizComponentSubject != nil
}

func (p *GetSchemaByIDReq) IsSetBase() bool {
	return p.Base != nil
}

func (p *GetSchemaByIDReq) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSchemaByIDReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetSchemaByIDReq) ReadField1(iprot thrift.TProtocol) error {
	_field := infra.NewBizCtx()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizCtx = _field
	return nil
}
func (p *GetSchemaByIDReq) ReadField2(iprot thrift.TProtocol) error {
	_field := infra.NewComponentSubject()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BizComponentSubject = _field
	return nil
}
func (p *GetSchemaByIDReq) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBase()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Base = _field
	return nil
}

func (p *GetSchemaByIDReq) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetSchemaByIDReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSchemaByIDReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizCtx() {
		if err = oprot.WriteFieldBegin("bizCtx", thrift.STRUCT, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizCtx.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetSchemaByIDReq) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetBizComponentSubject() {
		if err = oprot.WriteFieldBegin("bizComponentSubject", thrift.STRUCT, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BizComponentSubject.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *GetSchemaByIDReq) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBase() {
		if err = oprot.WriteFieldBegin("Base", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Base.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetSchemaByIDReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSchemaByIDReq(%+v)", *p)

}

type GetSchemaByIDResp struct {
	// Json格式的组件input信息，与Input Json Schema保持一致，不包含Value值信息
	SchemaJson *string        `thrift:"schemaJson,1,optional" form:"schemaJson" json:"schemaJson,omitempty" query:"schemaJson"`
	Code       *int32         `thrift:"code,253,optional" form:"code" json:"code,omitempty" query:"code"`
	Msg        *string        `thrift:"msg,254,optional" form:"msg" json:"msg,omitempty" query:"msg"`
	BaseResp   *base.BaseResp `thrift:"BaseResp,255,optional" form:"BaseResp" json:"BaseResp,omitempty" query:"BaseResp"`
}

func NewGetSchemaByIDResp() *GetSchemaByIDResp {
	return &GetSchemaByIDResp{}
}

func (p *GetSchemaByIDResp) InitDefault() {
}

var GetSchemaByIDResp_SchemaJson_DEFAULT string

func (p *GetSchemaByIDResp) GetSchemaJson() (v string) {
	if !p.IsSetSchemaJson() {
		return GetSchemaByIDResp_SchemaJson_DEFAULT
	}
	return *p.SchemaJson
}

var GetSchemaByIDResp_Code_DEFAULT int32

func (p *GetSchemaByIDResp) GetCode() (v int32) {
	if !p.IsSetCode() {
		return GetSchemaByIDResp_Code_DEFAULT
	}
	return *p.Code
}

var GetSchemaByIDResp_Msg_DEFAULT string

func (p *GetSchemaByIDResp) GetMsg() (v string) {
	if !p.IsSetMsg() {
		return GetSchemaByIDResp_Msg_DEFAULT
	}
	return *p.Msg
}

var GetSchemaByIDResp_BaseResp_DEFAULT *base.BaseResp

func (p *GetSchemaByIDResp) GetBaseResp() (v *base.BaseResp) {
	if !p.IsSetBaseResp() {
		return GetSchemaByIDResp_BaseResp_DEFAULT
	}
	return p.BaseResp
}

var fieldIDToName_GetSchemaByIDResp = map[int16]string{
	1:   "schemaJson",
	253: "code",
	254: "msg",
	255: "BaseResp",
}

func (p *GetSchemaByIDResp) IsSetSchemaJson() bool {
	return p.SchemaJson != nil
}

func (p *GetSchemaByIDResp) IsSetCode() bool {
	return p.Code != nil
}

func (p *GetSchemaByIDResp) IsSetMsg() bool {
	return p.Msg != nil
}

func (p *GetSchemaByIDResp) IsSetBaseResp() bool {
	return p.BaseResp != nil
}

func (p *GetSchemaByIDResp) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 253:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField253(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 254:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField254(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 255:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField255(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_GetSchemaByIDResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *GetSchemaByIDResp) ReadField1(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SchemaJson = _field
	return nil
}
func (p *GetSchemaByIDResp) ReadField253(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Code = _field
	return nil
}
func (p *GetSchemaByIDResp) ReadField254(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Msg = _field
	return nil
}
func (p *GetSchemaByIDResp) ReadField255(iprot thrift.TProtocol) error {
	_field := base.NewBaseResp()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.BaseResp = _field
	return nil
}

func (p *GetSchemaByIDResp) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetSchemaByIDResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField253(oprot); err != nil {
			fieldId = 253
			goto WriteFieldError
		}
		if err = p.writeField254(oprot); err != nil {
			fieldId = 254
			goto WriteFieldError
		}
		if err = p.writeField255(oprot); err != nil {
			fieldId = 255
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *GetSchemaByIDResp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSchemaJson() {
		if err = oprot.WriteFieldBegin("schemaJson", thrift.STRING, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.SchemaJson); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *GetSchemaByIDResp) writeField253(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err = oprot.WriteFieldBegin("code", thrift.I32, 253); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Code); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 253 end error: ", p), err)
}
func (p *GetSchemaByIDResp) writeField254(oprot thrift.TProtocol) (err error) {
	if p.IsSetMsg() {
		if err = oprot.WriteFieldBegin("msg", thrift.STRING, 254); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Msg); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 254 end error: ", p), err)
}
func (p *GetSchemaByIDResp) writeField255(oprot thrift.TProtocol) (err error) {
	if p.IsSetBaseResp() {
		if err = oprot.WriteFieldBegin("BaseResp", thrift.STRUCT, 255); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.BaseResp.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 255 end error: ", p), err)
}

func (p *GetSchemaByIDResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSchemaByIDResp(%+v)", *p)

}
