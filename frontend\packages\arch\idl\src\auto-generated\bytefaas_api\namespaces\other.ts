/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export enum ChargeItemType {
  ChargeItemTypeNew = 1,
  ChargeItemTypeExpand = 2,
  ChargeItemTypeShrink = 3,
  ChargeItemTypeUpgrade = 4,
  ChargeItemTypeDowngrade = 5,
  ChargeItemTypeRemove = 6,
}

export interface ByteTree {
  /** 必填
rds传_read/_write两个psm，其他产品传一个 */
  PSMs?: Array<string>;
  /** TreeOperationCreate/TreeOperationDelete 时必填
服务树provider */
  Provider?: string;
  /** 需要挂载的服务树的位置 */
  ParentNodeID?: Int64;
  /** 租户ID，用于选账号逻辑 */
  TenantID?: string;
  /** 环境：boe/prod/ppe */
  Env?: string;
  /** 对应的region，比如cn */
  Region?: string;
  /** 对应的partition，比如cn */
  Partition?: string;
  /** TreeOperationCreate 时必填
控制面对应资源的链接 */
  ResourceLink?: ResourceLink;
  /** 挂树操作人 */
  OpUser?: string;
  /** TreeOperationCreate 时选填
服务树描述 */
  Description?: string;
  /** 该资源初始化时候IAM的owner的个人账号 */
  OwnerList?: Array<string>;
  /** 该资源初始化时候IAM的owner的服务账号 */
  ServiceAccountList?: Array<string>;
  /** 服务树标签 */
  Tags?: Record<string, string>;
  /** 仅MQ挂载group的时候需要 */
  GroupName?: string;
  /** 仅MQ挂载topic的时候需要 */
  TopicName?: string;
}

/** 配置单 */
export interface ChargeItem {
  Flavor: string;
  BeforeValue: number;
  AfterValue: number;
  ChargeItemType: ChargeItemType;
  /** 根据业务是否按VDC分配quota而定 */
  VDC?: string;
  /** babi售卖区，有的计费项比较奇怪，这里支持直接传值 */
  SalesArea?: string;
  IsForPrice?: boolean;
}

export interface DataMessage18 {}

export interface DataMessage20 {
  format_regions_backend?: Array<DataMessage20FormatRegionsBackendMessage>;
}

export interface DataMessage20FormatRegionsBackendMessage {
  backend?: boolean;
  frontend?: boolean;
  region?: string;
}

export interface Error {
  CodeN: Int64;
  Code: string;
  Message: string;
}

export interface GetOrderConfigRequest {
  env: string;
  function_id: string;
  parent_node_id: Int64;
  psm: string;
  service_id: string;
  region: string;
  cluster?: OrderConfigCluster;
  mq_event?: OrderConfigMqEvent;
}

export interface GetOrderConfigResp {
  ResponseMetaData: ResponseMetaData;
  /** 用于挂树预检查和选火山账号使用 */
  ByteTree: ByteTree;
  /** 10: optional ark.ArkData ArkData
99: optional list<OrderConfig> OrderConfigs // 兼容tce等组件可以跨region下单 */
  OrderConfig?: OrderConfig;
}

export interface getRegionsEnabledRequest {}

export interface GetRegionsEnabledResponse {
  code?: number;
  data?: DataMessage20;
  error?: string;
}

export interface getRegionZonesRequest {}

export interface GetRegionZonesResponse {
  code?: number;
  data?: Record<string, Array<string>>;
  error?: string;
}

export interface getRuntimeRequest {}

export interface GetRuntimeResponse {
  code?: number;
  /** region will be key name */
  data?: Record<string, Array<Runtime>>;
  error?: string;
}

export interface OrderConfig {
  PID: string;
  ProductID: string;
  Region: string;
  NeedConvertToInnerChargeItem?: boolean;
  ChargeItems: Array<ChargeItem>;
}

export interface OrderConfigCluster {
  http_trigger_disable?: boolean;
  pod_type: string;
  replica_limit: Record<string, common.PodReplicaLimit>;
  resource_limit: common.Resource;
}

export interface OrderConfigMqEvent {
  resource: common.Resource;
  type: string;
}

export interface PrescanRequest {
  hours: string;
  /** region name */
  region: string;
  /** zone */
  zone: string;
}

export interface PrescanResponse {
  code?: number;
  data?: DataMessage18;
  error?: string;
}

export interface ResourceLink {
  Schema?: string;
  Action?: string;
  /** 在Thrift中没有完全直接对应的any原生类型 */
  Params?: Record<string, string>;
}

export interface ResponseMetaData {
  RequestId: string;
  Service: string;
  Error: Error;
}

export interface Runtime {
  category?: string;
  default_template?: string;
  function_type?: string;
  key?: string;
  name?: string;
  show?: boolean;
  supported_protocols?: Array<string>;
  supported_domains?: Array<string>;
}
/* eslint-enable */
