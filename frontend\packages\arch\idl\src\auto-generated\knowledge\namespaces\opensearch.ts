/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as common from './common';

export type Int64 = string | number;

export interface GetConfigRequest {
  storage_config_id: string;
  Base?: base.Base;
}

export interface GetConfigResponse {
  config: common.OpenSearchConfig;
  BaseResp?: base.BaseResp;
}

export interface GetInstancesRequest {
  region: string;
  Base?: base.Base;
}

export interface GetInstancesResponse {
  configs: Array<common.OpenSearchConfig>;
  BaseResp?: base.BaseResp;
}

export interface OpenPublicAddressRequest {
  config: common.OpenSearchConfig;
  Base?: base.Base;
}

export interface OpenPublicAddressResponse {
  BaseResp?: base.BaseResp;
}

export interface SetConfigRequest {
  storage_config_id: string;
  /** 只有用户名密码的修改会生效 */
  config: common.OpenSearchConfig;
  Base?: base.Base;
}

export interface SetConfigResponse {
  BaseResp?: base.BaseResp;
}

export interface TestConnectionRequest {
  config: common.OpenSearchConfig;
  Base?: base.Base;
}

export interface TestConnectionResponse {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
