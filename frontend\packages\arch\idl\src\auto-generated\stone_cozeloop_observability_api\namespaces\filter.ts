/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface FieldOptions {
  i64_list?: Array<string>;
  f64_list?: Array<number>;
  string_list?: Array<string>;
}

export interface FilterField {
  field_name?: string;
  field_type?: string;
  values?: Array<string>;
  query_type?: string;
  query_and_or?: string;
  sub_filter?: FilterFields;
}

export interface FilterFields {
  query_and_or?: string;
  filter_fields: Array<FilterField>;
}
/* eslint-enable */
