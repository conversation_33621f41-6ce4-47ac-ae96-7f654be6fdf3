/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum GenFileType {
  PDF = 1,
  /** WIP */
  DOCX = 2,
}

export enum OperateSandboxAction {
  DeleteCookie = 1,
}

export interface CodeRepairRequest {
  uri?: string;
  error_msg?: string;
}

export interface CodeRepairResponse {
  code?: Int64;
  msg?: string;
  data?: CodeRepairResponseData;
}

export interface CodeRepairResponseData {}

export interface DeleteCookieInfo {
  url_code?: string;
}

export interface GenDocRequest {
  task_id: string;
  target_file_type?: GenFileType;
  original_file_uri?: string;
}

export interface GenDocResponse {
  code?: Int64;
  msg?: string;
  data?: GenDocResponseData;
}

export interface GenDocResponseData {
  target_file_url?: string;
}

export interface GetUrlRequest {
  uri: string;
  /** Expiration time in seconds, default 3600 seconds, max 7 days, range [1, 604800] */
  expire_seconds?: Int64;
  is_short_url?: boolean;
}

export interface GetUrlResponse {
  code?: Int64;
  msg?: string;
  data?: GetUrlResponseData;
}

export interface GetUrlResponseData {
  url?: string;
}

export interface GetWebUrlRequest {
  original_url?: string;
  exchange_for_short_url?: boolean;
  exchange_for_qr_code?: boolean;
}

export interface GetWebUrlResponse {
  code?: Int64;
  msg?: string;
  data?: GetWebUrlResponseData;
}

export interface GetWebUrlResponseData {
  short_url?: string;
  qr_code_url?: string;
}

export interface ImageInfo {
  image_url?: string;
}

export interface OperateSandboxRequest {
  task_id?: string;
  action?: OperateSandboxAction;
  delete_cookie_info?: DeleteCookieInfo;
}

export interface OperateSandboxResponse {
  code?: Int64;
  msg?: string;
}

export interface SearchImageRequest {
  query: string;
  count?: number;
}

export interface SearchImageResponse {
  code?: Int64;
  msg?: string;
  data?: SearchImageResponseData;
}

export interface SearchImageResponseData {
  image_list?: Array<ImageInfo>;
}

export interface Text2ImageRequest {
  prompt: string;
  width?: Int64;
  height?: Int64;
}

export interface Text2ImageResponse {
  code?: Int64;
  msg?: string;
}
/* eslint-enable */
