/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export interface AuthorizeConsentRequest {
  authorize_key: string;
  consent: boolean;
}

export interface AuthorizeConsentRequest2 {
  authorize_key: string;
  consent: boolean;
}

export interface AuthorizeConsentResponse {
  data: AuthorizeConsentResponseData;
}

export interface AuthorizeConsentResponse2 {
  code: number;
  msg: string;
  data: AuthorizeConsentResponseData;
}

export interface AuthorizeConsentResponseData {
  redirect_uri?: string;
}

export interface DeviceVerificationRequest {
  user_code: string;
}

export interface DeviceVerificationRequest2 {
  user_code: string;
}

export interface DeviceVerificationResponse {
  data: DeviceVerificationResponseData;
}

export interface DeviceVerificationResponse2 {
  code: number;
  msg: string;
  data: DeviceVerificationResponseData;
}

export interface DeviceVerificationResponseData {
  redirect_uri: string;
}

export interface InlineResponse200 {
  code: number;
  msg: string;
  data: AuthorizeConsentResponseData;
}

export interface InlineResponse2001 {
  code: number;
  msg: string;
  data: DeviceVerificationResponseData;
}

export interface RespBaseModel {
  code: number;
  msg: string;
}
/* eslint-enable */
