/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

/** AI Sandbox */
export interface AiSandbox {
  /** Unique identifier for the sandbox */
  sandbox_id: string;
  /** Service ID associated with this sandbox */
  service_id: string;
  /** Type of sandbox (code or browser) */
  type: string;
  /** PSM identifier for the sandbox */
  psm: string;
  /** Environment name where the sandbox runs */
  env_name?: string;
  /** Display name of the sandbox */
  name: string;
  /** Description of the sandbox purpose */
  description?: string;
  /** Owner of the sandbox */
  owner: string;
  /** List of users subscribed to this sandbox */
  subscribers?: Array<string>;
  /** Timestamp when sandbox was created (Unix timestamp) */
  created_at: Int64;
  /** Timestamp when sandbox was last updated (Unix timestamp) */
  updated_at: Int64;
  /** Whether the sandbox is deleted */
  is_deleted: boolean;
  /** Timestamp when sandbox was deleted (Unix timestamp) */
  deleted_at: Int64;
  /** User who deleted the sandbox */
  deleted_by: string;
  /** Optional type-specific extras */
  code_sandbox_extras?: CodeSandboxExtras;
  /** Browser sandbox specific configuration */
  browser_sandbox_extras?: BrowserSandboxExtras;
  /** MCP (Model Context Protocol) server ID */
  mcp_server_id?: string;
  /** Current status of the service */
  service_status?: string;
  /** List of sandbox administrators */
  admins?: Array<string>;
  /** List of users who can authorize sandbox operations */
  authorizers?: Array<string>;
}

/** AI Sandbox network ACL */
export interface AiSandboxAcl {
  /** list of allowed rules, as regex patterns of urls */
  allowed_rules?: Array<string>;
  /** list of blocked rules, as regex patterns of urls */
  blocked_rules?: Array<string>;
}

/** Currently empty in the implementation */
export interface BrowserSandboxExtras {}

export interface CodeSandboxExtras {}

export interface CreateAiSandboxAclBpmTicketRequest {
  /** ID of the sandbox to create BPM ticket for */
  sandbox_id: string;
  /** list of allowed rules, as regex patterns of urls */
  allowed_rules?: Array<string>;
  /** list of blocked rules, as regex patterns of urls */
  blocked_rules?: Array<string>;
  /** reason for the update request */
  reason?: string;
}

export interface CreateAiSandboxAclBpmTicketResponse {
  code?: number;
  error?: string;
  data?: CreateAiSandboxAclBpmTicketResponseData;
}

export interface CreateAiSandboxAclBpmTicketResponseData {
  /** link to the BPM ticket for the ACL update request
Once approved, BPM will callback to the service to apply the ACL changes */
  bpm_link: string;
}

export interface CreateAiSandboxRequest {
  /** PSM identifier for the new sandbox */
  psm: string;
  /** Display name for the sandbox */
  name: string;
  /** "code" or "browser" */
  type: string;
  /** Description of the sandbox purpose */
  description?: string;
  /** Parent PSM ID if this is a child service */
  psm_parent_id?: number;
  /** Owner of the sandbox */
  owner: string;
  /** Region where the sandbox will be deployed */
  region: string;
  /** Runtime environment for the sandbox */
  runtime: string;
  /** Environment variables for the sandbox */
  format_envs?: Array<common.FormatEnvs>;
  /** List of sandbox administrators */
  admins?: Array<string>;
  /** List of users who can authorize sandbox operations */
  authorizers?: Array<string>;
  /** Service level configuration */
  service_level?: string;
  /** Purpose of the service */
  service_purpose?: string;
  /** Source type for the sandbox code */
  source_type: string;
  /** Source location for the sandbox code */
  source: string;
  /** MCP server ID to associate with the sandbox */
  mcp_server_id?: string;
}

export interface CreateAiSandboxResponse {
  code?: number;
  error?: string;
  /** Created sandbox data */
  data?: AiSandbox;
}

export interface DeleteAiSandboxResponse {
  code?: number;
  error?: string;
}

export interface GetAiSandboxAclRequest {
  /** ID of the sandbox to get ACL for */
  sandbox_id: string;
}

export interface GetAiSandboxAclResponse {
  code?: number;
  error?: string;
  data?: AiSandboxAcl;
}

export interface GetAiSandboxRequest {
  /** ID of the sandbox to retrieve */
  sandbox_id: string;
}

export interface GetAiSandboxResponse {
  code?: number;
  error?: string;
  /** Retrieved sandbox data */
  data?: AiSandbox;
}

export interface ListAiSandboxesRequest {
  /** search by name */
  search?: string;
  /** search by owner */
  owner?: string;
  /** search by subscriber */
  subscriber?: string;
  /** offset for pagination */
  offset?: number;
  /** limit for pagination */
  limit?: number;
  /** advanced search by key-value pairs */
  advanced_search?: Record<string, string>;
  /** sort by fields */
  sort_by?: string;
  /** search type: all/admin/own/subscribe */
  search_type?: string;
  /** supported search fields: sandbox_id/name/psm */
  search_fields?: string;
}

export interface ListAiSandboxesResponse {
  code?: number;
  error?: string;
  /** List of AI sandboxes */
  data: Array<AiSandbox>;
}

export interface UpdateAiSandboxAclRequest {
  /** ID of the sandbox to update ACL for */
  sandbox_id: string;
  /** list of allowed rules, as regex patterns of urls */
  allowed_rules?: Array<string>;
  /** list of blocked rules, as regex patterns of urls */
  blocked_rules?: Array<string>;
}

export interface UpdateAiSandboxRequest {
  /** ID of the sandbox to update */
  sandbox_id: string;
  /** new name for the sandbox */
  name?: string;
  /** new description for the sandbox */
  description?: string;
  /** new owner for the sandbox */
  owner?: string;
  /** new mcp server id for the sandbox */
  mcp_server_id?: string;
}

export interface UpdateAiSandboxResponse {
  code?: number;
  error?: string;
  /** Updated sandbox data */
  data?: AiSandbox;
}
/* eslint-enable */
