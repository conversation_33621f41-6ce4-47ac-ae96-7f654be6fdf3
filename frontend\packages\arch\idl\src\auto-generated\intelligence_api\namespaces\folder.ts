/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common_struct from './common_struct';

export type Int64 = string | number;

export enum FolderObjectType {
  /** 智能体 */
  Intelligence = 1,
  /** 项目 */
  App = 2,
  /** 插件 */
  Plugin = 3,
  /** 工作流 */
  Workflow = 4,
  /** 知识库 */
  Knowledge = 5,
  /** 卡片 */
  Card = 6,
  /** 提示词 */
  Prompt = 7,
  /** 数据库 */
  Database = 8,
  /** 音色 */
  Voice = 9,
}

export interface FolderCreateData {
  folder_id?: string;
}

export interface FolderCreateRequest {
  /** 空间id */
  space_id: string;
  /** 文件夹类型 */
  type: common_struct.FolderType;
  /** 文件夹名称 */
  name: string;
  /** 描述 */
  description?: string;
  /** 父文件夹id */
  parent_folder_id?: string;
}

export interface FolderCreateResponse {
  data?: FolderCreateData;
  code: Int64;
  msg: string;
}

export interface FolderDeleteRequest {
  folder_id: string;
}

export interface FolderDeleteResponse {
  code: Int64;
  msg: string;
}

export interface FolderMoveRequest {
  folder_id: string;
  /** 要移动到的文件夹下 */
  parent_folder_id: string;
}

export interface FolderMoveResponse {
  code: Int64;
  msg: string;
}

export interface FolderUpdateRequest {
  folder_id: string;
  /** 名称，不传则不更新 */
  name?: string;
  /** 描述，不传则不更新 */
  description?: string;
}

export interface FolderUpdateResponse {
  code: Int64;
  msg: string;
}

export interface GetFolderInfoByIdsRequest {
  /** 空间id */
  space_id?: string;
  /** 每次最多50个 */
  folder_ids?: Array<string>;
  /** 是否获取路径 */
  get_path_info?: boolean;
}

export interface GetFolderInfoByIdsResponse {
  data?: Record<Int64, common_struct.FolderInfo>;
  code: Int64;
  msg: string;
}

export interface GetFolderTreeRequest {
  /** 空间id */
  space_id: string;
  /** 文件夹类型 */
  type: common_struct.FolderType;
}

export interface GetFolderTreeResponse {
  /** 文件夹树形结构 */
  data?: Array<common_struct.FolderInfo>;
  code: Int64;
  msg: string;
}

export interface GetObjectFolderRefData {
  folder_id?: Int64;
}

export interface MoveObjectInfo {
  object_id?: string;
  object_type?: FolderObjectType;
}

export interface MoveObjectToFolderRequest {
  dest_folder_id: string;
  folder_space_id: string;
  /** 移动的资源列表，最大长度50 */
  move_objects?: Array<MoveObjectInfo>;
}

export interface MoveObjectToFolderResponse {
  code: Int64;
  msg: string;
}
/* eslint-enable */
