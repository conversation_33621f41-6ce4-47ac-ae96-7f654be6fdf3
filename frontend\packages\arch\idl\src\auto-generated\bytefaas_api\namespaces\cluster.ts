/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface AutoMeshParams {
  /** Whether service mesh is enabled */
  mesh_enable?: boolean;
  /** Whether HTTP egress through mesh is enabled */
  mesh_http_egress?: boolean;
  /** Whether MongoDB egress through mesh is enabled */
  mesh_mongo_egress?: boolean;
  /** Whether MySQL egress through mesh is enabled */
  mesh_mysql_egress?: boolean;
  /** Whether RPC egress through mesh is enabled */
  mesh_rpc_egress?: boolean;
  /** Percentage of sidecars in the mesh */
  mesh_sidecar_percent?: number;
  /** Whether HTTP ingress through mesh is enabled */
  mesh_http_ingress?: boolean;
  /** Whether RPC ingress through mesh is enabled */
  mesh_rpc_ingress?: boolean;
  /** Whether mesh sidecars are enabled */
  mesh_sidecars_enable?: boolean;
}

export interface ClusterResourceUsageRate {
  /** CPU usage rate for the cluster */
  cpu?: number;
  /** Memory usage rate for the cluster */
  memory?: number;
}

/** complete info of cluster */
export interface ClusterResponseData {
  /** Unique identifier of the cluster */
  id: string;
  /** Service ID that this cluster belongs to */
  service_id?: string;
  /** PSM (Program Service Model) identifier */
  psm?: string;
  /** Name of the cluster */
  cluster: string;
  /** Region where the cluster is deployed */
  region: string;
  /** Function ID associated with this cluster */
  function_id: string;
  /** Cell identifier for the cluster */
  cell?: string;
  /** Code revision number currently deployed */
  code_revision_number?: string;
  /** Code revision ID currently deployed */
  code_revision_id?: string;
  /** Revision number for the cluster configuration */
  revision_number?: number;
  /** Revision ID for the cluster configuration */
  revision_id?: string;
  /** Replica limits per zone or environment */
  replica_limit?: Record<string, common.PodReplicaLimit>;
  /** Resource limits with alias configuration */
  resource_limit?: common.ResourceLimitWithAlias;
  /** Formatted environment variables for the cluster */
  format_envs?: Array<common.FormatEnvs>;
  /** Map of zones that are disabled in this cluster */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Log throttle limits per zone in bytes per second */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** Whether GDPR compliance is enabled */
  gdpr_enable?: boolean;
  /** Whether authentication is enabled */
  auth_enable?: boolean;
  /** Whether ZTI (Zero Trust Infrastructure) is enabled */
  zti_enable?: boolean;
  cors_enable?: boolean;
  /** Whether cold start is disabled, true means disable */
  cold_start_disabled?: boolean;
  /** Whether the cluster runs in async mode */
  async_mode?: boolean;
  /** Whether the cluster is in online mode */
  online_mode?: boolean;
  /** Whether the cluster runs in exclusive mode */
  exclusive_mode?: boolean;
  /** Whether tracing is enabled */
  trace_enable?: boolean;
  /** Whether the cluster only supports IPv6 */
  is_ipv6_only?: boolean;
  /** Whether reserved deployment is enabled */
  reserved_dp_enabled?: boolean;
  /** Routing strategy for the cluster, enums：prefer_reserved, prefer_elastic. */
  routing_strategy?: string;
  /** Whether HTTP triggers are disabled, true means disable */
  http_trigger_disable?: boolean;
  /** Map of aliases for the cluster */
  aliases?: Record<string, common.Alias>;
  /** Environment name for the cluster */
  env_name: string;
  /** List of global KV namespace IDs */
  global_kv_namespace_i_ds?: Array<string>;
  /** Latency timeout in seconds */
  latency_sec?: number;
  /** Initializer timeout in seconds */
  initializer_sec?: number;
  /** Maximum concurrency allowed */
  max_concurrency?: number;
  /** Whether auto-scaling is enabled */
  scale_enabled?: boolean;
  /** Threshold value for scaling decisions */
  scale_threshold?: number;
  /** Type of scaling strategy */
  scale_type?: number;
  /** Current status of the cluster */
  status?: string;
  /** Memory allocation in megabytes */
  memory_mb?: number;
  /** Type of pod configuration */
  pod_type?: string;
  /** Whether async results emit to event bridge */
  async_result_emit_event_bridge?: boolean;
  /** Whether runtime file logging is enabled */
  enable_runtime_file_log?: boolean;
  /** Whether runtime console logging is enabled */
  enable_runtime_console_log?: boolean;
  /** Whether runtime stream logging is enabled */
  enable_runtime_stream_log?: boolean;
  /** Whether runtime Elasticsearch logging is enabled */
  enable_runtime_es_log?: boolean;
  /** Whether runtime JSON logging is enabled */
  enable_runtime_json_log?: boolean;
  /** Whether system stream logging is enabled */
  enable_system_stream_log?: boolean;
  /** Whether system Elasticsearch logging is enabled */
  enable_system_es_log?: boolean;
  /** Runtime stream log rate limit in bytes per second */
  runtime_stream_log_bytes_per_sec?: number;
  /** System stream log rate limit in bytes per second */
  system_stream_log_bytes_per_sec?: number;
  /** General log throttle rate in bytes per second */
  throttle_log_bytes_per_sec?: number;
  /** Stdout log throttle rate in bytes per second */
  throttle_stdout_log_bytes_per_sec?: number;
  /** Stderr log throttle rate in bytes per second */
  throttle_stderr_log_bytes_per_sec?: number;
  /** Whether scaling strategy is enabled */
  enable_scale_strategy?: boolean;
  /** Whether co-locate scheduling is enabled */
  enable_colocate_scheduling?: boolean;
  /** Whether ByteFaaS error responses are disabled */
  bytefaas_error_response_disabled?: boolean;
  /** Whether ByteFaaS response headers are disabled */
  bytefaas_response_header_disabled?: boolean;
  /** Whether gateway routing is enabled */
  gateway_route_enable?: boolean;
  /** Container runtime type */
  container_runtime?: string;
  /** Cold start timeout in seconds */
  cold_start_sec?: number;
  /** Whether scale optimization is enabled */
  enable_scale_optimise?: boolean;
  /** Timestamp when the cluster was created */
  created_at: string;
  /** Timestamp when the cluster was last updated */
  updated_at: string;
  /** Runtime environment for the cluster */
  runtime: string;
  /** Handler function name */
  handler: string;
  /** Initializer function name */
  initializer: string;
  /** Monitoring system unit ID */
  ms_unit_id?: Int64;
  /** List of monitoring system alarm IDs */
  ms_alarm_ids?: Array<string>;
  /** Adaptive concurrency mode setting */
  adaptive_concurrency_mode?: string;
  /** ICM region */
  icm_region?: string;
  /** Network mode configuration */
  network_mode?: string;
  /** Whether dynamic load balancing data reporting is enabled */
  dynamic_load_balancing_data_report_enabled?: boolean;
  /** Whether dynamic load balancing weight is enabled */
  dynamic_load_balancing_weight_enabled?: boolean;
  /** List of VDCs where dynamic load balancing is enabled */
  dynamic_load_balancing_enabled_vdcs?: Array<string>;
  /** Type of dynamic load balancing, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Whether this is a BytePaaS elastic cluster */
  is_bytepaas_elastic_cluster?: boolean;
  /** Whether service discovery is disabled，true means disable */
  disable_service_discovery?: boolean;
  /** Whether deployment is inactive */
  deployment_inactive?: boolean;
  /** Map of zones where deployment is inactive */
  is_this_zone_deployment_inactive?: Record<string, boolean>;
  /** Number of instances running */
  instances_num?: Int64;
  /** All triggers configuration for the cluster */
  triggers?: common.AllTriggers;
  /** Link to log viewer */
  log_link?: string;
  /** Link to stream log viewer */
  stream_log_link?: string;
  /** Link to Argos monitoring */
  argos_link?: string;
  /** Link to Grafana dashboard */
  grafana_link?: string;
  /** List of metrics dashboard links */
  metrics_links?: Array<string>;
  /** Current resource usage rates */
  resource_usage_rate?: ClusterResourceUsageRate;
  /** Map of reserved frozen replicas per zone */
  zone_reserved_frozen_replicas?: Record<string, number>;
  /** Whether resource guarantee is enabled */
  resource_guarantee?: boolean;
  /** Limit for MQ (Message Queue) triggers */
  mq_trigger_limit?: number;
  /** overload_protect_enabled */
  overload_protect_enabled?: boolean;
  /** MQ consumer metadata configuration */
  mq_consumer_meta?: Array<common.ClusterMQConsumerMeta>;
  /** Whether Consul IPv6 registration is enabled */
  enable_consul_ipv6_register?: boolean;
  /** Whether system mount is enabled */
  enable_sys_mount?: boolean;
  /** Whether mounting JWT bundles is disabled, true means disable */
  disable_mount_jwt_bundles?: boolean;
  /** Grace period for pod termination in seconds */
  termination_grace_period_seconds?: number;
  /** Whether this is an MQ application cluster */
  is_mq_app_cluster?: boolean;
  /** Volcengine-specific extensions */
  volc_ext?: ClusterVolcExt;
  /** Whether Consul registration is enabled */
  enable_consul_register?: boolean;
  /** Host uniqueness configuration */
  host_uniq?: common.HostUniq;
  /** Whether the cluster is soft deleted */
  soft_deleted?: boolean;
  /** Whether this is a cron job cluster */
  is_cronjob_cluster?: boolean;
  /** List of active zones */
  active_zones?: Array<string>;
  /** Volume mount configurations */
  volume_mounts?: Array<VolumeMount>;
  /** Maximum retry count for async mode */
  async_mode_max_retry?: number;
  /** User who performed soft deletion */
  soft_deleted_by?: string;
  /** Timestamp when soft deletion occurred */
  soft_deleted_at?: string;
  /** Whether service level degradation is enabled */
  service_level_degrade?: boolean;
  /** Whether service level is unreliable */
  service_level_unreliable?: boolean;
  /** Canary replica limits per zone */
  zone_canary_replica_limit?: Record<string, number>;
  /** Whether cluster is in cell migration */
  in_cell_migration?: boolean;
  /** Whether session requests are enabled */
  enable_session_request?: boolean;
  /** Frozen CPU allocation in millicores */
  frozen_cpu_milli?: number;
  /** enable privileged in pod mode */
  privileged?: boolean;
}

export interface ClusterVolcExt {
  /** Volcengine account ID associated with the cluster */
  account_id: string;
}

export interface CreateClusterRequest {
  /** async mode. 异步模式 */
  async_mode?: boolean;
  /** auth switch. 鉴权开关 */
  auth_enable?: boolean;
  /** cluster name, starts with faas-. 集群名 */
  cluster: string;
  /** ID of code revision. 部署代码版本 ID */
  code_revision_id?: string;
  /** number of code revision. 部署代码版本号 */
  code_revision_number?: string;
  /** cold start switch. 冷启动开关, true 为关闭 */
  cold_start_disabled?: boolean;
  /** CORS switch. CORS 开关 */
  cors_enable?: boolean;
  /** Whether co-locate scheduling is enabled */
  enable_colocate_scheduling?: boolean;
  /** Whether scaling strategy is enabled */
  enable_scale_strategy?: boolean;
  /** exclusive mode. 独占模式 */
  exclusive_mode?: boolean;
  /** Environment variables configuration */
  format_envs?: Array<common.FormatEnvs>;
  /** Whether gateway routing is enabled */
  gateway_route_enable?: boolean;
  /** GDPR switch. GDPR 鉴权开关 */
  gdpr_enable?: boolean;
  /** List of global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** Whether HTTP triggers are disabled, true means disable */
  http_trigger_disable?: boolean;
  /** Initializer timeout in seconds */
  initializer_sec?: number;
  /** Whether cluster only supports IPv6 */
  is_ipv6_only?: boolean;
  /** disable zones in a region */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Function execution timeout in seconds */
  latency_sec?: number;
  /** Maximum concurrent executions allowed */
  max_concurrency?: number;
  /** network mode, Optional values: empty string,bridge */
  network_mode?: string;
  /** region name */
  region: string;
  /** function reserved mode switch. 函数预留模式开关 */
  reserved_dp_enabled?: boolean;
  /** Revision ID for the cluster configuration */
  revision_id?: string;
  /** Revision number for the cluster configuration */
  revision_number?: number;
  /** function routing strategy, enums：prefer_reserved, prefer_elastic. 函数路由调度策略 */
  routing_strategy?: string;
  /** Whether auto-scaling is enabled */
  scale_enabled?: boolean;
  /** Threshold for scaling decisions */
  scale_threshold?: number;
  /** Type of scaling strategy */
  scale_type?: number;
  /** ID of service */
  service_id: string;
  /** Whether tracing is enabled */
  trace_enable?: boolean;
  /** zone throttle log bytes */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** ZTI switch. ZTI 鉴权开关 */
  zti_enable?: boolean;
  /** Whether cluster is in online mode */
  online_mode?: boolean;
  /** Whether runtime file logging is enabled */
  enable_runtime_file_log?: boolean;
  /** Whether runtime console logging is enabled */
  enable_runtime_console_log?: boolean;
  /** Whether runtime stream logging is enabled */
  enable_runtime_stream_log?: boolean;
  /** Whether runtime Elasticsearch logging is enabled */
  enable_runtime_es_log?: boolean;
  /** Whether runtime JSON logging is enabled */
  enable_runtime_json_log?: boolean;
  /** Whether system stream logging is enabled */
  enable_system_stream_log?: boolean;
  /** Whether system Elasticsearch logging is enabled */
  enable_system_es_log?: boolean;
  /** Runtime stream log rate limit in bytes per second */
  runtime_stream_log_bytes_per_sec?: number;
  /** System stream log rate limit in bytes per second */
  system_stream_log_bytes_per_sec?: number;
  /** Resource limits for the cluster */
  resource_limit?: common.ResourceLimit;
  /** Type of pod configuration */
  pod_type?: string;
  /** Whether to enable reserve frozen instances */
  enable_reserve_frozen_instance?: boolean;
  /** Command to run for the cluster */
  cluster_run_cmd?: string;
  /** Whether service discovery is disabled，true means disable */
  disable_service_discovery?: boolean;
  /** Whether async results emit to event bridge */
  async_result_emit_event_bridge?: boolean;
  /** Whether resource guarantee is enabled */
  resource_guarantee?: boolean;
  /** Limit for MQ triggers */
  mq_trigger_limit?: number;
  /** Cell identifier for deployment */
  cell?: string;
  /** Whether lazy loading is enabled */
  lazyload?: boolean;
  /** Whether image lazy loading is enabled */
  image_lazyload?: boolean;
  /** Initializer function name */
  initializer?: string;
  /** Handler function name */
  handler?: string;
  /** Run command for the function */
  run_cmd?: string;
  /** Whether log throttling is enabled */
  throttle_log_enabled?: boolean;
  /** Adaptive concurrency mode setting */
  adaptive_concurrency_mode?: string;
  /** Environment name */
  env_name?: string;
  /** Container runtime type */
  container_runtime?: string;
  /** Protocol used by the function */
  protocol?: string;
  /** overload_protect_enabled */
  overload_protect_enabled?: boolean;
  /** MQ consumer metadata configuration */
  mq_consumer_meta?: Array<common.ClusterMQConsumerMeta>;
  /** Whether Consul IPv6 registration is enabled */
  enable_consul_ipv6_register?: boolean;
  /** Whether system mount is enabled */
  enable_sys_mount?: boolean;
  /** Whether mounting JWT bundles is disabled, true means disable */
  disable_mount_jwt_bundles?: boolean;
  /** Grace period for pod termination in seconds */
  termination_grace_period_seconds?: number;
  /** Whether Consul registration is enabled */
  enable_consul_register?: boolean;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
  /** Host uniqueness configuration */
  host_uniq?: common.HostUniq;
  /** Whether service level degradation is enabled */
  service_level_degrade?: boolean;
  /** Whether cluster is in cell migration */
  in_cell_migration?: boolean;
  /** Reserved frozen replicas per zone */
  zone_reserved_frozen_replicas?: Record<string, number>;
  /** Frozen CPU allocation in millicores */
  frozen_cpu_milli?: number;
  /** Whether session requests are enabled */
  enable_session_request?: boolean;
  /** enable privileged in pod mode */
  privileged?: boolean;
}

export interface CreateClusterResponse {
  code?: number;
  data?: ClusterResponseData;
  error?: string;
}

export interface DataMessage85 {
  /** Deployment status of the cluster */
  status?: string;
}

export interface DeleteClusterRequest {
  /** cluster name */
  cluster: string;
  /** region name */
  region: string;
  /** service ID */
  service_id: string;
  /** soft delete cluster if set "true" */
  soft?: boolean;
  /** reason for soft deletion */
  reason?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface DeleteClusterResponse {
  code?: number;
  data?: ClusterResponseData;
  error?: string;
}

export interface GetClusterAutoMeshRequest {
  /** cluster name */
  cluster: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetClusterAutoMeshResponse {
  code?: number;
  data?: AutoMeshParams;
  error?: string;
}

export interface GetClusterDeployedStatusRequest {
  /** cluster name */
  cluster: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface GetClusterDeployedStatusResponse {
  code?: number;
  data?: DataMessage85;
  error?: string;
}

export interface GetClusterListByPsmRequest {
  /** Environment name */
  env: string;
  /** PSM identifier */
  psm: string;
  /** Region name */
  region: string;
}

export interface GetClusterListByPsmResponse {
  code?: number;
  data?: Array<ClusterResponseData>;
  error?: string;
}

export interface GetClusterRequest {
  /** cluster name */
  cluster: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
  /** whether use argos iframe */
  'use-argos-iframe'?: boolean;
  /** with verbose data */
  verbose?: boolean;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetClusterResponse {
  code?: number;
  data?: ClusterResponseData;
  error?: string;
}

export interface GetClustersListRequest {
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
  /** with verbose data */
  verbose?: boolean;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetClustersListResponse {
  code?: number;
  data?: Array<ClusterResponseData>;
  error?: string;
}

export interface GetClustersListWithPaginationRequest {
  /** cluster name */
  cluster?: string;
  /** limit for per page */
  limit?: number;
  /** offset */
  offset?: number;
  /** region name */
  region?: string;
  /** get released cluster in resource_lit page */
  resource_list?: boolean;
  /** fuzzy search in cluster id and cluster name */
  search?: string;
  /** ID of service */
  service_id: string;
  /** with verbose data */
  verbose?: boolean;
  /** filter soft deleted cluster */
  soft_deleted?: boolean;
}

export interface GetClustersListWithPaginationResponse {
  code?: number;
  data?: Array<ClusterResponseData>;
  error?: string;
}

export interface GetVefaasTrafficSchedulingRequest {}

export interface GetVefaasTrafficSchedulingResponse {
  code: number;
  data: VefaasTrafficSchedulingData;
  error: string;
}

export interface QueryReleasedClusterRequest {
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
  /** PSM identifier */
  psm?: string;
  /** Environment name */
  env_name?: string;
  /** Region name */
  region?: string;
  /** Cluster name */
  cluster?: string;
}

export interface QueryReleasedClusterResponse {
  code?: number;
  /** Released service information */
  data?: ServiceResponse;
  error?: string;
}

export interface RestoreClusterRequest {
  /** cluster name */
  cluster: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface RestoreClusterResponse {
  code?: number;
  data?: RestoreClusterResponseData;
  error?: string;
}

export interface RestoreClusterResponseData {
  /** Ticket ID for the restore operation */
  ticket_id?: string;
}

export interface ServiceResponse {
  /** Service unique identifier */
  id?: string;
  /** Service ID */
  service_id?: string;
  /** Service name */
  name?: string;
  /** Service description */
  description?: string;
  /** Handler function name */
  handler?: string;
  /** Initializer function name */
  initializer?: string;
  /** Service administrators */
  admins?: string;
  /** Service owner */
  owner?: string;
  /** PSM identifier */
  psm?: string;
  /** Parent PSM ID */
  psm_parent_id?: number;
  /** Runtime environment */
  runtime?: string;
  /** Programming language */
  language?: string;
  /** Run command */
  run_cmd?: string;
  /** Base container image */
  base_image?: string;
  /** Service origin */
  origin?: string;
  /** Service category */
  category?: string;
  /** Whether approval is needed */
  need_approve?: boolean;
  /** List of authorizers */
  authorizers?: string;
  /** List of subscribers */
  subscribers?: Array<string>;
  /** Plugin name */
  plugin_name?: string;
  /** Plugin SCM ID */
  plugin_scm_id?: number;
  /** Plugin SCM path */
  plugin_scm_path?: string;
  /** Whether PPE alarms are disabled */
  disable_ppe_alarm?: boolean;
  /** Whether service runs in async mode */
  async_mode?: boolean;
  /** Whether build install is disabled, true means disable */
  disable_build_install?: boolean;
  /** Maximum revision number */
  max_revision_number?: number;
  /** Maximum code revision number */
  max_code_revision_number?: number;
  /** Whether MS registration succeeded */
  ms_register_suc?: boolean;
  /** Log throttle rate in bytes per second */
  throttle_log_bytes_per_sec?: number;
  /** Stdout log throttle rate in bytes per second */
  throttle_stdout_log_bytes_per_sec?: number;
  /** Stderr log throttle rate in bytes per second */
  throttle_stderr_log_bytes_per_sec?: number;
  /** Environment name */
  env_name?: string;
  /** Source type for the service */
  source_type?: string;
  /** Source location */
  source?: string;
  /** Code file size in megabytes */
  code_file_size_mb?: number;
  /** List of global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** List of local cache namespace IDs */
  local_cache_namespace_ids?: Array<string>;
  /** Protocol used by the service */
  protocol?: string;
  /** Link to Argos monitoring */
  argos_link?: string;
  /** Timestamp when service was created */
  created_at?: string;
  /** Timestamp when service was last updated */
  updated_at?: string;
  /** Current revision ID */
  revision_id?: string;
  /** API version */
  api_version?: string;
  /** List of clusters for this service */
  clusters?: Array<ClusterResponseData>;
  /** Network queue configuration */
  net_queue?: string;
  /** Mount information */
  mount_info?: Array<string>;
  /** Whether service is soft deleted */
  soft_deleted?: boolean;
  /** User who performed soft deletion */
  soft_deleted_by?: string;
  /** Timestamp when soft deletion occurred */
  soft_deleted_at?: string;
  /** 审核权限点 [cluster_create, cluster_update, cluster_delete, code_release]
Approval permission points [cluster_create, cluster_update, cluster_delete, code_release] */
  approval_scope?: Array<string>;
  /** Whether this is an agent service */
  is_agent?: boolean;
  /** Whether service uses GPU */
  use_gpu?: boolean;
  /** status of extra steps that run after service creation
创建服务后运行的额外步骤的状态 */
  extra_steps_status?: string;
  /** link of extra steps that run after service creation */
  extra_steps_link?: string;
}

export interface UpdateClusterAutoMeshRequest {
  /** cluster name */
  cluster: string;
  /** Whether service mesh is enabled */
  mesh_enable?: boolean;
  /** Whether HTTP egress through mesh is enabled */
  mesh_http_egress?: boolean;
  /** Whether MongoDB egress through mesh is enabled */
  mesh_mongo_egress?: boolean;
  /** Whether MySQL egress through mesh is enabled */
  mesh_mysql_egress?: boolean;
  /** Whether RPC egress through mesh is enabled */
  mesh_rpc_egress?: boolean;
  /** Percentage of sidecars in the mesh */
  mesh_sidecar_percent?: number;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface UpdateClusterAutoMeshResponse {
  code?: number;
  data?: AutoMeshParams;
  error?: string;
}

export interface UpdateClusterRequest {
  /** auth switch. 鉴权开关 */
  auth_enable?: boolean;
  /** name of cluster */
  cluster: string;
  /** ID of code revision. 部署代码版本 ID */
  code_revision_id?: string;
  /** number of code revision. 部署代码版本号 */
  code_revision_number?: string;
  /** cold start switch. 冷启动开关，true 为关闭 */
  cold_start_disabled?: boolean;
  /** CORS switch. CORS 开关 */
  cors_enable?: boolean;
  /** Whether co-locate scheduling is enabled */
  enable_colocate_scheduling?: boolean;
  /** Whether scaling strategy is enabled */
  enable_scale_strategy?: boolean;
  /** exclusive mode. 独占模式 */
  exclusive_mode?: boolean;
  /** Environment variables configuration */
  format_envs?: Array<common.FormatEnvs>;
  /** Whether gateway routing is enabled */
  gateway_route_enable?: boolean;
  /** GDPR switch. GDPR 鉴权开关 */
  gdpr_enable?: boolean;
  /** List of global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** Whether HTTP triggers are disabled, true means disable */
  http_trigger_disable?: boolean;
  /** Initializer timeout in seconds */
  initializer_sec?: number;
  /** Whether cluster only supports IPv6 */
  is_ipv6_only?: boolean;
  /** disable zones in a region */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Function execution timeout in seconds */
  latency_sec?: number;
  /** Maximum concurrent executions allowed */
  max_concurrency?: number;
  /** Optional values: empty string,bridge */
  network_mode?: string;
  /** region name */
  region: string;
  /** function reserved mode switch. 函数预留模式开关 */
  reserved_dp_enabled?: boolean;
  /** Resource limits for the cluster */
  resource_limit?: common.ResourceLimit;
  /** Revision ID for the cluster configuration */
  revision_id?: string;
  /** Revision number for the cluster configuration */
  revision_number?: number;
  /** function routing strategy, enums：prefer_reserved, prefer_elastic. 函数路由调度策略 */
  routing_strategy?: string;
  /** Whether auto-scaling is enabled */
  scale_enabled?: boolean;
  /** Threshold for scaling decisions */
  scale_threshold?: number;
  /** Type of scaling strategy */
  scale_type?: number;
  /** service ID */
  service_id: string;
  /** Whether tracing is enabled */
  trace_enable?: boolean;
  /** zone throttle log bytes */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** ZTI switch. ZTI 鉴权开关 */
  zti_enable?: boolean;
  /** General log throttle rate in bytes per second */
  throttle_log_bytes_per_sec?: number;
  /** Stdout log throttle rate in bytes per second */
  throttle_stdout_log_bytes_per_sec?: number;
  /** Stderr log throttle rate in bytes per second */
  throttle_stderr_log_bytes_per_sec?: number;
  /** Cold start timeout in seconds */
  cold_start_sec?: number;
  /** Whether cluster runs in async mode */
  async_mode?: boolean;
  /** Whether scale optimization is enabled */
  enable_scale_optimise?: boolean;
  /** Whether runtime file logging is enabled */
  enable_runtime_file_log?: boolean;
  /** Whether runtime console logging is enabled */
  enable_runtime_console_log?: boolean;
  /** Whether runtime stream logging is enabled */
  enable_runtime_stream_log?: boolean;
  /** Whether runtime Elasticsearch logging is enabled */
  enable_runtime_es_log?: boolean;
  /** Whether runtime JSON logging is enabled */
  enable_runtime_json_log?: boolean;
  /** Whether system stream logging is enabled */
  enable_system_stream_log?: boolean;
  /** Whether system Elasticsearch logging is enabled */
  enable_system_es_log?: boolean;
  /** Runtime stream log rate limit in bytes per second */
  runtime_stream_log_bytes_per_sec?: number;
  /** System stream log rate limit in bytes per second */
  system_stream_log_bytes_per_sec?: number;
  /** Type of pod configuration */
  pod_type?: string;
  /** Whether cluster is in online mode */
  online_mode?: boolean;
  /** Whether to enable reserve frozen instances */
  enable_reserve_frozen_instance?: boolean;
  /** Command to run for the cluster */
  cluster_run_cmd?: string;
  /** Whether service discovery is disabled，true means disable */
  disable_service_discovery?: boolean;
  /** Whether async results emit to event bridge */
  async_result_emit_event_bridge?: boolean;
  /** Whether resource guarantee is enabled */
  resource_guarantee?: boolean;
  /** Limit for MQ triggers */
  mq_trigger_limit?: number;
  /** Cell identifier for deployment */
  cell?: string;
  /** Whether lazy loading is enabled */
  lazyload?: boolean;
  /** Whether image lazy loading is enabled */
  image_lazyload?: boolean;
  /** Initializer function name */
  initializer?: string;
  /** Handler function name */
  handler?: string;
  /** Run command for the function */
  run_cmd?: string;
  /** Whether log throttling is enabled */
  throttle_log_enabled?: boolean;
  /** Adaptive concurrency mode setting */
  adaptive_concurrency_mode?: string;
  /** Environment name */
  env_name?: string;
  /** Container runtime type */
  container_runtime?: string;
  /** Protocol used by the function */
  protocol?: string;
  /** Whether overload protection is enabled */
  overload_protect_enabled?: boolean;
  /** MQ consumer metadata configuration */
  mq_consumer_meta?: Array<common.ClusterMQConsumerMeta>;
  /** Whether Consul IPv6 registration is enabled */
  enable_consul_ipv6_register?: boolean;
  /** Whether system mount is enabled */
  enable_sys_mount?: boolean;
  /** Whether mounting JWT bundles is disabled, true means disable */
  disable_mount_jwt_bundles?: boolean;
  /** Grace period for pod termination in seconds */
  termination_grace_period_seconds?: number;
  /** Whether Consul registration is enabled */
  enable_consul_register?: boolean;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
  /** Host uniqueness configuration */
  host_uniq?: common.HostUniq;
  /** Whether cluster is in cell migration */
  in_cell_migration?: boolean;
  /** Whether session requests are enabled */
  enable_session_request?: boolean;
  /** Reserved frozen replicas per zone */
  zone_reserved_frozen_replicas?: Record<string, number>;
  /** Frozen CPU allocation in millicores */
  frozen_cpu_milli?: number;
  /** enable privileged in pod */
  privileged?: boolean;
}

export interface UpdateClusterResponse {
  code?: number;
  data?: ClusterResponseData;
  error?: string;
}

export interface UpdateClusterTicketRequest {
  /** User who approved the update */
  approved_by?: string;
  /** Type of user who approved the update. Enums: "person_account","service_account" */
  approved_by_usertype?: string;
  /** Update parameters for the cluster */
  params?: UpdateClusterRequest;
  /** Run the update without applying changes */
  dry_run?: boolean;
  service_id: string;
  region: string;
  cluster: string;
  /** is global cp ticket, for internal use only */
  'X-Bytefaas-Globalcp-Ticket'?: string;
}

export interface UpdateVefaasTrafficSchedulingRequest {
  /** 是否开启小流量引流火山函数功能
Whether to enable small-traffic diversion to Volcengine Function */
  enabled: boolean;
  /** 目标函数psm，留空则配置为当前服务的PSM
Target function PSM, if empty uses current service's PSM */
  psm?: string;
  /** 目标函数集群，留空则配置为默认火山集群
Target function cluster, if empty uses default Volcengine cluster */
  cluster?: string;
  /** 是否开启全局模式，开启则跳过触发器配置
Whether to enable global mode, skips trigger config if enabled */
  global_mode?: boolean;
  /** 全局模式流量配比
Traffic ratio for global mode */
  global_ratio?: number;
  /** 触发器流量配置
Trigger traffic configuration */
  trigger_config?: Record<
    string,
    Record<string, VefaasTrafficSchedulingTriggerData>
  >;
}

export interface UpdateVefaasTrafficSchedulingResponse {
  code: number;
  data: ClusterResponseData;
  error: string;
}

export interface VefaasTrafficSchedulingData {
  /** 小流量引流功能开启状态
Whether small-traffic diversion is enabled */
  enabled: boolean;
  /** 目标函数psm
Target function PSM */
  psm: string;
  /** 目标函数集群
Target function cluster */
  cluster: string;
  /** 全局模式
Global mode */
  global_mode: boolean;
  /** 全局模式流量配比
Traffic ratio for global mode */
  global_ratio: number;
  /** 触发器流量配置
Trigger traffic configuration */
  trigger_config: Record<
    string,
    Record<string, VefaasTrafficSchedulingTriggerData>
  >;
}

export interface VefaasTrafficSchedulingTriggerData {
  /** 触发器 ID，eventbus触发器的ID为EventName
Trigger ID, for eventbus triggers this is the EventName */
  id: string;
  /** 触发器流量配比
Traffic ratio for the trigger */
  ratio: number;
}

export interface VolumeMount {
  /** Name of the volume */
  name?: string;
  /** Version of the volume */
  version?: string;
  /** Path where the volume is mounted */
  mount_path?: string;
  /** Whether the volume is mounted as read-only */
  read_only?: boolean;
}
/* eslint-enable */
