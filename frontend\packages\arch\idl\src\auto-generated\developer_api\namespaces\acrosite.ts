/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AcrositeAPIRequest {
  action?: string;
  data?: string;
}

export interface AcrositeAPIResponse {
  code?: Int64;
  msg?: string;
  data?: string;
}

export interface Breadcrumb {
  uri: string;
  title: string;
}

export interface BuzList {
  _id?: string;
  status?: number;
  _sort?: Int64;
  deleted?: boolean;
  app_id?: string;
  name?: string;
  created_at?: string;
  updated_at?: string;
  __v?: number;
  page_id?: string;
}

export interface Doc {
  breadcrumbs?: Array<Breadcrumb>;
  site?: string;
  app_id?: string;
  site_status?: string;
  keywords?: Array<string>;
  status?: string;
  title?: string;
  path?: string;
  domain?: string;
  type?: string;
  business_id?: string;
  doc_id?: string;
  scope?: string;
  lang?: string;
  highlight?: Array<string>;
  url?: string;
  score?: number;
}

export interface DocCreator {
  id?: number;
  name?: string;
  email?: string;
  avatar_url?: string;
}

export interface GetBusinessData {
  Result: GetBuzResult;
}

export interface GetBusinessRequest {
  app_id?: string;
}

export interface GetBusinessResponse {
  code?: Int64;
  msg?: string;
  data?: GetBusinessData;
}

export interface GetBusStructureByIdData {
  Result?: Array<Result>;
}

export interface GetBusStructureByIdRequest {
  business_id?: string;
  lang?: string;
  source?: string;
  req_source?: string;
  business_version_id?: string;
  app_id?: string;
}

export interface GetBusStructureByIdResponse {
  code?: Int64;
  msg?: string;
  data?: GetBusStructureByIdData;
}

export interface GetBuzResult {
  list?: Array<BuzList>;
  pager?: Pager;
}

export interface GetDocByIdData {
  Result?: Result;
}

export interface GetDocByIdRequest {
  id: string;
  app_id: string;
}

export interface GetDocByIdResponse {
  code?: Int64;
  msg?: string;
  data?: GetDocByIdData;
}

export interface GetDocDetailByIdData {
  Result?: Result;
}

export interface GetDocDetailByIdRequest {
  id: string;
  app_id: string;
}

export interface GetDocDetailByIdResponse {
  code?: Int64;
  msg?: string;
  data?: GetDocDetailByIdData;
}

export interface Pager {
  count?: number;
  skip?: number;
  limit?: number;
}

export interface Resule {
  total?: number;
  limit?: number;
  skip?: number;
  list?: Array<Doc>;
}

export interface Result {
  _id?: string;
  status?: number;
  visible?: number;
  scope?: string;
  machine_translation?: boolean;
  is_dir?: boolean;
  is_modified?: boolean;
  _sort?: Int64;
  view_count?: number;
  type?: number;
  tags?: Array<string>;
  open_market?: boolean;
  usage?: number;
  approval_status?: number;
  lang?: string;
  deleted?: boolean;
  app_id?: string;
  business_id?: string;
  path?: string;
  title: string;
  creator?: string;
  keywords?: string;
  zh_doc_id?: string;
  created_at?: string;
  updated_at?: string;
  saved_time?: string;
  word_count?: number;
  reuse_word_count?: number;
  __v?: number;
  version_id?: string;
  content: string;
  create_user?: DocCreator;
  subs?: Array<Result>;
}

export interface SearchData {
  Result: Resule;
}

export interface SearchRequest {
  app_id?: string;
  app_ids?: Array<string>;
  q?: string;
  lang?: string;
  type?: string;
  business_id?: string;
  skip?: number;
  limit?: number;
  number_of_fragments?: number;
  fragment_size?: number;
  version_scope?: string;
  update_time_start?: string;
  update_time_end?: string;
}

export interface SearchResponse {
  code?: Int64;
  msg?: string;
  data?: SearchData;
}
/* eslint-enable */
