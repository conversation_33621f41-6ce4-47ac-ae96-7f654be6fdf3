/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface BaseInfo {
  created_by?: UserInfo;
  updated_by?: UserInfo;
  created_at?: string;
  updated_at?: string;
}

export interface OrderBy {
  field?: string;
  is_asc?: boolean;
}

export interface UserInfo {
  name?: string;
  en_name?: string;
  avatar_url?: string;
  avatar_thumb?: string;
  open_id?: string;
  union_id?: string;
  user_id?: string;
  email?: string;
}
/* eslint-enable */
