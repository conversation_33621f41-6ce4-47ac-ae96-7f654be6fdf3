/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as ob_query from './ob_query';

export type Int64 = string | number;

export interface ExportQueryToCsvV2Request {
  /** space id */
  space_id: string;
  /** 场景参数  不同场景使用的通用参数 */
  scene_param: common.SceneCommonParam;
  /** 筛选参数 */
  query_filter?: QueryFilterV2;
}

export interface ExportQueryToCsvV2Response {
  body?: Blob;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetQueryEnumsV2Request {
  /** space id */
  space_id: string;
  /** 场景参数  不同场景使用的通用参数参数 */
  scene_param: common.SceneCommonParam;
}

export interface GetQueryEnumsV2Response {
  intent?: Array<ob_query.Intent>;
  /** key: connector_id, value: connector_name */
  connectors?: Array<ob_query.KV>;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetQueryStatV2Request {
  /** space id */
  space_id: string;
  /** 场景参数  不同场景使用的通用参数 */
  scene_param: common.SceneCommonParam;
  query_filter?: QueryFilterV2;
}

export interface GetQueryStatV2Response {
  data: ob_query.GetQueryStatData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface ListQueryV2Request {
  /** space id */
  space_id: string;
  /** 场景参数  不同场景使用的通用参数 */
  scene_param: common.SceneCommonParam;
  /** 筛选参数 */
  query_filter?: QueryFilterV2;
  /** default 20 max 200 */
  limit?: number;
  /** 上次请求带过来的分页参数 */
  page_token?: string;
}

export interface ListQueryV2Response {
  data: Array<QueryDataV2>;
  /** 下一页的分页token，前端拉取下一页数据时回传 */
  next_page_token: string;
  /** 是否有更多数据 */
  has_more: boolean;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface QueryDataV2 {
  query_id?: string;
  /** trace_id，用于查询trace细节 */
  trace_id?: string;
  /** 状态 */
  status?: ob_query.Status;
  /** 用户ID */
  user_id?: string;
  /** 对话ID */
  message_id?: string;
  /** 会话ID */
  session_id?: string;
  /** 用户输入 */
  input?: string;
  /** bot输出 */
  output?: string;
  /** 模型输入长度 */
  input_tokens?: number;
  /** 模型输出长度 */
  output_tokens?: number;
  /** 发起请求时间 */
  start_time?: string;
  /** 整体耗时 */
  latency?: Int64;
  /** 首token时延 */
  latency_first_resp?: Int64;
  /** 渠道名称 */
  connector?: string;
  /** 意图标签 */
  intent?: string;
  /** 意图字段生产状态 */
  intent_status?: ob_query.CalFieldStatus;
  /** session字段生产状态 */
  session_id_status?: ob_query.CalFieldStatus;
  /** 会话ID（connector platform上报原始值） */
  conversation_id?: string;
  /** 发布管理新增
运行id */
  workflow_execute_id?: string;
  trigger_id?: string;
  /** 触发器name--来源name, */
  trigger_name?: string;
  /** Project版本 对外用户填写的 */
  version?: string;
  /** Project版本 时间戳 */
  version_code?: string;
  /** 运行token */
  total_tokens?: number;
}

export interface QueryFilterV2 {
  start_time?: ob_query.TimeRange;
  status?: Array<ob_query.Status>;
  user_ids?: Array<string>;
  message_ids?: Array<string>;
  session_ids?: Array<string>;
  input?: string;
  output?: string;
  input_tokens?: ob_query.I64Range;
  output_tokens?: ob_query.I64Range;
  latency?: ob_query.I64Range;
  latency_first_resp?: ob_query.I64Range;
  connector_ids?: Array<string>;
  second_class_intents?: Array<string>;
  /** 发布管理新增 */
  workflow_execute_ids?: Array<string>;
  trigger_ids?: Array<string>;
  trigger_name?: string;
  version_codes?: Array<string>;
  total_tokens?: ob_query.I64Range;
}
/* eslint-enable */
