/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface GetTradeConf {
  /** 场景开关。0-关闭；1-开启。当前支持 trade_available(交易可用性)，根据国家限制; */
  scene_switch?: Record<string, number>;
}

export interface GetTradeConfRequest {
  /** 场景列表，不填默认返回全部SceneSwitch。当前支持 trade_available(交易可用性); */
  scenes?: Array<string>;
  'Tt-Agw-Client-Ip'?: string;
}

export interface GetTradeConfResponse {
  data?: GetTradeConf;
  code: number;
  message: string;
}
/* eslint-enable */
