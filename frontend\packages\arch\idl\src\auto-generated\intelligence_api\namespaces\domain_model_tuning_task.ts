/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ModelType {
  /** 定制模型 */
  CustomModel = 1,
  /** 基础模型 */
  FoundationModel = 2,
}

export enum OutPutStatus {
  Available = 1,
  Exported = 2,
  Expired = 3,
}

export enum TuningTaskStatus {
  UnKnown = 0,
  /** 初始化 */
  Init = 1,
  /** 预检查 */
  Preprocessing = 2,
  /** 排队中 */
  Queued = 3,
  /** 部署中 */
  Deploying = 4,
  /** 执行中 */
  Running = 5,
  /** 完成中 */
  Completing = 6,
  /** 执行完成 */
  Completed = 7,
  /** 终止中 */
  Terminating = 8,
  /** 执行终止 */
  Terminated = 9,
  /** 执行失败 */
  Failed = 10,
  /** EndPoint调度中 */
  EndPointScheduling = 11,
  /** EndPoint使用中 */
  EndPointRunning = 12,
  /** EndPoint部署失败 */
  EndPointDeployAbnormal = 14,
  /** 任务删除 */
  TaskDelete = 16,
}

/** 训练类型 */
export enum TuningType {
  FinetuneSft = 1,
  FunetuneLoRA = 2,
  Pretrain = 3,
  DPOLoRA = 4,
}

export enum ValidationType {
  /** 上传文件 */
  Upload = 1,
  /** 训练集分割 */
  TrainingCut = 2,
  /** 没有验证集 */
  None = 3,
}
/* eslint-enable */
