{"name": "@coze-arch/bot-flags", "version": "0.0.1", "description": "feature gating for bot studio ", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "sideEffects": false, "exports": {".": "./src/index.ts", "./constant": "./src/constant.ts", "./_featureStorage": "./src/utils/storage.ts", "./init-flags": "./src/init-flags.ts"}, "main": "src/index.tsx", "typesVersions": {"*": {"constant": ["./src/constant.ts"], "_featureStorage": ["./src/utils/storage.ts"], "init-flags": ["./src/init-flags.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/logger": "workspace:*", "eventemitter3": "^5.0.1"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/react-hooks": "^8.0.1", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "sucrase": "^3.32.0", "vitest": "~3.0.5"}}