/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package entity

type Review struct {
	ReviewID      *int64
	DocumentName  string
	DocumentType  string
	Uri           string
	Url           string
	Status        *ReviewStatus
	DocTreeTosUrl *string
	PreviewTosUrl *string
}

type ReviewStatus int64

const (
	ReviewStatus_Processing ReviewStatus = 0
	ReviewStatus_Enable     ReviewStatus = 1
	ReviewStatus_Failed     ReviewStatus = 2
	ReviewStatus_ForceStop  ReviewStatus = 3
)
