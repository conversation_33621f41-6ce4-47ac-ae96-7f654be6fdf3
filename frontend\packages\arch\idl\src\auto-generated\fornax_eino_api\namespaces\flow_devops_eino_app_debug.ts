/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface NodeDebugMetrics {
  prompt_tokens?: Int64;
  completion_tokens?: Int64;
  invoke_time_ms?: Int64;
  completion_time_ms?: Int64;
}

export interface NodeDebugState {
  node_key: string;
  input?: string;
  output?: string;
  error?: string;
  error_type?: string;
  metrics?: NodeDebugMetrics;
}
/* eslint-enable */
