/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as shortcut_command from './shortcut_command';
import * as bot_common from './bot_common';
import * as bot_task_common from './bot_task_common';
import * as document from './document';
import * as product_common from './product_common';
import * as plugin_common from './plugin_common';
import * as bot_user_auth from './bot_user_auth';

export type Int64 = string | number;

export enum AccountCancelCheckStatus {
  Pass = 0,
  NoLeaveAllTeam = 1,
  NoLeaveEnterprise = 2,
}

export enum AgeGateMode {
  None = 0,
  NoNeutral = 1,
  Neutral = 2,
}

export enum AgentType {
  Start_Agent = 0,
  LLM_Agent = 1,
  Task_Agent = 2,
  Global_Agent = 3,
  Bot_Agent = 4,
}

/** 版本兼容：0-旧版本 1-可回退的新版本 2-不可回退的新版本 3-可回退的新版本(不再提示) */
export enum AgentVersionCompat {
  OldVersion = 0,
  MiddleVersion = 1,
  NewVersion = 2,
  MiddleVersionNotPrompt = 3,
}

export enum AllowPublishStatus {
  Allowed = 0,
  Forbid = 1,
}

export enum AnswerActionsMode {
  Default = 1,
  Customize = 2,
}

export enum AnswerActionTriggerType {
  /** 平台预设Trigger action */
  Direct = 1,
  /** 点击Action 显示自定义的H5页面 */
  WebView = 2,
  /** 点击Action 发送自定义的用户消息 */
  SendMessage = 3,
}

export enum APIDebugStatus {
  DebugWaiting = 0,
  DebugPassed = 1,
}

export enum ApiKeyCreatorType {
  Inner = 1,
  Outter = 2,
}

export enum APIListOrderBy {
  CreateTime = 1,
}

export enum APIMethod {
  GET = 1,
  POST = 2,
  PUT = 3,
  DELETE = 4,
  PATCH = 5,
}

export enum AuditStatus {
  /** 审核中 */
  Auditing = 0,
  /** 审核通过 */
  Success = 1,
  /** 审核失败 */
  Failed = 2,
}

export enum AuthorizationServiceLocation {
  Header = 1,
  Query = 2,
}

export enum AuthorizationType {
  None = 0,
  Service = 1,
  OAuth = 3,
}

export enum AuthStatus {
  /** 已授权 */
  Authorized = 1,
  /** 未授权 */
  Unauthorized = 2,
  /** 授权中 */
  Authorizing = 3,
}

export enum BacktrackMode {
  Current = 1,
  Previous = 2,
  Start = 3,
  MostSuitable = 4,
}

export enum BatchNodeParamType {
  /** 批量参数 */
  BatchParam = 1,
  /** 输入参数 */
  NormalParam = 2,
}

export enum BindType {
  /** 无需绑定 */
  NoBindRequired = 1,
  /** Auth绑定 */
  AuthBind = 2,
  /** Kv绑定= */
  KvBind = 3,
  /** Kv并Auth授权 */
  KvAuthBind = 4,
  /** api渠道绑定 */
  ApiBind = 5,
  WebSDKBind = 6,
  StoreBind = 7,
  /** 授权和配置各一个按钮 */
  AuthAndConfig = 8,
}

export enum BotConnectorStatus {
  /** 正常 */
  Normal = 0,
  /** 审核中 */
  InReview = 1,
  /** 已下线 */
  Offline = 2,
}

export enum BotExploreStatus {
  Online = 1,
  Offline = 2,
}

export enum BotMarketStatus {
  /** 下架 */
  Offline = 0,
  /** 上架 */
  Online = 1,
}

export enum BotMode {
  SingleMode = 0,
  MultiMode = 1,
  WorkflowMode = 2,
}

export enum BotSpecies {
  /** bot种类 */
  Default = 0,
  Function = 1,
}

export enum BotStatus {
  Using = 0,
  Deleted = 1,
  Ban = 3,
  Draft = 4,
}

export enum BotType {
  User = 0,
  Coco = 1,
  GuanFang = 2,
}

/** 分支 */
export enum Branch {
  Undefined = 0,
  /** 草稿 */
  PersonalDraft = 1,
  /** space草稿 */
  Base = 2,
  /** 线上版本,diff场景下使用 */
  Publish = 3,
}

export enum CacheType {
  /** 缓存关闭 */
  CacheClosed = 0,
  /** 前缀缓存 */
  PrefixCache = 1,
}

export enum CardBizType {
  Plugin = 1,
  Workflow = 2,
}

export enum CardBusinessType {
  Plugin = 1,
  WorkFlow = 2,
}

export enum CardCategory {
  Official = 1,
  Custom = 2,
}

export enum CardDisplayType {
  /** 基础 */
  Basic = 1,
  /** 竖向列表 */
  List = 2,
  /** 自定义卡片 */
  Custom = 3,
  /** 横向列表 */
  Slide = 4,
}

export enum CardImportStatus {
  /** 导入成功 */
  ImportSuccess = 1,
  /** 导入中 */
  Importing = 2,
  /** 失败 */
  ImportFailed = 3,
}

export enum CardSource {
  CardSourceLego = 1,
  CardSourceProCode = 2,
}

export enum CardStatus {
  Draft = 0,
  Published = 1,
  UnPublish = 2,
}

export enum ChannelType {
  Default = 0,
  Doubao = 100,
  CiCi = 101,
  Feishu = 200,
  WhatsApp = 300,
  Discord = 301,
  Twitter = 302,
}

/** follow copilot 定义的枚举 */
export enum ChatMessageMetaType {
  /** Compatible value */
  Default_0 = 0,
  /** 端侧直接替换 */
  Replaceable = 1,
  /** 插入引用 */
  Insertable = 2,
  /** 文档引用 */
  DocumentRef = 3,
  /** 知识库引用卡片 */
  KnowledgeCard = 4,
  /** 嵌入的多媒体信息，只是alice给端上用的，因为全链路复用这一个字段，所以在这儿改了 */
  EmbeddedMultimedia = 100,
}

export enum CommitResult {
  Undefined = 0,
  /** 提交成功 */
  Committed = 1,
  /** 版本落后 */
  Behind = 2,
}

export enum CommitStatus {
  Undefined = 0,
  /** 已是最新，同主草稿相同 */
  Uptodate = 1,
  /** 落后主草稿 */
  Behind = 2,
  /** 无个人草稿 */
  NoDraftReplica = 3,
}

export enum ConditionType {
  Equal = 1,
  NotEqual = 2,
  LengthGt = 3,
  LengthGtEqual = 4,
  LengthLt = 5,
  LengthLtEqual = 6,
  Contains = 7,
  NotContains = 8,
  Null = 9,
  NotNull = 10,
  True = 11,
  False = 12,
  Gt = 13,
  GtEqual = 14,
  Lt = 15,
  LtEqual = 16,
}

export enum ConfigStatus {
  /** 已配置 */
  Configured = 1,
  /** 未配置 */
  NotConfigured = 2,
  /** Token发生变化 */
  Disconnected = 3,
  /** 配置中，授权中 */
  Configuring = 4,
  /** 需要重新配置  */
  NeedReconfiguring = 5,
}

export enum ConnectorApiKeyStatus {
  Available = 1,
  Delete = 2,
  StopUse = 3,
}

export enum ConnectorDynamicStatus {
  Normal = 0,
  Offline = 1,
  TokenDisconnect = 2,
}

export enum ContentType {
  Text = 1,
  Suggest = 2,
  Music = 3,
  WebView = 4,
  Video = 5,
  Tako = 8,
  Card = 50,
  /** playground新增的contenttype */
  Time = 100,
}

/** 上下文允许传输的类型 */
export enum ContextContentType {
  /** 无任何处理版 */
  USER_RES = 0,
  USER_LLM_RES = 1,
  USER_LLM_APILEN_RES = 2,
  USER_LLM_API_RES = 3,
}

export enum CreationMethod {
  COZE = 0,
  IDE = 1,
}

export enum DataSetScopeType {
  ScopeAll = 1,
  ScopeSelf = 2,
}

/** 数据集搜索类型定义 */
export enum DataSetSearchType {
  SearchByCreateTime = 1,
  SearchByUpdateTime = 2,
}

export enum DataSetSource {
  SourceSelf = 1,
  SourceExplore = 2,
}

export enum DataSetStatus {
  /** 有文件处理中 */
  DataSetProcessing = 0,
  DataSetReady = 1,
}

export enum DebugOperation {
  /** 调试，会保存调试状态，会校验返回值 */
  Debug = 1,
  /** 仅解析返回值结构 */
  Parse = 2,
}

export enum DeleteStatus {
  SUCCESS = 0,
  FAIL = 1,
}

export enum DeveloperType {
  NORMAL = 1,
  ADVANCED = 2,
}

export enum DevelopMode {
  /** 个人模式 */
  DevModeSingle = 0,
  /** 协作模式 */
  DevModeCollaboration = 1,
}

export enum DiffModeIdentifier {
  ChatWithA = 1,
  ChatWithB = 2,
}

export enum DraftBotStatus {
  Deleted = 0,
  Using = 1,
  Banned = 2,
  MoveFail = 3,
}

export enum FavStatus {
  NotFav = 0,
  IsFav = 1,
}

export enum FieldType {
  Object = 1,
  String = 2,
  Integer = 3,
  Bool = 4,
  Array = 5,
}

export enum FileBizType {
  BIZ_UNKNOWN = 0,
  BIZ_BOT_ICON = 1,
  BIZ_BOT_DATASET = 2,
  BIZ_DATASET_ICON = 3,
  BIZ_PLUGIN_ICON = 4,
  BIZ_BOT_SPACE = 5,
  BIZ_BOT_WORKFLOW = 6,
  BIZ_SOCIETY_ICON = 7,
  BIZ_CONNECTOR_ICON = 8,
  BIZ_LIBRARY_VOICE_ICON = 9,
  BIZ_ENTERPRISE_ICON = 10,
}

export enum FileboxInfoMode {
  Off = 0,
  On = 1,
}

export enum GetBotListMode {
  /** 默认 */
  Default = 0,
  /** bot探索 */
  Discover = 1,
  /** 我的bot */
  MyBot = 2,
}

export enum GetRecType {
  LATEST = 1,
  HISTORY = 2,
}

export enum HistoryType {
  /** 废弃 */
  SUBMIT = 1,
  /** 发布 */
  FLAG = 2,
  /** 提交 */
  COMMIT = 4,
  /** 提交和发布 */
  COMMITANDFLAG = 5,
}

export enum IconType {
  Bot = 1,
  User = 2,
  Plugin = 3,
  Dataset = 4,
  Space = 5,
  Workflow = 6,
  Imageflow = 7,
  Society = 8,
  Connector = 9,
  ChatFlow = 10,
  Voice = 11,
  Enterprise = 12,
}

export enum IfConditionRelation {
  And = 1,
  Or = 2,
}

export enum IfNodeBranchType {
  If = 1,
  Else = 2,
}

export enum IndependentRecognitionModelType {
  /** 小模型 */
  SLM = 0,
  /** 大模型 */
  LLM = 1,
}

export enum IndependentTiming {
  /** 判断用户输入（前置） */
  Pre = 1,
  /** 判断节点输出（后置） */
  Post = 2,
  /** 前置模式和后置模式支持同时选择 */
  PreAndPost = 3,
}

export enum InputType {
  String = 1,
  Integer = 2,
  Boolean = 3,
  Double = 4,
  List = 5,
  Object = 6,
}

export enum InstallStatus {
  USING = 1,
  REMOVE = 2,
  OFFLINE = 3,
  /** 查询不传，展示用 */
  NOTINSTALL = 4,
}

export enum InviteFunc {
  GetInfo = 1,
}

export enum ItemStatus {
  USED = 1,
  DELETED = 2,
}

export enum ItemType {
  /** 用户和系统交互消息,json,每次全部传 */
  MESSAGEINFO = 1,
  /** 系统prompt */
  SYSTEMINFO = 2,
  /** 变量 */
  VARIABLE = 3,
  /** 其他信息,模型，温度,json */
  OTHERINFO = 4,
  /** 历史备注 */
  HISTORYINFO = 5,
  /** 选择的api */
  APIINFO = 6,
  /** 拼完变量的prompt,拉取bot信息用 */
  SYSTEMINFOAll = 7,
  /** 数据集 */
  DataSet = 8,
  /** Onboarding 文案，json的形式存 */
  ONBOARDING = 9,
  OUTPUTPARSER = 10,
  /** Profile Memory */
  PROFILEMEMORY = 11,
  /** 数据表 */
  TABLE = 12,
  /** workflow */
  WORKFLOW = 13,
  /** 任务设置 */
  TASK = 14,
  /** suggest reply */
  SUGGESTREPLY = 15,
  HOOKINFO = 16,
}

export enum LinkProcessType {
  Add = 1,
  Delete = 2,
}

export enum ListBotDraftType {
  /** 个人空间看可见和不可见 */
  MySpace = 0,
  /** team内个人草稿 -- 废弃 */
  MyDrafts = 1,
  /** team内所有 */
  TeamBots = 2,
  /** team内自己 */
  Mine = 3,
}

export enum ListType {
  USER = 1,
  ALL = 2,
}

export enum LoadDirection {
  Unknown = 0,
  Prev = 1,
  Next = 2,
}

export enum LoginValidationMode {
  DefaultMode = 0,
  StrongValidationMode = 1,
}

export enum MessageInfoRole {
  ASSISTANT = 1,
  USER = 2,
  /** llm中间结果 */
  MODELRESPONSE = 3,
  /** 执行api输出结果 */
  APIRESPONSE = 4,
  SYSTEM = 5,
  PLACEHOLDER = 6,
  /** 执行时间 */
  TIME = 100,
}

export enum MigrateRunModel {
  Debug = 1,
  Run = 2,
}

export enum MigrateType {
  Single = 1,
  Space = 2,
  ALL = 3,
}

export enum ModelClass {
  GPT = 1,
  SEED = 2,
  Claude = 3,
  /** name: MiniMax */
  MiniMax = 4,
  Plugin = 5,
  StableDiffusion = 6,
  ByteArtist = 7,
  Maas = 9,
  /** 废弃：千帆(百度云) */
  QianFan = 10,
  /** name：Google Gemini */
  Gemini = 11,
  /** name: Moonshot */
  Moonshot = 12,
  /** name：智谱 */
  GLM = 13,
  /** name: 火山方舟 */
  MaaSAutoSync = 14,
  /** name：通义千问 */
  QWen = 15,
  /** name: Cohere */
  Cohere = 16,
  /** name: 百川智能 */
  Baichuan = 17,
  /** name：文心一言 */
  Ernie = 18,
  /** name: 幻方 */
  DeekSeek = 19,
  /** name: Llama */
  Llama = 20,
  StepFun = 23,
  Other = 999,
}

export enum ModelParamType {
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum ModelScene {
  Douyin = 1,
}

export enum ModelTagClass {
  ModelType = 1,
  ModelUserRight = 2,
  ModelFeature = 3,
  ModelFunction = 4,
  ModelPaid = 15,
  /** 模型运行时能力 */
  ModelAbility = 16,
  /** 本期不做 */
  Custom = 20,
  Others = 100,
}

export enum ModelTagValue {
  Flagship = 1,
  HighSpeed = 2,
  ToolInvocation = 3,
  RolePlaying = 4,
  LongText = 5,
  ImageUnderstanding = 6,
  Reasoning = 7,
  VideoUnderstanding = 8,
  CostPerformance = 9,
  CodeSpecialization = 10,
  AudioUnderstanding = 11,
}

export enum MsgParticipantType {
  Bot = 1,
  User = 2,
}

export enum MsgType {
  FINALANSWER = 1,
  MODELRESPONSE = 3,
  APIRESPONSE = 4,
}

export enum MultiAgentConnectorType {
  Curve = 0,
  Straight = 1,
}

export enum MultiAgentSessionType {
  Flow = 1,
  Host = 2,
}

export enum NodeExeStatus {
  Waiting = 1,
  Running = 2,
  Success = 3,
  Fail = 4,
}

export enum NodeType {
  Start = 1,
  End = 2,
  LLM = 3,
  Api = 4,
  Code = 5,
  Dataset = 6,
  If = 8,
  SubWorkflow = 9,
  Variable = 11,
  Database = 12,
  Message = 13,
  Question = 18,
}

export enum OnboardingType {
  None = 0,
  /** 不展示开场白 */
  OnboardingNotDisplay = 1,
  /** 使用预设开场白,使用预设开场白时必须带prologue */
  OnboardingPreset = 2,
  /** 使用llm自动生成的开场白 */
  OnboardingLLMGen = 3,
}

export enum OnlineStatus {
  OFFLINE = 0,
  ONLINE = 1,
}

export enum OrderBy {
  CreateTime = 0,
  UpdateTime = 1,
  PublishTime = 2,
  Hot = 3,
}

export enum ParameterLocation {
  Path = 1,
  Query = 2,
  Body = 3,
  Header = 4,
}

export enum ParameterType {
  String = 1,
  Integer = 2,
  Number = 3,
  Object = 4,
  Array = 5,
  Bool = 6,
}

export enum ParamRequirementType {
  CanNotDelete = 1,
  CanNotChangeName = 2,
  CanChange = 3,
  CanNotChangeAnything = 4,
}

/** 以绑卡片的状态 */
export enum PluginCardStatus {
  Latest = 1,
  NeedUpdate = 2,
}

export enum PluginCurrentInfoItemType {
  source_code = 1,
  input_params = 2,
  openapi = 3,
}

export enum PluginListPluginType {
  /** 不存在workflow */
  ExceptWorkflow = 0,
  Workflow = 1,
  API = 2,
  /** 仅team内plugin */
  SpaceAPI = 3,
}

export enum PluginParamTypeFormat {
  FileUrl = 0,
  ImageUrl = 1,
  DocUrl = 2,
  CodeUrl = 3,
  PptUrl = 4,
  TxtUrl = 5,
  ExcelUrl = 6,
  AudioUrl = 7,
  ZipUrl = 8,
  VideoUrl = 9,
}

export enum PluginStatus {
  /** 默认值 */
  Draft = 0,
  SUBMITTED = 1,
  REVIEWING = 2,
  PREPARED = 3,
  PUBLISHED = 4,
  OFFLINE = 5,
}

export enum PluginType {
  PLUGIN = 1,
  APP = 2,
  FUNC = 3,
  WORKFLOW = 4,
  IMAGEFLOW = 5,
  LOCAL = 6,
}

/** --------------------------------------------workflow V2 end--------------------------------------------------
 ----------------- prompt optimize start -------------------- */
export enum PromptOptimizeType {
  MARKDOWN = 1,
}

export enum PromptType {
  SYSTEM = 1,
  USERPREFIX = 2,
  USERSUFFIX = 3,
}

export enum Publish {
  NoPublish = 0,
  HadPublished = 1,
}

export enum PublishOp {
  Online = 0,
  Offline = 1,
}

export enum PublishResultStatus {
  /** 成功 */
  Success = 1,
  /** 失败 */
  Failed = 2,
  /** 审批中 */
  InReview = 3,
}

export enum PublishStatus {
  All = 0,
  Publish = 1,
  NoPublish = 2,
}

export enum PublishType {
  OnlinePublish = 0,
  PrePublish = 1,
}

export enum QueryCardStatus {
  Published = 1,
  UnPublish = 2,
}

export enum RecognitionMode {
  FunctionCall = 1,
  Independent = 2,
}

export enum ReferenceInfoStatus {
  /** 1:有可用更新 */
  HasUpdates = 1,
  /** 2:被删除 */
  IsDelete = 2,
}

export enum ReferenceUpdateType {
  ManualUpdate = 1,
  AutoUpdate = 2,
}

export enum ReportMessageAction {
  /** 赞踩上报 */
  Feedback = 0,
  /** 消息删除 */
  Delete = 1,
  /** 更新卡片 */
  UpdataCard = 2,
}

/** ---------------  conversation --------------- ---------------   --------------- */
export enum Scene {
  Default = 0,
  Explore = 1,
  BotStore = 2,
  CozeHome = 3,
  Playground = 4,
  /** 评测平台 */
  Evaluation = 5,
  AgentAPP = 6,
  /** prompt优化 */
  PromptOptimize = 7,
  /** createbot的nl2bot功能 */
  GenerateAgentInfo = 8,
}

export enum SchemaAreaPageApi {
  /** 不执行 */
  NotQuery = 0,
  /** 查询config */
  GetBindConnectorConfig = 1,
  /** 保存config */
  SaveBindConnectorConfig = 2,
  /** 绑定 */
  BindConnector = 3,
  /** 解绑 */
  UnBindConnector = 4,
}

/** --------------------------------------------workflow V2----------------------------------- */
export enum SchemaType {
  DAG = 0,
  FDL = 1,
}

export enum ScopeType {
  /** 所有 */
  All = 0,
  /** 自己 */
  Self = 1,
}

export enum SearchStrategy {
  SemanticSearch = 0,
  HybirdSearch = 1,
  FullTextSearch = 20,
}

/** *********************url文档 end************************ */
export enum ShareOperation {
  Open = 1,
  Close = 2,
  GetLink = 3,
}

export enum Source {
  Explore = 1,
  BotStore = 3,
}

export enum SpaceMode {
  Normal = 0,
  DevMode = 1,
}

export enum SpaceRoleType {
  /** 默认 */
  Default = 0,
  /** owner */
  Owner = 1,
  /** 管理员 */
  Admin = 2,
  /** 普通成员 */
  Member = 3,
}

export enum SpaceTag {
  /** 专业版 */
  Professional = 1,
}

/** ---------------------space start------------------------------ */
export enum SpaceType {
  /** 个人 */
  Personal = 1,
  /** 小组 */
  Team = 2,
}

/** 回答附带建议问题 */
export enum SuggestReplyMode {
  /** 使用默认Suggest Prompt生成建议回复 */
  WithDefaultPrompt = 0,
  /** 使用自定义Suggest Prompt生成建议回复 */
  WithCustomizedPrompt = 1,
  /** 不需要Suggest */
  Disable = 2,
  /** (Agent)使用源bot的配置 */
  UseOriginBotMode = 3,
}

export enum SupportBatch {
  /** 1:不支持 */
  NOT_SUPPORT = 1,
  /** 2:支持 */
  SUPPORT = 2,
}

/** draft bot display info */
export enum TabStatus {
  Default = 0,
  Open = 1,
  Close = 2,
  Hide = 3,
}

export enum Tag {
  All = 1,
  Hot = 2,
  Information = 3,
  Music = 4,
  Picture = 5,
  UtilityTool = 6,
  Life = 7,
  Traval = 8,
  Network = 9,
  System = 10,
  Movie = 11,
  Office = 12,
  Shopping = 13,
  Education = 14,
  Health = 15,
  Social = 16,
  Entertainment = 17,
  Finance = 18,
  Hidden = 100,
}

export enum TaskStatus {
  USED = 1,
  DELETED = 2,
}

/** 同时新建bot和项目（使用默认model，agent，空白prompt） */
export enum TaskType {
  PROMPT = 0,
  BOT = 1,
  CHAIN = 2,
}

export enum TerminatePlanType {
  USELLM = 1,
  USESETTING = 2,
}

export enum TimeCapsuleMode {
  Off = 0,
  On = 1,
}

export enum ToolOutputStatus {
  Success = 0,
  Fail = 1,
}

export enum TriggerEnabled {
  /** 未设置 */
  Init = 0,
  /** 打开 */
  Open = 1,
  /** 关闭 */
  Close = 2,
}

export enum UserProfileEditStatus {
  Allowed = 0,
  Banned = 1,
}

export enum UserType {
  External = 0,
  Internal = 1,
}

export enum ValidateErrorType {
  BotValidateNodeErr = 1,
  BotValidatePathErr = 2,
  BotConcurrentPathErr = 3,
}

export enum VisibilityType {
  /** 不可见 */
  Invisible = 0,
  /** 可见 */
  Visible = 1,
}

export enum WebInfoStatus {
  /** 处理中 */
  Handleing = 0,
  /** 已完成 */
  Finish = 1,
  /** 失败 */
  Failed = 2,
}

export enum WorkflowExeHistoryStatus {
  NoHistory = 1,
  HasHistory = 2,
}

export enum WorkflowExeStatus {
  Running = 1,
  Success = 2,
  Fail = 3,
  Cancel = 4,
}

export enum WorkFlowListStatus {
  UnPublished = 1,
  HadPublished = 2,
}

/** WorkflowMode 用来区分 Workflow 和 Imageflow */
export enum WorkflowMode {
  Workflow = 0,
  Imageflow = 1,
}

/** 状态，1不可发布 2可发布  3已发布 4删除 */
export enum WorkFlowStatus {
  CanNotPublish = 1,
  CanPublish = 2,
  HadPublished = 3,
  Deleted = 4,
}

export enum WorkFlowType {
  /** 用户自定义 */
  User = 0,
  /** 官方模板 */
  GuanFang = 1,
}

export interface AccountCancelCheckData {
  check_status?: AccountCancelCheckStatus;
}

export interface AccountCancelRequest {}

export interface AccountCancelResponse {
  code?: Int64;
  msg?: string;
  data?: AccountCancelCheckData;
}

export interface ActionIcon {
  /** 自定义的按钮 type 不用传 */
  type?: string;
  /** 默认状态 */
  default_url?: string;
  /** 按下按钮的状态 */
  active_url?: string;
  /** 默认状态 */
  default_uri?: string;
  /** 按下按钮的状态 */
  active_uri?: string;
}

export interface AddApiKeyRequest {
  space_id?: string;
  key_name?: string;
}

export interface AddApiKeyResponse {
  code?: Int64;
  msg?: string;
  api_key_id?: string;
}

export interface AddFavoriteBotRequest {
  bot_id: string;
  fav_status: FavStatus;
}

export interface AddFavoriteBotResponse {
  code?: Int64;
  msg?: string;
}

export interface AddSpaceMemberRequest {
  /** 成员列表 */
  member_info_list: Array<MemberInfo>;
  /** 空间id */
  space_id: string;
}

export interface AddSpaceMemberResponse {
  code?: Int64;
  msg?: string;
}

export interface AddTaskRequest {
  name?: string;
  target?: string;
  task_type?: TaskType;
  bot_id?: string;
  bot_name?: string;
  bot_desc_to_human?: string;
  bot_uri?: string;
}

export interface AddTaskResponse {
  code?: Int64;
  msg?: string;
  data?: AddTaskResponseData;
}

export interface AddTaskResponseData {
  task_id: string;
  bot_id?: string;
}

export interface AgentData {
  agentInfo?: AgentInfo;
  /** 编辑的分支 */
  branch?: Branch;
  /** 是否与线上一致 */
  same_with_online?: boolean;
}

export interface AgentInfo {
  id?: string;
  agent_type?: AgentType;
  name?: string;
  position?: AgentPosition;
  icon_uri?: string;
  intents?: Array<Intent>;
  work_info?: AgentWorkInfo;
  reference_id?: string;
  first_version?: string;
  current_version?: string;
  /** 1:有可用更新 2:被删除 */
  reference_info_status?: ReferenceInfoStatus;
  description?: string;
  update_type?: ReferenceUpdateType;
}

export interface AgentPosition {
  x?: number;
  y?: number;
}

export interface AgentReferenceInfo {
  reference_id: string;
  version: string;
}

export interface AgentVersionCompatInfo {
  version_compat?: AgentVersionCompat;
  /** 升级版本 查看旧版用，version_compat=1、3时返回 */
  version?: string;
}

/** agent 工作区间各个模块的信息 */
export interface AgentWorkInfo {
  /** agent prompt 前端信息，server不需要感知 */
  prompt?: string;
  /** 模型配置 */
  other_info?: string;
  /** plugin 信息 */
  tools?: string;
  /** dataset 信息 */
  dataset?: string;
  /** workflow 信息 */
  workflow?: string;
  /** 同bot的 system_info_all */
  system_info_all?: string;
  /** 回溯配置 */
  jump_config?: JumpConfig;
  /** 推荐回复配置 */
  suggest_reply?: string;
  /** hook配置 */
  hook_info?: string;
}

export interface AnswerActionConfig {
  /** 预制的只需要传key */
  key?: string;
  /** 默认 */
  name?: string;
  /** 下发uri */
  icon?: ActionIcon;
  /** 存储用户i18的name */
  name_i18n?: Record<string, string>;
  /** Direct 没有值； WebView 包含 webview_url和 webview_callback_psm两个key；SendMessage 包含send_message_prompt */
  trigger_rule?: AnswerActionTriggerRule;
  position?: number;
}

export interface AnswerActionListConfig {
  action_list?: Array<AnswerActionConfig>;
  blank_num?: number;
  icon_urls?: Array<UriUrlObj>;
}

export interface AnswerActions {
  answer_actions_mode?: AnswerActionsMode;
  answer_action_configs?: Array<AnswerActionConfig>;
}

export interface AnswerActionTriggerRule {
  type?: AnswerActionTriggerType;
  need_preloading?: boolean;
  /** 根据 AnswerActionTriggerType决定 */
  trigger_data?: Record<string, string>;
}

export interface ApiDetailData {
  PluginID?: Int64;
  ApiName?: string;
  Inputs?: string;
  Outputs?: string;
  Icon?: string;
  Name?: string;
  Desc?: string;
  DebugExample?: DebugExample;
}

export interface APIInfo {
  plugin_id?: string;
  api_id?: string;
  plugin_version?: string;
  api_name?: string;
}

export interface ApiKeyInfo {
  id?: string;
  name?: string;
  key?: string;
  creat_time?: string;
  last_use_time?: string;
}

export interface APIListOrder {
  order_by?: APIListOrderBy;
  desc?: boolean;
}

export interface APIParam {
  plugin_id?: string;
  api_id?: string;
  plugin_version?: string;
  plugin_name?: string;
  api_name?: string;
  out_doc_link?: string;
  tips?: string;
}

export interface APIParameter {
  /** for前端，无实际意义 */
  id?: string;
  name?: string;
  desc?: string;
  type?: ParameterType;
  sub_type?: ParameterType;
  location?: ParameterLocation;
  is_required?: boolean;
  sub_parameters?: Array<APIParameter>;
}

export interface APIStruct {
  name?: string;
  type?: FieldType;
  children?: Array<APIStruct>;
}

export interface AppIDInfo {
  id?: string;
  name?: string;
  icon?: string;
}

export interface ApplyFakeLoginRequest {
  userId?: string;
  fakeUid?: string;
}

export interface ApplyFakeLoginResponse {
  code?: Int64;
  msg?: string;
}

export interface AuditInfo {
  audit_status?: AuditStatus;
  publish_id?: string;
  commit_version?: string;
}

export interface AuthLoginInfo {
  app_id?: string;
  response_type?: string;
  authorize_url?: string;
  scope?: string;
  client_id?: string;
  duration?: string;
  aid?: string;
  client_key?: string;
}

export interface AutoGenChangelogConnector {
  id?: string;
  name?: string;
}

export interface AutoGenChangelogData {}

export interface AutoGenChangelogRequest {
  space_id: string;
  bot_id: string;
  publish_id: string;
  connectors: Array<AutoGenChangelogConnector>;
  draft_version?: BotVersion;
}

export interface AutoGenChangelogResponse {
  code?: Int64;
  msg?: string;
  data: AutoGenChangelogData;
}

export interface BackgroundImageDetail {
  /** 原始图片 */
  origin_image_uri?: string;
  origin_image_url?: string;
  /** 实际使用图片 */
  image_uri?: string;
  image_url?: string;
  theme_color?: string;
  /** 渐变位置 */
  gradient_position?: GradientPosition;
  /** 裁剪画布位置 */
  canvas_position?: CanvasPosition;
}

export interface BackgroundImageInfo {
  /** web端背景图 */
  web_background_image?: BackgroundImageDetail;
  /** 移动端背景图 */
  mobile_background_image?: BackgroundImageDetail;
}

export interface Batch {
  /** batch开关是否打开 */
  is_batch?: boolean;
  /** 只处理数组[0,take_count)范围的输入 */
  take_count?: Int64;
  /** 需要Batch的输入 */
  input_param?: Parameter;
}

export interface BindCardRequest {
  plugin_id?: string;
  api_name?: string;
  card_id?: string;
  bot_id?: string;
  mapping_rule?: string;
  max_display_rows?: Int64;
  card_version_num?: string;
  agent_id?: string;
  llm_text_card?: boolean;
  biz_type?: CardBusinessType;
  business_id?: string;
  /** workflow end节点传入workflowName即原先传入apiName的字段 */
  unique_id?: string;
  /** 是否选择了预置卡片 */
  plugin_preset_card_selected?: boolean;
}

export interface BindCardResponse {
  code?: Int64;
  msg?: string;
}

export interface BindCardsStatusData {
  bind_cards?: Array<SignleBindCardStatus>;
}

export interface BindConnection {
  /** 渠道id */
  connector_id?: string;
  connector_name?: string;
  connector_icon?: string;
  /** 渠道绑定的用户 */
  connector_users?: Array<ConnectorUser>;
}

export interface BindConnectorConfigDetail {
  /** 渠道应用id */
  app_id?: string;
  /** 渠道id */
  connector_id?: string;
  /** 绑定的关系 */
  detail?: Record<string, string>;
}

export interface BindConnectorRequest {
  space_id: string;
  bot_id: string;
  connector_id: string;
  connector_info?: Record<string, string>;
  /** 0-bot，1-project */
  agent_type?: Int64;
}

export interface BindConnectorResponse {
  code?: Int64;
  msg?: string;
  bind_id?: string;
  /** 已经绑定过的bot_id,做跳转用 */
  bind_bot_id?: string;
  /** 已经绑定过的bot名称,做跳转用,为0的不做跳转 */
  bind_bot_name?: string;
  /** 已经绑bot所在space_id */
  bind_space_id?: string;
  /** 加密后的 state，bind_type 为 4 时前端透传 */
  encrypt_state?: string;
  /** auth 授权链接使用的 client_id */
  client_id?: string;
  /** auth 授权链接使用的 auth_params，用xxx=xxx拼接在渠道授权链接中，适用于kv+auth类型 */
  auth_params?: Record<string, string>;
  /** 已经绑 bot 的 agentID; 0-bot 1-project */
  bind_agent_type?: Int64;
}

export interface BindConnectorUserRequest {
  connector_id: string;
  param: string;
}

export interface BindConnectorUserResponse {
  code?: Int64;
  msg?: string;
  open_link?: string;
}

export interface BotCollaboratorStatus {
  /** 当前用户是否可以提交 */
  commitable?: boolean;
  /** 当前用户是否可运维 */
  operateable?: boolean;
  /** 当前用户是否可管理协作者 */
  manageable?: boolean;
}

export interface BotInfo {
  id?: string;
  name?: string;
  description_for_model?: string;
  description_for_human?: string;
  /** 展示用 */
  icon_url?: string;
  model?: Model;
  voice_type?: VoiceType;
  /** json结构，表示bot使用者，哪些字段可以编辑{icon:true,name:true}，空字符串或者{}表示不可编辑 */
  edit_pos?: string;
  create_time?: Int64;
  update_time?: Int64;
  /** 上传用 */
  icon_uri?: string;
  temperature?: number;
  recommend?: boolean;
  recommend_index?: Int64;
  bot_type?: BotType;
  top_k?: Int64;
  top_p?: number;
  /** bot 开场白 */
  prologue?: string;
  /** 如果不填则为llm自动生成的开场白 */
  onboarding_type?: OnboardingType;
  /** bot 开场白语言 */
  prologue_lang?: string;
  prompt?: string;
  tools?: string;
  bot_species?: BotSpecies;
  ext?: string;
  creator?: Creator;
  /** 是否允许链接分享 */
  sharable?: boolean;
  /** 是否发布过 */
  published?: boolean;
  /** 当前用户收藏状态，0 未收藏 1收藏 */
  fav_status?: FavStatus;
  /** bot的被收藏数 */
  fav_num?: number;
  /** 是否发布 */
  is_now_published?: boolean;
  bot_status?: BotStatus;
  /** bot空间 */
  app_id?: string;
}

export interface BotParticipantInfo {
  is_store_favorite?: boolean;
  trigger_enabled?: TriggerEnabled;
}

export interface BotPrompt {
  prompt_type?: PromptType;
  data?: string;
  record_id?: string;
}

export interface BotSpace {
  /** 空间id，新建为0 */
  id?: string;
  /** 发布平台 */
  app_ids?: Array<AppIDInfo>;
  /** 空间名称 */
  name?: string;
  /** 空间描述 */
  description?: string;
  /** 图标url */
  icon_url?: string;
  /** 空间类型 */
  space_type?: SpaceType;
  /** 发布平台 */
  connectors?: Array<ConnectorInfo>;
  /** 是否隐藏新建，复制删除按钮 */
  hide_operation?: boolean;
  /** 是否显示端侧插件创建入口 */
  display_local_plugin?: boolean;
  /** 空间模式 */
  space_mode?: SpaceMode;
  /** 空间标签 */
  space_tag?: SpaceTag;
}

export interface BotTagInfo {
  bot_id?: Int64;
  /** time_capsule */
  key?: string;
  /** TimeCapsuleInfo json */
  value?: string;
  version?: Int64;
}

export interface BotVersion {
  branch?: Branch;
  version_id?: string;
}

export interface BreakMessageRequest {
  conversation_id: string;
  query_message_id: string;
  /** 当前问题下哪一条回复被打断了 */
  answer_message_id?: string;
  /** 打断位置 */
  broken_pos?: number;
  scene?: Scene;
}

export interface BreakMessageResponse {
  code?: Int64;
  msg?: string;
}

export interface CancelUserAuthRequest {
  connector_id?: string;
}

export interface CancelUserAuthResponse {
  code?: Int64;
  msg?: string;
}

export interface CancelWorkFlowRequest {
  executeId?: string;
}

export interface CancelWorkFlowResponse {
  code?: Int64;
  msg?: string;
}

export interface CancelWorkFlowV2Request {
  execute_id: string;
  space_id: string;
}

export interface CancelWorkFlowV2Response {
  code?: Int64;
  msg?: string;
}

export interface CanvasPosition {
  width?: number;
  height?: number;
  left?: number;
  top?: number;
}

export interface CardBuilderProxyRequest {}

export interface CardBuilderProxyResponse {
  code?: Int64;
  msg?: string;
  data?: string;
}

export interface CardBusinessInfo {
  biz_type: CardBusinessType;
  business_id: string;
  unique_id: string;
}

export interface CardHistoryData {
  total: Int64;
  history_infos: Array<CardPublishHistoryInfo>;
}

export interface CardInfoData {
  card_id?: string;
  /** 草稿ID */
  draft_card_id?: string;
  /** 卡片名称 */
  name?: string;
  creator_id?: string;
  /** 卡片类型 */
  card_category?: CardCategory;
  /** 卡片展示类型 */
  card_display_type?: CardDisplayType;
  /** 卡片版本号 */
  version_num?: string;
  /** 卡片版本名称 */
  version_name?: string;
  /** 发布描述 */
  description?: string;
  /** 毫秒级时间戳 */
  update_time?: string;
  properties_hash?: string;
  card_meta_info?: Array<CardMetaInfo>;
  card_status?: CardStatus;
  properties?: string;
}

export interface CardMetaInfo {
  dsl_content?: string;
  lynx_url?: string;
  thumbnail?: string;
  channel_type?: ChannelType;
}

export interface CardMetaPublishData {
  card_id: string;
  version_num: string;
}

export interface CardMetaPublishRequest {
  card_id: string;
  draft_card_id: string;
  description?: string;
  version_name: string;
  thumbnail_info?: Array<ThumbnailInfo>;
}

export interface CardMetaPublishResponse {
  code?: Int64;
  msg?: string;
  data?: CardMetaPublishData;
}

export interface CardPublishHistoryInfo {
  card_id?: string;
  draft_id?: string;
  name?: string;
  version_num?: string;
  publish_time?: string;
  version_name?: string;
  description?: string;
}

export interface CardTemplateInfo {
  template_id: string;
  creator_id: string;
  name: string;
  channel_type: ChannelType;
  thumbnail: string;
  dsl_content: string;
  category: CardCategory;
  create_time: Int64;
}

export interface CardUploadFileRequest {
  /** 文件类型，后缀 */
  file_type?: string;
  /** 文件数据 */
  data?: Blob;
}

export interface CardUploadFileResponse {
  code?: Int64;
  msg?: string;
  /** 数据 */
  data?: UploadFileData;
}

export interface ChatGetOnboardingRequest {
  bot_id?: string;
  draft_mode?: boolean;
  bot_version?: string;
  preset_bot?: string;
  scene?: Scene;
}

export interface ChatGetOnboardingResponse {
  prologue: ChatMessage;
  suggested_questions: Array<ChatMessage>;
  code?: Int64;
  msg?: string;
  name?: string;
  icon_url?: string;
  shortcut_list?: Array<shortcut_command.ShortcutCommand>;
  suggested_questions_show_mode?: bot_common.SuggestedQuestionsShowMode;
}

export interface ChatMessage {
  role?: string;
  type?: string;
  content?: string;
  content_type?: string;
  message_id?: string;
  reply_id?: string;
  section_id?: string;
  extra_info?: ExtraInfo;
  /** 正常、打断状态 拉消息列表时使用，chat运行时没有这个字段 */
  status?: string;
  /** 打断位置 */
  broken_pos?: number;
  sender_id?: string;
  mention_list?: Array<MsgParticipantInfo>;
  content_time?: Int64;
  message_index?: string;
  /** 消息来源，0 普通聊天消息，1 定时任务，2 通知，3 异步结果 */
  source?: number;
  /** 对应回复的query 找不到后端加一个兜底的 */
  reply_message?: ChatMessage;
  /** 打断信息 */
  required_action?: RequiredAction;
  /** 引用、高亮等文本标记 */
  meta_infos?: Array<ChatMessageMetaInfo>;
  /** 卡片状态 */
  card_status?: Record<string, string>;
  /** 模型思维链 */
  reasoning_content?: string;
}

export interface ChatMessageMetaInfo {
  type?: ChatMessageMetaType;
  info?: string;
}

export interface ChatRequest {
  bot_id?: string;
  conversation_id: string;
  /** coze 不使用 openapi参数 */
  bot_version?: string;
  /** coze 不使用 openapi参数 */
  user?: string;
  query: string;
  /** coze 不使用 openapi参数 */
  chat_history?: Array<ChatMessage>;
  extra?: Record<string, string>;
  /** coze不使用 openapi参数 */
  stream?: boolean;
  custom_variables?: Record<string, string>;
  /** 草稿bot or 线上bot */
  draft_mode?: boolean;
  /** explore场景 */
  scene?: Scene;
  /** 文件 file 图片 image 等 */
  content_type?: string;
  /** 重试消息id */
  regen_message_id?: string;
  /** 前端本地的message_id 在extra_info 里面透传返回 */
  local_message_id?: string;
  /** 使用的bot模版 代替bot_id bot_version draft_mode参数， coze home使用 preset_bot="coze_home" */
  preset_bot?: string;
  insert_history_message_list?: Array<string>;
  device_id?: string;
  space_id?: string;
  mention_list?: Array<MsgParticipantInfo>;
  toolList?: Array<Tool>;
  commit_version?: string;
  /** scene粒度下进一步区分场景，目前仅给bot模版使用 = bot_template */
  sub_scene?: string;
  /** diff模式下的聊天配置，仅草稿single bot */
  diff_mode_identifier?: DiffModeIdentifier;
}

export interface ChatResponse {
  code?: Int64;
  msg?: string;
}

export interface CheckDraftBotCommitData {
  status?: CommitStatus;
  /** 主草稿版本 */
  base_commit_version?: string;
  /** 主草稿提交信息 */
  base_committer?: Committer;
  /** 个人草稿版本 */
  commit_version?: string;
}

export interface CheckDraftBotCommitRequest {
  space_id: string;
  bot_id: string;
  commit_version?: string;
}

export interface CheckDraftBotCommitResponse {
  code?: Int64;
  msg?: string;
  data?: CheckDraftBotCommitData;
}

export interface ClearMessageRequest {
  conversation_id: string;
  scene?: Scene;
  bot_id?: string;
}

export interface ClearMessageResponse {
  code?: Int64;
  msg?: string;
  new_section_id: string;
}

export interface CodeInfo {
  /** json */
  plugin_desc?: string;
  /** yaml */
  openapi_desc?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
}

export interface CodeParam {
  code_snippet?: string;
}

export interface CommitDraftBotData {
  commit_result?: CommitResult;
  committer?: Committer;
  branch?: Branch;
  commit_version?: string;
  same_with_online?: boolean;
}

export interface CommitDraftBotRequest {
  space_id: string;
  bot_id: string;
  /** 3 ~ 11 如果传了会先更新这部分到个人作为提交的内容，为了防止前端auto-save的时序问题
如果没有传，则会使用当前已经auto-save的内容 */
  work_info?: WorkInfo;
  name?: string;
  description?: string;
  icon_uri?: string;
  visibility?: VisibilityType;
  update_agents?: Array<AgentInfo>;
  canvas_data?: string;
  bot_mode?: BotMode;
  delete_agents?: Array<string>;
  /** 本次提交的备注信息 */
  remark?: string;
}

export interface CommitDraftBotResponse {
  code?: Int64;
  msg?: string;
  data?: CommitDraftBotData;
}

export interface Committer {
  id?: string;
  name?: string;
  commit_time?: string;
}

/** 上传文件，文件头 */
export interface CommonFileInfo {
  /** 文件类型，后缀 */
  file_type?: string;
  /** 业务类型 */
  biz_type?: FileBizType;
}

export interface commonParamSchema {
  name?: string;
  value?: string;
}

export interface Connector {
  /** connector_name 枚举 飞书="feishu" */
  name?: string;
  app_id?: string;
  app_secret?: string;
  share_link?: string;
  bind_info?: Record<string, string>;
}

export interface ConnectorBindResult {
  connector?: Connector;
  /** 发布调用下游返回的状态码，前端不消费 */
  code?: Int64;
  /** 发布状态的附加文案，前端按照markdown格式解析 */
  msg?: string;
  /** 发布结果状态 */
  publish_result_status?: PublishResultStatus;
}

export interface ConnectorBrandInfo {
  id: Int64;
  name: string;
  icon: string;
}

export interface ConnectorInfo {
  id?: string;
  name?: string;
  icon?: string;
  connector_status?: ConnectorDynamicStatus;
  share_link?: string;
}

export interface ConnectorUser {
  connector_uid?: string;
  connector_user_name?: string;
  connector_user_icon?: string;
}

export interface ConversationBotInfo {
  bot_info?: MsgParticipantInfo;
  user_info?: UserBasicInfo;
}

export interface CopyAgentRequest {
  space_id: string;
  /** draftbotid */
  bot_id: string;
  agent_id: string;
  /** 修改的基线版本 */
  base_commit_version?: string;
}

export interface CopyAgentResponse {
  code?: Int64;
  msg?: string;
  agentData?: AgentData;
}

export interface CopyFromTemplateData {
  workflow_id: string;
}

export interface CopyFromTemplateRequest {
  template_workflow_id: string;
  space_id?: string;
}

export interface CopyFromTemplateResponse {
  code?: Int64;
  msg?: string;
  data?: CopyFromTemplateData;
}

export interface CopyLinkAreaInfo {
  link_list?: Array<CopyLinkItem>;
  /** 链接区域标题下描述 */
  description?: string;
  /** 链接区域标题文本 */
  title_text?: string;
  /** 步骤号,只是展示指定的步骤号，不影响SchemaArea的展示顺序。 */
  step_order?: Int64;
}

export interface CopyLinkItem {
  /** copy link名称 */
  title?: string;
  link?: string;
}

export interface CopyNodeData {
  node?: Node;
}

export interface CopyNodeRequest {
  node_id: string;
  /** 新Node的位置 */
  layout?: LayOut;
}

export interface CopyNodeResponse {
  code?: Int64;
  msg?: string;
  data?: CopyNodeData;
}

export interface CopyWorkflowV2Data {
  workflow_id: string;
  schema_type: SchemaType;
}

export interface CopyWorkflowV2Request {
  workflow_id: string;
  space_id: string;
}

export interface CopyWorkflowV2Response {
  code?: Int64;
  msg?: string;
  data?: CopyWorkflowV2Data;
}

export interface CozeCardInfo {
  card_id?: string;
  draft_card_id?: string;
  name?: string;
  creator_id?: string;
  card_category?: CardCategory;
  card_display_type?: CardDisplayType;
  version_num?: string;
  version_name?: string;
  description?: string;
  update_time?: string;
  creator?: Creator;
  basic_card_id?: string;
  list_card_id?: string;
  properties?: string;
  properties_hash?: string;
  thumbnail?: string;
  card_status?: CardStatus;
  publish_time?: string;
}

export interface CreateAgentData {
  agentInfo?: AgentInfo;
  /** 编辑的分支 */
  branch?: Branch;
  /** 是否与线上一致 */
  same_with_online?: boolean;
}

export interface CreateAgentRequest {
  space_id: string;
  /** draftbotid */
  bot_id: string;
  agent_type?: AgentType;
  position?: AgentPosition;
  references?: AgentReferenceInfo;
  /** 修改的基线版本 */
  base_commit_version?: string;
  /** 0或者2 */
  version_compat?: AgentVersionCompat;
}

export interface CreateAgentResponse {
  code?: Int64;
  msg?: string;
  agentData?: CreateAgentData;
}

export interface CreateAPIRequest {
  /** 第一次调用保存并继续的时候使用这个接口 */
  plugin_id: string;
  name: string;
  desc: string;
  path: string;
  method: APIMethod;
}

export interface CreateAPIResponse {
  code?: Int64;
  msg?: string;
  api_id?: string;
}

export interface CreateBotTaskRequest {
  bot_id: string;
  /** 传了代表更新 */
  task_id?: string;
  name: string;
  trigger_type: bot_task_common.BotTaskTriggerType;
  mode?: bot_task_common.BotTaskMode;
  /** trigger_type == time */
  time_data?: bot_task_common.PresetTimeTriggerData;
  /** trigger_type == event && mode == webhook */
  webhook_data?: bot_task_common.PresetWebhookTriggerData;
  action: bot_task_common.Action;
}

export interface CreateBotTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface CreateBotTaskWebhookRequest {
  bot_id: string;
}

export interface CreateBotTaskWebhookResponse {
  code?: Int64;
  msg?: string;
  webhook_url?: string;
  /** 自动生成的 */
  bearer_token?: string;
}

export interface CreateCardData {
  card_id?: string;
  draft_card_id?: string;
}

export interface CreateCardRequest {
  space_id?: string;
  card_id?: string;
  /** 版本号 */
  version_num?: string;
}

export interface CreateCardResponse {
  code?: Int64;
  msg?: string;
  data?: CreateCardData;
}

export interface CreateDataSetRequest {
  name?: string;
  /** 空间id */
  space_id?: string;
  description?: string;
  icon_uri?: string;
}

export interface CreateDataSetResponse {
  code?: Int64;
  msg?: string;
  id?: string;
}

export interface CreateNodeData {
  node?: Node;
}

export interface CreateNodeRequest {
  workflow_id: string;
  type: NodeType;
  /** 如果是api类型，需要传过来api对应的信息 */
  api_info?: APIInfo;
  /** Node的位置 */
  layout: LayOut;
  space_id?: string;
}

export interface CreateNodeResponse {
  code?: Int64;
  msg?: string;
  data?: CreateNodeData;
}

export interface CreatePresetTaskRequest {
  bot_id: string;
  /** cron表达式 */
  cron_expr: string;
  /** 定时推送内容 */
  content?: string;
  user_question?: string;
  time_zone?: string;
}

export interface CreatePresetTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface CreateSectionRequest {
  conversation_id: string;
  scene?: Scene;
  /** 存在需要插入聊天的情况 */
  insert_history_message_list?: Array<string>;
}

export interface CreateSectionResponse {
  code?: Int64;
  msg?: string;
  new_section_id: string;
  /** 插入聊天记录后 返回插入的聊天记录 */
  new_section_message_list: Array<ChatMessage>;
}

export interface CreateSpaceRequest {
  /** 空间名称 */
  name: string;
  /** 空间描述 */
  description: string;
  /** 空间图像 */
  icon_uri: string;
  /** 空间类型 */
  space_type: SpaceType;
}

export interface CreateSpaceResponse {
  code?: Int64;
  msg?: string;
  /** 空间id */
  id?: string;
  /** true：机审校验不通过 */
  check_not_pass?: boolean;
}

export interface CreateWorkFlowData {
  workflow_id?: string;
  name?: string;
  url?: string;
  status?: WorkFlowStatus;
  node_list?: Array<Node>;
}

export interface CreateWorkFlowRequest {
  name: string;
  desc: string;
  icon_uri: string;
  space_id?: string;
}

export interface CreateWorkFlowResponse {
  code?: Int64;
  msg?: string;
  data?: CreateWorkFlowData;
}

export interface CreateWorkflowV2Data {
  workflow_id?: string;
  name?: string;
  url?: string;
  status?: WorkFlowStatus;
  type?: SchemaType;
  node_list?: Array<Node>;
}

export interface CreateWorkflowV2Request {
  name: string;
  desc: string;
  icon_uri: string;
  space_id?: string;
  /** workflow or imageflow, 默认为workflow */
  flow_mode?: WorkflowMode;
}

export interface CreateWorkflowV2Response {
  code?: Int64;
  msg?: string;
  data: CreateWorkflowV2Data;
}

export interface Creator {
  id?: string;
  /** 昵称 */
  name?: string;
  avatar_url?: string;
  /** 是否是自己创建的 */
  self?: boolean;
  /** 用户名 */
  user_unique_name?: string;
  /** 用户标签 */
  user_label?: UserLabel;
}

export interface DataSetItem {
  id?: string;
  /** 数据集名称 */
  name?: string;
  /** 文件列表 */
  file_list?: Array<string>;
  /** 所有文件大小 */
  all_file_size?: Int64;
  /** 使用Bot数 */
  bot_used_count?: number;
  status?: DataSetStatus;
  /** 处理中的文件 */
  processing_file_list?: Array<string>;
  /** 处理失败的文件 */
  failed_file_list?: Array<string>;
  /** 更新时间，秒级时间戳 */
  update_time?: number;
  description?: string;
  icon_url?: string;
  icon_uri?: string;
  /** 是否可以编辑 */
  can_edit?: boolean;
  /** 创建时间，秒级时间戳 */
  create_time?: number;
  creator_id?: string;
  creator_name?: string;
  avatar_url?: string;
}

export interface DatasetParam {
  dataset_list?: Array<string>;
}

export interface DebugAPIRequest {
  plugin_id: string;
  api_id: string;
  /** json */
  parameters: string;
  operation: DebugOperation;
}

export interface DebugAPIResponse {
  code?: Int64;
  msg?: string;
  /** parse时会返回这个字段 */
  response_params?: Array<APIParameter>;
  success?: boolean;
  resp?: string;
  reason?: string;
}

export interface DebugExample {
  req_example?: string;
  resp_example?: string;
}

export interface DeleteAPIRequest {
  plugin_id: string;
  api_id: string;
}

export interface DeleteAPIResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteCardRequest {
  card_id?: string;
}

export interface DeleteCardResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteCardTemplateData {
  success: boolean;
}

export interface DeleteCardTemplateRequest {
  template_id: string;
}

export interface DeleteCardTemplateResponse {
  code?: Int64;
  msg?: string;
  data?: DeleteCardTemplateData;
}

export interface DeleteDataSetRequest {
  id?: string;
}

export interface DeleteDataSetResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteDraftBotData {}

export interface DeleteDraftBotRequest {
  space_id: string;
  bot_id: string;
}

export interface DeleteDraftBotResponse {
  code?: Int64;
  msg?: string;
  data: DeleteDraftBotData;
}

export interface DeleteMentionRequest {
  /** 不传则删除所有历史mention bot */
  bot_id?: string;
}

export interface DeleteMentionResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteMessageRequest {
  conversation_id: string;
  message_id: string;
  scene?: Scene;
  bot_id?: string;
}

export interface DeleteMessageResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteNodeData {
  node_list?: Array<Node>;
}

export interface DeleteNodeRequest {
  workflow_id: string;
  node_id: string;
  space_id?: string;
}

export interface DeleteNodeResponse {
  code?: Int64;
  msg?: string;
  data?: DeleteNodeData;
}

export interface DeleteSpaceRequest {
  /** 空间id */
  space_id: string;
}

export interface DeleteSpaceResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteWorkflowV2Data {
  status?: DeleteStatus;
}

export interface DeleteWorkflowV2Request {
  workflow_id: string;
}

export interface DeleteWorkflowV2Response {
  code?: Int64;
  msg?: string;
  data: DeleteWorkflowV2Data;
}

export interface DelPluginRequest {
  plugin_id?: string;
}

export interface DelPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface DelWebDataRequest {
  web_id?: string;
}

export interface DelWebDataResponse {
  code?: Int64;
  msg?: string;
}

export interface DoActionRequest {
  PluginID: string;
  APIName: string;
  BotId?: string;
  Parameters?: string;
  /** 目前的消息ID */
  MessageID?: Int64;
  PluginName?: string;
  DeviceID?: Int64;
}

export interface DoActionResponse {
  code?: Int64;
  msg?: string;
  DoActionResponseData?: DoActionResponseData;
}

export interface DoActionResponseData {
  Resp?: string;
  Success?: boolean;
}

export interface DocumentFileInfo {
  name?: string;
  uri?: string;
  document_id?: string;
}

export interface DocumentTableTaskInfo {
  /** 用户选择的 sheet id */
  sheet_id: string;
  /** 用户选择的表头行数 */
  header_line_idx: string;
  /** 用户选择的起始行号 */
  start_line_idx: string;
}

export interface DocumentTaskInfo {
  name?: string;
  uri?: string;
  document_id?: string;
  /** 格式类型 */
  format_type?: document.FormatType;
  /** 表格元数据 */
  doc_table_meta?: Array<document.DocTableColumn>;
  /** 表格解析信息 */
  doc_table_info?: DocumentTableTaskInfo;
}

export interface DraftBot {
  /** drft_bot_id */
  id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  icon_url?: string;
  visibility?: VisibilityType;
  has_published?: Publish;
  app_ids?: Array<AppIDInfo>;
  create_time?: string;
  update_time?: string;
  creator_id?: string;
  space_id?: string;
  model_info?: ModelInfo;
  creator?: Creator;
  publish_time?: string;
  connectors?: Array<ConnectorInfo>;
  index?: string;
  bot_explore_status?: BotExploreStatus;
  space_name?: string;
  /** explore_bot_id */
  explore_id?: string;
  last_online_time?: string;
  bot_mode?: BotMode;
  explore_bot_info?: ExploreBotInfo;
  model_name?: string;
  bot_tag_info?: Array<BotTagInfo>;
  filebox_info?: FileboxInfo;
  /** for前端，当前bot是否可编辑 */
  editable?: boolean;
  /** for前端，当前bot是否可删除 */
  deletable?: boolean;
  /** 是否收藏 */
  is_fav?: boolean;
  /** 收藏时间 */
  favorite_at?: string;
  /** true 为多人协作模式，否则单人模式 */
  in_collaboration?: boolean;
  status?: DraftBotStatus;
  /** 最近一次审核详情 */
  latest_audit_info?: AuditInfo;
  /** 最近一次打开时间(用户维度) */
  recently_open_time?: string;
}

export interface DraftBotCreateData {
  bot_id?: string;
  /** true：机审校验不通过 */
  check_not_pass?: boolean;
  /** 机审校验不通过文案 */
  check_not_pass_msg?: string;
}

export interface DraftBotCreateRequest {
  space_id: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  visibility?: VisibilityType;
  monetization_conf?: MonetizationConf;
  /** 创建来源  navi:导航栏 space:空间 */
  create_from?: string;
  /** 关联的抖音分身应用id */
  app_id?: string;
  business_type?: bot_common.BusinessType;
  folder_id?: string;
}

export interface DraftBotCreateResponse {
  code?: Int64;
  msg?: string;
  data: DraftBotCreateData;
}

export interface DraftBotDisplayInfoData {
  tab_display_info?: TabDisplayItems;
}

export interface DraftBotInfo {
  /** draftid */
  id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  icon_url?: string;
  visibility?: VisibilityType;
  has_published?: Publish;
  app_ids?: Array<AppIDInfo>;
  create_time?: string;
  update_time?: string;
  creator_id?: string;
  space_id?: string;
  work_info?: WorkInfo;
  connectors?: Array<ConnectorInfo>;
  /** explore_id */
  explore_id?: Int64;
  bot_mode?: BotMode;
  agents?: Array<AgentInfo>;
  canvas_data?: string;
  version?: string;
  bot_tag_info?: Array<BotTagInfo>;
  filebox_info?: FileboxInfo;
  /** 获取的是什么分支的内容 */
  branch?: Branch;
  /** 如果branch=PersonalDraft，则为checkout/rebase的版本号；如果branch=base，则为提交的版本 */
  commit_version?: string;
  /** for前端，最近一次的提交人 */
  committer_name?: string;
  /** for前端，提交时间 */
  commit_time?: string;
  /** for前端，发布时间 */
  publish_time?: string;
  /** multi_agent数据结构体 */
  multi_agent_info?: MultiAgentInfo;
}

export interface DuplicateBotRequest {
  bot_id?: string;
}

export interface DuplicateBotResponse {
  code?: Int64;
  msg?: string;
  data?: DuplicateBotResponseData;
}

export interface DuplicateBotResponseData {
  bot_id?: string;
  task_id?: string;
}

export interface DuplicateBotToSpaceData {
  bot_id?: string;
}

export interface DuplicateBotToSpaceRequest {
  target_space_id: string;
  draft_bot_id: string;
  name?: string;
}

export interface DuplicateBotToSpaceResponse {
  code?: Int64;
  msg?: string;
  data: DuplicateBotToSpaceData;
}

export interface DuplicateDraftBotData {
  bot_id?: string;
  name?: string;
  user_info?: Creator;
}

export interface DuplicateDraftBotRequest {
  space_id: string;
  bot_id: string;
}

export interface DuplicateDraftBotResponse {
  code?: Int64;
  msg?: string;
  data: DuplicateDraftBotData;
}

export interface DuplicateTaskRequest {
  task_id?: string;
  task_name?: string;
}

export interface DuplicateTaskResponse {
  code?: Int64;
  msg?: string;
  data?: DuplicateTaskResponseData;
}

export interface DuplicateTaskResponseData {
  task_id?: string;
}

export interface ExecuteDraftBotData {}

export interface ExecuteDraftBotRequest {
  space_id: string;
  bot_id: string;
  work_info: WorkInfo;
  device_id: string;
  push_uuid: string;
  source?: Source;
  online_mode?: boolean;
  bot_version?: string;
}

export interface ExecuteDraftBotResponse {
  code?: Int64;
  msg?: string;
  data: ExecuteDraftBotData;
}

export interface ExitSpaceRequest {
  /** 空间id */
  space_id?: string;
  /** 权限转移user_id */
  transfer_user_id?: string;
}

export interface ExitSpaceResponse {
  code?: Int64;
  msg?: string;
}

export interface ExploreBotCategory {
  id?: string;
  name?: string;
}

export interface ExploreBotInfo {
  category?: Array<ExploreBotCategory>;
  heat_value?: number;
}

export interface ExtraInfo {
  local_message_id?: string;
  input_tokens?: string;
  output_tokens?: string;
  token?: string;
  /** "success" or "fail" */
  plugin_status?: string;
  time_cost?: string;
  workflow_tokens?: string;
  bot_state?: string;
  plugin_request?: string;
  tool_name?: string;
  plugin?: string;
  mock_hit_info?: string;
  log_id?: string;
  stream_id?: string;
  message_title?: string;
  stream_plugin_running?: string;
  new_section_id?: string;
  remove_query_id?: string;
  execute_display_name?: string;
  /** 对应定时任务task_type，1-预设任务，2-用户任务，3-Plugin后台任务 */
  task_type?: string;
  /** agent app使用引用格式 */
  refer_format?: string;
  call_id?: string;
}

export interface FeelGoodAuthData {
  token?: string;
  token_type?: string;
  expires_in?: string;
}

export interface FeelGoodAuthRequest {}

export interface FeelGoodAuthResponse {
  code?: Int64;
  msg?: string;
  data: FeelGoodAuthData;
}

export interface FileboxInfo {
  mode?: FileboxInfoMode;
}

export interface FormSchemaItem {
  /** 提交字段key */
  name?: string;
  /** 展示字段名称 */
  title?: string;
  /** 是否必填 */
  required?: boolean;
  /** 'Input' | 'InputNumber' ｜'Select' | 'Radio' | 'Checkbox'; 渲染组件 */
  component?: string;
  /** Options[]当为'Select' | 'Radio' | 'Checkbox' 时提供 枚举值 */
  enums?: Array<Options>;
  /** 'string' | 'number' | 'boolean'; 字段类型 (目前没有 array / object场景，暂不考虑 array / object) */
  type?: string;
  /** 校验规则 */
  rules?: Array<FormSchemaRule>;
}

export interface FormSchemaRule {
  /** string最大长度 */
  max?: number;
  /** string最小长度 */
  min?: number;
  /** string精准长度 */
  len?: number;
  /** 正则 */
  pattern?: string;
  /** 校验错误时的提示信息，走 starling 配置 */
  message?: string;
  /** 是否必填 */
  required?: boolean;
}

export interface FrontierConfig {
  product_id?: string;
  app_id?: string;
  access_key?: string;
  domain?: string;
  message_service_id?: string;
  /** 建连超时时间，单位 ms */
  timeout_interval?: number;
  /** 最大重试次数，默认为 10 次，超过重试次数会抛出 error 事件并关闭连接，需要业务方处理逻辑 */
  max_retries?: number;
  /** 单位 ms，如果从发送上行消息到收到 ACK 的时间间隔超过该阈值，前端需要做打点等处理 */
  ack_delay_threshold?: number;
}

export interface GenerateIconData {
  /** 文件url */
  icon_url?: string;
  /** 文件uri，提交使用这个 */
  icon_uri?: string;
  /** 用户访问次数 */
  count?: Int64;
}

export interface GenerateIconRequest {
  /** bot名称 */
  bot_name?: string;
  /** 相关描述 */
  description?: string;
  /** 超时时间 */
  timeout?: number;
}

export interface GenerateIconResponse {
  code?: Int64;
  msg?: string;
  data?: GenerateIconData;
}

export interface GetApiDetailRequest {
  pluginID?: Int64;
  apiName?: string;
  space_id?: string;
}

export interface GetApiDetailResponse {
  code?: Int64;
  msg?: string;
  data?: ApiDetailData;
}

export interface GetApiKeyListRequest {
  space_id?: string;
  api_key_id?: string;
  page_index?: number;
  page_size?: number;
}

export interface GetApiKeyListResponse {
  code?: Int64;
  msg?: string;
  data?: GetApiKeyListResponseData;
}

export interface GetApiKeyListResponseData {
  api_keys?: Array<ApiKeyInfo>;
  total?: number;
}

export interface GetAPIRespStructRequest {
  plugin_id?: string;
  api_name?: string;
  space_id?: string;
}

export interface GetAPIRespStructResponse {
  code?: Int64;
  msg?: string;
  api_struct?: Array<APIStruct>;
}

export interface GetBindCardsStatusRequest {
  space_id?: string;
  bot_id?: string;
  business_list?: Array<CardBusinessInfo>;
  agent_id?: string;
  using_master?: boolean;
}

export interface GetBindCardsStatusResponse {
  code?: Int64;
  msg?: string;
  data?: BindCardsStatusData;
}

export interface GetBindConnectionsRequest {}

export interface GetBindConnectionsResponse {
  code?: Int64;
  msg?: string;
  connections?: Array<BindConnection>;
}

export interface GetBindConnectorConfigRequest {
  /** 空间id */
  space_id: string;
  /** bot_id */
  bot_id: string;
  /** 渠道id */
  connector_id: string;
  /** 渠道应用id */
  app_id?: string;
  /** 查询参数 */
  detail?: Record<string, string>;
  /** 0: bot 1: project */
  agent_type?: Int64;
}

export interface GetBindConnectorConfigResponse {
  code?: Int64;
  msg?: string;
  config?: BindConnectorConfigDetail;
}

export interface GetBotInfoRequest {
  Version?: number;
  BotID?: string;
}

export interface GetBotInfoResponse {
  Tools?: Array<PluginApi>;
  PromptList?: string;
  ModelInfo?: string;
}

export interface GetBotInitInfoRequest {
  bot_id?: string;
  draft_mode?: boolean;
  bot_version?: string;
  preset_bot?: string;
  scene?: Scene;
}

export interface GetBotInitInfoResponse {
  background_image_info_list?: Array<BackgroundImageInfo>;
  code?: Int64;
  msg?: string;
  /** 快捷指令 */
  shortcuts?: shortcut_command.ShortcutStruct;
  bot_info?: InitBotInfo;
}

export interface GetBotListData {
  /** 结果 */
  bot_list?: Array<BotInfo>;
  page_index?: Int64;
  page_size?: Int64;
  /** 总个数 */
  total?: Int64;
}

export interface GetBotListRequest {
  /** bot创建者id */
  creator_id?: string;
  recommend?: boolean;
  bot_ids?: Array<string>;
  bot_types?: Array<BotType>;
  bot_name?: string;
  page_index?: Int64;
  page_size?: Int64;
  bot_status?: BotStatus;
  order_by?: OrderBy;
  GetBotListMode?: GetBotListMode;
}

export interface GetBotListResponse {
  code?: Int64;
  msg?: string;
  data?: GetBotListData;
}

export interface GetBotModuleInfoData {
  module_infos?: Array<string>;
  answer_action_list_config?: AnswerActionListConfig;
}

export interface GetBotModuleInfoRequest {
  space_id?: string;
}

export interface GetBotModuleInfoResponse {
  code?: Int64;
  msg?: string;
  data: GetBotModuleInfoData;
}

export interface GetBotParticipantInfoByBotIdsRequest {
  /** bot_id列表 */
  bot_ids: Array<string>;
}

export interface GetBotParticipantInfoByBotIdsResponse {
  code?: Int64;
  msg?: string;
  participant_info_map?: Record<string, BotParticipantInfo>;
}

export interface GetBotsIDETokenRequest {
  space_id?: string;
  can_write?: boolean;
}

export interface GetBotsIDETokenResponse {
  /** 返回码 */
  code?: Int64;
  /** 返回信息 */
  msg?: string;
  /** 提供给BizIDE侧的鉴权信息 */
  data: IDETokenData;
}

export interface GetBotTaskListRequest {
  bot_id: string;
  conversation_id?: string;
}

export interface GetBotTaskListResponse {
  code?: Int64;
  msg?: string;
  bot_task_list?: Array<bot_task_common.TaskInfo>;
}

export interface GetCardBindRequest {
  plugin_id?: string;
  api_name?: string;
  bot_id?: string;
  agent_id?: string;
  biz_type?: CardBusinessType;
  business_id?: string;
  unique_id?: string;
}

export interface GetCardBindResponse {
  code?: Int64;
  msg?: string;
  mapping_rule?: string;
  card_id?: string;
  max_display_rows?: Int64;
  card_version_num?: string;
  category?: CardCategory;
  llm_text_card?: boolean;
  /** 是否选中了插件预置卡片 */
  plugin_preset_card_selected?: boolean;
}

export interface GetCardInfoRequest {
  card_id: string;
  /** 不指定渠道，就返回所有渠道 */
  channel_type?: ChannelType;
  /** 如果不指定版本，那么就返回最新的版本 */
  version_num?: string;
}

export interface GetCardInfoResponse {
  code?: Int64;
  msg?: string;
  data?: CardInfoData;
}

export interface GetCardRespStructRequest {
  biz_type?: CardBizType;
  plugin_id?: string;
  unique_id?: string;
  space_id?: string;
}

export interface GetConnectorAuthStateData {
  state?: Record<string, string>;
}

export interface GetConnectorAuthStateRequest {
  connector_id?: string;
}

export interface GetConnectorAuthStateResponse {
  code?: Int64;
  msg?: string;
  data?: GetConnectorAuthStateData;
}

export interface GetConnectorUserBindConfigRequest {
  connector_id: string;
  redirect_uri: string;
}

export interface GetConnectorUserBindConfigResponse {
  code?: Int64;
  msg?: string;
  auth_url?: string;
}

export interface GetConversationBotInfoRequest {
  bot_id: Array<string>;
}

export interface GetConversationBotInfoResponse {
  code?: Int64;
  msg?: string;
  data?: Record<string, ConversationBotInfo>;
}

export interface GetConversationDetailRequest {
  bot_id?: string;
  draft_mode?: boolean;
  bot_version?: string;
  preset_bot?: string;
  scene?: Scene;
  conversation_id?: string;
  /** 首次传0/-1，0-最后一页，-1-未读第一页 */
  cursor: string;
  count: number;
  load_direction?: LoadDirection;
}

export interface GetConversationDetailResponse {
  message_list: Array<ChatMessage>;
  /** 下一刷存在时的位置（向上翻页），与next_cursor翻页方向相反。兼容旧逻辑，不加prev前缀 */
  cursor: string;
  /** 下一刷是否存在（向上翻页），与next_has_more翻页方向相反。兼容旧逻辑，不加prev前缀 */
  hasmore: boolean;
  conversation_id: string;
  /** 会话最新的section_id 只有第一刷返回 */
  last_section_id?: string;
  prologue: ChatMessage;
  suggested_questions: Array<ChatMessage>;
  name?: string;
  icon_url?: string;
  code?: Int64;
  msg?: string;
  participant_info_map: Record<string, MsgParticipantInfo>;
  /** 下一刷存在时的位置（向下翻页）， */
  next_cursor?: string;
  /** 下一刷是否存在（向下翻页） */
  next_has_more?: boolean;
  read_message_index?: string;
  /** 快捷指令 */
  shortcuts?: shortcut_command.ShortcutStruct;
  suggested_questions_show_mode?: bot_common.SuggestedQuestionsShowMode;
}

export interface GetConversationParticipantsReadIndexRequest {
  conversation_id?: string;
  bot_id?: string;
  draft_mode?: boolean;
  /** 使用的bot模版 */
  preset_bot?: string;
  scene?: Scene;
  /** 同一个bot和uid下面的不同业务情况 */
  biz_kind?: string;
}

export interface GetConversationParticipantsReadIndexResponse {
  code?: Int64;
  msg?: string;
  /** 当前用户在会话中的已读游标 */
  read_message_index: string;
  /** 当前会话中的最新消息游标 */
  end_message_index: string;
}

export interface GetConversationRequest {
  bot_id?: string;
  draft_mode?: boolean;
  /** 使用的bot模版 代替bot_id  draft_mode参数， coze home使用 preset_bot="coze_home"，prompt优化使用"coze_prompt"" */
  preset_bot?: string;
  scene?: Scene;
  biz_kind?: string;
}

export interface GetConversationResponse {
  conversation_id: string;
  /** 会话最新的section_id */
  last_section_id: string;
  code?: Int64;
  msg?: string;
}

export interface GetDraftBotDisplayInfoRequest {
  bot_id: string;
}

export interface GetDraftBotDisplayInfoResponse {
  code?: Int64;
  msg?: string;
  data?: DraftBotDisplayInfoData;
}

export interface GetDraftBotInfoRequest {
  space_id: string;
  /** draftbotid */
  bot_id: string;
  /** 查历史记录，历史版本的id */
  version?: string;
  source?: Source;
  /** 默认0 */
  botMode?: BotMode;
  /** 查询指定commit_version版本 */
  commit_version?: string;
}

export interface GetDraftBotInfoResponse {
  code?: Int64;
  msg?: string;
  data: DraftBotInfo;
  /** 是否有未发布的变更 */
  has_unpublished_change?: boolean;
  /** bot上架后的商品状态 */
  bot_market_status?: BotMarketStatus;
  /** 命中了多人协作的灰度 */
  in_collaboration?: boolean;
  /** commit内容是否和线上内容一致 */
  same_with_online?: boolean;
  /** for前端，权限相关，当前用户是否可编辑此bot */
  editable?: boolean;
  /** for前端，权限相关，当前用户是否可删除此bot */
  deletable?: boolean;
  /** 是最新发布版本时传发布人 */
  publisher?: UserInfo;
  /** 多人协作相关操作权限 */
  collaborator_status?: BotCollaboratorStatus;
}

export interface GetDraftBotListData {
  /** 结果 */
  bot_draft_list?: Array<DraftBot>;
  /** 总个数 */
  total?: number;
  /** 下次传入 */
  cursor_id?: string;
  has_more?: boolean;
}

export interface GetDraftBotListRequest {
  /** 空间id */
  space_id: string;
  /** bot_name 搜索 */
  bot_name?: string;
  /** 排序 */
  order_by?: OrderBy;
  /** 发布平台  -- 废弃 */
  publish_platform?: Array<string>;
  /** team bot 类型，代表team内的个人草稿、公开可见 */
  team_bot_type?: ListBotDraftType;
  /** 范围类型，代表team公开可见的 All、Mine -- 废弃 */
  scope_type?: ScopeType;
  /** 分页 */
  page_index?: number;
  /** 分页大小 */
  page_size?: number;
  /** 是否已发布 */
  is_publish?: PublishStatus;
  /** 获取第一页不传，后续调用时传上一次返回的cursor_id */
  cursor_id?: string;
  is_fav?: boolean;
  /** 需要的状态列表 默认只返回 Using = 1 */
  draft_bot_status_list?: Array<DraftBotStatus>;
  /** 是否按最近打开筛选 */
  recently_open?: boolean;
}

export interface GetDraftBotListResponse {
  code?: Int64;
  msg?: string;
  data?: GetDraftBotListData;
}

export interface GetExploreBotListData {
  /** 结果 */
  bot_draft_list?: Array<DraftBot>;
  /** 总个数 */
  total?: number;
}

export interface GetExploreBotListRequest {
  /** 发布平台 */
  publish_platform?: Array<string>;
  /** 分页 */
  page_index?: number;
  /** 分页大小 */
  page_size?: number;
  key_word?: string;
  category_id?: Array<string>;
}

export interface GetExploreBotListResponse {
  code?: Int64;
  msg?: string;
  data?: GetExploreBotListData;
}

export interface GetExploreCategoryListData {
  category?: Array<ExploreBotCategory>;
}

export interface GetExploreCategoryListRequest {}

export interface GetExploreCategoryListResponse {
  code?: Int64;
  msg?: string;
  data?: GetExploreCategoryListData;
}

export interface GetGenerateIconInfoData {
  /** 用户当天访问次数 */
  current_day_count?: Int64;
}

export interface GetGenerateIconInfoRequest {}

export interface GetGenerateIconInfoResponse {
  code?: Int64;
  msg?: string;
  data?: GetGenerateIconInfoData;
}

export interface GetIconRequest {
  icon_type?: IconType;
}

export interface GetIconResponse {
  code?: Int64;
  msg?: string;
  data?: GetIconResponseData;
}

export interface GetIconResponseData {
  icon_list?: Array<Icon>;
}

export interface GetLoginInfoRequest {}

export interface GetLoginInfoResponse {
  code?: Int64;
  msg?: string;
  data?: LoginInfo;
}

export interface GetMessageListRequest {
  conversation_id?: string;
  /** 首次传0/-1，0-最后一页，-1-未读第一页 */
  cursor: string;
  count: number;
  bot_id?: string;
  draft_mode?: boolean;
  /** 使用的bot模版 */
  preset_bot?: string;
  scene?: Scene;
  /** 同一个bot和uid下面的不同业务情况 */
  biz_kind?: string;
  /** 存在创建聊天记录前需要插入聊天的情况 */
  insert_history_message_list?: Array<string>;
  load_direction?: LoadDirection;
  /** 在已有conversation情况下，是否强制append message */
  must_append?: boolean;
  /** 分享ID */
  share_id?: string;
}

export interface GetMessageListResponse {
  message_list: Array<ChatMessage>;
  /** 下一刷存在时的位置（向上翻页），与next_cursor翻页方向相反。兼容旧逻辑，不加prev前缀 */
  cursor: string;
  /** 下一刷是否存在（向上翻页），与next_has_more翻页方向相反。兼容旧逻辑，不加prev前缀 */
  hasmore: boolean;
  conversation_id: string;
  /** 会话最新的section_id 只有第一刷返回 */
  last_section_id?: string;
  code?: Int64;
  msg?: string;
  participant_info_map?: Record<string, MsgParticipantInfo>;
  /** 下一刷存在时的位置（向下翻页）， */
  next_cursor?: string;
  /** 下一刷是否存在（向下翻页） */
  next_has_more?: boolean;
  read_message_index?: string;
  /** botconnector对应的id */
  connector_conversation_id?: string;
}

export interface GetOAuthSchemaRequest {}

export interface GetOAuthSchemaResponse {
  code?: Int64;
  msg?: string;
  /** 约定的json */
  oauth_schema?: string;
  /** ide创建plugin的配置 */
  ide_conf?: string;
}

export interface GetOnboardingRequest {
  bot_id?: string;
  bot_prompt?: string;
}

export interface GetOnboardingResponse {
  code?: Int64;
  msg?: string;
  data?: GetOnboardingResponseData;
}

export interface GetOnboardingResponseData {
  onboarding_content?: OnboardingContent;
}

export interface GetPERulesRequest {
  scene: string;
  action: string;
}

export interface GetPERulesResponse {
  rule?: string;
  code?: Int64;
  msg?: string;
}

export interface GetPlaygroundPluginListData {
  plugin_infos?: Array<PluginInfoForPlayground>;
  total?: Int64;
}

export interface GetPlaygroundPluginListRequest {
  page: number;
  size: number;
  /** 按照api名称搜索 */
  name?: string;
  /** 插件id */
  plugin_ids?: Array<string>;
  /** 插件标签 */
  plugin_tag?: Int64;
  /** 为true只返回自己创建的插件 */
  self_created?: boolean;
  plugin_type?: PluginListPluginType;
  /** 空间id */
  space_id?: string;
  order_by?: OrderBy;
}

export interface GetPlaygroundPluginListResponse {
  code?: Int64;
  msg?: string;
  get_plugin_list_data: GetPlaygroundPluginListData;
}

export interface GetPlaygroundRecordRequest {
  /** 查历史记录 */
  version?: number;
  task_id?: string;
  /** 传task_name，不传task_id，默认是chain task */
  task_name?: string;
}

export interface GetPlaygroundRecordResponse {
  code?: Int64;
  msg?: string;
  data?: GetPlaygroundRecordResponseData;
}

export interface GetPlaygroundRecordResponseData {
  item_infos?: Array<ItemInfo>;
  bot_name?: string;
  task_name?: string;
}

export interface GetPluginAPIsRequest {
  plugin_id: string;
  api_ids?: Array<string>;
  page?: number;
  size?: number;
  order?: APIListOrder;
}

export interface GetPluginAPIsResponse {
  code?: Int64;
  msg?: string;
  api_info?: Array<PluginAPIInfo>;
  total?: number;
}

export interface GetPluginCardsData {
  plugin_cards?: Array<PluginCards>;
}

export interface GetPluginCardsRequest {
  space_id?: string;
  bot_id?: string;
  plugins?: Array<APIInfo>;
  agent_id?: string;
  using_master?: boolean;
}

export interface GetPluginCardsResponse {
  code?: Int64;
  msg?: string;
  data?: GetPluginCardsData;
}

export interface GetPluginCurrentRequest {
  /** plugin_id */
  plugin_id: string;
}

export interface GetPluginCurrentResponse {
  code?: Int64;
  msg?: string;
  data?: PluginCurrentInfo;
}

export interface GetPluginInfoRequest {
  /** 目前只支持插件openapi插件的信息 */
  plugin_id: string;
}

export interface GetPluginInfoResponse {
  code?: Int64;
  msg?: string;
  meta_info?: PluginMetaInfo;
  code_info?: CodeInfo;
  /** 0 无更新 1 有更新未发布 */
  status?: boolean;
  /** 是否已发布 */
  published?: boolean;
  /** 创建人信息 */
  creator?: Creator;
  statistic_data?: PluginStatisticData;
  /** plugin的商品状态 */
  plugin_product_status?: product_common.ProductStatus;
  creation_method?: CreationMethod;
  ide_code_runtime?: string;
}

export interface GetPluginListData {
  plugin_infos?: Array<PluginInfo>;
  total?: Int64;
  page?: number;
  size?: number;
}

export interface GetPluginListRequest {
  status?: Array<PluginStatus>;
  page?: number;
  size?: number;
  space_id?: string;
  scope_type?: ScopeType;
  order_by?: OrderBy;
  /** 发布状态筛选：true:已发布, false:未发布 */
  publish_status?: boolean;
  /** 插件名或工具名 */
  name?: string;
}

export interface GetPluginListResponse {
  code?: Int64;
  msg?: string;
  get_plugin_list_data: GetPluginListData;
}

export interface GetPluginTagsRequest {}

export interface GetPluginTagsResponse {
  code?: Int64;
  msg?: string;
  plugin_tags?: Array<PluginTag>;
}

export interface GetPublishedPluginListRequest {
  space_id?: string;
  page?: number;
  size?: number;
  user_space_ids?: Array<string>;
  plugin_ids?: Array<string>;
}

export interface GetPublishedPluginListResponse {
  code?: Int64;
  msg?: string;
  data?: PublishedPluginListData;
}

export interface GetReleasedWorkflowsRequest {
  page?: number;
  size?: number;
  type?: WorkFlowType;
  name?: string;
  workflow_ids?: Array<string>;
  tags?: Tag;
  space_id?: string;
  order_by?: OrderBy;
  login_user_create?: boolean;
}

export interface GetReleasedWorkflowsResponse {
  code?: Int64;
  msg?: string;
  data: ReleasedWorkflowData;
}

export interface GetSSOUserInfoData {
  /** 姓名 */
  username?: string;
  /** region */
  region?: string;
  /** 工号 */
  employee_id?: Int64;
  /** 邮箱 */
  email?: string;
  /** 头像地址 */
  avatar_url?: string;
  /** 开发者平台的userid */
  user_id?: string;
}

export interface GetSSOUserInfoRequest {}

export interface GetSSOUserInfoResponse {
  code?: Int64;
  msg?: string;
  data?: GetSSOUserInfoData;
}

export interface GetTaskIntroRequest {
  system_prompt?: string;
  /** \n分隔 */
  suggested_questions?: string;
}

export interface GetTaskIntroResponse {
  code: number;
  msg: string;
  intro?: string;
}

export interface GetTaskProgressRequest {
  document_ids?: Array<string>;
}

export interface GetTaskProgressResponse {
  data?: Array<ProgressData>;
  code?: Int64;
  msg?: string;
}

export interface GetTypeListData {
  model_list?: Array<Model>;
  voice_list?: Array<VoiceType>;
  raw_model_list?: Array<Model>;
  model_show_family_list?: Array<ModelShowFamily>;
  default_model_id?: Int64;
}

export interface GetTypeListRequest {
  model?: boolean;
  voice?: boolean;
  raw_model?: boolean;
  space_id?: string;
  /** 当前bot使用的模型ID，用于处理cici/doubao同步过来的bot模型不能展示的问题 */
  cur_model_id?: string;
  /** 兼容MultiAgent，有多个cur_model_id */
  cur_model_ids?: Array<string>;
  /** 模型场景 */
  model_scene?: ModelScene;
}

export interface GetTypeListResponse {
  code?: Int64;
  msg?: string;
  data: GetTypeListData;
}

export interface GetUpdatedAPIsRequest {
  plugin_id: string;
}

export interface GetUpdatedAPIsResponse {
  code?: Int64;
  msg?: string;
  created_api_names?: Array<string>;
  deleted_api_names?: Array<string>;
  updated_api_names?: Array<string>;
}

export interface GetUploadAuthTokenData {
  service_id?: string;
  upload_path_prefix?: string;
  auth?: UploadAuthTokenInfo;
  upload_host?: string;
}

export interface GetUploadAuthTokenRequest {
  scene?: string;
  data_type?: string;
}

export interface GetUploadAuthTokenResponse {
  code?: Int64;
  msg?: string;
  data?: GetUploadAuthTokenData;
}

export interface GetUserAuthListRequest {}

export interface GetUserAuthListResponse {
  code?: Int64;
  msg?: string;
  data?: Array<UserAuthInfo>;
}

export interface GetUserBotFavoriteData {
  bot_infos?: Array<BotInfo>;
  total?: Int64;
}

export interface GetUserBotFavoriteRequest {
  page: number;
  size: number;
  /** 按照bot名称搜索 */
  name?: string;
}

export interface GetUserBotFavoriteResponse {
  code?: Int64;
  msg?: string;
  get_user_bot_fav_data: GetUserBotFavoriteData;
}

export interface GetVoiceConfigRequest {}

export interface GetVoiceConfigResponse {
  code?: Int64;
  msg?: string;
  data?: Array<VoiceInfo>;
}

export interface GetVoiceTokenRequest {}

export interface GetVoiceTokenResponse {
  code?: Int64;
  msg?: string;
  data?: VoiceToken;
}

export interface GetWorkflowGrayFeatureRequest {
  /** 空间id */
  space_id?: string;
}

export interface GetWorkflowGrayFeatureResponse {
  /** 返回码 */
  code?: Int64;
  /** 返回信息 */
  msg?: string;
  /** 灰度feature结果 */
  data?: Array<WorkflowGrayFeatureItem>;
}

export interface GetWorkflowMessageNodesData {
  id?: string;
  plugin_id?: string;
  name?: string;
  message_nodes?: Array<NodeInfo>;
}

export interface GetWorkflowMessageNodesRequest {
  /** 空间id */
  space_id?: string;
  plugin_id?: string;
}

export interface GetWorkflowMessageNodesResponse {
  /** 返回码 */
  code?: Int64;
  /** 返回信息 */
  msg?: string;
  /** 结果 */
  data?: GetWorkflowMessageNodesData;
}

export interface GetWorkflowPluginListData {
  plugin_infos?: Array<PluginInfoForPlayground>;
  total?: Int64;
}

export interface GetWorkflowPluginListRequest {
  page: number;
  size: number;
  /** 按照api名称搜索 */
  name?: string;
  /** 空间id */
  space_id: string;
}

export interface GetWorkflowPluginListResponse {
  code?: Int64;
  msg?: string;
  get_plugin_list_data: GetWorkflowPluginListData;
}

export interface GetWorkFlowProcessData {
  workFlowId?: string;
  executeId?: string;
  executeStatus?: WorkflowExeStatus;
  nodeResults?: Array<NodeResult>;
  /** 执行进度 */
  rate?: string;
  /** 现节点试运行状态 1：没有试运行 2：试运行过 */
  exeHistoryStatus?: WorkflowExeHistoryStatus;
  /** workflow试运行耗时 */
  workflowExeCost?: string;
  /** 消耗 */
  tokenAndCost?: TokenAndCost;
  /** 失败原因 */
  reason?: string;
}

export interface GetWorkFlowProcessReq {
  executeId?: string;
  workflowId?: string;
  space_id?: string;
}

export interface GetWorkFlowProcessResponse {
  code?: Int64;
  msg?: string;
  data?: GetWorkFlowProcessData;
}

export interface GetWorkflowProcessV2Request {
  workflow_id: string;
  space_id: string;
  execute_id?: string;
}

export interface GetWorkflowProcessV2Response {
  code?: Int64;
  msg?: string;
  data?: GetWorkFlowProcessData;
}

export interface GetWorkflowReferencesRequest {
  workflow_id?: string;
  space_id?: string;
}

export interface GetWorkflowReferencesResponse {
  code?: Int64;
  msg?: string;
  data: WorkflowReferencesData;
}

export interface GradientPosition {
  left?: number;
  right?: number;
}

/** 如果保存历史信息 */
export interface HistoryInfo {
  version?: string;
  history_type?: HistoryType;
  /** 对历史记录补充的其他信息 */
  info?: string;
  create_time?: string;
  connector_infos?: Array<ConnectorInfo>;
  creator?: Creator;
  publish_id?: string;
  /** 提交时填写的说明 */
  commit_remark?: string;
}

export interface Icon {
  url?: string;
  uri?: string;
}

export interface IDETokenData {
  /** 提供给BizIDE侧的临时token */
  token: string;
  /** token过期时间 */
  expired_at: Int64;
}

export interface IfBranch {
  /** 该分支的条件 */
  if_conditions?: Array<IfCondition>;
  /** 该分支各条件的关系 */
  if_condition_relation?: IfConditionRelation;
  /** 该分支对应的下一个节点 */
  next_node_id?: Array<string>;
}

export interface IfCondition {
  first_parameter: Parameter;
  condition: ConditionType;
  second_parameter: Parameter;
}

export interface IfParam {
  if_branch?: IfBranch;
  else_branch?: IfBranch;
}

export interface IndependentModeConfig {
  /** 判断时机 */
  judge_timing?: IndependentTiming;
  history_round?: number;
  model_type?: IndependentRecognitionModelType;
  model_id?: string;
  prompt?: string;
}

export interface InitBotInfo {
  /** bot id */
  bot_id?: string;
  /** bot名称 */
  name?: string;
  /** bot描述 */
  description?: string;
  /** bot 图标uri */
  icon_uri?: string;
  /** bot 图标url */
  icon_url?: string;
  /** 音色配置 */
  voice_info?: InitVoicesInfo;
}

export interface InitVoicesInfo {
  /** 默认用户输入类型 */
  default_user_input_type?: bot_common.DefaultUserInputType;
  /** 多语音音色配置 */
  i18n_lang_voice?: Record<string, string>;
  /** 是否自动播放 */
  autoplay?: boolean;
  /** 是否关闭语音通话，true:关闭 false:开启  默认为false */
  close_voice_call?: boolean;
  video_call_config?: bot_common.VideoCallConfig;
  voiceprint_recognition_config?: bot_common.VoiceprintRecognitionConfig;
  /** key: lang value: voiceConfig */
  i18nlang_voice_parameter_config?: Record<
    string,
    bot_common.I18nLangVoiceParameterConfig
  >;
}

export interface Intent {
  intent_id?: string;
  prompt?: string;
  next_agent_id?: string;
}

export interface InterruptFunction {
  name?: string;
  arguments?: string;
}

export interface InterruptPlugin {
  id?: string;
  /** 1 function, 2 require_info */
  type?: string;
  function?: InterruptFunction;
  require_info?: InterruptRequireInfo;
}

export interface InterruptRequireInfo {
  require_fields?: string;
  name?: string;
}

export interface InviteMemberLinkData {
  key?: string;
}

export interface InviteMemberLinkRequest {
  space_id: string;
  /** true-打开链接；false-关闭链接 */
  team_invite_link_status: boolean;
  /** 1 获取信息 */
  func?: InviteFunc;
}

export interface InviteMemberLinkResponse {
  code?: Int64;
  msg?: string;
  data?: InviteMemberLinkData;
}

export interface IsvWebhookEventReq {
  encrypt?: string;
}

export interface IsvWebhookEventResp {
  code?: Int64;
  msg?: string;
  challenge?: string;
}

export interface ItemInfo {
  item_id?: string;
  item_type?: ItemType;
  name?: string;
  value?: string;
  item_status?: ItemStatus;
}

export interface JoinSpaceData {
  space_id?: string;
}

export interface JoinSpaceRequest {
  space_id: string;
}

export interface JoinSpaceResponse {
  code?: Int64;
  msg?: string;
  Data: JoinSpaceData;
}

export interface JumpConfig {
  backtrack?: BacktrackMode;
  recognition?: RecognitionMode;
  independent_conf?: IndependentModeConfig;
}

export interface LaunchConfig {
  /** frontier 配置(1~5) 用于 Bot 调试, Prompt 优化等长期. 后续该通道可能废弃. */
  frontier_app_key?: string;
  frontier_access_key?: string;
  frontier_app_id?: string;
  frontier_product_id?: string;
  frontier_domain?: string;
  message_service_id?: string;
  /** event_frontier 是 Coze 页面通用的上下行消息通道配置.
详见:  */
  event_frontier?: FrontierConfig;
  biz_config?: string;
}

export interface LaunchData {
  config?: LaunchConfig;
  type?: DeveloperType;
}

export interface LaunchRequest {
  device_id?: Int64;
}

export interface LaunchResponse {
  code?: Int64;
  msg?: string;
  data?: LaunchData;
}

export interface LayOut {
  x?: number;
  y?: number;
}

export interface LayoutInfo {
  /** workflowId */
  workflow_id?: string;
  /** PluginId */
  plugin_id?: string;
}

export interface ListDataSetData {
  data_set_infos?: Array<DataSetItem>;
  total?: number;
}

export interface ListDataSetRequest {
  /** 关键字搜索 */
  query?: string;
  /** 搜索类型 */
  search_type?: DataSetSearchType;
  dataset_ids?: Array<string>;
  page?: number;
  size?: number;
  /** 空间id */
  space_id?: string;
  /** 范围类型 */
  scope_type?: DataSetScopeType;
  /** 来源 */
  source_type?: DataSetSource;
}

export interface ListDataSetResponse {
  code?: Int64;
  msg?: string;
  data?: ListDataSetData;
}

export interface ListDraftBotHistoryData {
  history_infos?: Array<HistoryInfo>;
  total?: number;
}

export interface ListDraftBotHistoryRequest {
  space_id: string;
  bot_id: string;
  page_index: number;
  page_size: number;
  history_type: HistoryType;
  connector_id?: string;
}

export interface ListDraftBotHistoryResponse {
  code?: Int64;
  msg?: string;
  data: ListDraftBotHistoryData;
}

export interface ListItemsResultItem {
  iid?: string;
  text: string;
  event_ms: string;
  ext?: Record<string, string>;
}

export interface ListPlaygroundHistoryInfoRequest {
  task_name?: string;
  task_id?: string;
}

export interface ListPlaygroundHistoryInfoResponse {
  code?: Int64;
  msg?: string;
  data?: ListPlaygroundHistoryInfoResponseData;
}

export interface ListPlaygroundHistoryInfoResponseData {
  playground_history_infos?: Array<PlaygroundHistoryInfo>;
}

export interface LLMParam {
  model_type?: number;
  temperature?: number;
  prompt?: string;
  model_name?: string;
}

export interface LoginInfo {
  IsForbiddenRegion?: boolean;
  LoginValidationMode?: LoginValidationMode;
  AgeGateMode?: AgeGateMode;
  CountryCode?: string;
  UserProfileEditStatus?: UserProfileEditStatus;
}

export interface LogoutRequest {}

export interface LogoutResponse {}

export interface MapData {
  workflow_id?: string;
  node_id?: string;
  node_name?: string;
  parameters?: Array<Parameter>;
}

export interface MapDataData {
  map_data?: Array<MapData>;
}

export interface MapDataRequest {
  workflow_id: string;
  node_id: string;
  param_type?: BatchNodeParamType;
  space_id?: string;
}

export interface MapDataResponse {
  code?: Int64;
  msg?: string;
  data: MapDataData;
}

export interface MarkReadRequest {
  conversation_id: string;
  read_message_index: string;
  mark_time: Int64;
}

export interface MarkReadResponse {
  code?: Int64;
  msg?: string;
  read_message_index: string;
}

export interface MemberInfo {
  /** 用户id */
  user_id?: string;
  /** 用户名称 */
  name?: string;
  /** 用户图标 */
  icon_url?: string;
  /** 成员角色 */
  space_role_type?: SpaceRoleType;
  /** 是否已经加入空间 */
  is_join?: boolean;
  /** 加入日期 */
  join_date?: string;
  /** bot平台唯一用户名称 */
  user_name?: string;
}

export interface MessageaAttributes {
  /** 需要更新的卡片状态，覆盖更新传入的 kv 对，而不是直接覆盖整个 map */
  card_status?: Record<string, string>;
}

export interface MessageInfo {
  role?: MessageInfoRole;
  content?: string;
  /** 1 文本消息(默认) 2 建议词 50 卡片,enum和contenttype对齐 */
  content_type?: number;
  ext?: Record<string, string>;
}

/** 调试prompt任务的msginfo */
export interface MessageInfoPrompt {
  role?: string;
  content?: string;
}

export interface MGetCardTemplateData {
  total: Int64;
  card_templates: Array<CardTemplateInfo>;
}

export interface MGetCardTemplateRequest {
  creator_id?: string;
  size?: Int64;
  page?: Int64;
  channel_type: ChannelType;
  category: CardCategory;
}

export interface MGetCardTemplateResponse {
  code?: Int64;
  msg?: string;
  data?: MGetCardTemplateData;
}

export interface MigrateRequest {
  type: MigrateType;
  run_model: MigrateRunModel;
  space_id?: Int64;
  workflow_id?: Int64;
}

export interface MigrateResponse {
  data?: string;
  success_id?: Array<Int64>;
  failed_id?: Array<Int64>;
}

export interface Model {
  name?: string;
  model_type?: Int64;
  model_class?: ModelClass;
  /** model icon的url */
  model_icon?: string;
  model_input_price?: number;
  model_output_price?: number;
  model_quota?: ModelQuota;
  /** model真实名，前端计算token用 */
  model_name?: string;
  model_class_name?: string;
  is_offline?: boolean;
  model_params?: Array<ModelParameter>;
  model_desc?: Array<ModelDescGroup>;
  /** 模型功能配置 */
  func_config?: Partial<
    Record<bot_common.ModelFuncConfigType, bot_common.ModelFuncConfigStatus>
  >;
  /** 方舟模型节点名称 */
  endpoint_name?: string;
  /** 模型标签 */
  model_tag_list?: Array<ModelTag>;
  /** user prompt是否必须有且不能为空 */
  is_up_required?: boolean;
  /** 模型简要描述 */
  model_brief_desc?: string;
  /** 模型系列 */
  model_series?: ModelSeriesInfo;
  /** 模型状态 */
  model_status_details?: ModelStatusDetails;
  /** 模型能力 */
  model_ability?: ModelAbility;
  model_show_family_id?: string;
  hot_flag?: number;
  hot_ranking?: number;
  online_time?: Int64;
  /** 0-用户可见 1-用户不可见 */
  config_type?: number;
  offline_time?: Int64;
}

export interface ModelAbility {
  /** 是否展示cot */
  cot_display?: boolean;
  /** 是否支持function call */
  function_call?: boolean;
  /** 是否支持图片理解 */
  image_understanding?: boolean;
  /** 是否支持视频理解 */
  video_understanding?: boolean;
  /** 是否支持音频理解 */
  audio_understanding?: boolean;
  /** 是否支持多模态 */
  support_multi_modal?: boolean;
  /** 是否支持续写 */
  prefill_resp?: boolean;
}

export interface ModelDescGroup {
  group_name?: string;
  desc?: Array<string>;
}

export interface ModelInfo {
  model?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  ShortMemPolicy?: ShortMemPolicy;
  prompt_id?: number;
  card_ids?: Array<number>;
  model_name?: string;
  answer_actions?: AnswerActions;
  /** 生成时，采样候选集的大小 */
  top_k?: number;
  /** 模型回复内容格式 */
  response_format?: bot_common.ModelResponseFormat;
  /** 用户选择的模型风格 */
  model_style?: bot_common.ModelStyle;
  cache_type?: bot_common.CacheType;
  /** sp拼接当前时间 */
  sp_current_time?: boolean;
  /** sp拼接防泄露指令 */
  sp_anti_leak?: boolean;
  /** sp拼接声纹信息 */
  sp_voice_info?: boolean;
}

export interface ModelParamClass {
  /** 1="Generation diversity", 2="Input and output length", 3="Output format" */
  class_id?: number;
  label?: string;
}

export interface ModelParamDefaultValue {
  default_val: string;
  creative?: string;
  balance?: string;
  precise?: string;
}

export interface ModelParameter {
  /** 配置字段，如max_tokens */
  name: string;
  /** 配置字段展示名称 */
  label?: string;
  /** 配置字段详情描述 */
  desc?: string;
  /** 类型 */
  type: ModelParamType;
  /** 数值类型参数，允许设置的最小值 */
  min?: string;
  /** 数值类型参数，允许设置的最大值 */
  max?: string;
  /** float类型参数的精度 */
  precision?: number;
  /** 参数默认值{"default": xx, "creative":xx} */
  default_val: ModelParamDefaultValue;
  /** 枚举值，如response_format支持text,markdown,json */
  options?: Array<Option>;
  /** 参数分类，"Generation diversity", "Input and output length", "Output format" */
  param_class?: ModelParamClass;
  custom_flag?: boolean;
}

export interface ModelQuota {
  /** 最大总 token 数量 */
  token_limit?: number;
  /** 最终回复最大 token 数量 */
  token_resp?: number;
  /** Prompt 系统最大 token 数量 */
  token_system?: number;
  /** Prompt 用户输入最大 token 数量 */
  token_user_in?: number;
  /** Prompt 工具输入最大 token 数量 */
  token_tools_in?: number;
  /** Prompt 工具输出最大 token 数量 */
  token_tools_out?: number;
  /** Prompt 数据最大 token 数量 */
  token_data?: number;
  /** Prompt 历史最大 token 数量 */
  token_history?: number;
  /** Prompt 历史最大 token 数量 */
  token_cut_switch?: boolean;
  /** 输入成本 */
  price_in?: number;
  /** 输出成本 */
  price_out?: number;
  /** systemprompt输入限制，如果没有传，对输入不做限制 */
  system_prompt_limit?: number;
}

export interface ModelSeriesInfo {
  series_name?: string;
  icon_url?: string;
  model_vendor?: string;
  model_tips?: string;
}

export interface ModelShowFamily {
  id?: Int64;
  icon?: string;
  iconUrl?: string;
  name?: string;
  ranking?: number;
}

export interface ModelStatusDetails {
  /** 是否为新模型 */
  is_new_model?: boolean;
  /** 是否是高级模型 */
  is_advanced_model?: boolean;
  /** 是否是免费模型 */
  is_free_model?: boolean;
  /** 是否即将下架 */
  is_upcoming_deprecated?: boolean;
  /** 下架日期 */
  deprecated_date?: string;
  /** 下架替换的模型 */
  replace_model_name?: string;
  /** 最近更新信息 */
  update_info?: string;
  /** 模型特色 */
  model_feature?: ModelTagValue;
}

export interface ModelTag {
  tag_name?: string;
  tag_class?: ModelTagClass;
  tag_icon?: string;
  tag_descriptions?: string;
}

export interface MonetizationConf {
  is_enable?: boolean;
}

export interface MsgParticipantInfo {
  id?: string;
  type?: MsgParticipantType;
  name?: string;
  desc?: string;
  avatar_url?: string;
  space_id?: string;
  user_id?: string;
  user_name?: string;
  allow_mention?: boolean;
  access_path?: string;
  /** 是否被收藏 */
  is_fav?: boolean;
  /** 快捷指令 */
  shortcuts?: shortcut_command.ShortcutStruct;
  /** 是否允许被分享 */
  allow_share?: boolean;
}

export interface MultiAgentInfo {
  session_type?: MultiAgentSessionType;
  version_compat_info?: AgentVersionCompatInfo;
  connector_type?: MultiAgentConnectorType;
}

/** 节点结构 */
export interface Node {
  workflow_id?: string;
  /** 节点id */
  node_id?: string;
  /** 更改node名称 */
  node_name?: string;
  /** 节点类型 */
  node_type?: NodeType;
  /** 节点的核心参数 */
  node_param?: NodeParam;
  /** Node的位置 */
  lay_out?: LayOut;
  /** Node的描述，说明链接 */
  desc?: NodeDesc;
  /** 依赖的上游节点 */
  depends_on?: Array<string>;
  /** 所有的输入和输出 */
  open_api?: OpenAPI;
}

export interface NodeDesc {
  desc?: string;
  /** 副标题名称 */
  name?: string;
  /** 该类型的icon */
  icon_url?: string;
  /** 是否支持批量，1不支持，2支持 */
  support_batch?: number;
  /** 连接要求 1左右都可连接 2只支持右侧 */
  link_limit?: number;
}

export interface NodeError {
  node_id?: string;
}

export interface NodeInfo {
  node_id?: string;
  node_type?: string;
  node_title?: string;
}

export interface NodeListData {
  node_list?: Array<Node>;
}

export interface NodeListRequest {
  workflow_id: string;
  node_ids?: Array<string>;
  space_id?: string;
}

export interface NodeListResponse {
  code?: Int64;
  msg?: string;
  data: NodeListData;
}

export interface NodeParam {
  /** 输入参数列表，支持多级；支持mapping */
  input_list?: Array<Param>;
  /** 输出参数列表，支持多级 */
  output_list?: Array<Param>;
  /** 如果是API类型的Node，插件名、API名、插件版本、API的描述 */
  api_param?: APIParam;
  /** 如果是代码片段，则包含代码内容 */
  code_param?: CodeParam;
  /** 如果是模型，则包含模型的基础信息 */
  llm_param?: LLMParam;
  /** 如果是数据集，选择数据集的片段 */
  dataset_param?: DatasetParam;
  /** end节点，如何结束 */
  terminate_plan?: TerminatePlan;
  /** （新）输入参数列表 */
  input_parameters?: Array<Parameter>;
  /** （新）输出参数列表 */
  output_parameters?: Array<Parameter>;
  /** 批量设置情况 */
  batch?: Batch;
  /** if节点参数 */
  if_param?: IfParam;
}

export interface NodeProps {
  id?: string;
  type?: string;
  is_enable_chat_history?: boolean;
  is_enable_user_query?: boolean;
}

export interface NodeResult {
  nodeId?: string;
  NodeType?: string;
  NodeName?: string;
  nodeStatus?: NodeExeStatus;
  errorInfo?: string;
  /** 入参 jsonstring类型 */
  input?: string;
  /** 出参 jsonstring */
  output?: string;
  /** 运行耗时 eg：3s */
  nodeExeCost?: string;
  /** 消耗 */
  tokenAndCost?: TokenAndCost;
  /** 直接输出 */
  raw_output?: string;
  errorLevel?: string;
  index?: number;
  items?: string;
  maxBatchSize?: number;
  limitVariable?: string;
  loopVariableLen?: number;
  batch?: string;
  isBatch?: boolean;
  logVersion?: number;
  extra?: string;
}

export interface NodeTemplate {
  id?: string;
  type?: NodeType;
  name?: string;
  desc?: string;
  icon_url?: string;
  support_batch?: SupportBatch;
}

export interface NodeTemplateListData {
  template_list?: Array<NodeTemplate>;
}

export interface NodeTemplateListRequest {
  /** 需要的节点类型 不传默认返回全部 */
  need_types?: Array<NodeType>;
}

export interface NodeTemplateListResponse {
  code?: Int64;
  msg?: string;
  data?: NodeTemplateListData;
}

/** Onboarding json结构 */
export interface OnboardingContent {
  /** 开场白 */
  prologue?: string;
  /** 建议问题 */
  suggested_questions?: Array<string>;
}

export interface OpenAPI {
  input_list?: Array<Parameter>;
  output_list?: Array<Parameter>;
}

export interface Option {
  /** option展示的值 */
  label?: string;
  /** 填入的值 */
  value?: string;
}

export interface Options {
  label?: string;
  value?: string;
}

export interface Param {
  key?: Array<string>;
  desc?: string;
  type?: InputType;
  required?: boolean;
  value?: string;
  /** 要求  1不允许删除 2不允许更改名称 3什么都可修改 4只显示，全部不允许更改 */
  requirement?: ParamRequirementType;
  from_node_id?: string;
  from_output?: Array<string>;
}

export interface Parameter {
  name?: string;
  desc?: string;
  required?: boolean;
  type?: InputType;
  sub_parameters?: Array<Parameter>;
  /** 如果Type是数组，则有subtype */
  sub_type?: InputType;
  /** 如果入参的值是引用的则有fromNodeId */
  from_node_id?: string;
  /** 具体引用哪个节点的key */
  from_output?: Array<string>;
  /** 如果入参是用户手输 就放这里 */
  value?: string;
  format?: PluginParamTypeFormat;
}

export interface parametersStruct {
  value?: string;
  /** "uri" */
  resource_type?: string;
}

export interface PathError {
  start?: string;
  end?: string;
  /** 路径上的节点ID */
  path?: Array<string>;
}

/** 如果保存历史信息 */
export interface PlaygroundHistoryInfo {
  history_id?: string;
  history_type?: HistoryType;
  /** 对历史记录补充的其他信息 */
  history_info?: string;
  history_time?: string;
  history_version?: number;
}

export interface PluginApi {
  /** operationId */
  name?: string;
  /** summary */
  desc?: string;
  parameters?: Array<PluginParameter>;
  plugin_id?: string;
  plugin_name?: string;
  /** 序号和playground保持一致 */
  api_id?: string;
  record_id?: string;
  plugin_is_official?: boolean;
  plugin_icon_url?: string;
  plugin_type?: PluginType;
}

export interface PluginAPIInfo {
  plugin_id?: string;
  api_id?: string;
  name?: string;
  desc?: string;
  path?: string;
  method?: APIMethod;
  request_params?: Array<APIParameter>;
  response_params?: Array<APIParameter>;
  create_time?: string;
  debug_status?: APIDebugStatus;
  disabled?: boolean;
  statistic_data?: PluginStatisticData;
  /** ide创建插件展示tool的在线状态 */
  online_status?: OnlineStatus;
}

export interface PluginCards {
  card_id?: string;
  plugin_id?: string;
  api_name?: string;
  status?: PluginCardStatus;
}

export interface PluginCurrentInfo {
  plugin_id?: string;
  item_infos?: Array<PluginCurrentInfoItemInfo>;
}

export interface PluginCurrentInfoItemInfo {
  item_id?: string;
  /** 1. source_code 类型 2. input 类型 3. api yaml 类型 */
  item_type?: PluginCurrentInfoItemType;
  content?: string;
}

export interface PluginDebugRunRequest {
  /** 函数代码 */
  source_code: string;
  /** 函数入参 */
  input_params?: string;
}

export interface PluginDebugRunResponse {
  code?: Int64;
  msg?: string;
  DebugResult?: string;
}

export interface PluginIcon {
  uri?: string;
  url?: string;
}

export interface PluginInfo {
  id?: string;
  /** name_for_human */
  name?: string;
  /** description_for_human */
  desc_for_human?: string;
  plugin_icon?: string;
  plugin_type?: PluginType;
  status?: PluginStatus;
  /** json */
  plugin_desc?: string;
  /** yaml,openapi插件不返回 */
  openapi_desc?: string;
  auth?: number;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  create_time?: Int64;
  update_time?: Int64;
  ClientUrl?: string;
  Scope?: string;
  RedirectUri?: string;
  /** 查询用户和插件关系时返回
用户安装状态 */
  InstallStatus?: InstallStatus;
  /** 用户id，用户查询安装/未安装列表时选择 */
  UserID?: Int64;
  WorkFlowId?: string;
  /** 插件包含的api名称，所有类型的插件都支持 */
  api_names?: Array<string>;
  /** 创建人信息 */
  creator?: Creator;
  /** 发布状态 */
  publish_status?: boolean;
  space_id?: string;
  /** 插件统计数据 */
  statistic_data?: PluginStatisticData;
  /** 公共参数列表 */
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  /** plugin的商品上下架状态 */
  plugin_product_list_status?: product_common.ProductStatus;
  /** plugin的商品状态(组合状态) */
  plugin_product_status?: plugin_common.PluginProductStatus;
  /** 插件创建方式 */
  creation_method?: CreationMethod;
}

export interface PluginInfoForPlayground {
  id?: string;
  /** name_for_human */
  name?: string;
  /** description_for_human */
  desc_for_human?: string;
  plugin_icon?: string;
  plugin_type?: PluginType;
  status?: PluginStatus;
  auth?: number;
  client_id?: string;
  client_secret?: string;
  plugin_apis?: Array<PluginApi>;
  /** 插件标签 */
  tag?: Int64;
  create_time?: string;
  update_time?: string;
  /** 创建人信息 */
  creator?: Creator;
  /** 空间id */
  space_id?: string;
  /** 插件统计数据 */
  statistic_data?: PluginStatisticData;
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  /** plugin的商品状态 */
  plugin_product_status?: product_common.ProductStatus;
  /** plugin商品下架类型 */
  plugin_product_unlist_type?: product_common.ProductUnlistType;
  /** 插件创建方式 */
  creation_method?: CreationMethod;
}

export interface PluginMetaInfo {
  name?: string;
  desc?: string;
  url?: string;
  icon?: PluginIcon;
  auth_type?: Array<AuthorizationType>;
  /** service */
  location?: AuthorizationServiceLocation;
  /** service */
  key?: string;
  /** service */
  service_token?: string;
  /** json序列化 */
  oauth_info?: string;
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
}

export interface PluginParameter {
  name?: string;
  desc?: string;
  required?: boolean;
  type?: string;
  sub_parameters?: Array<PluginParameter>;
  /** 如果Type是数组，则有subtype */
  sub_type?: string;
  /** 如果入参的值是引用的则有fromNodeId */
  from_node_id?: string;
  /** 具体引用哪个节点的key */
  from_output?: Array<string>;
  /** 如果入参是用户手输 就放这里 */
  value?: string;
  format?: PluginParamTypeFormat;
}

export interface PluginStatisticData {
  /** 为空就不展示 */
  bot_quote?: number;
}

export interface PluginTag {
  type?: Int64;
  name?: string;
  icon?: string;
  active_icon?: string;
}

export interface ProcessDocumentsTaskRequest {
  dataset_id?: string;
  document_info?: Array<DocumentTaskInfo>;
  rule?: string;
}

export interface ProcessDocumentsTaskResponse {
  code?: Int64;
  msg?: string;
  document_infos?: Array<DocumentFileInfo>;
}

export interface ProcessWebDocumentsTaskRequest {
  dataset_id?: string;
  document_info?: Array<WebDocumentInfo>;
  formatType?: document.FormatType;
}

export interface ProcessWebDocumentsTaskResponse {
  code?: Int64;
  msg?: string;
  document_infos?: Array<DocumentFileInfo>;
}

export interface ProgressData {
  document_id?: string;
  progress?: number;
  status?: document.DocumentStatus;
  /** 状态的详细描述；如果切片失败，返回失败信息 */
  status_descript?: string;
}

export interface PromptOptimizeData {
  record_id?: string;
  optimized_prompt?: string;
}

export interface PromptOptimizeRequest {
  device_id?: string;
  push_uuid?: string;
  original_prompt: string;
  optimize_type?: PromptOptimizeType;
  bot_id?: string;
  sync?: boolean;
}

export interface PromptOptimizeResponse {
  code?: Int64;
  msg?: string;
  data: PromptOptimizeData;
}

export interface PublishConnectorInfo {
  /** 发布平台 connector_id */
  id: string;
  /** 发布平台名称 */
  name: string;
  /** 发布平台图标 */
  icon: string;
  /** 发布平台描述 */
  desc: string;
  /** 分享链接 */
  share_link: string;
  /** 配置状态 1:已绑定 2:未绑定 */
  config_status: ConfigStatus;
  /** 最近发布时间 */
  last_publish_time: Int64;
  /** 绑定类型 1:无需绑定  2:Auth  3: kv值 */
  bind_type: BindType;
  /** 绑定信息 key字段名 value是值 */
  bind_info: Record<string, string>;
  /** 绑定id信息，用于解绑使用 */
  bind_id?: string;
  /** 用户授权登陆信息 */
  auth_login_info?: AuthLoginInfo;
  /** 是否为上次发布 */
  is_last_published?: boolean;
  /** bot渠道状态 */
  connector_status?: BotConnectorStatus;
  /** 隐私政策 */
  privacy_policy?: string;
  /** 用户协议 */
  user_agreement?: string;
  /** 渠道是否允许发布 */
  allow_punish?: AllowPublishStatus;
  /** 不允许发布原因 */
  not_allow_reason?: string;
  /** 配置状态toast */
  config_status_toast?: string;
  /** 品牌 ID */
  brand_id?: Int64;
  /** 支持商业化 */
  support_monetization?: boolean;
  /** 1: 已授权，2:未授权. 目前仅 bind_type == 8 时这个字段才有  */
  auth_status?: bot_user_auth.UserAuthStatus;
  /** 补全信息按钮的 url */
  to_complete_info_url?: string;
  /** 渠道发布提示 */
  connector_tips?: string;
}

export interface PublishConnectorListRequest {
  space_id: string;
  bot_id: string;
  commit_version?: string;
}

export interface PublishConnectorListResponse {
  code?: Int64;
  msg?: string;
  publish_connector_list?: Array<PublishConnectorInfo>;
  submit_bot_market_option?: SubmitBotMarketOption;
  /** 上次提交market的配置 */
  last_submit_config?: SubmitBotMarketConfig;
  /** 渠道品牌信息 */
  connector_brand_info_map?: Record<Int64, ConnectorBrandInfo>;
  /** 发布提醒 */
  publish_tips?: PublishTips;
}

export interface PublishDraftBotData {
  /** key代表connector_name 枚举 飞书="feishu" -- 废弃 */
  connector_bind_result?: Record<string, Array<ConnectorBindResult>>;
  /** key代表connector_id，value是发布结果 */
  publish_result?: Record<string, ConnectorBindResult>;
  /** true：机审校验不通过 */
  check_not_pass?: boolean;
  /** 上架bot market结果 */
  submit_bot_market_result?: SubmitBotMarketResult;
  /** 是否命中人审 */
  hit_manual_check?: boolean;
  /** 机审校验不通过原因的starlingKey列表 */
  not_pass_reason?: Array<string>;
  /** 发布bot计费结果 */
  publish_monetization_result?: boolean;
}

export interface PublishDraftBotRequest {
  space_id: string;
  bot_id: string;
  work_info?: WorkInfo;
  /** key代表connector_name 枚举 飞书="feishu" -- 废弃 */
  connector_list?: Record<string, Array<Connector>>;
  /** key代表connector_id，value是发布的参数 */
  connectors?: Record<string, Record<string, string>>;
  /** 默认0 */
  botMode?: BotMode;
  agents?: Array<AgentInfo>;
  canvas_data?: string;
  bot_tag_info?: Array<BotTagInfo>;
  /** 发布到market的配置 */
  submit_bot_market_config?: SubmitBotMarketConfig;
  publish_id?: string;
  /** 指定发布某个CommitVersion */
  commit_version?: string;
  /** 发布类型，线上发布/预发布 */
  publish_type?: PublishType;
  /** 预发布其他信息 */
  pre_publish_ext?: string;
  /** 替换原workinfo中的 history_info */
  history_info?: string;
}

export interface PublishDraftBotResponse {
  code?: Int64;
  msg?: string;
  data: PublishDraftBotData;
}

export interface PublishedPluginListData {
  plugin_list?: Array<PluginInfoForPlayground>;
  total?: Int64;
  page?: number;
  size?: number;
}

export interface PublishPluginRequest {
  plugin_id: string;
}

export interface PublishPluginResponse {
  code?: Int64;
  msg?: string;
}

export interface PublishTips {
  /** 成本承担提醒 */
  cost_tips?: string;
}

export interface PublishWorkflowV2Data {
  workflow_id?: string;
  commit_id?: string;
  success?: boolean;
}

export interface PublishWorkflowV2Request {
  workflow_id: string;
  space_id: string;
  user_id?: Int64;
}

export interface PublishWorkflowV2Response {
  code?: Int64;
  msg?: string;
  data: PublishWorkflowV2Data;
}

export interface QueryCardDetailData {
  card_detail?: CozeCardInfo;
}

export interface QueryCardDetailRequest {
  space_id?: string;
  card_id?: string;
  card_version?: string;
}

export interface QueryCardDetailResponse {
  code?: Int64;
  msg?: string;
  data?: QueryCardDetailData;
}

export interface QueryCardHistoryRequest {
  card_id: string;
  page: number;
  size: number;
}

export interface QueryCardHistoryResponse {
  code?: Int64;
  msg?: string;
  data?: CardHistoryData;
}

export interface QueryCardListData {
  card_list?: Array<CozeCardInfo>;
  total?: Int64;
}

export interface QueryCardListRequest {
  page?: number;
  size?: number;
  space_id?: string;
  category?: CardCategory;
  bind_card_id?: string;
  status?: QueryCardStatus;
}

export interface QueryCardListResponse {
  code?: Int64;
  msg?: string;
  data?: QueryCardListData;
}

export interface QuerySchemaConfig {
  schema_area?: SchemaAreaInfo;
  copy_link_area?: CopyLinkAreaInfo;
  /** 引导link文本 */
  guide_link_text?: string;
  /** 引导documenturl */
  guide_link_url?: string;
  /** 弹窗标题 */
  title_text?: string;
  /** 弹窗起始语 */
  start_text?: string;
  code?: Int64;
  msg?: string;
  schema_area_pages?: Array<SchemaAreaPage>;
}

export interface QuerySchemaRequest {
  connector_id?: string;
  /** 场景 */
  scene?: string;
}

export interface QueryWebInfoRequest {
  web_id?: Array<string>;
  /** 是否包含内容 */
  include_content?: boolean;
}

export interface QueryWebInfoResponse {
  code?: Int64;
  msg?: string;
  data?: Record<string, WebInfoData>;
}

export interface QueryWorkflowNodeTypeRequest {
  space_id?: string;
  workflow_id?: string;
}

export interface QueryWorkflowNodeTypeResponse {
  code?: Int64;
  msg?: string;
  data?: WorkflowNodeTypeData;
}

export interface QueryWorkflowV2Request {
  workflow_id: string;
  space_id?: string;
}

export interface QueryWorkflowV2Response {
  code?: Int64;
  msg?: string;
  data: WorkflowV2Data;
}

export interface RegisterPluginData {
  plugin_id?: string;
  openapi?: string;
}

export interface RegisterPluginMetaRequest {
  name: string;
  desc: string;
  url?: string;
  /** uri */
  icon: PluginIcon;
  auth_type?: AuthorizationType;
  /** service */
  location?: AuthorizationServiceLocation;
  /** service   Authorization: xxxxxx */
  key?: string;
  /** service */
  service_token?: string;
  /** json序列化 */
  oauth_info?: string;
  space_id: string;
  /** 公共参数列表 */
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  /** 默认0 默认原来表单创建方式，1 coze ide创建方式 */
  creation_method?: CreationMethod;
  /** ide创建下的代码编程语言 */
  ide_code_runtime?: string;
}

export interface RegisterPluginMetaResponse {
  code?: Int64;
  msg?: string;
  plugin_id?: string;
}

export interface RegisterPluginRequest {
  /** ap_json */
  ai_plugin?: string;
  /** openapi.yaml */
  openapi?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  /** plugin 类型，1 plugin 2=app 3= func */
  plugin_type?: PluginType;
  space_id?: string;
}

export interface RegisterPluginResponse {
  code?: Int64;
  msg?: string;
  RegisterPluginData?: RegisterPluginData;
}

export interface ReleasedWorkflow {
  plugin_id?: string;
  workflow_id?: string;
  space_id?: string;
  name?: string;
  desc?: string;
  icon?: string;
  inputs?: string;
  outputs?: string;
  end_type?: number;
  type?: number;
  sub_workflow_list?: Array<SubWorkflow>;
  version?: string;
  create_time?: Int64;
  update_time?: Int64;
  /** workflow创作者信息 */
  creator?: Creator;
}

export interface ReleasedWorkflowData {
  workflow_list?: Array<ReleasedWorkflow>;
  total?: Int64;
}

export interface RemoveBotTaskRequest {
  bot_id: string;
  task_id: string;
}

export interface RemoveBotTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface RemoveSpaceMemberRequest {
  /** 空间id */
  space_id?: string;
  /** 移除用户uid */
  remove_user_id?: string;
}

export interface RemoveSpaceMemberResponse {
  code?: Int64;
  msg?: string;
}

export interface RemoveTaskRequest {
  bot_id: string;
  task_id: string;
}

export interface RemoveTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface ReportMessageRequest {
  /** 回话ID */
  biz_conversation_id: string;
  /** 消息ID */
  message_id: string;
  /** 当前会话所处场景 */
  scene: bot_common.Scene;
  /** 动作 */
  action: ReportMessageAction;
  message_feedback?: bot_common.MessageFeedback;
  /** 消息属性 */
  attributes?: MessageaAttributes;
}

export interface ReportMessageResponse {
  code?: Int64;
  msg?: string;
}

/** 和 bot_connector_platform保持同步 */
export interface RequiredAction {
  type?: string;
  submit_tool_outputs?: SubmitToolOutputs;
}

export interface ResegmentRequest {
  dataset_id: string;
  document_id: string;
  rule?: string;
  /** 格式类型 */
  format_type?: document.FormatType;
}

export interface ResegmentResponse {
  code?: Int64;
  msg?: string;
  document_infos?: DocumentFileInfo;
}

export interface ResumeChatRequest {
  /** 打断的 verbose 消息的 message_id */
  interrupt_message_id?: string;
  conversation_id?: string;
  tool_outputs?: Array<ToolOutput>;
  scene?: Scene;
  /** 原始 user_input 请求的 message_id */
  resume_message_id?: string;
}

export interface ResumeChatResponse {
  code?: Int64;
  msg?: string;
}

export interface RevertDraftBotData {
  branch?: Branch;
  same_with_online?: boolean;
}

export interface RevertDraftBotRequest {
  space_id: string;
  bot_id: string;
  /** 查历史记录，历史版本的id */
  version: string;
}

export interface RevertDraftBotResponse {
  code?: Int64;
  msg?: string;
  data: RevertDraftBotData;
}

export interface RevertPlaygroundRecordRequest {
  /** 历史记录 */
  version?: number;
  task_id?: string;
}

export interface RevertPlaygroundRecordResponse {
  code?: Int64;
  msg?: string;
}

export interface RunWorkFlowData {
  workflowId?: string;
  executeId?: string;
}

export interface RunWorkFlowRequest {
  workflowId?: string;
  input?: Record<string, string>;
  space_id?: string;
}

export interface RunWorkFlowResponse {
  code?: Int64;
  msg?: string;
  data?: RunWorkFlowData;
}

export interface SaveBindConnectorConfigRequest {
  /** 空间id */
  space_id: string;
  /** bot_id */
  bot_id: string;
  /** 渠道id */
  connector_id: string;
  /** 要绑定的关系 */
  detail: Record<string, string>;
  /** 0: bot 1: project */
  agent_type?: Int64;
}

export interface SaveBindConnectorConfigResponse {
  code?: Int64;
  msg?: string;
}

export interface SaveCardData {
  template_id: string;
}

export interface SaveCardTemplateRequest {
  channel_type: ChannelType;
  thumbnail: string;
  name: string;
  dsl_content: string;
}

export interface SaveCardTemplateResponse {
  code?: Int64;
  msg?: string;
  data?: SaveCardData;
}

export interface SavePlaygroundRecordRequest {
  task_id?: string;
  /** 分块信息 */
  item_infos?: Array<ItemInfo>;
  /** 保存历史信息 */
  playground_history_info?: PlaygroundHistoryInfo;
  task_name?: string;
}

export interface SavePlaygroundRecordResponse {
  code?: Int64;
  msg?: string;
  data?: SavePlaygroundRecordResponseData;
}

export interface SavePlaygroundRecordResponseData {
  /** 变量，常量 */
  item_infos?: Array<ItemInfo>;
}

export interface SavePluginRequest {
  plugin_id?: string;
  item_infos?: Array<PluginCurrentInfoItemInfo>;
}

export interface SavePluginResponse {
  code?: Int64;
  msg?: string;
}

/** ---------------wait list end -------------------------------- */
export interface SaveWorkflowV2Data {
  name?: string;
  url?: string;
  status?: WorkFlowStatus;
}

export interface SaveWorkflowV2Request {
  workflow_id: string;
  schema?: string;
  space_id?: string;
  name?: string;
  desc?: string;
  icon_uri?: string;
  ignore_status_transfer?: boolean;
}

export interface SaveWorkflowV2Response {
  code?: Int64;
  msg?: string;
  data: SaveWorkflowV2Data;
}

export interface SchemaAreaInfo {
  schema_list?: Array<FormSchemaItem>;
  /** 输入信息区域标题文本 */
  title_text?: string;
  /** 输入信息区域标题下描述 */
  description?: string;
  /** 步骤号,只是展示指定的步骤号，不影响SchemaArea的展示顺序。 */
  step_order?: Int64;
}

export interface SchemaAreaPage {
  schema_area?: SchemaAreaInfo;
  copy_link_area?: CopyLinkAreaInfo;
  /** 页面要执行的api调用动作 */
  api_action?: SchemaAreaPageApi;
}

export interface SearchMemberRequest {
  /** 搜索字段列表 */
  search_list: Array<string>;
  /** 空间id */
  space_id: string;
}

export interface SearchMemberResponse {
  code?: Int64;
  msg?: string;
  /** 成员列表 */
  member_info_list?: Array<MemberInfo>;
  /** 查询失败列表信息 */
  failed_search_list?: Array<string>;
}

export interface SearchMentionRequest {
  preset_bot: string;
  keyword: string;
  cursor_id: string;
  page_size: number;
}

export interface SearchMentionResponse {
  code?: Int64;
  msg?: string;
  recently_used_bot_list?: Array<MsgParticipantInfo>;
  favorite_bot_list?: Array<MsgParticipantInfo>;
  cursor_id?: string;
  has_more?: boolean;
  id_user_info_map?: Record<Int64, UserBasicInfo>;
}

export interface ShareBotRequest {
  bot_id: string;
  /** 0代表获取分享链接 */
  operation: ShareOperation;
}

export interface ShareBotResponse {
  code?: Int64;
  msg?: string;
  /** 分享链接 */
  share_link?: string;
}

export interface ShortMemPolicy {
  ContextContentType?: ContextContentType;
  HistoryRound?: number;
}

export interface SignleBindCardStatus {
  card_id?: string;
  plugin_id?: string;
  /** ""查询所有，"-1":查询end节点 */
  unique_id?: string;
  status?: PluginCardStatus;
  business_type?: CardBusinessType;
  card_version?: string;
}

export interface SpaceInfoForInviteData {
  space_name?: string;
  description?: string;
  icon_url?: string;
  owner_name?: string;
}

export interface SpaceInfoForInviteRequest {
  space_id: string;
}

export interface SpaceInfoForInviteResponse {
  code?: Int64;
  msg?: string;
  data?: SpaceInfoForInviteData;
}

export interface SpaceListRequest {}

export interface SpaceListResponse {
  code?: Int64;
  msg?: string;
  /** 用户加入空间列表 */
  bot_space_list?: Array<BotSpace>;
  /** 是否有个人空间 */
  has_personal_space?: boolean;
  /** 个人创建team空间数量 */
  team_space_num?: number;
  /** 个人最大能创建的空间数量 */
  max_team_space_num?: number;
}

export interface SpaceMemberDetailData {
  /** 空间id */
  space_id?: string;
  /** 空间名称 */
  name?: string;
  /** 空间描述 */
  description?: string;
  /** 空间图标url */
  icon_url?: string;
  /** 当前用户角色 */
  space_role_type?: SpaceRoleType;
  /** 查询总数，用于分页 */
  total?: number;
  /** 成员列表 */
  member_info_list?: Array<MemberInfo>;
  /** 总共多少admin角色 */
  admin_total_num?: number;
  /** 总共多少member角色 */
  member_total_num?: number;
  /** 允许最多admin数量 */
  max_admin_num?: number;
  /** 允许最多member数量 */
  max_member_num?: number;
  /** team通过分享链接加入空间按钮的状态 */
  team_invite_link_status?: boolean;
}

export interface SpaceMemberDetailRequest {
  /** 空间id */
  space_id?: string;
  /** 搜索词 */
  search_word?: string;
  /** 角色  0: all */
  space_role_type?: SpaceRoleType;
  /** 分页 */
  page?: number;
  /** 大小 */
  size?: number;
}

export interface SpaceMemberDetailResponse {
  code?: Int64;
  msg?: string;
  data?: SpaceMemberDetailData;
}

export interface SpaceRoleAuthRequest {}

export interface SpaceRoleAuthResponse {
  code?: Int64;
  msg?: string;
  role_auth?: Record<string, Array<string>>;
}

export interface StoreCookieBannerRequest {
  cookie_banner_info?: string;
}

export interface StoreCookieBannerResponse {
  code?: Int64;
  msg?: string;
}

export interface SubmitBotMarketConfig {
  /** 是否发布到market */
  need_submit?: boolean;
  /** 是否开源 */
  open_source?: boolean;
  /** 分类 */
  category_id?: string;
}

export interface SubmitBotMarketOption {
  /** 是否可以公开编排 */
  can_open_source?: boolean;
}

export interface SubmitBotMarketResult {
  /** 上架状态，0-成功 */
  result_code?: Int64;
  /** 上架结果的文案 */
  msg?: string;
}

export interface SubmitBotTaskRequest {
  task_id?: string;
  model_info?: ModelInfo;
  /** 用户勾选的api */
  plugin_apis?: Array<PluginApi>;
  /** 用户编辑的prompt */
  bot_prompts?: Array<BotPrompt>;
  /** 包括思考过程，中间结果 */
  messages?: Array<MessageInfo>;
  device_id?: string;
  push_uuid?: string;
}

export interface SubmitBotTaskResponse {
  ai_msg: string;
  code?: Int64;
  msg?: string;
}

export interface SubmitTaskRequest {
  task_id?: string;
  model?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  messages?: Array<MessageInfoPrompt>;
}

export interface SubmitTaskResponse {
  ai_msg: string;
}

export interface SubmitToolOutputs {
  tool_calls?: Array<InterruptPlugin>;
}

export interface SubmitUserWaitListInfoRequest {
  using_for?: string;
  hear_from?: string;
  ext_message?: string;
}

export interface SubmitUserWaitListInfoResponse {
  code?: Int64;
  msg?: string;
}

export interface SubmitWebContentRequest {
  web_id?: string;
  content?: string;
}

export interface SubmitWebContentResponse {
  code?: Int64;
  msg?: string;
}

export interface SubmitWebUrlRequest {
  web_url?: string;
  /** 0 不包换子页面。 */
  subpages_count?: number;
  /** 3:                  i64            creator_id
文件格式类型 */
  format_type?: document.FormatType;
  /** 网页标题 url 类型必传 */
  title?: string;
}

export interface SubmitWebUrlResponse {
  code?: Int64;
  msg?: string;
  data?: WebUrlInfo;
}

export interface SubWorkflow {
  id?: string;
  name?: string;
}

export interface SuggestPluginRequest {
  space_id?: string;
  bot_id?: string;
  bot_prompts: string;
  plugin_apis?: Array<PluginApi>;
  workflow_ids?: Array<string>;
  bot_name?: string;
  bot_description?: string;
}

export interface SuggestPluginResponse {
  code?: Int64;
  msg?: string;
  /** 插件id */
  data: Array<PluginApi>;
  /** json字符串 */
  plugin_apis?: string;
}

export interface SwitchDevelopModeRequest {
  space_id: string;
  bot_id: string;
  target_develop_mode: DevelopMode;
}

export interface SwitchDevelopModeResponse {
  code?: Int64;
  msg?: string;
}

export interface TabDisplayItems {
  plugin_tab_status?: TabStatus;
  workflow_tab_status?: TabStatus;
  knowledge_tab_status?: TabStatus;
  database_tab_status?: TabStatus;
  variable_tab_status?: TabStatus;
  opening_dialog_tab_status?: TabStatus;
  scheduled_task_tab_status?: TabStatus;
  suggestion_tab_status?: TabStatus;
  tts_tab_status?: TabStatus;
  filebox_tab_status?: TabStatus;
  long_term_memory_tab_status?: TabStatus;
  answer_action_tab_status?: TabStatus;
  imageflow_tab_status?: TabStatus;
  background_image_tab_status?: TabStatus;
  shortcut_tab_status?: TabStatus;
  knowledge_table_tab_status?: TabStatus;
  knowledge_text_tab_status?: TabStatus;
  knowledge_photo_tab_status?: TabStatus;
  hook_info_tab_status?: TabStatus;
  default_user_input_tab_status?: TabStatus;
  knowledge_volcano_unstructured_tab_status?: TabStatus;
  knowledge_volcano_structured_tab_status?: TabStatus;
  model_tab_status?: TabStatus;
}

/** deprecated */
export interface TaskInfoData {
  task_id?: string;
  user_question?: string;
  create_time?: string;
  next_time?: string;
  action_for_playground?: string;
  status?: Int64;
  /** 0非预设 1预设(测试) 2预设(发布) */
  preset_type?: number;
  cron_expr?: string;
  task_content?: string;
  time_zone?: string;
}

export interface TaskListRequest {
  bot_id: string;
  space_id?: string;
  source?: Source;
}

export interface TaskListResponse {
  code?: Int64;
  msg?: string;
  data?: Array<TaskInfoData>;
}

export interface TerminatePlan {
  /** 结束方式 */
  plan?: TerminatePlanType;
  content?: string;
}

export interface ThumbnailInfo {
  channel_type?: ChannelType;
  /** 缩略图 */
  thumbnail?: string;
  /** 是否是主图 */
  main_image?: boolean;
}

export interface TimeCapsuleListItemsData {
  list_items_result_list?: Array<ListItemsResultItem>;
  total?: number;
}

export interface TimeCapsuleListItemsRequest {
  bot_id: string;
  start_event_time?: string;
  end_event_time?: string;
  page?: number;
  size?: number;
}

export interface TimeCapsuleListItemsResponse {
  code?: Int64;
  msg?: string;
  data?: TimeCapsuleListItemsData;
}

export interface TokenAndCost {
  /** input消耗Token数 */
  inputTokens?: string;
  /** input花费 */
  inputCost?: string;
  /** Output消耗Token数 */
  outputTokens?: string;
  /** Output花费 */
  outputCost?: string;
  /** 总消耗Token数 */
  totalTokens?: string;
  /** 总花费 */
  totalCost?: string;
}

export interface Tool {
  plugin_id?: string;
  parameters?: Record<string, parametersStruct>;
  api_name?: string;
}

export interface ToolOutput {
  /** submit_tool_outputs->tool_calls里的ID */
  tool_call_id?: string;
  /** 传入的经纬度结构 */
  output?: string;
}

export interface TransferSpaceRequest {
  /** 空间id */
  space_id?: string;
  /** 权限转移user_id */
  transfer_user_id?: string;
}

export interface TransferSpaceResponse {
  code?: Int64;
  msg?: string;
}

export interface TriggerBotTaskRequest {
  bot_id: string;
  task_id: string;
  webhook_url?: string;
  bearer_token?: string;
  params?: string;
  conversation_id?: string;
  extra?: Record<string, string>;
}

export interface TriggerBotTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface UnBindConnectorRequest {
  space_id: string;
  bot_id: string;
  connector_id: string;
  bind_id: string;
  /** 0-bot，1-project */
  agent_type?: Int64;
}

export interface UnBindConnectorResponse {
  code?: Int64;
  msg?: string;
}

export interface UnbindConnectorUserRequest {
  connector_id: string;
  connector_uid: string;
}

export interface UnbindConnectorUserResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateApiKeyRequest {
  api_key_id?: string;
  space_id?: string;
  key_name?: string;
  /** 1-编辑字段 2-删除api_key */
  operate_type?: number;
}

export interface UpdateApiKeyResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateAPIRequest {
  plugin_id: string;
  api_id: string;
  name?: string;
  desc?: string;
  path?: string;
  method?: APIMethod;
  request_params?: Array<APIParameter>;
  response_params?: Array<APIParameter>;
  /** 启用/禁用 */
  disabled?: boolean;
}

export interface UpdateAPIResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateBotData {
  /** 更新结果 */
  res?: boolean;
}

export interface UpdateBotRequest {
  bot_id: string;
  name?: string;
  description_for_human?: string;
  icon_uri?: string;
  /** true：删除  false：修改为不删除 */
  delete?: boolean;
}

export interface UpdateBotResponse {
  code?: Int64;
  msg?: string;
  data: UpdateBotData;
}

export interface UpdateCardInfoRequest {
  card_id: string;
  name?: string;
  card_status?: CardStatus;
}

export interface UpdateCardInfoResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateDataSetMetaRequest {
  id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
}

export interface UpdateDataSetMetaResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateDraftBotData {
  /** 是否有变更 */
  has_change?: boolean;
  /** true：机审校验不通过 */
  check_not_pass?: boolean;
  /** 当前是在哪个分支 */
  branch?: Branch;
  same_with_online?: boolean;
  /** 机审校验不通过文案 */
  check_not_pass_msg?: string;
}

export interface UpdateDraftBotDisplayInfoRequest {
  bot_id: string;
  display_info?: DraftBotDisplayInfoData;
  space_id?: string;
}

export interface UpdateDraftBotDisplayInfoResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateDraftBotRequest {
  space_id: string;
  bot_id: string;
  work_info?: WorkInfo;
  name?: string;
  description?: string;
  icon_uri?: string;
  /** 可见类型 */
  visibility?: VisibilityType;
  update_agents?: Array<AgentInfo>;
  canvas_data?: string;
  bot_mode?: BotMode;
  delete_agents?: Array<string>;
  bot_tag_info?: Array<BotTagInfo>;
  filebox_info?: FileboxInfo;
  /** rebase操作中base的commit version */
  base_commit_version?: string;
  version_compat?: AgentVersionCompat;
}

export interface UpdateDraftBotResponse {
  code?: Int64;
  msg?: string;
  data: UpdateDraftBotData;
}

export interface UpdateHomeTriggerConfigRequest {
  bot_id: string;
  action: TriggerEnabled;
}

export interface UpdateHomeTriggerUserConfigResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateLinkData {
  node_list?: Array<Node>;
}

export interface UpdateLinkRequest {
  workflow_id: string;
  from_node_id: string;
  to_node_id: string;
  /** 1增加  2删除 */
  type: LinkProcessType;
  /** if节点传入从哪个分支来的连线 */
  if_node_branch?: IfNodeBranchType;
  space_id?: string;
}

export interface UpdateLinkResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateLinkData;
}

export interface UpdateNodeData {
  node_list?: Array<Node>;
}

export interface UpdateNodeRequest {
  /** 理论上只更新一项 */
  workflow_id: string;
  node_id: string;
  name?: string;
  node_param?: NodeParam;
  /** Node的位置 */
  layout?: LayOut;
  space_id?: string;
}

export interface UpdateNodeResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateNodeData;
}

export interface UpdatePluginData {
  res?: boolean;
}

export interface UpdatePluginMetaRequest {
  plugin_id: string;
  name?: string;
  desc?: string;
  url?: string;
  /** uri */
  icon?: PluginIcon;
  auth_type?: AuthorizationType;
  /** service */
  location?: AuthorizationServiceLocation;
  /** service   Authorization: xxxxxx */
  key?: string;
  /** service */
  service_token?: string;
  /** json序列化 */
  oauth_info?: string;
  common_params?: Partial<Record<ParameterLocation, Array<commonParamSchema>>>;
  creation_method?: CreationMethod;
}

export interface UpdatePluginMetaResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdatePluginRequest {
  plugin_id?: string;
  ai_plugin?: string;
  openapi?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  /** 函数代码 */
  source_code?: string;
}

export interface UpdatePluginResponse {
  code?: Int64;
  msg?: string;
  update_plugin_data: UpdatePluginData;
}

export interface UpdateSpaceMemberRequest {
  /** 空间id */
  space_id?: string;
  /** 更新用户id */
  user_id?: string;
  /** 更新用户角色 */
  space_role_type?: SpaceRoleType;
}

export interface UpdateSpaceMemberResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateSpaceRequest {
  /** 空间id */
  space_id: string;
  /** 空间名称 */
  name: string;
  /** 空间描述 */
  description: string;
  /** 空间图像 */
  icon_uri?: string;
}

export interface UpdateSpaceResponse {
  code?: Int64;
  msg?: string;
  /** true：机审校验不通过 */
  check_not_pass?: boolean;
}

export interface UpdateUserProfileCheckRequest {
  user_unique_name?: string;
}

export interface UpdateUserProfileCheckResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateUserProfileMsg {
  code?: Int64;
  /** 更新失败原因 */
  msg?: string;
}

export interface UpdateUserProfileRequest {
  /** bot平台唯一名称 */
  user_unique_name?: string;
  /** 用户昵称 */
  name?: string;
  /** 用户图像 */
  avatar?: string;
}

export interface UpdateUserProfileResponse {
  code?: Int64;
  msg?: string;
  /** 更新结果，key=要更新的字段名，如user_unique_name */
  data?: Record<string, UpdateUserProfileMsg>;
}

export interface UpdateWorkFlowData {
  name?: string;
  url?: string;
  status?: WorkFlowStatus;
}

export interface UpdateWorkFlowRequest {
  workflow_id: string;
  name?: string;
  desc?: string;
  icon_uri?: string;
  delete?: boolean;
  space_id?: string;
}

export interface UpdateWorkFlowResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateWorkFlowData;
}

export interface UploadAuthTokenInfo {
  access_key_id?: string;
  secret_access_key?: string;
  session_token?: string;
  expired_time?: string;
  current_time?: string;
}

export interface UploadFileData {
  /** 文件url */
  upload_url?: string;
  /** 文件uri，提交使用这个 */
  upload_uri?: string;
}

export interface UploadFileRequest {
  /** 文件相关描述 */
  file_head?: CommonFileInfo;
  /** 文件数据 */
  data?: string;
}

export interface UploadFileResponse {
  code?: Int64;
  msg?: string;
  /** 数据 */
  data?: UploadFileData;
}

export interface UriUrlObj {
  uri?: string;
  url?: string;
}

export interface UserAuthCodeRequest {
  code?: string;
  connector_id?: string;
  encrypt_state?: string;
  state?: Record<string, string>;
}

export interface UserAuthCodeResponse {
  code?: Int64;
  msg?: string;
}

export interface UserAuthInfo {
  id: string;
  name: string;
  icon: string;
  auth_status: AuthStatus;
  auth_login_info: AuthLoginInfo;
}

export interface UserBasicInfo {
  user_id: Int64;
  user_type: UserType;
  /** 昵称 */
  user_name: string;
  /** 头像 */
  user_avatar: string;
  /** 用户名 */
  user_unique_name?: string;
  /** 用户标签 */
  user_label?: bot_common.UserLabel;
}

export interface UserInfo {
  /** 用户id */
  user_id?: Int64;
  /** 用户名称 */
  name?: string;
  /** 用户图标 */
  icon_url?: string;
}

export interface UserLabel {
  label_id?: string;
  label_name?: string;
  icon_uri?: string;
  icon_url?: string;
  jump_link?: string;
}

export interface UserQueryCollectConf {
  /** 是否开启收集开关 */
  is_collected?: boolean;
  /** 隐私协议链接 */
  private_policy?: string;
}

export interface ValidateErrorData {
  node_error?: NodeError;
  path_error?: PathError;
  message?: string;
  type?: ValidateErrorType;
}

export interface ValidateSchemaRequest {
  schema: string;
}

export interface ValidateSchemaResponse {
  code?: Int64;
  msg?: string;
  data?: Array<ValidateErrorData>;
}

export interface VoiceInfo {
  id: Int64;
  language_code: string;
  preview_text: string;
  preview_audio: string;
  language_name: string;
  name: string;
  style_id: string;
}

export interface VoiceToken {
  token: string;
}

export interface VoiceType {
  id?: Int64;
  model_name?: string;
  name?: string;
  language?: string;
  style_id?: string;
  style_name?: string;
}

export interface WaitListConfig {
  wait_list_switch?: boolean;
  user_grant?: boolean;
  user_grant_info?: boolean;
}

export interface WaitListConfigRequest {}

export interface WaitListConfigResponse {
  code?: Int64;
  msg?: string;
  data?: WaitListConfig;
}

export interface WebDocumentInfo {
  name?: string;
  web_id?: string;
  update_type?: document.DocumentUpdateType;
  /** 更新间隔天数 0表示不更新 */
  update_interval?: number;
  sub_web_ids?: Array<string>;
  /** 如果是已有更新 */
  document_id?: string;
  /** 表格类型元数据 */
  table_meta?: Array<document.DocTableColumn>;
  /** 原有的表格类型元数据 */
  orig_table_meta?: Array<document.DocTableColumn>;
}

export interface WebInfo {
  id?: string;
  url?: string;
  content?: string;
  title?: string;
  subpages?: Array<WebInfo>;
  subpages_count?: number;
  status?: WebInfoStatus;
}

export interface WebInfoData {
  progress?: number;
  web_info?: WebInfo;
  status?: WebInfoStatus;
  status_descript?: string;
}

export interface WebUrlInfo {
  id?: string;
}

export interface WKExecutePreCheckRequest {
  workflow_id: string;
}

export interface WKExecutePreCheckResponse {
  code?: Int64;
  msg?: string;
}

export interface WorkFlow {
  workflow_id?: string;
  name?: string;
  desc?: string;
  url?: string;
  icon_uri?: string;
  status?: WorkFlowStatus;
  /** 类型，1:官方模版 */
  type?: WorkFlowType;
  /** 该workflow对应的插件id */
  plugin_id?: string;
  create_time?: Int64;
  update_time?: Int64;
  start_node?: Node;
  tag?: Tag;
  /** 模版创作者id */
  template_author_id?: Int64;
  /** 模版创作者昵称 */
  template_author_name?: string;
  /** 模版创作者头像 */
  template_author_picture_url?: string;
  /** 空间id */
  space_id?: string;
  /** 工作流creator信息 */
  creator?: Creator;
  /** workflow or imageflow, 默认为workflow */
  flow_mode?: WorkflowMode;
}

export interface WorkflowGrayFeatureItem {
  /** 灰度feature */
  feature: string;
  /** 是否命中灰度featire。true-命中灰度，false-未命中灰度。 */
  in_gray: boolean;
}

export interface WorkFlowListData {
  workflow_list?: Array<WorkFlow>;
  total?: Int64;
}

export interface WorkFlowListRequest {
  page?: number;
  size?: number;
  workflow_ids?: Array<string>;
  with_node?: boolean;
  /** 不传 全部 */
  type?: WorkFlowType;
  name?: string;
  /** type=1时有效 */
  tags?: Tag;
  space_id?: string;
  status?: WorkFlowListStatus;
  order_by?: OrderBy;
  /** 登录用户自己创建的 */
  login_user_create?: boolean;
}

export interface WorkFlowListResponse {
  code?: Int64;
  msg?: string;
  data?: WorkFlowListData;
}

export interface WorkflowListV2Data {
  workflow_list?: Array<WorkflowV2>;
  total?: Int64;
}

export interface WorkflowListV2Request {
  page?: number;
  size?: number;
  workflow_ids?: Array<string>;
  type?: WorkFlowType;
  name?: string;
  tags?: Tag;
  space_id?: string;
  status?: WorkFlowListStatus;
  order_by?: OrderBy;
  login_user_create?: boolean;
  /** workflow or imageflow, 默认为workflow */
  flow_mode?: WorkflowMode;
}

export interface WorkflowListV2Response {
  code?: Int64;
  msg?: string;
  data: WorkflowListV2Data;
}

export interface WorkFlowNodeDebugDataV2 {
  workflow_id?: string;
  node_id?: string;
  execute_id?: string;
  session_id?: string;
}

export interface WorkFlowNodeDebugV2Request {
  workflow_id?: string;
  node_id?: string;
  input?: Record<string, string>;
  batch?: Record<string, string>;
  space_id?: string;
  bot_id?: string;
}

export interface WorkFlowNodeDebugV2Response {
  code?: Int64;
  msg?: string;
  data?: WorkFlowNodeDebugDataV2;
}

export interface WorkflowNodeTypeData {
  node_types?: Array<string>;
  sub_workflow_node_types?: Array<string>;
  nodes_properties?: Array<NodeProps>;
  sub_workflow_nodes_properties?: Array<NodeProps>;
}

export interface WorkFlowPublishData {
  res?: boolean;
}

export interface WorkFlowPublishRequest {
  workflow_id: string;
  space_id?: string;
}

export interface WorkFlowPublishResponse {
  code?: Int64;
  msg?: string;
  data?: WorkFlowPublishData;
}

export interface WorkflowReferencesData {
  workflow_list?: Array<WorkflowV2>;
}

export interface WorkFlowTemplateTagData {
  tags: Array<PluginTag>;
}

export interface WorkFlowTemplateTagRequest {
  /** workflow or imageflow, 默认为workflow */
  flow_mode?: WorkflowMode;
}

export interface WorkFlowTemplateTagResponse {
  code?: Int64;
  msg?: string;
  data?: WorkFlowTemplateTagData;
}

export interface WorkFlowTestRunDataV2 {
  workflow_id?: string;
  execute_id?: string;
  session_id?: string;
}

export interface WorkFlowTestRunV2Request {
  workflow_id?: string;
  input?: Record<string, string>;
  space_id?: string;
  bot_id?: string;
}

export interface WorkFlowTestRunV2Response {
  code?: Int64;
  msg?: string;
  data?: WorkFlowTestRunDataV2;
}

export interface WorkflowV2 {
  workflow_id?: string;
  name?: string;
  desc?: string;
  url?: string;
  icon_uri?: string;
  status?: WorkFlowStatus;
  /** 类型，1:官方模版 */
  type?: WorkFlowType;
  /** workfklow对应的插件id */
  plugin_id?: string;
  create_time?: Int64;
  update_time?: Int64;
  schema_type?: SchemaType;
  start_node?: Node;
  tag?: Tag;
  /** 模版创作者id */
  template_author_id?: string;
  /** 模版创作者昵称 */
  template_author_name?: string;
  /** 模版创作者头像 */
  template_author_picture_url?: string;
  /** 空间id */
  space_id?: string;
  /** 流程出入参 */
  interface_str?: string;
  /** 新版workflow的定义 schema */
  schema_json?: string;
  /** workflow创作者信息 */
  creator?: Creator;
  /** workflow or imageflow, 默认为workflow */
  flow_mode?: WorkflowMode;
}

export interface WorkflowV2Data {
  workflow?: WorkflowV2;
}

/** 工作区间各个模块的信息 */
export interface WorkInfo {
  message_info?: string;
  prompt?: string;
  variable?: string;
  other_info?: string;
  history_info?: string;
  tools?: string;
  system_info_all?: string;
  dataset?: string;
  onboarding?: string;
  profile_memory?: string;
  table_info?: string;
  workflow?: string;
  task?: string;
  suggest_reply?: string;
  tts?: string;
  background_image_info_list?: string;
  /** 快捷指令 */
  shortcuts?: shortcut_command.ShortcutStruct;
  /** hook配置 */
  hook_info?: string;
  /** 用户query收集配置 */
  user_query_collect_conf?: UserQueryCollectConf;
  /** workflow模式编排数据 */
  layout_info?: LayoutInfo;
}
/* eslint-enable */
