/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as connector from './namespaces/connector';
import * as feishu_base from './namespaces/feishu_base';
import * as open_api from './namespaces/open_api';

export { base, connector, feishu_base, open_api };
export * from './namespaces/base';
export * from './namespaces/connector';
export * from './namespaces/feishu_base';
export * from './namespaces/open_api';

export type Int64 = string | number;

export default class ConnectorApiService<T> {
  private request: any = () => {
    throw new Error('ConnectorApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /api/connector_api/get_feishu_base_config */
  GetFeishuBaseConfig(
    req: feishu_base.GetFeishuBaseConfigRequest,
    options?: T,
  ): Promise<feishu_base.GetFeishuBaseConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/connector_api/get_feishu_base_config');
    const method = 'GET';
    const params = { bot_id: _req['bot_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /open_api/v1/connector/update_connector_bot
   *
   * 渠道 OpenAPI
   */
  OpenAPIUpdateConnectorBot(
    req: open_api.OpenAPIUpdateConnectorBotRequest,
    options?: T,
  ): Promise<open_api.OpenAPIUpdateConnectorBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/open_api/v1/connector/update_connector_bot');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      audit_status: _req['audit_status'],
      reason: _req['reason'],
      share_link: _req['share_link'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/connector/oauth_config/get_schema */
  GetOauthConfigSchema(
    req?: connector.GetOauthConfigSchemaRequest,
    options?: T,
  ): Promise<connector.GetOauthConfigSchemaResponse> {
    const url = this.genBaseURL('/api/connector/oauth_config/get_schema');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/connector/oauth_config/update */
  UpdateOauthConfig(
    req?: connector.UpdateOauthConfigRequest,
    options?: T,
  ): Promise<connector.UpdateOauthConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/oauth_config/update');
    const method = 'POST';
    const data = {
      connector_id: _req['connector_id'],
      oauth_config: _req['oauth_config'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/create */
  CreateConnector(
    req?: connector.CreateConnectorRequest,
    options?: T,
  ): Promise<connector.CreateConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/create');
    const method = 'POST';
    const data = {
      connector_title: _req['connector_title'],
      connector_desc: _req['connector_desc'],
      connector_icon_uri: _req['connector_icon_uri'],
      oauth_app_id: _req['oauth_app_id'],
      callback_url: _req['callback_url'],
      space_id_list: _req['space_id_list'],
      account_id: _req['account_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/update */
  UpdateConnector(
    req?: connector.UpdateConnectorRequest,
    options?: T,
  ): Promise<connector.UpdateConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/update');
    const method = 'POST';
    const data = {
      id: _req['id'],
      connector_title: _req['connector_title'],
      connector_desc: _req['connector_desc'],
      connector_icon_uri: _req['connector_icon_uri'],
      oauth_app_id: _req['oauth_app_id'],
      callback_url: _req['callback_url'],
      space_id_list: _req['space_id_list'],
      account_id: _req['account_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/delete */
  DeleteConnector(
    req?: connector.DeleteConnectorRequest,
    options?: T,
  ): Promise<connector.DeleteConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/delete');
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/list */
  ListConnector(
    req?: connector.ListConnectorRequest,
    options?: T,
  ): Promise<connector.ListConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/list');
    const method = 'POST';
    const data = {
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      account_id: _req['account_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector_api/update_feishu_base_complete_status */
  UpdateFeishuBaseCompleteStatus(
    req: feishu_base.UpdateFeishuBaseCompleteStatusRequest,
    options?: T,
  ): Promise<feishu_base.UpdateFeishuBaseCompleteStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/connector_api/update_feishu_base_complete_status',
    );
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/connector/create_preview_version
   *
   * 预览
   */
  CreatePreviewVersion(
    req: connector.CreatePreviewVersionRequest,
    options?: T,
  ): Promise<connector.CreatePreviewVersionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/connector/create_preview_version');
    const method = 'POST';
    const data = {
      agent_id: _req['agent_id'],
      agent_type: _req['agent_type'],
      connector_ids: _req['connector_ids'],
      Base: _req['Base'],
    };
    const headers = {
      'COZE-WX-TEMPLATE-ID': _req['COZE-WX-TEMPLATE-ID'],
      'COZE-DY-TEMPLATE-ID': _req['COZE-DY-TEMPLATE-ID'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/connector/get_preview_connector */
  GetPreviewConnector(
    req?: connector.GetPreviewConnectorRequest,
    options?: T,
  ): Promise<connector.GetPreviewConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/get_preview_connector');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /v1/connectors/:connector_id/install */
  OpenAPIInstallConnectorToWorkspace(
    req: open_api.OpenAPIInstallConnectorToWorkspaceRequest,
    options?: T,
  ): Promise<open_api.OpenAPIInstallConnectorToWorkspaceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v1/connectors/${_req['connector_id']}/install`,
    );
    const method = 'POST';
    const data = { workspace_id: _req['workspace_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /v1/connectors/:connector_id/user_configs */
  OpenAPIBindConnectorUserConfig(
    req: open_api.OpenAPIBindConnectorUserConfigRequest,
    options?: T,
  ): Promise<open_api.OpenAPIBindConnectorUserConfigResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v1/connectors/${_req['connector_id']}/user_configs`,
    );
    const method = 'POST';
    const data = { configs: _req['configs'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/bind_space */
  BindSpaceConnector(
    req?: connector.BindSpaceConnectorRequest,
    options?: T,
  ): Promise<connector.BindSpaceConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/bind_space');
    const method = 'POST';
    const data = {
      connector_id_list: _req['connector_id_list'],
      space_id: _req['space_id'],
      uninstall: _req['uninstall'],
      space_id_list: _req['space_id_list'],
      account_id: _req['account_id'],
      operate_all_space: _req['operate_all_space'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/space_connector/list */
  ListSpaceConnector(
    req?: connector.ListSpaceConnectorRequest,
    options?: T,
  ): Promise<connector.ListSpaceConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/space_connector/list');
    const method = 'POST';
    const data = {
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      connector_type: _req['connector_type'],
      search_word: _req['search_word'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/connector/account_connector/manage
   *
   * ManageAccountConnector 管理企业渠道
   */
  ManageAccountConnector(
    req?: connector.ManageAccountConnectorRequest,
    options?: T,
  ): Promise<connector.ManageAccountConnectorResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/account_connector/manage');
    const method = 'POST';
    const data = {
      account_id: _req['account_id'],
      connector_id_list: _req['connector_id_list'],
      action: _req['action'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/connector/account_connector/list
   *
   * 企业渠道管理
   *
   * GetAccountConnectorList 获取企业渠道列表
   */
  GetAccountConnectorList(
    req?: connector.GetAccountConnectorListRequest,
    options?: T,
  ): Promise<connector.GetAccountConnectorListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/account_connector/list');
    const method = 'GET';
    const params = {
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      search_word: _req['search_word'],
      space_id: _req['space_id'],
      connector_type: _req['connector_type'],
      account_id: _req['account_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/connector/space_connector/status
   *
   * GetSpaceConnectorStatus 获取空间下渠道的安装状态
   */
  GetSpaceConnectorStatus(
    req?: connector.GetSpaceConnectorStatusRequest,
    options?: T,
  ): Promise<connector.GetSpaceConnectorStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/space_connector/status');
    const method = 'GET';
    const params = {
      connector_id: _req['connector_id'],
      space_id_list: _req['space_id_list'],
      account_id: _req['account_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/connector/list_mini_program_domain */
  ListMiniProgramDomain(
    req?: connector.ListMiniProgramDomainRequest,
    options?: T,
  ): Promise<connector.ListMiniProgramDomainResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/list_mini_program_domain');
    const method = 'GET';
    const params = {
      enterprise_id: _req['enterprise_id'],
      connector_id: _req['connector_id'],
      search_word: _req['search_word'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/connector/set_mini_program_domain
   *
   * 小程序域名管理
   */
  SetMiniProgramDomain(
    req?: connector.SetMiniProgramDomainRequest,
    options?: T,
  ): Promise<connector.SetMiniProgramDomainResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/set_mini_program_domain');
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      domain_list: _req['domain_list'],
      action: _req['action'],
      connector_id: _req['connector_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/update_mini_program_domain */
  UpdateMiniProgramDomain(
    req: connector.UpdateMiniProgramDomainRequest,
    options?: T,
  ): Promise<connector.UpdateMiniProgramDomainResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/connector/update_mini_program_domain');
    const method = 'POST';
    const data = { id: _req['id'], domain: _req['domain'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
