/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';

export type Int64 = string | number;

export enum AsyncStatus {
  Running = 1,
  Success = 2,
  Failure = 3,
}

export enum CozeBotInfoType {
  /** 草稿 bot */
  DraftBot = 1,
  /** 商店 bot */
  ProductBot = 2,
}

/** 以一行数据集为例：
列名：      input             output         context              person
列值：  "我适合什么样的工作"   "你适合休息"   "不喜欢挑战、不喜欢出力"   "{性别：男， 年龄：18， 文凭：名牌大学毕业}"
Input 参数构建：
  Input: "我适合什么样的工作"
  Variables: map{ context: "不喜欢挑战、不喜欢出力", person: "{性别：男， 年龄：18， 文凭：名牌大学毕业}" }
  Histories: null */
export interface Input {
  /** 数据集中的 input 列，一般代表评测Case中的用户输入 */
  input?: string;
  /** 数据集中，除 input、output 列之外，其他所有的列均视为是 Variable，列名作为 key、列值作为 value */
  variables?: Record<string, flow_devops_evaluation_callback_common.Content>;
  /** 多轮评测场景中，数据集中的一行数据中又可拆分成 n 轮评测输入。
在第 n 轮的评测中，Histories 传入 [1 ~ n-1] 的信息，采用 Json 序列化。 第 n 轮的信息由 Input 字段传入
此处前 n-1 轮的信息，采用 Json 序列化。序列化的 Schema 由评测任务制定，由评估器进行解析使用
例如：
Input: "我今天出门适合什么穿搭？"
Histories：[{ "human": "我在XX市XX区，今天天气怎么样", "assistant": "经过查询天气API，今天有雷阵雨，5级大风，温度5度左右" }] */
  histories?: Array<flow_devops_evaluation_callback_common.Message>;
  input_v2?: flow_devops_evaluation_callback_common.Content;
}

/** 回调的壳子：
 既作为服务端返回参数
 关联对象时候，又作为前端传参的壳子 */
export interface Object {
  object_type: Int64;
  /** UI上针对评估对象的拉列表页展示的名称,objectMetaName */
  name?: string;
  /** 一方 Agent 中子评估对象的唯一标识和配置等相关信息。建议采用 Json 序列化透传
ObjectMeta 的生产、传递、消费路径：SearchObject(生产方)->评测平台UI->评测平台用户圈选->评测平台服务端->评测对象Playground(消费方)
ObjectMeta 由 评估对象服务方 生成和解析，评测平台仅透传
像内置的接口没有这个字段 */
  object_meta?: string;
  /** 用于筛选哪些object可见 */
  space_id: Int64;
  /** 只用于展示的object信息，例如bot头像 */
  avatar_url?: string;
  /** 回调业务方的env, 前端透传该值，由evaluation解析后执行对应泳道的回调 */
  env?: string;
  /** UI在用例列表展示唯一子对象的id，需回调业务方填 */
  object_meta_id?: string;
  /** UI在用例列表展示，用户在前端选中评测对象后，快照存储用于在用例列表中展示 */
  object_type_name?: string;
  callback_type?: flow_devops_evaluation_entity.CallbackType;
}

export interface Output {
  /** 评估对象的输出信息。评估器会以数据集中 output 列为基准，对评估对象输出的 Prediction 进行评测
Prediction 可以是 string、也可以是 JSON 结构体，需要与评估器对齐解析方式 */
  prediction?: string;
  prediction_v2?: flow_devops_evaluation_callback_common.Content;
  ext?: Record<string, string>;
}

/** 去回调业务search接口需要传的key，通过这个key模糊搜索出一批ObjectMeta */
export interface SearchKey {
  /** 可以再改 */
  key: string;
}

export interface Usage {
  /** 计费信息。一次评估对象Playground执行时，内部总的输入、输出的Tokens的消耗 */
  input_tokens?: Int64;
  output_tokens?: Int64;
  first_token_latency?: Int64;
}
/* eslint-enable */
