/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as common from './common';

export type Int64 = string | number;

export enum PhotoSliceType {
  /** 描述文字 */
  Caption = 1,
  /** 标签信息 */
  Tags = 2,
}

export enum SliceStatus {
  /** 未向量化 */
  PendingVectoring = 0,
  /** 已向量化 */
  FinishVectoring = 1,
  /** 禁用 */
  Deactive = 9,
  /** 审核不通过 */
  AuditFailed = 1000,
}

export enum SourceType {
  /** 使用 TOS 存储，由 Flink 算子获取数据，进行切片和向量化 */
  TOS = 1,
  /** 直接传输原始文本数据，向量化原始数据 */
  Raw = 2,
}

export enum VectorRuleType {
  /** 选择部分列进行向量化 */
  SelectedFields = 1,
}

export interface CreateSliceRequest {
  /** 1: optional i64  operator_id // deprecated */
  document_id: string;
  /** 3: optional Source  source  // deprecated, 数据源
4: optional VectorRule vector_rule  // deprecated, 向量化规则, 未启用 */
  raw_text?: string;
  sequence?: string;
  extra?: string;
  tree_node_id?: Int64;
  front_tree_node_id?: Int64;
  parent_tree_node_id?: Int64;
  /** 用来区分是否是火山知识库，不传默认为coze知识库 */
  dataset_id?: string;
  /** 由于火山侧document是非数字串，新增这个字段传入document_id */
  document_id_new?: string;
  Base?: base.Base;
}

export interface CreateSliceResponse {
  slice_id?: string;
  /** 由于火山侧slice id是非数字串，新增这个字段返回火山侧slice_id */
  slice_id_new?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteSliceRequest {
  slice_ids?: Array<string>;
  Base?: base.Base;
}

export interface DeleteSliceResponse {
  code: Int64;
  msg: string;
}

export interface HitInfo {
  /** 1: i64 operator_id */
  document_id: Int64;
  slice_id: Int64;
  /** 命中时的用户输入 */
  keyword: string;
  /** 分数 */
  score: number;
}

export interface ListSliceRequest {
  /** 1:  optional i64    operator_id                     // deprecated */
  document_id?: string;
  /** 序号 */
  sequence?: string;
  /** 查询关键字 */
  keyword?: string;
  /** 如果只传 dataset_id，则返回该知识库下的分片 */
  dataset_id?: string;
  /** 从1开始 */
  page_no?: string;
  page_size?: string;
  sort_field?: string;
  is_asc?: boolean;
  Base?: base.Base;
}

export interface ListSliceResponse {
  slices?: Array<SliceInfo>;
  total?: string;
  hasmore?: boolean;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface SaveSliceOfflineElement {
  content?: string;
  extra?: string;
}

export interface SliceInfo {
  slice_id?: string;
  content?: string;
  status?: SliceStatus;
  /** 命中次数 */
  hit_count?: string;
  /** 字符数 */
  char_count?: string;
  /** token数 */
  token_count?: string;
  /** 序号 */
  sequence?: string;
  document_id?: string;
  /** 分片相关的元信息, 透传 slice 表里的 extra->chunk_info 字段 (json) */
  chunk_info?: string;
}

export interface TaskProgress {
  document_id?: Int64;
  progress?: number;
  status?: common.DocumentType;
  /** 状态的详细描述；如果切片失败，返回失败信息 */
  status_descript?: string;
  document_name?: string;
}

export interface UpdateSliceRequest {
  /** 1: optional i64  operator_id // deprecated */
  slice_id: string;
  /** 3: optional Source  source  // deprecated, 数据源
4: optional VectorRule vector_rule  // 向量化规则, 未启用
deprecated */
  document_id?: Int64;
  /** 更新的状态 */
  status?: SliceStatus;
  /** 要更新的内容 */
  raw_text?: string;
  /** 表格要更新的单元格内容 */
  table_unit_text?: string;
  Base?: base.Base;
}

export interface UpdateSliceResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
