/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** Options for Abase Binlog */
export interface AbaseBinlogOptions {
  /** Database name for Abase */
  abase_database_name?: string;
  /** Topic name */
  topic?: string;
  /** Whether to process in order */
  orderly?: boolean;
  /** Subscription expression */
  sub_expr?: string;
  /** Cluster name */
  cluster_name?: string;
  /** Consumer group name */
  consumer_group?: string;
  /** Filter source type */
  filter_source_type?: string;
  /** Filter source */
  filter_source?: string;
  /** Filter plugin ID */
  filter_plugin_id?: string;
  /** Filter plugin version */
  filter_plugin_version?: string;
  /** Retry interval in seconds */
  retry_interval_seconds?: number;
  /** Subscribe throughput */
  subscribe_throughput?: number;
  /** default is rocketmq, other option is kafka */
  mq_type?: string;
  /** Quick subscription flag */
  quick_sub?: boolean;
}

/** Traffic routing alias configuration */
export interface Alias {
  /** Name of the traffic alias */
  alias_name?: string;
  /** Traffic distribution by revision ID */
  traffic_config?: Record<string, number>;
  /** Zone-specific traffic distribution */
  zone_traffic_config?: Record<string, Record<string, number>>;
  /** Formatted traffic configurations */
  format_traffic_configs?: Array<FormatTrafficConfig>;
  /** Formatted zone traffic configurations */
  format_zone_traffic_config?: Array<FormatZoneTrafficConfig>;
  /** Canary traffic distribution */
  canary_traffic_config?: Record<string, number>;
  /** Formatted canary traffic configurations */
  format_canary_traffic_configs?: Array<FormatTrafficConfig>;
}

/** All triggers for a function */
export interface AllTriggers {
  /** Consul triggers */
  consul?: Array<ConsulTriggerResponseData>;
  /** Eventbus triggers */
  eventbus?: Array<GlobalMQEventTriggerResponseData>;
  /** HTTP triggers */
  http?: Array<HttpTriggerResponse>;
  /** MQ event triggers */
  mqevents?: Array<GlobalMQEventTriggerResponseData>;
  /** Timer triggers */
  timers?: Array<TimerTrigger>;
  /** TOS triggers */
  tos?: Array<GlobalMQEventTriggerResponseData>;
  /** Abase binlog triggers */
  abase_binlog?: Array<GlobalMQEventTriggerResponseData>;
  /** EventBridge triggers */
  event_bridge?: Array<EventBridgeTrigger>;
}

export interface ApiResponse {
  code?: number;
  /** API response data */
  data?: ApiResponseDataMessage2;
  error?: string;
}

/** Empty API response data */
export interface ApiResponseDataMessage2 {}

export interface BasicFunctionParamsVpcConfigMessage2 {
  /** ID of the VPC */
  vpc_id?: string;
}

/** Basic regional metadata parameters */
export interface BasicRegionalMetaParams {
  /** Traffic aliases for the region */
  aliases?: Record<string, Alias>;
  /** Whether async mode is enabled */
  async_mode?: boolean;
  /** Whether authentication is enabled */
  auth_enable?: boolean;
  /** Disable ByteFaas error response */
  bytefaas_error_response_disabled?: boolean;
  /** Disable ByteFaas response header */
  bytefaas_response_header_disabled?: boolean;
  /** Cell name */
  cell?: string;
  /** Whether cold start is disabled, true means disabled */
  cold_start_disabled?: boolean;
  /** Whether CORS is enabled */
  cors_enable?: boolean;
  /** Enable dynamic load balancing data report */
  dynamic_load_balancing_data_report_enabled?: boolean;
  /** List of enabled VDCs for dynamic load balancing */
  dynamic_load_balancing_enabled_vdcs?: Array<string>;
  /** Enable dynamic load balancing weight */
  dynamic_load_balancing_weight_enabled?: boolean;
  /** Enable colocate scheduling */
  enable_colocate_scheduling?: boolean;
  /** Environment name */
  env_name?: string;
  /** Whether exclusive mode is enabled */
  exclusive_mode?: boolean;
  /** List of formatted environment variables */
  format_envs?: Array<FormatEnvs>;
  /** Function ID */
  function_id?: string;
  /** Enable gateway route */
  gateway_route_enable?: boolean;
  /** Enable GDPR compliance */
  gdpr_enable?: boolean;
  /** List of global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** Disable HTTP trigger，true means disable */
  http_trigger_disable?: boolean;
  /** Whether only IPv6 is enabled */
  is_ipv6_only?: boolean;
  /** Disabled zones in the region */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Latency timeout in seconds */
  latency_sec?: number;
  /** List of local cache namespace IDs */
  local_cache_namespace_ids?: Array<string>;
  /** Network class ID */
  net_class_id?: number;
  /** Network name */
  network?: string;
  /** Owner of the region */
  owner?: string;
  /** Protocol used */
  protocol?: string;
  /** PSM name */
  psm?: string;
  /** Region name */
  region?: string;
  /** Enable reserved DP */
  reserved_dp_enabled?: boolean;
  /** Revision ID */
  revision_id?: string;
  /** Revision number */
  revision_number?: number;
  /** Routing strategy, enums：prefer_reserved, prefer_elastic. */
  routing_strategy?: string;
  /** Runtime environment */
  runtime?: string;
  /** Service ID */
  service_id?: string;
  /** Enable tracing */
  trace_enable?: boolean;
  /** Throttle log bytes per zone */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** Enable ZTI */
  zti_enable?: boolean;
  /** Whether online mode is enabled */
  online_mode?: boolean;
  /** List of preferred clusters for elastic scaling */
  formatted_elastic_prefer_cluster?: Array<FormattedPreferCluster>;
  /** List of preferred clusters for reserved scaling */
  formatted_reserved_prefer_cluster?: Array<FormattedPreferCluster>;
  /** Enable reserve frozen instance */
  enable_reserve_frozen_instance?: boolean;
  /** Disable cgroup v2 */
  disable_cgroup_v2?: boolean;
  /** Enable overload protection */
  overload_protect_enabled?: boolean;
  /** Enable federated on-demand resource */
  enable_fed_on_demand_resource?: Record<string, boolean>;
  /** enable privileged in pod */
  privileged?: boolean;
}

/** Build business data */
export interface BuildBizData {
  /** Request ID */
  request_id?: string;
  /** User who created the build */
  created_by?: string;
  /** JWT of the user who created the build */
  created_user_jwt?: string;
  /** Service ID */
  service_id?: string;
  /** Region name */
  region?: string;
  /** Target revision ID */
  target_revision_id?: string;
  /** Whether to rebuild */
  rebuild?: boolean;
  /** Function ID */
  function_id?: string;
  /** User type of the creator */
  created_user_type?: string;
  /** Build log link */
  build_log_link?: string;
}

/** Build process description and metadata */
export interface BuildDescription {
  /** Build identifier */
  build_id?: string;
  /** Build process information */
  build_info?: string;
  /** Link to build log */
  build_log_link?: string;
  /** Build status */
  status?: string;
  /** Built object size */
  built_object_size?: string;
}

/** Cluster information */
export interface ClusterInfo {
  /** Region name */
  region?: string;
  /** Cluster name */
  cluster?: string;
  /** Function ID */
  function_id?: string;
}

/** MQ consumer metadata for a cluster */
export interface ClusterMQConsumerMeta {
  /** MQ type, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type: string;
  /** MQ region */
  mq_region: string;
  /** Topic name */
  topic: string;
  /** MQ cluster name */
  mq_cluster: string;
  /** Consumer group name */
  consumer_group: string;
  /** Kafka options */
  kafka_options?: ClusterMQConsumerMetaKafkaOptions;
  /** RMQ options */
  rmq_options?: ClusterMQConsumerMetaRMQOptions;
  /** MQ topic link */
  mq_topic_link?: string;
  /** MQ consumer link */
  mq_consumer_link?: string;
  /** Multi environment version */
  multi_env_version: string;
  /** Eventbus link */
  eventbus_link?: string;
  /** Event name */
  event_name?: string;
}

/** Kafka options for MQ consumer meta */
export interface ClusterMQConsumerMetaKafkaOptions {
  /** Enable orderly consumption */
  orderly?: boolean;
}

/** RMQ options for MQ consumer meta */
export interface ClusterMQConsumerMetaRMQOptions {
  /** Enable orderly consumption */
  orderly?: boolean;
  /** Subscription expression */
  sub_expr?: string;
}

/** Code revision metadata */
export interface CodeRevision {
  /** Creation timestamp */
  created_at?: string;
  /** Creator */
  created_by?: string;
  /** List of dependencies */
  dependency?: Array<Dependency>;
  /** Deployment method. Enums: online-edit, scm, command-line, online-edit, image, lego
部署方式 */
  deploy_method?: string;
  /** Description */
  description?: string;
  /** Build install disabled, true means disable */
  disable_build_install?: boolean;
  /** Function ID */
  function_id?: string;
  /** Handler entry point */
  handler?: string;
  /** Code revision ID */
  id?: string;
  /** Initializer function */
  initializer?: string;
  /** Lazy loading enabled */
  lazyload?: boolean;
  /** Code revision number */
  number?: string;
  /** Protocol */
  protocol?: string;
  /** Run command */
  run_cmd?: string;
  /** Runtime environment */
  runtime?: string;
  /** Runtime container port */
  runtime_container_port?: number;
  /** Runtime debug container port */
  runtime_debug_container_port?: number;
  /** Service ID */
  service_id?: string;
  /** Source code URI
代码版本 URI */
  source?: string;
  /** Source type
代码版本类型 */
  source_type?: string;
  /** Plugin function details */
  plugin_function_detail?: PluginFunctionDetail;
  /** Build descriptions by region */
  build_desc_map?: Record<string, BuildDescription>;
  /** Image lazy loading enabled */
  open_image_lazyload?: boolean;
  /** Additional runtime container ports */
  runtime_other_container_ports?: Array<number>;
  /** Health check failure threshold */
  health_check_failure_threshold?: number;
  /** Health check period */
  health_check_period?: number;
  /** Health check path */
  health_check_path?: string;
}

/** Concurrency-based scaling thresholds */
export interface ConcurrencyScaleSettings {
  /** Memory threshold for scaling in */
  mem_scale_in_threshold?: number;
  /** Memory threshold for scaling out */
  mem_scale_out_threshold?: number;
  /** Target memory utilization */
  mem_scale_target?: number;
}

/** Confirmation business data */
export interface ConfirmBizData {
  /** User who confirmed */
  confirmed_by?: string;
  /** User type of the confirmer */
  confirmed_by_usertype?: string;
  /** Confirmation timestamp */
  comfirm_at?: string;
}

/** Consul trigger response data */
export interface ConsulTriggerResponseData {
  /** Name of the Consul trigger */
  name?: string;
  /** Description of the Consul trigger */
  description?: string;
  /** ID of the Consul trigger */
  id?: string;
  /** Function ID associated with the trigger */
  function_id?: string;
  /** Region where the trigger is deployed */
  region?: string;
  /** Product Subsys Module (PSM) name */
  psm?: string;
  /** Runtime environment for the trigger */
  runtime?: string;
  /** Protocol used by the trigger */
  protocol?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Trigger strategy */
  strategy?: string;
  /** Creation timestamp */
  created_at?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** Consul cluster name */
  consul_cluster?: string;
  /** Whether the trigger is deleted */
  is_deleted?: boolean;
  /** Deletion timestamp */
  deleted_at?: string;
  /** User who deleted the trigger */
  deleted_by?: string;
  /** Status of the trigger, enums: changing, pending, ready, failed, resetting, reset_failed */
  status?: string;
  /** Number of times meta has been synced */
  meta_synced_times?: number;
  /** Whether meta has been synced */
  meta_synced?: boolean;
  /** Timestamp when meta was last synced */
  meta_synced_at?: string;
  /** Internal ID */
  _id?: string;
}

/** Auto limit settings for consume migrate */
export interface ConsumeMigrateAutoLimit {
  /** QPS limit for consume migrate */
  qps_limit?: number;
  /** Expiration time for the QPS limit */
  expire_at?: string;
}

/** CPU-based scaling thresholds */
export interface CpuScaleSettings {
  /** CPU threshold for scaling in */
  cpu_scale_in_threshold?: number;
  /** CPU threshold for scaling out */
  cpu_scale_out_threshold?: number;
  /** Target CPU utilization */
  cpu_scale_target?: number;
}

/** Request to create an MQ trigger */
export interface CreateMQTriggerRequest {
  /** Alarm parameters for MQ trigger */
  alarm_params?: CreateMQTriggerRequestAlarmParamsMessage2;
  /** Enable ByteSuite debug */
  allow_bytesuite_debug?: boolean;
  /** Batch size for message consumption */
  batch_size?: number;
  /** Cell name */
  cell?: string;
  /** Cluster name */
  cluster: string;
  /** Mark deployment as inactive */
  deployment_inactive?: boolean;
  /** Description of the trigger */
  description?: string;
  /** Disable backoff strategy, true means disable */
  disable_backoff?: boolean;
  /** Disable smooth weighted round robin, true means disable */
  disable_smooth_wrr?: boolean;
  /** Type of dynamic load balancing, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Enable dynamic worker thread */
  dynamic_worker_thread?: boolean;
  /** Enable backoff strategy */
  enable_backoff?: boolean;
  /** Enable concurrency filter */
  enable_concurrency_filter?: boolean;
  /** Enable congestion control */
  enable_congestion_control?: boolean;
  /** Enable dynamic load balancing */
  enable_dynamic_load_balance?: boolean;
  /** Enable global rate limiter */
  enable_global_rate_limiter?: boolean;
  /** Enable IPC mode */
  enable_ipc_mode?: boolean;
  /** Enable MQ debug */
  enable_mq_debug?: boolean;
  /** Enable pod colocate scheduling */
  enable_pod_colocate_scheduling?: boolean;
  /** Enable static membership */
  enable_static_membership?: boolean;
  /** Enable traffic priority scheduling */
  enable_traffic_priority_scheduling?: boolean;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Environment variables */
  envs?: Record<string, string>;
  /** Function ID */
  function_id?: string;
  /** Enable hot reload */
  hot_reload?: boolean;
  /** Trigger ID */
  id?: string;
  /** Image alias */
  image_alias?: string;
  /** Image version */
  image_version?: string;
  /** Initial offset start position */
  initial_offset_start_from?: string;
  /** Whether auth info is updated */
  is_auth_info_updated?: boolean;
  /** Max retries from function status */
  max_retries_from_function_status?: number;
  /** MQ logger size limit */
  mq_logger_limit_size?: number;
  /** MQ message type, enums: native (to be deprecated), faas_native, cloudEvent_0_2 */
  mq_msg_type?: string;
  /** MQ region */
  mq_region?: string;
  /** MQ type, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** Alarm IDs for MS */
  ms_alarm_id?: Array<string>;
  /** Message channel length */
  msg_chan_length?: number;
  /** Trigger name */
  name?: string;
  /** Enable auto sharding */
  need_auto_sharding?: boolean;
  /** Number of MQ pods per function pod */
  num_of_mq_pod_to_one_func_pod?: number;
  /** Additional trigger options */
  options?: TriggerOptions;
  /** Plugin function parameters */
  plugin_function_param?: PluginFunctionParam;
  /** QPS limit */
  qps_limit?: number;
  /** Region name */
  region: string;
  /** Max replica limit */
  replica_max_limit?: number;
  /** Min replica limit */
  replica_min_limit?: number;
  /** Number of replicas */
  replicas?: number;
  /** Request timeout */
  request_timeout?: number;
  /** Resource limits */
  resource?: ResourceLimit;
  /** Enable runtime agent mode */
  runtime_agent_mode?: boolean;
  /** Enable scaling */
  scale_enabled?: boolean;
  /** MQ event scaling settings */
  scale_settings?: MQEventScaleSettings;
  /** SDK version */
  sdk_version?: string;
  /** Service ID */
  service_id: string;
  /** Enable vertical scaling */
  vertical_scale_enabled?: boolean;
  /** Number of worker v2 per half core */
  worker_v2_num_per_half_core?: number;
  /** Number of workers per pod */
  workers_per_pod?: number;
  /** Enable plugin function */
  enable_plugin_function?: boolean;
  /** Disable infinite retry for timeout, true means disable */
  disable_infinite_retry_for_timeout?: boolean;
  /** Mirror region filter */
  mirror_region_filter?: string;
  /** Enable GC tuner */
  enable_gctuner?: boolean;
  /** GC tuner percent */
  gctuner_percent?: number;
  /** Retry strategy, enums: fixed_interval, exponential */
  retry_strategy?: string;
  /** Max retry time */
  max_retry_time?: number;
  /** QPS limit time ranges */
  qps_limit_time_ranges?: Array<QPSLimitTimeRanges>;
  /** Rate limit step settings */
  rate_limit_step_settings?: RateLimitStepSettings;
  /** Enable step rate limit */
  enable_step_rate_limit?: boolean;
  /** Batch flush duration in milliseconds */
  batch_flush_duration_milliseconds?: number;
  /** Enforce replica to meet partition */
  replica_force_meet_partition?: boolean;
  /** Disaster scenario limit */
  limit_disaster_scenario?: number;
  /** Max dwell time in minutes */
  max_dwell_time_minute?: number;
  /** Enable canary update */
  enable_canary_update?: boolean;
  /** Traffic configuration */
  traffic_config?: Record<string, number>;
  /** Pod type */
  pod_type?: string;
  /** Package name */
  package?: string;
  /** QPS auto limit settings */
  qps_auto_limit?: ConsumeMigrateAutoLimit;
  /** Enable filter congestion control */
  enable_filter_congestion_control?: boolean;
  /** Enable congestion control cache */
  enable_congestion_control_cache?: boolean;
  /** Host unique identifier */
  host_uniq?: HostUniq;
  /** Enable in-cell migration */
  in_cell_migration?: boolean;
  /** Parameters for the pipeline */
  pipeline_params?: PipelineParams;
}

/** Alarm parameters for creating MQ trigger request */
export interface CreateMQTriggerRequestAlarmParamsMessage2 {
  /** Lag alarm threshold for MQ trigger */
  lag_alarm_threshold?: number;
}

export interface CreateTicketResponse {
  code?: number;
  /** Ticket data */
  data?: Ticket;
  error?: string;
}

/** Request to create a timer trigger */
export interface CreateTimerTriggerRequest {
  /** Cell where the timer trigger is deployed */
  cell?: string;
  /** Cluster where the service is deployed */
  cluster: string;
  /** Concurrency limit for the timer trigger */
  concurrency_limit?: number;
  /** Creation timestamp */
  created_at?: string;
  /** Cron expression for scheduling */
  cron?: string;
  /** Description of the timer trigger */
  description?: string;
  /** Whether the timer trigger is enabled */
  enabled?: boolean;
  /** Name of the timer trigger */
  name?: string;
  /** Payload for the timer trigger */
  payload?: string;
  /** Region where the service is deployed */
  region: string;
  /** Number of retries for the timer trigger */
  retries?: number;
  /** Scheduled time for the timer trigger */
  scheduled_at?: string;
  /** Service ID */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

/** Data for creating a trigger in pipeline */
export interface CreateTriggerBizData {
  /** Trigger type (timer or mqevent) */
  trigger_type: string;
  /** Creator of the trigger */
  created_by: string;
  /** Timer trigger request data */
  timer_trigger_request_data?: CreateTimerTriggerRequest;
  /** MQ trigger request data */
  mq_trigger_request_data?: CreateMQTriggerRequest;
  /** Trigger ID */
  trigger_id?: string;
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Service ID */
  service_id: string;
  /** Function ID */
  function_id: string;
  /** BPM order list */
  bpm_orders: Array<TriggerBizDataBPMOrderData>;
  /** Trigger name */
  trigger_name: string;
}

/** Cron strategy for scheduled scaling */
export interface CronStrategy {
  /** required if bpm_status is create_pending or update_pending */
  bpm_id?: number;
  /** could be create_pending, update_pending, in_effect, rejected or empty */
  bpm_status?: string;
  /** could be daily_interval, weekly_interval, monthly_interval */
  cron_interval?: string;
  /** which days should be effective if it is monthly_interval, 1 - 31 */
  day_of_monthly?: Array<number>;
  /** which days should be effective if it is weekly_interval, 0 - 6 */
  day_of_weekly?: Array<number>;
  /** how long the strategy will be effective */
  duration_minutes?: number;
  /** how many replicas should be keep in each zone */
  min_zone_replicas?: Record<string, number>;
  /** start time for the cron strategy */
  start_time?: CronStrategyStartTimeMessage2;
  /** update config for pending strategies */
  update_config?: UpdateConfig;
}

/** Start time for cron strategy */
export interface CronStrategyStartTimeMessage2 {
  /** the hours to start */
  hours?: number;
  /** the minutes to start */
  minutes?: number;
}

/** Dependency information */
export interface Dependency {
  /** Dependency name */
  name?: string;
  /** Sub-path for dependency */
  sub_path?: string;
  /** Dependency type */
  type?: string;
  /** Dependency version */
  version?: string;
}

/** Directed edge in pipeline DAG */
export interface Edge {
  /** Source node */
  from?: number;
  /** Target node */
  to?: number;
}

/** Empty object */
export interface EmptyObject {}

/** Help information for error troubleshooting */
export interface ErrorHelp {
  /** Show start logs for troubleshooting
是否需要展示启动日志用于排查问题 */
  show_start_logs?: boolean;
}

/** EventBridge trigger information */
export interface EventBridgeTrigger {
  /** Project UID */
  project_uid: string;
  /** Rule UID */
  rule_uid: string;
  /** Rule name */
  rule_name: string;
  /** Whether enabled */
  enabled: boolean;
  /** EventBridge link */
  event_bridge_link: string;
  /** Event source */
  event_source: string;
  /** Event types */
  event_types: Array<string>;
  /** Creation time */
  created_at: string;
  /** Update time */
  updated_at: string;
}

/** Options for EventBus */
export interface EventBusOptions {
  /** Name of the EventBus */
  eventbus_name?: string;
  /** Dispatcher cluster name */
  dispatcher_cluster?: string;
  /** Consumer group name */
  consumer_group?: string;
  /** Number of consumers */
  consumer_num?: number;
  /** Retry interval in seconds */
  retry_interval?: number;
  /** Whether message consumption is orderly */
  orderly?: boolean;
  /** Subscription expression */
  sub_expr?: string;
  /** Cluster name */
  cluster?: string;
  /** Type of EventBus */
  type?: string;
  /** Topic name */
  topic?: string;
  /** List of EventBus topic information */
  event_bus_topic_infos?: Array<EventBusTopicInfo>;
}

/** EventBus topic information */
export interface EventBusTopicInfo {
  /** Region of the EventBus topic */
  region?: string;
  /** Cluster of the EventBus topic */
  cluster?: string;
  /** Type of the EventBus topic */
  type?: string;
  /** Topic name */
  topic?: string;
}

/** Base image details for FaaS */
export interface FaasBaseImageDesc {
  /** Base image ID */
  image_id?: string;
  /** Build timestamp */
  build_time_stamp?: string;
  /** Runtime used for building */
  build_runtime?: string;
  /** Base image description */
  desc?: string;
}

/** A pair of environment variable in the form of static key-value object
 used as `list<FormatEnvs>` for easier tagging on DECC where dynamic maps are not supported properly */
export interface FormatEnvs {
  /** Key of the environment variable */
  env_key: string;
  /** Value of the environment variable */
  env_value: string;
}

/** Preferred cluster configuration for a zone */
export interface FormattedPreferCluster {
  /** Zone name */
  zone?: string;
  /** Preferred cluster name for this zone */
  prefer_cluster?: string;
}

/** Configuration for traffic distribution to specific revisions */
export interface FormatTrafficConfig {
  /** ID of the revision receiving traffic */
  revision_id?: string;
  /** Percentage of traffic to send to this revision (0-100) */
  traffic_value?: number;
}

/** Zone-specific traffic configuration */
export interface FormatZoneTrafficConfig {
  /** Name of the zone */
  zone?: string;
  /** Traffic distribution configuration for this zone */
  zone_traffic_config?: Array<FormatTrafficConfig>;
}

/** Function CPU scaling settings */
export interface FuncCPUScaleSettings {
  /** CPU scale out threshold */
  cpu_scale_out_threshold?: number;
  /** CPU scale in threshold */
  cpu_scale_in_threshold?: number;
  /** CPU scale target */
  cpu_scale_target?: number;
  /** CPU scale in target */
  cpu_scale_in_target?: number;
}

/** Fast scaling configuration */
export interface FuncFastScaleSettings {
  /** Whether fast scaling is enabled */
  enable_fast_scale?: boolean;
  /** Unhealthy pod rate threshold for scaling */
  unhealthy_pod_rate_to_scale?: number;
}

/** Lag-based scaling configuration */
export interface FuncLagScaleSettings {
  /** Name of the lag scale set */
  lag_scale_set?: string;
  /** Lag threshold for scaling out */
  lag_scale_out_threshold?: number;
  /** Lag threshold for scaling in */
  lag_scale_in_threshold?: number;
  /** Time window for lag measurement (seconds) */
  lag_tolerate_window_in_sec?: number;
  /** Dwell time threshold for scaling out */
  dwell_time_scale_out_threshold?: number;
  /** Dwell time threshold for scaling in */
  dwell_time_scale_in_threshold?: number;
  /** Maximum step size for dwell time scaling */
  dwell_time_scale_max_step?: number;
}

/** Predictive scaling configuration */
export interface FuncPredictiveScalingSettings {
  /** Whether predictive scaling is enabled */
  enable_predictive_scaling?: boolean;
}

/** Complete function scaling configuration */
export interface FunctionScaleSettings {
  /** Name of the scale set */
  scale_set_name?: string;
  /** Concurrency-based scaling settings */
  concurrency_scale_settings?: ConcurrencyScaleSettings;
  /** CPU-based scaling settings */
  cpu_scale_settings?: CpuScaleSettings;
  /** Memory-based scaling settings */
  mem_scale_settings?: MEMScaleSettings;
  /** Fast scaling settings */
  fast_scale_settings?: FuncFastScaleSettings;
  /** Predictive scaling settings */
  predictive_scaling_setting?: FuncPredictiveScalingSettings;
  /** Lag-based scaling settings */
  lag_scale_settings?: FuncLagScaleSettings;
}

/** Global MQ event trigger response data */
export interface GlobalMQEventTriggerResponseData {
  /** Batch size for MQ event */
  batch_size?: number;
  /** Batch flush duration in milliseconds */
  batch_flush_duration_milliseconds?: number;
  /** Description of the MQ event trigger */
  description?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Function ID associated with the trigger */
  function_id?: string;
  /** Cell name */
  cell?: string;
  /** Service ID */
  service_id?: string;
  /** Cluster name */
  cluster?: string;
  /** Trigger ID */
  id?: string;
  /** List of MS alarm IDs */
  ms_alarm_id?: Array<string>;
  /** MQ type, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type?: string;
  /** Maximum retries from function status */
  max_retries_from_function_status?: number;
  /** QPS limit */
  qps_limit?: number;
  /** Name of the MQ event trigger */
  name?: string;
  /** Trigger options */
  options?: TriggerOptions;
  /** Region name */
  region?: string;
  /** MQ region */
  mq_region?: string;
  /** Runtime agent mode enabled */
  runtime_agent_mode?: boolean;
  /** Dynamic worker thread enabled */
  dynamic_worker_thread?: boolean;
  /** Maximum replica limit per key */
  replica_max_limit?: Record<string, number>;
  /** Minimum replica limit per key */
  replica_min_limit?: Record<string, number>;
  /** Number of replicas */
  replicas?: number;
  /** Whether scaling is enabled */
  scale_enabled?: boolean;
  /** Whether vertical scaling is enabled */
  vertical_scale_enabled?: boolean;
  /** Enable static membership */
  enable_static_membership?: boolean;
  /** Status of the MQ event trigger */
  status?: string;
  /** Status message */
  status_message?: string;
  /** Whether the trigger is deleted */
  is_deleted?: boolean;
  /** Deletion timestamp */
  deleted_at?: string;
  /** User who deleted the trigger */
  deleted_by?: string;
  /** Creation timestamp */
  created_at?: string;
  /** User who created the trigger */
  created_by?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** User who last updated the trigger */
  updated_by?: string;
  /** Whether meta has been synced */
  meta_synced?: boolean;
  /** Timestamp when meta was last synced */
  meta_synced_at?: string;
  /** Enable MQ debug mode */
  enable_mq_debug?: boolean;
  /** MQ logger limit size */
  mq_logger_limit_size?: number;
  /** Enable backoff */
  enable_backoff?: boolean;
  /** Disable backoff */
  disable_backoff?: boolean;
  /** Number of worker v2 per half core */
  worker_v2_num_per_half_core?: number;
  /** Enable concurrency filter */
  enable_concurrency_filter?: boolean;
  /** MQ message type, enums: native (to be deprecated), faas_native, cloudEvent_0_2 */
  mq_msg_type?: string;
  /** Whether the trigger is in releasing state */
  in_releasing?: boolean;
  /** Mirror region filter */
  mirror_region_filter?: string;
  /** Retry strategy, enums: fixed_interval, exponential */
  retry_strategy?: string;
  /** Maximum retry time */
  max_retry_time?: number;
  /** List of QPS limit time ranges */
  qps_limit_time_ranges?: Array<QPSLimitTimeRanges>;
  /** Rate limit step settings */
  rate_limit_step_settings?: RateLimitStepSettings;
  /** Enable step rate limit */
  enable_step_rate_limit?: boolean;
  /** Number of workers per pod */
  workers_per_pod?: number;
  /** Message channel length */
  msg_chan_length?: number;
  /** Number of MQ pods to one function pod */
  num_of_mq_pod_to_one_func_pod?: number;
  /** Whether auto sharding is needed */
  need_auto_sharding?: boolean;
  /** Enable traffic priority scheduling */
  enable_traffic_priority_scheduling?: boolean;
  /** Enable pod colocate scheduling */
  enable_pod_colocate_scheduling?: boolean;
  /** Enable global rate limiter */
  enable_global_rate_limiter?: boolean;
  /** Enable congestion control */
  enable_congestion_control?: boolean;
  /** Allow bytesuite debug */
  allow_bytesuite_debug?: boolean;
  /** Enable dynamic load balance */
  enable_dynamic_load_balance?: boolean;
  /** Disable smooth weighted round robin, true means disable */
  disable_smooth_wrr?: boolean;
  /** Dynamic load balance type, enums: round_robin, smooth_weighted_round_robin, weighted_round_robin */
  dynamic_load_balance_type?: string;
  /** Enable GC tuner */
  enable_gctuner?: boolean;
  /** GC tuner percent */
  gctuner_percent?: number;
  /** Whether deployment is inactive */
  deployment_inactive?: boolean;
  /** Force replica to meet partition */
  replica_force_meet_partition?: boolean;
  /** Plugin function parameters */
  plugin_function_param?: PluginFunctionParam;
  /** MQ event revision */
  mqevent_revision?: MQEventRevision;
  /** Enable plugin function */
  enable_plugin_function?: boolean;
  /** Enable canary update */
  enable_canary_update?: boolean;
  /** Traffic configuration */
  traffic_config?: Record<string, number>;
  /** Environment variables */
  envs?: Record<string, string>;
  /** Image version */
  image_version?: string;
  /** Image alias */
  image_alias?: string;
  /** SDK version */
  sdk_version?: string;
  /** Offset reset result */
  offset_reset_result?: ResetOffsetResult;
  /** Kafka reset offset user data */
  kafka_reset_offset_user_data?: ResetOffsetReq;
  /** Request timeout */
  request_timeout?: number;
  /** Disable infinite retry for timeout */
  disable_infinite_retry_for_timeout?: boolean;
  /** Initial offset start from */
  initial_offset_start_from?: string;
  /** Kafka metric prefix */
  kafka_metric_prefix?: string;
  /** MQ event scale settings */
  scale_settings?: MQEventScaleSettings;
  /** Enable hot reload */
  hot_reload?: boolean;
  /** Enable RMQ lease */
  enable_rmq_lease?: boolean;
  /** Package name */
  package?: string;
  /** Pod type */
  pod_type?: string;
  /** Volc ID */
  id_volc?: string;
  /** Resource configuration */
  resource?: Resource;
  /** Latest image alias */
  latest_image_alias?: string;
  /** Latest SDK version */
  latest_sdk_version?: string;
  /** Log link */
  log_link?: string;
  /** Streaming log link */
  streaming_log_link?: string;
  /** Argos link */
  argos_link?: string;
  /** Grafana link */
  grafana_link?: string;
  /** Grafana eventbus link */
  grafana_eventbus_link?: string;
  /** MQ topic link */
  mq_topic_link?: string;
  /** MQ consumer link */
  mq_consumer_link?: string;
  /** Ticket ID */
  ticket_id?: string;
  /** Enable filter congestion control */
  enable_filter_congestion_control?: boolean;
  /** Enable congestion control cache */
  enable_congestion_control_cache?: boolean;
  /** Host uniqueness configuration */
  host_uniq?: HostUniq;
  /** Image version number */
  image_version_number?: string;
  /** Latest image version number */
  latest_image_version_number?: string;
  /** Current image version tag */
  current_image_version_tag?: string;
  /** Current image version status */
  current_image_version_status?: string;
  /** Latest image version tag */
  latest_image_version_tag?: string;
  /** Whether in cell migration */
  in_cell_migration?: boolean;
}

/** GPU resource configuration */
export interface GPU {
  /** GPU accelerator type */
  accelerator?: string;
  /** Number of GPU partitions */
  partitions?: number;
  /** GPU memory in MB */
  gpu_mem_mb?: number;
}

/** MQ event configuration for grey release */
export interface GreyMQEvent {
  /** MQ event ID */
  mqevent_id: string;
  /** Grey release traffic percentage */
  grey_percentage: number;
  /** Region for grey release */
  region: string;
  /** Cluster for grey release */
  cluster: string;
}

/** Host uniqueness constraint */
export interface HostUniq {
  /** Uniqueness type, Enums: soft/function-level, soft/service-level, hard/function-level, hard/service-level */
  host_unique_type?: string;
  /** Tolerance for uniqueness */
  unique_tolerance?: number;
}

/** HTTP trigger response */
export interface HttpTriggerResponse {
  /** Trigger name */
  name?: string;
  /** Description */
  description?: string;
  /** Trigger ID */
  id?: string;
  /** Cell name */
  cell?: string;
  /** Function ID */
  function_id?: string;
  /** URL prefix */
  url_prefix?: string;
  /** Disable ByteFaas error response */
  bytefaas_error_response_disabled?: boolean;
  /** Disable ByteFaas response header */
  bytefaas_response_header_disabled?: boolean;
  /** Runtime environment */
  runtime?: string;
  /** Version type */
  version_type?: string;
  /** Version value */
  version_value?: string;
  /** Region name */
  region?: string;
  /** Whether enabled */
  enabled?: boolean;
  /** Creation time */
  created_at?: string;
  /** Update time */
  updated_at?: string;
  /** Zone URLs */
  zone_urls?: Record<string, string>;
  /** Main URL */
  url?: string;
  /** Secondary URL */
  secondary_url?: string;
  /** New URL */
  new_url?: string;
}

/** Inner strategy combining cron and replica controller strategies */
export interface InnerStrategy {
  /** cron strategy for scheduled scaling */
  cron_strategy?: CronStrategy;
  /** replica controller strategy for managing replicas */
  replica_controller_strategy?: ReplicaControllerStrategy;
}

/** Kafka MQ options */
export interface KafkaMQOptions {
  /** Topic name */
  topic?: string;
  /** Cluster name */
  cluster_name?: string;
  /** Consumer group name */
  consumer_group?: string;
  /** Whether to enable message filtering */
  enable_filter?: boolean;
  /** Filter source type */
  filter_source_type?: string;
  /** Filter source */
  filter_source?: string;
  /** Filter plugin ID */
  filter_plugin_id?: string;
  /** Filter plugin version */
  filter_plugin_version?: string;
  /** Consumer fetch buffer size */
  consumer_fetch_buffer?: number;
  /** Retry interval in seconds */
  retry_interval_seconds?: number;
  /** Enable multi environment v2 */
  enable_multi_env_v2?: boolean;
  /** Multi environment version */
  multi_env_version?: string;
  /** Whether to close multi environment */
  close_multi_env?: boolean;
  /** Whether to enable fetch limit */
  fetch_limit?: boolean;
  /** Enable cooperative mode */
  enable_cooperative?: boolean;
  /** Custom config center URL */
  config_center_url_custom?: string;
  /** Config center region */
  config_center_region?: string;
  /** Whether message consumption is orderly */
  orderly?: boolean;
  /** Enable non-compression consume */
  enable_non_compression_consume?: boolean;
}

/** Map message for additional properties */
export interface MapMessage {
  /** Additional properties (string to int) */
  additional_properties?: Record<string, number>;
}

/** Memory-based scaling thresholds */
export interface MEMScaleSettings {
  /** Memory threshold for scaling in */
  mem_scale_in_threshold?: number;
  /** Memory threshold for scaling out */
  mem_scale_out_threshold?: number;
  /** Target memory utilization */
  mem_scale_target?: number;
}

/** Common scaling settings for MQ events */
export interface MQCommonScalingSetting {
  /** Maximum step for scale down */
  scale_down_max_step?: number;
  /** Maximum step for scale up */
  scale_up_max_step?: number;
  /** Minimum interval for continuous scale down (seconds) */
  continuous_scale_down_min_interval_sec?: number;
  /** 缩容步长比例
Scale down max step ratio */
  scale_down_max_step_ratio?: number;
  /** 扩容步长比例
Scale up max step ratio */
  scale_up_max_step_ratio?: number;
  /** 是否开启rebalance敏感型
Enable rebalance sensitivity */
  rebalance_sensitive?: boolean;
}

/** CPU scaling settings for MQ events */
export interface MQEventCPUScaleSettings {
  /** CPU scale out threshold */
  cpu_scale_out_threshold?: number;
  /** CPU scale in threshold */
  cpu_scale_in_threshold?: number;
  /** Target CPU scale value */
  cpu_scale_target?: number;
}

/** Memory scaling settings for MQ events */
export interface MQEventMEMScaleSettings {
  /** Memory scale out threshold */
  mem_scale_out_threshold?: number;
  /** Memory scale in threshold */
  mem_scale_in_threshold?: number;
  /** Target memory scale value */
  mem_scale_target?: number;
}

/** MQ event revision */
export interface MQEventRevision {
  /** Revision ID */
  revision_id?: string;
  /** Request timeout in milliseconds */
  request_timeout_ms?: number;
  /** Plugin function parameters */
  plugin_function_param?: PluginFunctionParam2;
}

/** Scaling settings for MQ events */
export interface MQEventScaleSettings {
  /** Name of the scale set */
  scale_set_name?: string;
  /** CPU scaling settings */
  cpu_scale_settings?: MQEventCPUScaleSettings;
  /** Memory scaling settings */
  mem_scale_settings?: MQEventMEMScaleSettings;
  /** Predictive scaling settings */
  predictive_scale_settings?: MQPredictiveScalingSetting;
  /** Lag scaling settings */
  lag_scale_settings?: MQTriggerLagScaleSettings;
  /** Common scaling settings */
  common_scale_settings?: MQCommonScalingSetting;
}

/** Periodic traffic prediction settings for MQ */
export interface MQPredictiveScalingSetting {
  /** Enable periodic traffic prediction */
  enable_periodic_traffic_prediction?: boolean;
}

/** Lag scaling settings for MQ triggers */
export interface MQTriggerLagScaleSettings {
  /** Lag scale set name */
  lag_scale_set?: string;
  /** Lag scale out threshold */
  lag_scale_out_threshold?: number;
  /** Lag scale in threshold */
  lag_scale_in_threshold?: number;
  /** Lag tolerate window in seconds */
  lag_tolerate_window_in_sec?: number;
  /** Dwell time scale out threshold */
  dwell_time_scale_out_threshold?: number;
  /** Dwell time scale in threshold */
  dwell_time_scale_in_threshold?: number;
  /** Maximum step for dwell time scale */
  dwell_time_scale_max_step?: number;
}

/** NSQ options */
export interface NSQOptions {}

/** Partition detail for offset reset */
export interface PartitionDetail {
  /** Partition name */
  name?: string;
  /** New offset value */
  new_offset?: number;
  /** Old offset value */
  old_offset?: number;
}

/** Business data for pipeline compile step */
export interface PipelineCompileStepBizData {
  /** Request ID */
  request_id?: string;
  /** Creator of the step */
  created_by?: string;
  /** Service ID */
  service_id?: string;
  /** Target revision ID */
  target_revision_id?: string;
  /** Whether to rebuild */
  rebuild?: boolean;
  /** Plugin SCM ID */
  plugin_scm_id?: string;
  /** Plugin SCM version */
  plugin_scm_version?: string;
}

/** Error response for pipeline */
export interface PipelineErrorResponse {
  /** Error number */
  errno?: number;
  /** Error code string */
  error_code?: string;
  /** Error message */
  error_message?: string;
}

export interface PipelineParams {
  /** ID of the pipeline template */
  pipeline_template_id?: string;
  /** Type of the pipeline template, enums: NormalRelease, IdcRelease, MultiClusterGrayNormalRelease, MultiClusterGrayIdcRelease, MultiClusterNormalRelease, MultiClusterIdcRelease, ClusterUpdate, ClusterCreate, ClusterDelete, LegoRelease, LegoIdcRelease, MultiClusterLegoGrayNormalRelease, MultiClusterLegoGrayIdcRelease, MultiClusterLegoNormalRelease, MultiClusterLegoIdcRelease, MultiClusterLegoAMLRelease, MqCanaryRelease */
  pipeline_template_type?: string;
  /** Approver ID */
  approved_by?: string;
  /** Approver user type. Enums: "person_account","service_account" */
  approved_by_usertype?: string;
}

/** Business data for pipeline release MQ event step */
export interface PipelineReleaseMQEventStepBizData {
  /** Request ID */
  request_id?: string;
  /** Creator of the step */
  created_by?: string;
  /** Service ID */
  service_id?: string;
  /** MQ event ID */
  mqevent_id?: string;
  /** Rolling step size */
  rolling_step?: number;
  /** Target traffic config */
  target_traffic_config?: number;
  /** Source revision ID */
  source_revision_id?: string;
  /** Target revision ID */
  target_revision_id?: string;
  /** Release ID */
  release_id?: string;
  /** Region name */
  region?: string;
  /** Cluster name */
  cluster?: string;
  /** Default release type */
  default_release_type?: string;
  /** Function ID */
  function_id?: string;
  /** List of release IDs */
  release_ids?: Array<string>;
  /** Rolling strategy (0: kill old then start new, 1: start new then kill old) */
  rolling_strategy?: number;
  /** Rolling interval (seconds) */
  rolling_interval?: number;
  /** Minimum created percentage (1-100) */
  min_created_percentage?: number;
  /** Minimum ready percentage (1-100) */
  min_ready_percentage?: number;
}

/** Pipeline response data */
export interface PipelineResponseData {
  /** Pipeline ID */
  id?: string;
  /** Pipeline status */
  status?: string;
  /** Pipeline template */
  template?: PipelineTemplateLegacy;
  /** Pipeline steps */
  steps?: Array<PipelineStepResponseData>;
  /** DAG edges between steps */
  step_dag_edges?: Array<Edge>;
  /** Creation time */
  created_at?: string;
  /** Update time */
  updated_at?: string;
}

/** Pipeline step business data */
export interface PipelineStepBizData {
  /** Compile step business data */
  compile_biz_data?: PipelineCompileStepBizData;
  /** Upload step business data */
  upload_biz_data?: PipelineUploadStepBizData;
  /** MQ event release step business data */
  release_mqevent_biz_data?: PipelineReleaseMQEventStepBizData;
  /** Release region step business data */
  release_region_biz_data?: ReleaseRegionStepBizData;
  /** Release single IDC step business data */
  release_idc_biz_data?: ReleaseSingleIdcStepBizData;
  /** Release all IDC step business data */
  release_all_idc_biz_data?: ReleaseAllIdcStepBizData;
  /** Confirm step business data */
  confirm_biz_data?: ConfirmBizData;
  /** Build step business data */
  build_biz_data?: BuildBizData;
  /** Create trigger business data (used by copy trigger pipeline) */
  create_trigger_biz_data?: CreateTriggerBizData;
  /** Trigger business data (used by single trigger CRUD pipeline) */
  trigger_biz_data?: TriggerBizData;
  /** Reset MQ trigger business data */
  reset_mq_trigger_biz_data?: ResetMQTriggerBizData;
  /** Canary release step business data */
  release_canary_biz_data?: ReleaseCanaryStepBizData;
  /** Quality check step business data */
  quality_check_biz_data?: QualityCheckStepBizData;
}

/** Pipeline step response data */
export interface PipelineStepResponseData {
  /** Step ID */
  id?: string;
  /** Step type */
  type?: string;
  /** Whether rollback is supported */
  support_rollback?: boolean;
  /** Whether manual trigger is needed */
  manual_trigger?: boolean;
  /** Status code */
  status?: string;
  /** Status message */
  status_msg?: string;
  /** Error information */
  error_info?: PipelineErrorResponse;
  /** Target status */
  target_status?: string;
  /** List of async statuses */
  async_status_list?: Array<string>;
  /** Allowed actions */
  allowed_actions?: Array<StepAction>;
  /** Associated stage metadata */
  stage_meta?: StageMeta;
  /** Creation time */
  created_at?: string;
  /** Update time */
  updated_at?: string;
  /** Step start execution time */
  start_exec_time?: string;
  /** Step business data */
  biz_data?: PipelineStepBizData;
}

/** Legacy pipeline template */
export interface PipelineTemplateLegacy {
  /** Template name */
  name?: string;
  /** Template description */
  description?: string;
  /** Template type */
  type?: string;
  /** Stages in this pipeline template */
  stages?: Array<Stage>;
}

/** Business data for pipeline upload step */
export interface PipelineUploadStepBizData {
  /** Request ID */
  request_id?: string;
  /** Creator of the step */
  created_by?: string;
  /** Service ID */
  service_id?: string;
  /** Target revision ID */
  target_revision_id?: string;
  /** List of release IDs */
  release_ids?: Array<string>;
  /** Whether to rebuild */
  rebuild?: boolean;
}

/** Plugin function compile version information */
export interface PluginFunctionCompileVersion {
  /** SCM compilation version number
scm编译版本号 */
  version?: string;
  /** Go version number
go版本号 */
  go_version?: string;
}

/** Plugin function details */
export interface PluginFunctionDetail {
  /** Plugin name */
  plugin_name?: string;
  /** Commit hash */
  commit_hash?: string;
  /** Lego compilation version number
lego编译版本号 */
  version?: string;
  /** List of compile versions */
  compile_versions?: Array<PluginFunctionCompileVersion>;
}

/** Plugin function parameters */
export interface PluginFunctionParam {
  /** Plugin function revision ID */
  plugin_function_revision_id?: string;
  /** Initialization timeout */
  init_timeout?: number;
  /** System environment variables */
  system_envs?: Record<string, string>;
  /** Custom environment variables */
  environments?: Record<string, string>;
  /** Plugin name */
  plugin_name?: string;
  /** Fake PSM value */
  fake_psm?: string;
  /** Plugin version */
  plugin_version?: string;
  /** Plugin scope */
  plugin_scope?: string;
  /** Package key */
  package_key?: string;
  /** Revision map for plugin function */
  revision_map?: Record<string, PluginFunctionRevisionParam>;
  /** Plugin function revision number */
  plugin_function_revision_number?: number;
}

/** Plugin function parameters (version 2) */
export interface PluginFunctionParam2 {
  /** Plugin function version */
  plugin_function_version?: string;
  /** Plugin function scope */
  plugin_function_scope?: string;
  /** Initialization timeout */
  init_timeout?: number;
  /** Custom environment variables */
  environments?: Record<string, string>;
  /** Revision map for plugin function */
  revision_map?: Record<string, PluginFunctionRevisionInfo>;
  /** Plugin function revision ID */
  plugin_function_revision_id?: string;
  /** Plugin function revision number */
  plugin_function_revision_number?: number;
}

/** Plugin function revision information */
export interface PluginFunctionRevisionInfo {
  /** Plugin function version */
  plugin_function_version?: string;
  /** Plugin function scope */
  plugin_function_scope?: string;
}

/** Plugin function revision parameters */
export interface PluginFunctionRevisionParam {
  /** Plugin version */
  plugin_version?: string;
  /** Plugin scope */
  plugin_scope?: string;
  /** Package key */
  package_key?: string;
}

/** Pod replica limit */
export interface PodReplicaLimit {
  /** Maximum replicas */
  max?: number;
  /** Minimum replicas */
  min?: number;
}

/** QPS limit time ranges */
export interface QPSLimitTimeRanges {
  /** Start time of the QPS limit */
  start_time?: TimeOfDay;
  /** End time of the QPS limit */
  end_time?: TimeOfDay;
  /** QPS limit value */
  qps_limit?: number;
}

/** Quality check cluster information */
export interface QualityCheckFaasCluster {
  /** Function ID */
  func_id: string;
  /** Target traffic */
  target_traffic: number;
  /** Target revision ID */
  target_revision_id: string;
  /** Previous revision ID */
  pre_revision_id: string;
  /** Function region */
  func_region: string;
  /** Cluster name */
  cluster_name: string;
  /** Virtual region */
  v_region: string;
}

/** Quality check step business data */
export interface QualityCheckStepBizData {
  /** Guard ID */
  guard_id: number;
  /** Guard result */
  guard_result: string;
  /** Description */
  desc: string;
  /** Number of checks */
  check_count: number;
  /** Guard URL */
  guard_url: string;
  /** List of FaaS clusters */
  faas_clusters: Array<QualityCheckFaasCluster>;
}

/** Rate limit step settings */
export interface RateLimitStepSettings {
  /** Step value for rate limit */
  rate_limit_step?: number;
  /** Step interval for rate limit */
  rate_limit_step_interval?: number;
  /** Increase weight for rate limit step */
  rate_limit_step_increase_weight?: number;
  /** Decrease weight for rate limit step */
  rate_limit_step_decrease_weight?: number;
}

/** Business data for release all IDC step */
export interface ReleaseAllIdcStepBizData {
  /** Region name */
  region?: string;
  /** Function ID */
  function_id?: string;
  /** Zone traffic configuration */
  zone_traffic_config?: Record<string, MapMessage>;
  /** Release IDs (comma separated) */
  release_ids?: string;
  /** Service ID */
  service_id?: string;
  /** Current zone traffic configuration */
  current_zone_traffic_config?: Record<string, MapMessage>;
  /** Cluster name */
  cluster?: string;
  /** System log link */
  system_log_link?: string;
  /** Runtime log link */
  runtime_log_link?: string;
  /** Runtime stream log link */
  runtime_stream_log_link?: string;
  /** Error help information */
  error_help?: ErrorHelp;
  /** Step progress information */
  step_progress?: ReleaseStepProgress;
}

/** Canary release step business data */
export interface ReleaseCanaryStepBizData {
  /** Region name */
  region?: string;
  /** Service ID */
  service_id?: string;
  /** Function ID */
  function_id?: string;
  /** PSM name */
  psm?: string;
  /** Cluster name */
  cluster?: string;
  /** Alias name */
  alias_name?: string;
  /** Target canary traffic configuration */
  target_canary_traffic_config?: Record<string, number>;
  /** Current canary traffic configuration */
  current_canary_traffic_config?: Record<string, number>;
  /** Rolling step size */
  rolling_step?: number;
  /** Canary rolling step size */
  canary_rolling_step?: number;
  /** Release ID list */
  release_ids?: Array<string>;
  /** System log link */
  system_log_link?: string;
  /** Runtime log link */
  runtime_log_link?: string;
  /** Runtime stream log link */
  runtime_stream_log_link?: string;
  /** Error help information */
  error_help?: ErrorHelp;
  /** Step progress information */
  step_progress?: ReleaseStepProgress;
  /** 滚动策略
Rolling strategy (0: kill old then start new, 1: start new then kill old) */
  rolling_strategy?: number;
  /** 滚动间隔，单位（s）
Rolling interval in seconds */
  rolling_interval?: number;
  /** 滚动完成判断条件 1：最少百分之 N 的容器创建；数值范围（1-100）
Minimum created percentage (1-100) */
  min_created_percentage?: number;
  /** 滚动完成判断条件 2：最少百分之 N 的容器启动完成；数值范围（1-100）
Minimum ready percentage (1-100) */
  min_ready_percentage?: number;
}

/** Business data for release region step */
export interface ReleaseRegionStepBizData {
  /** Region name */
  region?: string;
  /** Service ID */
  service_id?: string;
  /** Function ID */
  function_id?: string;
  /** PSM name */
  psm?: string;
  /** Alias name */
  alias_name?: string;
  /** Target traffic configuration */
  target_traffic_config?: Record<string, number>;
  /** Rolling step size */
  rolling_step?: number;
  /** Revision ID */
  revision_id?: string;
  /** List of release IDs */
  release_ids?: Array<string>;
  /** Current traffic configuration */
  current_traffic_config?: Record<string, number>;
  /** Cluster name */
  cluster?: string;
  /** System log link */
  system_log_link?: string;
  /** Runtime log link */
  runtime_log_link?: string;
  /** Runtime stream log link */
  runtime_stream_log_link?: string;
  /** Error help information */
  error_help?: ErrorHelp;
  /** Step progress information */
  step_progress?: ReleaseStepProgress;
  /** Rolling strategy (0: kill old then start new, 1: start new then kill old) */
  rolling_strategy?: number;
  /** Rolling interval in seconds */
  rolling_interval?: number;
  /** Minimum created percentage (1-100) */
  min_created_percentage?: number;
  /** Minimum ready percentage (1-100) */
  min_ready_percentage?: number;
}

/** Business data for release single IDC step */
export interface ReleaseSingleIdcStepBizData {
  /** Region name */
  region?: string;
  /** Service ID */
  service_id?: string;
  /** Function ID */
  function_id?: string;
  /** PSM name */
  psm?: string;
  /** Alias name */
  alias_name?: string;
  /** Zone traffic configuration */
  zone_traffic_config?: Record<string, MapMessage>;
  /** Rolling step size */
  rolling_step?: number;
  /** Revision ID */
  revision_id?: string;
  /** List of release IDs */
  release_ids?: Array<string>;
  /** Current zone traffic configuration */
  current_zone_traffic_config?: Record<string, MapMessage>;
  /** Cluster name */
  cluster?: string;
  /** System log link */
  system_log_link?: string;
  /** Runtime log link */
  runtime_log_link?: string;
  /** Runtime stream log link */
  runtime_stream_log_link?: string;
  /** Error help information */
  error_help?: ErrorHelp;
  /** Step progress information */
  step_progress?: ReleaseStepProgress;
  /** Rolling strategy (0: kill old then start new, 1: start new then kill old) */
  rolling_strategy?: number;
  /** Rolling interval in seconds */
  rolling_interval?: number;
  /** Minimum created percentage (1-100) */
  min_created_percentage?: number;
  /** Minimum ready percentage (1-100) */
  min_ready_percentage?: number;
}

/** Release step progress information */
export interface ReleaseStepProgress {
  /** Status messages for each step */
  status_message?: Record<string, string>;
  /** Pod status counts */
  pod_status?: Record<string, number>;
  /** Failure reasons for steps */
  failed_reason?: Record<string, string>;
  /** Solutions for failed steps */
  solution?: Record<string, string>;
  /** Failed pod names */
  failed_pods?: Record<string, string>;
  /** Status categories for each zone */
  zone_status_categories?: Array<ZoneStatusCategories>;
}

/** Strategy for replica controller */
export interface ReplicaControllerStrategy {
  /** could be daily_interval, weekly_interval, monthly_interval */
  cron_interval?: string;
  /** which days should be effective if it is monthly_interval, 1 - 31 */
  day_of_monthly?: Array<number>;
  /** which days should be effective if it is weekly_interval, 0 - 6 */
  day_of_weekly?: Array<number>;
  /** how long the strategy will be effective */
  duration_minutes?: number;
  /** how many replicas should be keep in each zone */
  min_zone_replicas?: Record<string, number>;
  /** how many replicas should be keep in each zone */
  max_zone_replicas?: Record<string, number>;
  /** how many replicas should be keep in each zone */
  cron_recover_min_zone_replicas?: Record<string, number>;
  /** how many replicas should be keep in each zone */
  cron_recover_max_zone_replicas?: Record<string, number>;
  /** when the strategy will be effective */
  start_time?: CronStrategyStartTimeMessage2;
}

/** Business data for resetting MQ trigger in pipeline */
export interface ResetMQTriggerBizData {
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Service ID */
  service_id: string;
  /** Function ID */
  function_id: string;
  /** Creator of the trigger */
  created_by: string;
  /** Trigger ID */
  trigger_id: string;
  /** MQ type, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Trigger name */
  trigger_name: string;
  /** Reset offset result */
  result?: ResetOffsetResult;
}

/** Request for offset reset */
export interface ResetOffsetReq {
  /** Initial MQ status */
  initial_mq_status?: boolean;
  /** Cluster name */
  clusterName?: string;
  /** Topic name */
  topicName?: string;
  /** Consumer group name */
  consumerGroup?: string;
  /** Whence value for offset */
  whence?: string;
  /** Relative offset value */
  relativeOffset?: number;
  /** Time string for offset */
  timestring?: string;
  /** Event center event ID */
  event_center_event_id?: string;
  /** Event center stage ID */
  event_center_stage_id?: string;
  /** PSM value */
  psm?: string;
  /** Partition number */
  partition?: number;
}

/** Result of offset reset */
export interface ResetOffsetResult {
  /** Status of the reset operation */
  reset_status?: string;
  /** Error message for reset */
  reset_error?: string;
  /** Timestamp when reset occurred */
  reset_at?: string;
  /** User who performed the reset */
  reset_by?: string;
  /** List of partition details */
  partitions?: Array<PartitionDetail>;
  /** API status code */
  api_status?: number;
  /** Timestamp when API was called */
  api_called_at?: string;
}

/** Resource configuration for functions */
export interface Resource {
  /** Memory limit in MB */
  mem_mb?: number;
  /** CPU limit in millicores */
  cpu_milli?: number;
  /** Disk limit in MB */
  disk_mb?: number;
  /** GPU configuration */
  gpu_config?: GPU;
  /** Number of sockets */
  socket?: number;
  /** Resource alias configuration */
  resource_alias?: ResourceAlias;
}

/** Resource alias configuration */
export interface ResourceAlias {
  /** CPU limit in millicores */
  cpu_milli?: number;
  /** Memory limit in MB */
  mem_mb?: number;
  /** GPU configuration */
  gpu_config?: GPU;
  /** Disk limit in MB */
  disk_mb?: number;
  /** Number of sockets */
  socket?: number;
}

/** Resource limits for a function or revision */
export interface ResourceLimit {
  /** CPU limit (millicores) */
  cpu_milli?: number;
  /** Disk limit (MB) */
  disk_mb?: number;
  /** GPU configuration */
  gpu_config?: GPU;
  /** Memory limit (MB) */
  mem_mb?: number;
  /** SGX enclave size */
  sgx_enclave?: number;
  /** Number of sockets */
  socket?: number;
}

/** Resource limit with alias */
export interface ResourceLimitWithAlias {
  /** CPU in milli units */
  cpu_milli?: number;
  /** Disk in MB */
  disk_mb?: number;
  /** GPU configuration */
  gpu_config?: GPU;
  /** Memory in MB */
  mem_mb?: number;
  /** SGX enclave */
  sgx_enclave?: number;
  /** Socket count */
  socket?: number;
  /** Resource alias */
  resource_alias?: ResourceAlias;
}

/** Revision metadata */
export interface Revision {
  /** Revision identifier */
  id?: string;
  /** Revision name */
  name?: string;
  /** Revision number */
  number?: number;
  /** Associated function ID */
  function_id?: string;
  /** Creation timestamp */
  created_at?: string;
  /** Revision description */
  description?: string;
  /** Deployment cluster */
  cluster?: string;
  /** Deployment region */
  region?: string;
  /** Environment variables by region/router */
  envs?: Record<string, Record<string, string>>;
  /** Formatted environment variables by region/router */
  format_envs?: Record<string, Array<FormatEnvs>>;
  /** Deployment method. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** Handler entry point */
  handler?: string;
  /** Initializer function */
  initializer?: string;
  /** Built package keys by region */
  built_region_package_keys?: Record<string, string>;
  /** Build descriptions by region */
  build_desc_map?: Record<string, BuildDescription>;
  /** Built package keys by worker and region */
  worker_built_region_package_keys?: Record<string, Record<string, string>>;
  /** Custom images by region */
  built_region_custom_images?: Record<string, string>;
  /** Runtime environment */
  runtime?: string;
  /** Base image for this revision */
  base_image?: string;
  /** Base image for rollback */
  base_image_for_rollback?: string;
  /** Base image description */
  base_image_desc?: FaasBaseImageDesc;
  /** Source code URI */
  source?: string;
  /** Source type. Enums: url, tos, scm, ceph, image, image-scm, private-tos, lego */
  source_type?: string;
  /** List of dependencies */
  dependency?: Array<Dependency>;
  /** Serialized dependency string */
  dependency_str?: string;
  /** Run command */
  run_cmd?: string;
  /** Code revision number */
  code_revision_number?: string;
  /** Code revision ID */
  code_revision_id?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** PSM name */
  psm?: string;
  /** Initializer timeout (seconds) */
  initializer_sec?: number;
  /** Latency timeout (seconds) */
  latency_sec?: number;
  /** Cold start timeout (seconds) */
  cold_start_sec?: number;
  /** Cold start disabled flag, true means disable */
  cold_start_disabled?: boolean;
  /** Resource limits */
  resource_limit?: ResourceLimit;
  /** Maximum concurrency */
  max_concurrency?: number;
  /** Adaptive concurrency mode */
  adaptive_concurrency_mode?: string;
  /** Exclusive mode flag */
  exclusive_mode?: boolean;
  /** Lazy loading enabled */
  lazyload?: boolean;
  /** Image lazy loading enabled */
  open_image_lazyload?: boolean;
  /** Authentication enabled */
  auth_enable?: boolean;
  /** Build install disabled, true means disable */
  disable_build_install?: boolean;
  /** CORS enabled */
  cors_enable?: boolean;
  /** Disabled zones by region */
  is_this_zone_disabled?: Record<string, boolean>;
  /** GDPR compliance enabled */
  gdpr_enable?: boolean;
  /** Log bytes per second throttle */
  throttle_log_bytes_per_sec?: number;
  /** Stdout log bytes per second throttle */
  throttle_stdout_log_bytes_per_sec?: number;
  /** Stderr log bytes per second throttle */
  throttle_stderr_log_bytes_per_sec?: number;
  /** Last ticket status */
  last_ticket_status?: string;
  /** Creator of the revision */
  created_by?: string;
  /** Online mode enabled */
  online_mode?: boolean;
  /** Network mode */
  network_mode?: string;
  /** Runtime container port */
  runtime_container_port?: number;
  /** Runtime debug container port */
  runtime_debug_container_port?: number;
  /** Additional runtime container ports */
  runtime_other_container_ports?: Array<number>;
  /** Overload protection enabled */
  overload_protect_enabled?: boolean;
  /** Host uniqueness configuration */
  host_uniq?: HostUniq;
  /** Cell migration flag */
  in_cell_migration?: boolean;
  /** enable privileged in pod */
  privileged?: boolean;
}

/** RocketMQ options */
export interface RocketMQOptions {
  /** Topic name */
  topic?: string;
  /** Whether message consumption is orderly */
  orderly?: boolean;
  /** Subscription expression */
  sub_expr?: string;
  /** Cluster name */
  cluster_name?: string;
  /** Consumer group name */
  consumer_group?: string;
  /** Enable message filtering */
  enable_filter?: boolean;
  /** Filter source type */
  filter_source_type?: string;
  /** Filter source */
  filter_source?: string;
  /** Filter plugin ID */
  filter_plugin_id?: string;
  /** Filter plugin version */
  filter_plugin_version?: string;
  /** Retry interval in seconds */
  retry_interval_seconds?: number;
  /** Enable retry queue */
  enable_retry_queue?: boolean;
  /** Close multi environment */
  close_multi_env?: boolean;
  /** Enable multi tags */
  enable_multi_tags?: boolean;
  /** Multi environment version */
  multi_env_version?: string;
  /** Whether this is an eventbus type */
  is_eventbus_type?: boolean;
  /** Epoch value */
  epoch?: Int64;
  /** Enable local consume */
  enable_local_consume?: boolean;
  /** Maximum in-flight messages */
  max_in_flight?: number;
  /** Enable MQ lease */
  enable_mq_lease?: boolean;
  /** Enable order subscription retry */
  order_sub_retry?: boolean;
  /** Region for mirror */
  region_for_mirror?: string;
  /** Enable batch consume */
  enable_batch_consume?: boolean;
  /** Maximum linger time for batch message consumption */
  consume_message_batch_max_linger_time?: number;
  /** Maximum batch size for message consumption */
  consume_message_batch_max_size?: number;
  /** Enable per orderly queue worker */
  enable_per_orderly_queue_worker?: boolean;
}

/** Runtime release parameters */
export interface RuntimeReleaseParams {
  /** Release info */
  release_info?: RuntimeReleaseParamsReleaseInfoMessage2;
  /** Release parameters */
  release_params?: RuntimeReleaseParamsReleaseParamsMessage2;
}

/** Release info message for runtime release parameters */
export interface RuntimeReleaseParamsReleaseInfoMessage2 {
  /** Release IDs for this release info */
  release_ids?: Array<string>;
}

/** Release parameters message for runtime release */
export interface RuntimeReleaseParamsReleaseParamsMessage2 {
  /** Alias name for release */
  alias_name?: string;
  /** Release region */
  region?: string;
  /** Rolling step size */
  rolling_step?: number;
  /** Target traffic config per revision */
  target_traffic_config?: Record<string, number>;
  /** Target traffic config per zone */
  zone_traffic_config?: Record<string, MapMessage>;
  /** Grey release MQ event config */
  grey_mqevent_config?: Array<GreyMQEvent>;
  /** Release cluster */
  cluster?: string;
  /** Function ID for release */
  function_id?: string;
  /** Rolling strategy (0: kill old then start new, 1: start new then kill old) */
  rolling_strategy?: number;
  /** Rolling interval (seconds) */
  rolling_interval?: number;
  /** Minimum created percentage (1-100) */
  min_created_percentage?: number;
  /** Minimum ready percentage (1-100) */
  min_ready_percentage?: number;
}

/** Scaling strategy for functions */
export interface ScaleStrategy {
  /** when the strategy will be effective */
  effective_time?: string;
  /** strategy is enabled or not */
  enabled?: boolean;
  /** when the strategy will be expired */
  expired_time?: string;
  /** function id, no need for post/patch method, it is a path param */
  function_id?: string;
  /** inner scaling strategy */
  inner_strategy?: InnerStrategy;
  /** function id or mqevent id */
  item_id?: string;
  /** function or mqevent */
  item_type?: string;
  /** region, no need for post/patch method, it is a path param */
  region?: string;
  /** strategy id, no need for post/patch method, it is a path param */
  strategy_id?: string;
  /** strategy name */
  strategy_name?: string;
  /** only cron for now */
  strategy_type?: string;
  /** updated by */
  updated_by?: string;
  /** ReservedInstance or FrozenReservedInstance default is ReservedInstance */
  instance_type?: string;
}

/** Stage in a pipeline */
export interface Stage {
  /** Metadata for the stage */
  stage_meta?: StageMeta;
}

/** Stage metadata for pipelines */
export interface StageMeta {
  /** Stage type */
  type?: string;
  /** Stage group */
  group?: string;
  /** Whether this stage is a rollback */
  is_rollback?: boolean;
  /** Stage name */
  name?: string;
}

/** Status category and replica count */
export interface StatusCategoryReplicas {
  /** Status category name */
  category: string;
  /** Number of replicas in this category */
  replicas: number;
}

/** Action that can be performed in a step */
export interface StepAction {
  /** Name of the action */
  name?: string;
}

/** Ticket information */
export interface Ticket {
  /** Approver name */
  approved_by?: string;
  /** Approver user type. Enums: "person_account","service_account" */
  approved_by_usertype?: string;
  /** Ticket category */
  category?: string;
  /** Change type. Enums: "create","update","delete" */
  change_type?: string;
  /** Number of child tickets (for batch tickets) */
  child_tickets_num?: number;
  /** Creation time */
  created_at?: string;
  /** Creator name */
  created_by?: string;
  /** Ticket detail information */
  detail_info?: TicketDetailInfo;
  /** Error response */
  error_response?: TicketErrorResponseMessage2;
  /** Finish time */
  finished_at?: string;
  /** Function ID */
  function_id?: string;
  /** Ticket ID */
  id?: string;
  /** Whether ticket is from admin/ops */
  is_admin_ticket?: boolean;
  /** Note */
  note?: string;
  /** Original resource metadata */
  origin_data?: TicketResourceMeta;
  /** Parent ticket ID (for batch tickets) */
  parent_id?: string;
  /** Request ID */
  request_id?: string;
  /** Service ID */
  service_id?: string;
  /** Ticket status */
  status?: string;
  /** Status before failure */
  status_before_failed?: string;
  /** Status message */
  status_message?: string;
  /** Ticket type */
  type?: string;
  /** Updated resource metadata */
  update_data?: TicketResourceMeta;
  /** Update time */
  updated_at?: string;
  /** Whether ticket is a pipeline ticket */
  is_pipeline_ticket?: boolean;
  /** Pipeline template type */
  pipeline_template_type?: string;
  /** Pipeline data */
  pipeline?: PipelineResponseData;
  /** Region name */
  region?: string;
  /** Skipped status */
  skipped?: string;
  /** Cluster list */
  clusters?: Array<ClusterInfo>;
  /** Description */
  description?: string;
  /** MQ event release type, enums: hot_load, deployment_rolling */
  mqevent_release_type?: string;
  /** Release platform */
  release_platform?: string;
  /** Pipeline template ID */
  pipeline_template_id?: string;
}

/** Ticket detail information */
export interface TicketDetailInfo {
  /** Runtime release parameters */
  runtime_release?: RuntimeReleaseParams;
  /** Cluster release parameters */
  cluster_release?: Array<RuntimeReleaseParams>;
}

/** Ticket error response message */
export interface TicketErrorResponseMessage2 {
  /** Error number */
  errno?: number;
  /** Error code string */
  error_code?: string;
  /** Error message */
  error_message?: string;
  /** Help control instructions for troubleshooting
排查失败问题的一些 Help 控制指令 */
  error_help?: ErrorHelp;
}

/** Complete ticket resource metadata */
export interface TicketResourceMeta {
  /** Function resource metadata */
  function_resource?: TicketResourceMetaFunctionResourceMessage2;
  /** Runtime release metadata */
  runtime_release?: TicketResourceMetaRuntimeReleaseMessage2;
  /** Runtime update metadata */
  runtime_update?: TicketResourceMetaRuntimeUpdateMessage2;
  /** Cluster release metadata */
  cluster_release_meta?: Array<TicketResourceMetaRuntimeReleaseMessage2>;
}

/** Function resource metadata */
export interface TicketResourceMetaFunctionResourceMessage2 {
  /** Code revision metadata */
  code_revision_meta?: CodeRevision;
  /** Function metadata */
  function_meta?: TicketResourceMetaFunctionResourceMessage2FunctionMetaMessage2;
  /** Regional metadata */
  regional_meta?: TicketResourceMetaFunctionResourceMessage2RegionalMetaMessage2;
  /** Trigger metadata */
  trigger_meta?: TicketResourceMetaFunctionResourceMessage2TriggerMetaMessage2;
  /** Scaling strategy metadata */
  scale_strategy_meta?: ScaleStrategy;
}

/** Function resource metadata */
export interface TicketResourceMetaFunctionResourceMessage2FunctionMetaMessage2 {
  /** Enable auth check (using jwt token) */
  auth_enable?: boolean;
  /** Authorizers of function, split by ',', 函数的授权人，用 ',' 分隔 */
  authorizers?: string;
  /** Limit of cold start, 冷启动超时参数 */
  cold_start_sec?: number;
  /** Enable cors，允许跨域 */
  cors_enable?: boolean;
  /** Description of function, 函数描述 */
  description?: string;
  /** Disable build install, true means disable. Only support python/nodejs, 是否调过构建，仅支持 python/nodejs */
  disable_build_install?: boolean;
  /** Envs of function by region or router, if key is router, it will be applied in all region */
  envs?: Record<string, Record<string, string>>;
  /** Handler of function, most scenario no need to use this param */
  handler?: string;
  /** Initializer function name, default could be none */
  initializer?: string;
  /** Limit of function latency, 函数时延超时参数 */
  latency_sec?: number;
  /** Max concurrency for one function instance */
  max_concurrency?: number;
  /** Memory of function, used for set function resource, unit is MB */
  memory_mb?: number;
  /** Name of function, 函数名称 */
  name?: string;
  /** Origin of function, from bytefaas ori light(like qingfuwu), 函数的来源，除了 faas 也有可能是来自轻服务等 */
  origin?: string;
  /** The owner of function, 函数的 Owner */
  owner?: string;
  /** Protocol of function, such as TTHeader etc. */
  protocol?: string;
  /** PSM of function */
  psm?: string;
  /** Parent id of psm, only used in create, can not be updated through faas api */
  psm_parent_id?: number;
  /** Which runtime language function used. Optional values: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1 */
  runtime?: string;
  /** Service level, could be P0 ~ P3 */
  service_level?: string;
  /** Service purpose */
  service_purpose?: string;
  /** Source code name of latest revision */
  source?: string;
  /** Source code type of latest revision */
  source_type?: string;
  /** Name of template, 选择的模板名称 */
  template_name?: string;
  /** VPC config, only for ToB logic */
  vpc_config?: BasicFunctionParamsVpcConfigMessage2;
  /** Restricted access, only open to administrators */
  async_mode?: boolean;
}

/** Regional metadata for function resources (copied from BasicRegionalMetaParams) */
export interface TicketResourceMetaFunctionResourceMessage2RegionalMetaMessage2 {
  /** Traffic aliases configuration for the region */
  aliases?: Record<string, Alias>;
  /** Whether async mode is enabled */
  async_mode?: boolean;
  /** Whether authentication is enabled */
  auth_enable?: boolean;
  /** Disable ByteFaas error response formatting */
  bytefaas_error_response_disabled?: boolean;
  /** Disable ByteFaas response headers */
  bytefaas_response_header_disabled?: boolean;
  /** Cell name for deployment */
  cell?: string;
  /** Whether cold start is disabled, true means disable */
  cold_start_disabled?: boolean;
  /** Whether CORS is enabled */
  cors_enable?: boolean;
  /** Enable dynamic load balancing data reporting */
  dynamic_load_balancing_data_report_enabled?: boolean;
  /** List of VDCs enabled for dynamic load balancing */
  dynamic_load_balancing_enabled_vdcs?: Array<string>;
  /** Enable dynamic load balancing weight adjustment */
  dynamic_load_balancing_weight_enabled?: boolean;
  /** Enable colocate scheduling */
  enable_colocate_scheduling?: boolean;
  /** Environment name */
  env_name?: string;
  /** Whether exclusive mode is enabled */
  exclusive_mode?: boolean;
  /** Formatted environment variables (overrides BasicRegionalMetaParams) */
  format_envs?: Record<string, Array<FormatEnvs>>;
  /** Function ID */
  function_id?: string;
  /** Enable gateway routing */
  gateway_route_enable?: boolean;
  /** Enable GDPR compliance */
  gdpr_enable?: boolean;
  /** Global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** Disable HTTP triggers, true means disable */
  http_trigger_disable?: boolean;
  /** Whether IPv6-only mode is enabled */
  is_ipv6_only?: boolean;
  /** Disabled zones in the region */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Latency threshold in seconds */
  latency_sec?: number;
  /** Local cache namespace IDs */
  local_cache_namespace_ids?: Array<string>;
  /** Network class ID */
  net_class_id?: number;
  /** Network configuration */
  network?: string;
  /** Owner of the resource */
  owner?: string;
  /** Protocol used (e.g., TTHeader) */
  protocol?: string;
  /** PSM (Product-Subsys-Module) identifier */
  psm?: string;
  /** Region name */
  region?: string;
  /** Whether reserved deployment is enabled */
  reserved_dp_enabled?: boolean;
  /** Revision ID */
  revision_id?: string;
  /** Revision number */
  revision_number?: number;
  /** Routing strategy, enums：prefer_reserved, prefer_elastic. */
  routing_strategy?: string;
  /** Runtime environment (e.g. golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1) */
  runtime?: string;
  /** Service ID */
  service_id?: string;
  /** Enable tracing */
  trace_enable?: boolean;
  /** Zone-specific log throttling */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** Enable ZTI */
  zti_enable?: boolean;
  /** Whether online mode is enabled */
  online_mode?: boolean;
  /** Elastic prefer clusters (formatted) */
  formatted_elastic_prefer_cluster?: Array<FormattedPreferCluster>;
  /** Reserved prefer clusters (formatted) */
  formatted_reserved_prefer_cluster?: Array<FormattedPreferCluster>;
  /** Enable reserve frozen instances */
  enable_reserve_frozen_instance?: boolean;
  /** Disable cgroup v2 */
  disable_cgroup_v2?: boolean;
  /** Enable overload protection */
  overload_protect_enabled?: boolean;
  /** Enable federated on-demand resources by zone */
  enable_fed_on_demand_resource?: Record<string, boolean>;
  /** Function scaling settings */
  scale_settings?: FunctionScaleSettings;
  /** enable privileged in pod */
  privileged?: boolean;
}

/** Trigger metadata for function resource */
export interface TicketResourceMetaFunctionResourceMessage2TriggerMetaMessage2 {
  /** Consul trigger response data */
  consul?: ConsulTriggerResponseData;
  /** Global MQ event trigger response data */
  mqevents?: GlobalMQEventTriggerResponseData;
  /** Scaling strategy metadata */
  scale_strategy_meta?: ScaleStrategy;
  /** Timer trigger configuration */
  timers?: TimerTrigger;
}

/** Ticket resource metadata for runtime release */
export interface TicketResourceMetaRuntimeReleaseMessage2 {
  /** Revision metadata */
  revision?: Revision;
}

/** Ticket resource metadata for runtime update */
export interface TicketResourceMetaRuntimeUpdateMessage2 {
  /** Function meta information for runtime update */
  function_meta?: TicketResourceMetaRuntimeUpdateMessage2FunctionMetaMessage2;
  /** Regional meta information for runtime update */
  regional_metas?: Record<string, BasicRegionalMetaParams>;
}

/** Ticket resource metadata for runtime update (version 2) */
export interface TicketResourceMetaRuntimeUpdateMessage2FunctionMetaMessage2 {
  /** Enable auth check (using jwt token) */
  auth_enable?: boolean;
  /** Authorizers of function, split by ',', 函数的授权人，用 ',' 分隔 */
  authorizers?: string;
  /** Limit of cold start, 冷启动超时参数 */
  cold_start_sec?: number;
  /** Enable cors，允许跨域 */
  cors_enable?: boolean;
  /** Description of function, 函数描述 */
  description?: string;
  /** Disable build install, true means disable. Only support python/nodejs, 是否调过构建，仅支持 python/nodejs */
  disable_build_install?: boolean;
  /** Envs of function by region or router, if key is router, it will be applied in all region */
  envs?: Record<string, Record<string, string>>;
  /** Handler of function, most scenario no need to use this param */
  handler?: string;
  /** Initializer function name, default could be none */
  initializer?: string;
  /** Limit of function latency, 函数时延超时参数 */
  latency_sec?: number;
  /** Max concurrency for one function instance */
  max_concurrency?: number;
  /** Memory of function, used for set function resource, unit is MB */
  memory_mb?: number;
  /** Name of function, 函数名称 */
  name?: string;
  /** Origin of function, from bytefaas ori light(like qingfuwu), 函数的来源，除了 faas 也有可能是来自轻服务等 */
  origin?: string;
  /** The owner of function, 函数的 Owner */
  owner?: string;
  /** Protocol of function, such as TTHeader etc. */
  protocol?: string;
  /** PSM of function */
  psm?: string;
  /** Parent id of psm, only used in create, can not be updated through faas api */
  psm_parent_id?: number;
  /** Which runtime language function used. Optional values: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1 */
  runtime?: string;
  /** Service level, could be P0 ~ P3 */
  service_level?: string;
  /** Service purpose */
  service_purpose?: string;
  /** Source code name of latest revision */
  source?: string;
  /** Source code type of latest revision. Enums: url, tos, scm, ceph, image, image-scm, private-tos, lego */
  source_type?: string;
  /** Name of template, 选择的模板名称 */
  template_name?: string;
  /** VPC config, only for ToB logic */
  vpc_config?: BasicFunctionParamsVpcConfigMessage2;
  /** Restricted access, only open to administrators */
  async_mode?: boolean;
}

/** Time of day for scheduling */
export interface TimeOfDay {
  /** Hour of the day */
  hours?: number;
  /** Minute of the hour */
  minutes?: number;
}

/** Timer trigger configuration */
export interface TimerTrigger {
  /** Cell where the timer trigger is deployed */
  cell?: string;
  /** Cluster where the service is deployed */
  cluster?: string;
  /** Concurrency limit for the trigger */
  concurrency_limit?: number;
  /** Creation timestamp */
  created_at?: string;
  /** Cron expression for scheduling */
  cron?: string;
  /** Description of the trigger */
  description?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Associated function ID */
  function_id?: string;
  /** Trigger ID */
  id?: string;
  /** Whether the trigger is deleted */
  is_deleted?: boolean;
  /** Whether meta has been synced */
  meta_synced?: boolean;
  /** Timestamp when meta was last synced */
  meta_synced_at?: string;
  /** Name of the trigger */
  name?: string;
  /** Payload for the trigger */
  payload?: string;
  /** Region where the trigger is deployed */
  region?: string;
  /** Number of retries */
  retries?: number;
  /** Service ID */
  service_id?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** Log link */
  log_link?: string;
  /** Argos link */
  argos_link?: string;
}

/** TOS options */
export interface TOSOptions {
  /** Bucket name */
  bucket_name?: string;
  /** Bucket ID */
  bucket_id?: number;
  /** Rule ID */
  rule_id?: number;
  /** List of event types */
  event_types?: Array<string>;
  /** Topic name */
  topic?: string;
  /** Cluster name */
  cluster_name?: string;
  /** Consumer group name */
  consumer_group?: string;
  /** Enable message filtering */
  enable_filter?: boolean;
  /** Whether message consumption is unordered */
  un_orderly?: boolean;
  /** Filter source type */
  filter_source_type?: string;
  /** Filter source */
  filter_source?: string;
  /** Filter plugin ID */
  filter_plugin_id?: string;
  /** Filter plugin version */
  filter_plugin_version?: string;
  /** Retry interval in seconds */
  retry_interval_seconds?: number;
}

/** Business data for trigger in pipeline */
export interface TriggerBizData {
  /** Region name */
  region: string;
  /** Cluster name */
  cluster: string;
  /** Service ID */
  service_id: string;
  /** Function ID */
  function_id: string;
  /** Creator of the trigger */
  created_by: string;
  /** Trigger ID */
  trigger_id: string;
  /** Other request parameters */
  other_request_params?: Record<string, string>;
  /** MQ trigger parameters */
  trigger_params?: CreateMQTriggerRequest;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Trigger name */
  trigger_name: string;
  /** BPM order list */
  bpm_orders: Array<TriggerBizDataBPMOrderData>;
}

/** BPM order data for trigger business logic */
export interface TriggerBizDataBPMOrderData {
  /** Order name */
  name: string;
  /** Record ID */
  record_id: string;
  /** Link to order */
  link: string;
  /** Whether order is finished */
  finished: number;
}

/** Trigger options for various event sources */
export interface TriggerOptions {
  /** Abase Binlog options */
  abase_binlog_option?: AbaseBinlogOptions;
  /** EventBus options */
  eventbus_option?: EventBusOptions;
  /** Kafka MQ options */
  kafka_option?: KafkaMQOptions;
  /** NSQ options */
  nsq_option?: NSQOptions;
  /** RocketMQ options */
  rocketmq_option?: RocketMQOptions;
  /** TOS options */
  tos_option?: TOSOptions;
}

/** Update config for pending strategies */
export interface UpdateConfig {
  /** how long the strategy will be effective */
  duration_minutes?: number;
  /** how many replicas should be keep in each zone */
  min_zone_replicas?: Record<string, number>;
  /** time when the strategy will be effective */
  start_time?: UpdateConfigStartTimeMessage2;
}

/** Start time for update config */
export interface UpdateConfigStartTimeMessage2 {
  /** the hours to start */
  hours?: number;
  /** the minutes to start */
  minutes?: number;
}

/** Zone status categories */
export interface ZoneStatusCategories {
  /** Zone name */
  zone: string;
  /** List of status categories and replica counts in this zone */
  status_categories: Array<StatusCategoryReplicas>;
}
/* eslint-enable */
