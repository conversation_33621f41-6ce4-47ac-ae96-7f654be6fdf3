/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as base from './base';
import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_evaluator_callback from './flow_devops_evaluation_evaluator_callback';

export type Int64 = string | number;

export enum ContentType {
  Txt = 1,
  Image = 2,
  Audio = 3,
  Video = 4,
  Link = 6,
  Music = 7,
  Tako = 8,
  File = 9,
  Card = 50,
  BotCard = 51,
  APP = 100,
  OutputSearchResult = 200,
}

/** Evaluator 执行时的处理流程的类型，表达 Evaluator 实现方式的属性信息
 EvaluatorID 是不可枚举的，但 EvaluatorProcessType 是可枚举的
 EvaluatorProcessType 是创建 Evaluator 时必须确定下来的信息。 */
export enum EvaluatorProcessType {
  Prompt = 1,
  PythonCode = 2,
  JSCode = 3,
  BuildinFunc = 4,
  BuildinPrompt = 5,
  /** 目前包含了RPCCallback 和 HTTPCallback */
  CustomCallback = 6,
  /** 人工评测 */
  Manual = 7,
  /** 自定义指标上报 */
  CustomMetric = 8,
  /** 专项测试规则 */
  BuiltinSpecTest = 9,
  /** fornax平台Prompt开发中的prompt */
  FornaxPrompt = 10,
  /** coze2.0 裁判模型评估器 */
  CozePrompt = 11,
}

export enum LLMResponseFormat {
  TEXT = 0,
  MARKDOWN = 1,
  JSON = 2,
}

export enum ReplyType {
  Answer = 1,
  Suggest = 2,
  LLMOutput = 3,
  ToolOutput = 4,
  DataSet = 5,
  QueryKeyword = 6,
  IntermediateOutput = 7,
}

export enum RuleRunState {
  /** 运行状态, 异步下状态流转, 同步下只有 Success / Fail */
  NotInit = 0,
  Init = 1,
  Processing = 2,
  Success = 3,
  Fail = 4,
}

export enum SupportUpdateRuleGroupField {
  Name = 4,
  Desc = 5,
}

export interface AddToHubEvaluatorTypeMeta {
  evaluator_hub_id: Int64;
  /** 展示用名称 */
  name?: string;
  /** 权重 */
  weight?: Int64;
  /** 评测粒度 */
  granularity?: flow_devops_evaluation_entity.EvaluatorGranularity;
  prompt_rule?: PromptRule;
  python_rule?: PythonRule;
  js_rule?: JSRule;
  manual_rule?: ManualRule;
  fornax_prompt_rule?: FornaxPromptRule;
}

export interface BatchGetRuleGroupMetaRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule_group_ids: Array<Int64>;
  /** true 时 response 返回 rules 数量, 目前对接口性能有影响 */
  need_rules_count?: boolean;
  Base?: base.Base;
}

export interface BatchGetRuleGroupMetaResponse {
  metas?: Array<RuleGroupMeta>;
  BaseResp?: base.BaseResp;
}

export interface BuildinFuncRule {
  contents?: Array<string>;
}

export interface BuildinPromptRule {
  contents?: Array<string>;
  model_name?: string;
  /** 20240815 服务端内部使用 */
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

export interface CopyRuleGroupRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  source_rule_group_id: Int64;
  /** 为空时创建一个新的 rule_group_id，不为空时向目标 rule_group 中添加 rules */
  target_rule_group_id?: Int64;
  /** 复制到目标空间，为空时，默认复制到 SpaceID 空间 */
  to_space_id?: Int64;
  cid?: string;
  Base?: base.Base;
}

export interface CopyRuleGroupResponse {
  /** 如果 req.TargetRuleGroupID 为空则是生成的新 id, 不为空则是 TargetRuleGroupID */
  rule_group_id: Int64;
  /** copy 出来的 rules, id 都是新的 */
  copied_rules: Array<Rule>;
  /** 复制到的目标空间ID */
  to_space_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CozePromptRule {
  model_info: ModelInfo;
  prompt: string;
}

export interface CreateRuleAndSaveToHubRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule: Rule;
  rule_group_id: Int64;
  /** 保存到Hub时，支持对evaluatorType打自定义标签以及模态标签 */
  tags?: Array<string>;
  modal_tags?: Array<string>;
  cid?: string;
  Base?: base.Base;
}

export interface CreateRuleAndSaveToHubResponse {
  rule?: Rule;
  evaluator_hub_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CreateRuleGroupRequest {
  'FlowDevops-Agw-UserId'?: string;
  name?: string;
  desc?: string;
  'FlowDevops-Agw-AppId'?: number;
  /** 规则界面可见 */
  publish_to_rule_group?: boolean;
  space_id: Int64;
  /** 是否打上tag, 默认不打上 */
  tag_list?: Array<flow_devops_evaluation_entity.TagInfo>;
  cid?: string;
  Base?: base.Base;
}

export interface CreateRuleGroupResponse {
  rule_group?: RuleGroup;
  BaseResp?: base.BaseResp;
}

export interface CreateRuleRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 向目标 rule_group 添加 rule */
  rule_group_id: Int64;
  rule: Rule;
  cid?: string;
  Base?: base.Base;
}

export interface CreateRuleResponse {
  rule?: Rule;
  BaseResp?: base.BaseResp;
}

export interface CreateRulesRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 向目标 rule_group 添加 rule */
  rule_group_id: Int64;
  rules: Array<Rule>;
  cid?: string;
  Base?: base.Base;
}

export interface CreateRulesResponse {
  rules?: Array<Rule>;
  BaseResp?: base.BaseResp;
}

export interface CustomCallback {
  content?: string;
  /** 回调业务方的env */
  env?: string;
}

/** 自定义指标规则 */
export interface CustomMetricsRule {
  data_type: flow_devops_evaluation_entity.EvaluateResultDataType;
  value_type?: flow_devops_evaluation_entity.EvaluateResultValueType;
}

export interface DebugRuleRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule: Rule;
  data: RunRuleInputData;
  Base?: base.Base;
}

export interface DebugRuleResponse {
  result: RunRuleResult;
  BaseResp?: base.BaseResp;
}

export interface DeleteEvaluatorHubRequest {
  evaluator_hub_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
}

export interface DeleteEvaluatorHubResponse {
  BaseResp?: base.BaseResp;
}

export interface DeleteEvaluatorTypeRequest {
  evaluator_type: string;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  cid?: string;
}

export interface DeleteEvaluatorTypeResponse {
  BaseResp?: base.BaseResp;
}

export interface DeleteRuleGroupRequest {
  'FlowDevops-Agw-UserId'?: string;
  rule_group_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  cid?: string;
  Base?: base.Base;
}

export interface DeleteRuleGroupResponse {
  BaseResp?: base.BaseResp;
}

export interface EvalautorTypeMeta {
  name: string;
  evaluator_type: Int64;
  process_type: EvaluatorProcessType;
  tool_tip_content?: string;
  is_positive?: boolean;
  /** 与评估器绑定的评估器规则的 Json Schema 描述 */
  rule_config_json_schema?: string;
  /** 仅 process_type = 4 生效, true 时代表 Is, false 时代表 NotIs，其余规则为空 */
  builtin_func_use_is_syntax?: boolean;
  receive_chat_history?: boolean;
  /** 规则可选的模型列表 */
  available_model_name?: Array<string>;
  /** 模态标签 */
  modal_tag?: Array<string>;
  /** 自定义标签显示 */
  tags?: Array<string>;
  /** 为true时,代表是保存到hub的 */
  is_add_to_hub?: boolean;
  /** 用户保存到hub中EvalautorTypeMeta信息 */
  hub_evaluator_type_meta?: AddToHubEvaluatorTypeMeta;
  /** 样例 */
  sample?: string;
}

export interface EvaluatorTypeDetailInfo {
  evaluator_type?: Int64;
  evaluator_type_name: string;
  space_id: string;
  callback_type: flow_devops_evaluation_entity.CallbackType;
  description?: string;
  /** 与评估器绑定的评估器规则的 Json Schema 描述 */
  rule_config_json_schema?: string;
  creator_id?: Int64;
  rpc_callback_evaluator_params?: RPCCallbackEvaluatorParams;
  http_callback_evaluator_params?: HttpCallbackEvaluatorParams;
}

export interface FornaxPromptRule {
  /** prompt开发中prompt的唯一标识 */
  prompt_id: Int64;
  /** prompt版本 */
  version?: string;
  /** 服务端内部使用 */
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

export interface GetEvaluatorTypeRequest {
  evaluator_type: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
}

export interface GetEvaluatorTypeResponse {
  evaluator_type_detail_info: EvaluatorTypeDetailInfo;
  BaseResp?: base.BaseResp;
}

export interface GetRuleGroupMetaRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule_group_id: Int64;
  Base?: base.Base;
}

export interface GetRuleGroupMetaResponse {
  meta?: RuleGroupMeta;
  BaseResp?: base.BaseResp;
}

export interface GetRuleGroupRulesRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule_group_id: Int64;
  Base?: base.Base;
}

export interface GetRuleGroupRulesResponse {
  rules?: Array<Rule>;
  default_custom_metrics_rules?: Array<Rule>;
  BaseResp?: base.BaseResp;
}

export interface GetRulesRequest {
  'FlowDevops-Agw-UserId'?: string;
  rule_ids?: Array<Int64>;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetRulesResponse {
  /** len 和下标与 MGetRulesRequest.RuleIDs 一致 */
  rules?: Array<Rule>;
  BaseResp?: base.BaseResp;
}

export interface HttpCallbackEvaluatorParams {
  psm?: string;
  cluster?: string;
  /** 单位ms */
  timeout?: Int64;
  agent_execute_path?: string;
  http_auth_type?: flow_devops_evaluation_entity.HTTPAuthType;
  method?: flow_devops_evaluation_entity.HTTPMethod;
}

export interface JSRule {
  code: string;
}

/** ListCustomSupportRuleTypeFilter 请求过滤规则 */
export interface ListCustomSupportRuleTypeFilter {
  evaluator_type_name?: string;
}

export interface ListCustomSupportRuleTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  /** first 为空 */
  cursor?: string;
  'FlowDevops-Agw-AppId'?: number;
  /** 默认 20 */
  limit?: Int64;
  list_custom_support_rule_type_filter?: ListCustomSupportRuleTypeFilter;
  Base?: base.Base;
}

export interface ListCustomSupportRuleTypeResponse {
  types?: Array<EvalautorTypeMeta>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

export interface ListEvaluatorTypeDetailRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  list_evaluator_type_filter?: ListEvaluatorTypeFilter;
  /** first 为空 */
  cursor?: string;
  limit?: Int64;
}

export interface ListEvaluatorTypeDetailResponse {
  evaluator_type_detail_infos?: Array<EvaluatorTypeDetailInfo>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

/** ListEvaluatorTypeFilter 请求过滤规则 */
export interface ListEvaluatorTypeFilter {
  evaluator_type_name?: string;
  callback_type?: flow_devops_evaluation_entity.CallbackType;
  creator_id?: Int64;
}

export interface ListRuleGroupMetaRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  /** first 为空 */
  cursor?: string;
  'FlowDevops-Agw-AppId'?: number;
  /** 默认 20 */
  limit?: Int64;
  /** true 时 response 返回 rules 数量, 目前对接口性能有影响 */
  need_rules_count?: boolean;
  tag_name?: string;
  tag_value?: string;
  /** -- search fields -- */
  fuzzy_name?: string;
  creator_id?: Int64;
  Base?: base.Base;
}

export interface ListRuleGroupMetaResponse {
  /** 只返回 meta */
  metas?: Array<RuleGroupMeta>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

export interface ListSupportRuleTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  /** first 为空 */
  cursor?: string;
  'FlowDevops-Agw-AppId'?: number;
  /** 默认 20 */
  limit?: Int64;
  Base?: base.Base;
}

export interface ListSupportRuleTypeResponse {
  types?: Array<EvalautorTypeMeta>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

export interface LLMSetting {
  model_version: string;
  temperature: number;
  respose_max_length: Int64;
}

/** 多维度人工评测评分规则 */
export interface ManualRule {
  /** 数据类型：数值评分、选项、纯文本 */
  data_type: flow_devops_evaluation_entity.EvaluateResultDataType;
  /** 评分范围 */
  scope?: flow_devops_evaluation_entity.ScoringScope;
  options?: Array<flow_devops_evaluation_entity.EvaluateResultOption>;
}

export interface ModelInfo {
  model_id: Int64;
  temperature: number;
  max_tokens: Int64;
  top_p?: number;
  response_format?: LLMResponseFormat;
  presence_penalty?: number;
  frequency_penalty?: number;
  model_style?: Int64;
  model_name?: string;
}

export interface PromptRule {
  setting: LLMSetting;
  content: string;
  /** 20240815 服务端内部使用 */
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

export interface PythonRule {
  code: string;
}

export interface RegisterEvaluatorTypeRequest {
  evaluator_type_detail_info: EvaluatorTypeDetailInfo;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  cid?: string;
}

export interface RegisterEvaluatorTypeResponse {
  evaluator_type_detail_info: EvaluatorTypeDetailInfo;
  BaseResp?: base.BaseResp;
}

export interface RemoveRuleRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule_group_id: Int64;
  rule_id: Int64;
  cid?: string;
  Base?: base.Base;
}

export interface RemoveRuleResponse {
  BaseResp?: base.BaseResp;
}

export interface ReplyContent {
  reply_type: ReplyType;
  content_type: ContentType;
  content: string;
  input_token_cnt: Int64;
}

export interface RPCCallbackEvaluatorParams {
  psm?: string;
  cluster?: string;
  /** 单位ms */
  timeout?: Int64;
}

export interface Rule {
  /** 规则 id */
  rule_id?: Int64;
  /** 评估器标识 */
  evaluator_type: Int64;
  process_type: EvaluatorProcessType;
  /** 自定义评估器的名称 */
  evaluator_type_name?: string;
  /** 权重 */
  weight?: Int64;
  creator_id?: Int64;
  /** 展示用名称 */
  name?: string;
  /** 评测粒度 */
  granularity?: flow_devops_evaluation_entity.EvaluatorGranularity;
  receive_chat_history?: boolean;
  /** 数据类型 */
  data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  /** 不同的 EvaluatorProcessType 对应着不同的结构体定义 */
  prompt_rule?: PromptRule;
  python_rule?: PythonRule;
  js_rule?: JSRule;
  buildin_func_rule?: BuildinFuncRule;
  buildin_prompt_rule?: BuildinPromptRule;
  custom_callback?: CustomCallback;
  manual_rule?: ManualRule;
  custom_metrics?: CustomMetricsRule;
  spec_test_rule?: SpecTestRule;
  fornax_prompt_rule?: FornaxPromptRule;
  coze_prompt_rule?: CozePromptRule;
}

export interface RuleGroup {
  meta: RuleGroupMeta;
  rules: Array<Rule>;
}

export interface RuleGroupMeta {
  /** rule_group_id */
  id: Int64;
  name?: string;
  desc?: string;
  space_id: Int64;
  /** true 时该 rule group 在规则界面可见 */
  is_published?: boolean;
  creator_id?: Int64;
  created_at?: Int64;
  updated_at?: Int64;
  deleted_at?: Int64;
  /** 默认为空 */
  rules_count?: Int64;
}

export interface RunRuleInput {
  /** task id */
  TaskID?: Int64;
  /** 评测规则 id */
  RuleID: Int64;
  /** 评测数据输入: 数据集行内容 + 评测目标输出内容与历史记录 + 评测目标的 trace */
  InputData: RunRuleInputData;
}

export interface RunRuleInputData {
  /** 1: optional dataset.Row Row              // 数据集内容, 包含 本轮输入 与 预期输出
历史会话记录 */
  history_messages?: Array<flow_devops_evaluation_callback_common.Message>;
  /** 评测目标本轮输入 */
  input?: flow_devops_evaluation_callback_common.Message;
  /** 评测目标本轮输出 */
  prediction?: ReplyContent;
  /** 评测目标预期输出 */
  output?: string;
  /** 变量 */
  variables?: Record<string, flow_devops_evaluation_callback_common.Content>;
  /** 评测目标本轮输入 v2 */
  input_v2?: flow_devops_evaluation_callback_common.Content;
  /** 评测目标预期输出 v2 */
  output_v2?: flow_devops_evaluation_callback_common.Content;
  /** 评测目标本轮输出 v2 */
  prediction_v2?: flow_devops_evaluation_callback_common.Content;
  /** 评测目标运行轨迹 */
  trajectory?: flow_devops_evaluation_evaluator_callback.Trajectory;
  /** 预留的扩展字段，暂无使用场景。由评测平台传递定制信息给评估器Rule的实现方 */
  ext?: Record<string, string>;
}

export interface RunRuleResult {
  /** 规则运行状态 */
  run_state: RuleRunState;
  /** 报错时的信息 */
  err_msg?: string;
  /** 打分 */
  score?: number;
  /** 打分过程与结果相关信息 */
  score_content?: string;
  /** token count */
  tokens?: Int64;
  /** 运行开始时间 */
  start_time?: Int64;
  /** 运行结束时间 */
  end_time?: Int64;
  /** 冗余 */
  extra?: string;
  input_tokens?: Int64;
  output_tokens?: Int64;
  /** 打分范围，如 Coze 场景下由用户 LLM Prompt 决定评估器输出打分范围 */
  scoring_scope?: flow_devops_evaluation_entity.ScoringScope;
}

export interface SpecTestRule {
  content?: string;
}

export interface UpdateEvaluatorTypeRequest {
  evaluator_type_detail_info: EvaluatorTypeDetailInfo;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
}

export interface UpdateEvaluatorTypeResponse {
  BaseResp?: base.BaseResp;
}

export interface UpdateRuleGroupRequest {
  'FlowDevops-Agw-UserId'?: string;
  id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  /** name, description, content */
  update_fields: Array<SupportUpdateRuleGroupField>;
  name?: string;
  desc?: string;
  cid?: string;
  Base?: base.Base;
}

export interface UpdateRuleGroupResponse {
  rule_group?: RuleGroup;
  BaseResp?: base.BaseResp;
}

export interface UpdateRuleRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  rule_group_id: Int64;
  rule_id: Int64;
  rule: Rule;
  cid?: string;
  Base?: base.Base;
}

export interface UpdateRuleResponse {
  rule?: Rule;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
