/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum CallbackType {
  /** rpc接口实现 */
  RPC = 1,
  /** 通过psm，http协议访问服务 */
  PSMHTTP = 2,
  /** 为开放评测对象类型；无法通过平台触发评测，被评测对象自行上报数据  */
  Open = 3,
}

/** 评估结果数据类型 */
export enum EvaluateResultDataType {
  Unknown = 0,
  /** 数值打分 */
  Score = 1,
  /** 数值 */
  Value = 2,
  /** 选项 */
  Select = 3,
  /** 纯文本描述 */
  PlainText = 4,
}

/** 数值类型评估结果数值的具体类型 (EvaluateResultDataType = Value) */
export enum EvaluateResultValueType {
  /** 浮点数数值, 默认 */
  Double = 0,
  /** 整数数值 */
  Int = 1,
}

/** 评估粒度 */
export enum EvaluatorGranularity {
  Unknown = 0,
  /** 单轮对话 */
  Row = 1,
  /** 对话组 */
  RowGroup = 2,
}

/** faas http函数如果开启auth有自己的鉴权方式，和http不一样。 */
export enum HTTPAuthType {
  /** 默认 */
  default = 0,
  /** 如果faas函数开启auth，需要给evaluation的服务账号secret开白名单 */
  JWT = 1,
}

export enum HTTPMethod {
  Undefined = 0,
  Get = 1,
  Post = 2,
}

export enum ModelPlatform {
  Unknown = 0,
  GPTOpenAPI = 1,
  MAAS = 2,
}

export interface Session {
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
}
/* eslint-enable */
