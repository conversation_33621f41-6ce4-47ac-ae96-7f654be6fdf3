/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface ChatContext {
  /** 上下文携带的历史消息 */
  MessageContext: Array<Message>;
}

export interface Message {
  Role: string;
  Content: string;
}

export interface SearchItem {
  /** 本轮的query */
  Query: string;
}

export interface SearchResultItem {
  /** 事件文本 */
  Text: string;
  /** 时间戳 */
  EventMs?: Int64;
}
/* eslint-enable */
