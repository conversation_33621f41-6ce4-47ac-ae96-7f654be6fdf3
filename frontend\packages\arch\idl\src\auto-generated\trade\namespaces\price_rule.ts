/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as marketplace_common from './marketplace_common';

export type Int64 = string | number;

export interface GetPricingRulesData {
  rules?: Array<PricingRule>;
}

export interface GetPricingRulesRequest {
  scene: common.Scene;
  coze_account_id?: Int64;
  coze_account_type?: common.CozeAccountType;
}

export interface GetPricingRulesResponse {
  data?: GetPricingRulesData;
  code: number;
  message: string;
}

export interface PricingRule {
  rule: common.AmountType;
  calculation_type: common.DiscountCalculationType;
  discount: number;
  minimum: string;
  maximum: string;
  unit_price?: marketplace_common.Price;
}
/* eslint-enable */
