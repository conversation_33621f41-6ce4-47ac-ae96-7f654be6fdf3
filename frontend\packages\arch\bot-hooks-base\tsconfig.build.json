{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": false, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../agent-ide/tool-config/tsconfig.build.json"}, {"path": "../bot-api/tsconfig.build.json"}, {"path": "../bot-tea/tsconfig.build.json"}, {"path": "../bot-typings/tsconfig.build.json"}, {"path": "../bot-utils/tsconfig.build.json"}, {"path": "../../common/assets/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../responsive-kit/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../../studio/user-store/tsconfig.build.json"}]}