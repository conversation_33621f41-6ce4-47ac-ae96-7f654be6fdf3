/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface Agent {
  /** Unique identifier for the agent */
  agent_id: string;
  /** Service ID associated with the agent */
  service_id: string;
  /** Service PSM identifier */
  psm: string;
  /** Environment name */
  env_name: string;
  /** Name of the agent */
  name: string;
  /** Description of the agent */
  description?: string;
  /** Owner of the agent */
  owner?: string;
  /** List of subscribers to the agent */
  subscribers?: Array<string>;
  /** Runtime environment for the agent */
  runtime?: string;
  /** Timestamp when the agent was created */
  created_at?: string;
  /** Timestamp when the agent was last updated */
  updated_at?: string;
  /** Deprecated: status of the agent */
  status?: string;
  /** Deprecated: extra information about the agent */
  extras?: AgentExtras;
  /** Status of extra steps in the agent lifecycle */
  extra_steps_status?: string;
  /** Link to details about extra steps */
  extra_steps_link?: string;
}

export interface AgentExtras {
  /** Error message encountered during agent creation */
  create_error?: string;
  /** List of steps performed during agent creation */
  create_steps?: Array<AsyncCreateStep>;
}

export interface AsyncCreateAgentRequest {
  /** PSM identifier */
  psm: string;
  /** Name of the agent to create */
  name: string;
  /** Description of the agent */
  description?: string;
  /** Parent PSM ID */
  psm_parent_id: number;
  /** Owner of the agent */
  owner?: string;
  /** Region where the agent is deployed */
  region: string;
  /** Runtime environment for the agent */
  runtime: string;
  /** List of environment variables for the agent */
  format_envs?: Array<common.FormatEnvs>;
  /** Create git repo and SCM repo for the user */
  git_group: string;
  /** git repository name */
  git_repo: string;
  /** scm repository name */
  scm_repo: string;
  /** Template name used for the agent */
  template_name: string;
  /** List of admin users for the agent */
  admins?: string;
  /** List of authorizers for the agent */
  authorizers?: string;
  /** Service level of the agent */
  service_level?: string;
  /** Purpose of the service */
  service_purpose?: string;
}

export interface AsyncCreateAgentResponse {
  /** Response code for the async create agent operation */
  code?: number;
  /** Error message if the operation failed */
  error?: string;
  /** Data about the async creation process */
  data?: AsyncCreateAgentResponseData;
}

export interface AsyncCreateAgentResponseData {
  /** Unique identifier for the async creation process */
  creation_id?: string;
}

export interface AsyncCreateAgentStatus {
  /** Unique identifier for the async creation process */
  creation_id: string;
  /** Current status of the async creation process */
  status: string;
  /** List of steps in the async creation process */
  steps?: Array<AsyncCreateStep>;
  /** Resulting agent, only set when status is success */
  result?: Agent;
}

export interface AsyncCreateStep {
  /** Unique identifier for the step */
  step_id?: string;
  /** Name of the step in the async creation process */
  step_name?: string;
  /** Description of what this step does */
  description?: string;
  /** Current status of the step */
  status?: string;
  /** Additional details about the step's execution */
  detail?: string;
  /** Time when the step was created */
  create_time?: string;
  /** Time when the step started */
  start_time?: string;
  /** Time when the step ended */
  end_time?: string;
  /** Error message if the step failed */
  error_message?: string;
}

export interface CreateAgentRequest {
  /** PSM identifier */
  psm: string;
  /** Name of the agent to create */
  name: string;
  /** Description of the agent */
  description?: string;
  /** Parent PSM ID */
  psm_parent_id: number;
  /** Owner of the agent */
  owner?: string;
  /** Region where the agent is deployed */
  region: string;
  /** Runtime environment for the agent */
  runtime: string;
  /** List of environment variables for the agent */
  format_envs?: Array<common.FormatEnvs>;
  /** Source type for the agent */
  source_type: string;
  /** Source identifier for the agent */
  source: string;
  /** List of dependencies for the agent */
  dependency?: Array<common.Dependency>;
  /** List of admin users for the agent */
  admins?: string;
  /** List of authorizers for the agent */
  authorizers?: string;
  /** Service level of the agent */
  service_level?: string;
  /** Purpose of the service */
  service_purpose?: string;
  /** Git group for the agent's repository */
  git_group?: string;
  /** Git repository name */
  git_repo?: string;
  /** SCM repository name */
  scm_repo?: string;
  /** Template name used for the agent */
  template_name?: string;
  /** Programming language used by the agent */
  language?: string;
}

export interface CreateAgentResponse {
  /** Response code for the create agent operation */
  code?: number;
  /** Error message if the operation failed */
  error?: string;
  /** Created agent data */
  data?: Agent;
}

export interface DeleteAgentRequest {
  /** Unique identifier for the agent to delete */
  agent_id: string;
}

export interface GetAgentRequest {
  /** Unique identifier for the agent */
  agent_id: string;
}

export interface GetAgentResponse {
  /** Response code for the get agent operation */
  code?: number;
  /** Error message if the operation failed */
  error?: string;
  /** Agent data returned in the response */
  data?: Agent;
}

export interface GetAsyncCreateAgentStatusRequest {
  /** Unique identifier for the async creation process */
  creation_id: string;
}

export interface GetAsyncCreateAgentStatusResponse {
  /** Response code for the get async create agent status operation */
  code?: number;
  /** Error message if the operation failed */
  error?: string;
  /** Data about the async creation status */
  data?: AsyncCreateAgentStatus;
}

export interface ListAgentsRequest {
  /** Whether to list all agents */
  all?: string;
  /** Environment name to filter agents, e.g. prod/ppe/boe_feature */
  env?: string;
  /** Limit for pagination */
  limit?: number;
  /** Filter agents by name */
  name?: string;
  /** Offset for pagination */
  offset?: number;
  /** Filter agents by owner */
  owner?: string;
  /** Prefix search across multiple fields */
  search?: string;
  /** Type of search: all, admin, own, subscribe */
  search_type?: string;
  /** Field to sort agents by */
  sort_by?: string;
  /** Supported search fields */
  search_fields?: string;
}

export interface ListAgentsResponse {
  /** Response code for the list agents operation */
  code?: number;
  /** List of agents returned in the response */
  data?: Array<Agent>;
  /** Error message if the operation failed */
  error?: string;
}

export interface UpdateAgentRequest {
  /** Unique identifier for the agent */
  agent_id: string;
  /** New name for the agent */
  name?: string;
  /** New description for the agent */
  description?: string;
}

export interface UpdateAgentResponse {
  /** Response code for the update agent operation */
  code?: number;
  /** Error message if the operation failed */
  error?: string;
  /** Updated agent data */
  data?: Agent;
}
/* eslint-enable */
