/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_marketplace_interaction_common from './flow_marketplace_interaction_common';

export type Int64 = string;

export interface GetShareInfoByBIDData {
  ShareLinkData?: ShareLinkData;
}

export interface GetShareLinkData {
  BID?: string;
}

export interface GetShareLinkDataV2 {
  BID?: string;
  ShareChannels?: Array<flow_marketplace_interaction_common.ShareChannel>;
}

export interface GetShareLinkRequest {
  entity_id: string;
  entity_type: flow_marketplace_interaction_common.InteractionEntityType;
}

export interface GetShareLinkResponse {
  code?: number;
  msg?: string;
  data: GetShareLinkData;
}

export interface GetShareLinkV2Request {
  entity_id: string;
  entity_type: flow_marketplace_interaction_common.InteractionEntityType;
  invitation_method: flow_marketplace_interaction_common.InvitationMethod;
}

export interface GetShareLinkV2Response {
  code?: number;
  msg?: string;
  data: GetShareLinkDataV2;
}

export interface GetShortURLData {
  ShortURLs?: Record<string, string>;
}

export interface GetShortURLRequest {
  source_urls: Array<string>;
}

export interface GetShortURLResponse {
  code?: number;
  msg?: string;
  data: GetShortURLData;
}

/** ----------------------------- RPC ----------------------------- */
export interface ShareLinkData {
  EntityID?: Int64;
  EntityType?: flow_marketplace_interaction_common.InteractionEntityType;
  SharerID?: Int64;
  CreatedTime?: Int64;
  Ticket?: Int64;
  BaseParam?: string;
  InvitationMethod?: string;
}
/* eslint-enable */
