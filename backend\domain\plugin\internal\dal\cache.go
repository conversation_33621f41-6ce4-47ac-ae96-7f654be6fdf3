/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package dal

import (
	"context"
	"errors"
	"time"

	redisV9 "github.com/redis/go-redis/v9"

	"github.com/coze-dev/coze-studio/backend/pkg/lang/ptr"
)

type OAuthCache struct {
	cacheCli *redisV9.Client
}

func NewOAuthCache(cacheCli *redisV9.Client) *OAuthCache {
	return &OAuthCache{
		cacheCli: cacheCli,
	}
}

func (o *OAuthCache) Get(ctx context.Context, key string) (value string, exist bool, err error) {
	cmd := o.cacheCli.Get(ctx, key)
	if cmd.Err() != nil {
		if errors.Is(cmd.Err(), redisV9.Nil) {
			return "", false, nil
		}
		return "", false, cmd.Err()
	}

	return cmd.Val(), true, nil
}

func (o *OAuthCache) Set(ctx context.Context, key string, value string, expiration *time.Duration) (err error) {
	_expiration := ptr.FromOrDefault(expiration, 0)

	cmd := o.cacheCli.Set(ctx, key, value, _expiration)

	return cmd.Err()
}
