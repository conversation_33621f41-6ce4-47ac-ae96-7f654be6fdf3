/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum ChangeType {
  /** 上传完成 */
  Created = 1,
  /** 已删除 */
  Deleted = 2,
}

export enum MDType {
  Default = 0,
  Image = 1,
  Audio = 2,
  Video = 3,
  Document = 4,
  Font = 5,
  Archive = 6,
  Other = 255,
}

/** 区分 MDType 这个枚举值，因为 Album 不是多模态文件 */
export enum ObjType {
  Image = 1,
  Audio = 2,
  Video = 3,
  Document = 4,
  Album = 5,
  Other = 255,
}

export enum PermObjType {
  User = 0,
  Plugin = 1,
  Workflow = 2,
}

export enum PermScope {
  /** 允许一次 */
  Once = 0,
  /** 永久允许 */
  Always = 1,
}

export interface AddPhotosToAlbumRequest {
  req_common_params: ReqCommonBaseInfo;
  /** 需校验 item 类型为图片、视频 */
  md_items: Array<FileMetaCreateAlbumInfo>;
  album_name: string;
  Base?: base.Base;
}

export interface AddPhotosToAlbumResponse {
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface AlbumInfo {
  album_id?: string;
  /** 相册名称 */
  name?: string;
  /** 相册描述 */
  desc?: string;
  user_id: Int64;
  bot_id: Int64;
  conversation_id: Int64;
  /** 相册创建业务方类型，人工分配给 coze、豆包、cici、Plugin 等上传业务方的标识 */
  biz_type: string;
  /** 相册创建业务方 ID，例如对 Plugin 来说是 PluginID */
  biz_id: string;
  /** 创建时间，ms */
  created_at: Int64;
  /** 更新时间 ms */
  updated_at: Int64;
}

export interface APIItem {
  id: string;
  name: string;
  desc: string;
  group_id?: string;
}

export interface BatchCreateFilesRequest {
  /** 公共参数 */
  req_common_base_info: ReqCommonBaseInfo;
  /** 源文件 URL 列表 */
  source_urls: Array<string>;
  Base?: base.Base;
}

export interface BatchCreateFilesResponse {
  /** 创建结果 */
  create_results?: Array<FileCreateResult>;
  /** 返回给模型的数据 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface BatchDeleteFileRequest {
  req_common_params: ReqCommonBaseInfo;
  ids?: Array<string>;
  uris?: Array<string>;
  Base?: base.Base;
}

export interface BatchDeleteFileResponse {
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface BatchUpdateFileMetaRequest {
  /** 这个也可以通过context给过来 */
  req_common_params: ReqCommonBaseInfo;
  update_items: Array<FileMetaUpdateInfo>;
  Base?: base.Base;
}

export interface BatchUpdateFileMetaResponse {
  /** 成功数 */
  SuccessNum?: number;
  /** 失败数 */
  FailNum?: number;
  /** 处理结果详情，k:id,v:true表示成功 */
  ResultDetail?: Record<string, boolean>;
  /** LLM 仅感知改字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface BizInfo {
  BizType: string;
  BizID?: string;
}

export interface CommonParam {
  BotID: Int64;
  UserID: Int64;
  ConversationID: Int64;
  MessageID: Int64;
  ConnectorID: string;
}

export interface CreateAlbumRequest {
  req_common_params: ReqCommonBaseInfo;
  album_name?: string;
  album_desc?: string;
  Base?: base.Base;
}

export interface CreateAlbumResponse {
  album_info?: AlbumInfo;
  photos?: Array<FileMetaInfo>;
  /** 详情页链接 */
  detail_page_url?: string;
  /** 相册包含的图片总数 */
  album_length?: number;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface DelAlbumRequest {
  req_common_params: ReqCommonBaseInfo;
  /** deprecated，通过 AlbumName 删除 */
  album_id: string;
  /** 有AlbumNmae时，根据AlbumName删除 */
  album_name?: string;
  Base?: base.Base;
}

export interface DelAlbumResponse {
  /** 用户自身创建的相册列表 */
  album_name_list?: Array<string>;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface DestFileInfo {
  /** 目标文件 URI */
  file_uri?: string;
  /** 目标文件 URL */
  file_url?: string;
  /** 文件名称 */
  file_name?: string;
  /** 文件上传状态 */
  status?: number;
}

export interface FileCreateResult {
  /** 源文件 URL */
  source_url: string;
  /** 文件 ID */
  file_id: string;
  /** 文件 URI */
  uri: string;
}

export interface FileMeta {
  FileID: string;
  MDType: MDType;
  Format: string;
  FileSize: Int64;
  FileName: string;
  URI: string;
  Position: string;
  Device: string;
  CreatedAt: Int64;
  UpdatedAt: Int64;
  Tags: string;
}

/** FileBaseMetaInfo 包含创建 album 时需要的 file 基本信息 */
export interface FileMetaCreateAlbumInfo {
  file_id?: string;
  uri?: string;
}

export interface FileMetaInfo {
  id: string;
  file_id: string;
  file_name: string;
  md_type: MDType;
  format: string;
  uri: string;
  position: string;
  device: string;
  size: number;
  tags: string;
  created_at_ms: Int64;
  updated_at_ms: Int64;
  user_id: Int64;
  conversation_id: Int64;
  biz_type: string;
  biz_id: string;
  /** 注意：该字段返回给前端可能因精度丢失而不一致 */
  bot_id: Int64;
  message_id: Int64;
  /** 原图地址 */
  normal_file_url?: FileURL;
  /** 缩略图地址 */
  thumbnail_file_url?: FileURL;
  /** 图片 meta 信息，当文件类型为图片时有效 */
  image_meta?: ImageMeta;
  /** 255:文件未构建索引; 254:没有启用byterag; 0:rag索引构建成功; 1:rag索引构建中; 2:rag索引构建失败; 3:rag索引已删除 */
  rag_document_status?: Int64;
  /** rag内容的切片数 */
  rag_content_chunk_num?: number;
}

export interface FileMetaUpdateInfo {
  /** 文件ID */
  file_id?: string;
  /** 文件名称 */
  file_name?: string;
  /** 上传地址 */
  position?: string;
  /** 上传设备 */
  device?: string;
  /** 图片URI */
  uri?: string;
  /** 特征标签，多个用英文逗号隔开 */
  tags?: string;
  /** 图片描述 */
  caption?: string;
}

export interface FileSearchItem {
  file_name?: string;
  file_url?: string;
  content?: string;
}

export interface FileURL {
  main_url: string;
  backup_url: string;
}

export interface GetDisplayAPIsRequest {
  Base?: base.Base;
}

export interface GetDisplayAPIsResponse {
  apis: Array<APIItem>;
  groups?: Array<GroupItem>;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetFileMetaInfoRequest {
  req_common_params: ReqCommonBaseInfo;
  file_id: string;
  Base?: base.Base;
}

export interface GetFileMetaInfoResponse {
  data?: FileMetaInfo;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface GetMDCardDetailPageInfoRequest {
  /** 卡片资源类型：如相册、图片、视频、文件，需要感知到多模态文件类型 */
  obj_type: ObjType;
  /** 卡片详情页 ID */
  dpid: string;
  /** 资源所属botId */
  bid: Int64;
  /** 资源所属connectorId */
  cid: Int64;
  /** 过期时间 */
  expires: Int64;
  /** 参数签名 */
  signature: string;
  page_num?: number;
  page_size?: number;
  Base?: base.Base;
}

export interface GetMDCardDetailPageInfoResponse {
  file_list: Array<FileMetaInfo>;
  /** 前端展示场景为相册时，需要在详情页展示相册名称 */
  show_title?: string;
  /** 文件总数 */
  total?: Int64;
  next_page_num?: number;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code?: Int64;
  msg?: string;
  base_resp: base.BaseResp;
}

export interface GroupItem {
  id: string;
  name: string;
}

export interface ImageMeta {
  /** 图片宽度，单位：px */
  width: number;
  /** 图片高度，单位：px */
  height: number;
}

export interface PageIsWritableRequest {
  /** 详情页 id */
  dpid: string;
  /** 不依赖前端传递，根据 detail_page_id 读取 abase 缓存后获取 */
  UserID?: string;
  /** 渠道 id：coze，豆包，cici */
  cid?: string;
  bid?: string;
  Base?: base.Base;
}

export interface PageIsWritableResponse {
  is_writable: boolean;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: Int64;
  msg: string;
  base_resp: base.BaseResp;
}

export interface PublicBatchDeleteFilesRequest {
  /** 不依赖前端传递，根据 detail_page_id 读取 abase 缓存后获取 */
  UserID?: string;
  bot_id?: string;
  /** 渠道 id：coze，豆包，cici */
  connector_id?: string;
  /** 详情页 id */
  detail_page_id: string;
  uris?: Array<string>;
  Base?: base.Base;
}

export interface PublicBatchDeleteFilesResponse {
  success_ids?: Array<string>;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface PublicBatchRemovePhotosRequest {
  /** 不依赖前端传递，根据 detail_page_id 读取 abase 缓存后获取 */
  UserID?: string;
  bot_id?: string;
  /** 渠道 id：coze，豆包，cici */
  connector_id?: string;
  /** 详情页 id */
  detail_page_id: string;
  /** 文件 id 列表 */
  ids?: Array<string>;
  Base?: base.Base;
}

export interface PublicBatchRemovePhotosResponse {
  success_ids?: Array<string>;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface PublicDeleteAlbumRequest {
  /** 不依赖前端传递，根据 detail_page_id 读取 abase 缓存后获取 */
  UserID?: string;
  bot_id?: string;
  /** 渠道 id：coze，豆包，cici */
  connector_id?: string;
  /** 详情页 id */
  detail_page_id: string;
  Base?: base.Base;
}

export interface PublicDeleteAlbumResponse {
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface PublicUpdateFileRequest {
  /** 不依赖前端传递，根据 detail_page_id 读取 abase 缓存后获取 */
  UserID?: string;
  bot_id?: string;
  /** 渠道 id：coze，豆包，cici */
  connector_id?: string;
  /** 详情页 id */
  detail_page_id: string;
  update_items?: FileMetaUpdateInfo;
  Base?: base.Base;
}

export interface PublicUpdateFileResponse {
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface RecallFileMetaInfosRequest {
  req_common_params: ReqCommonBaseInfo;
  need_rag?: boolean;
  /** 文件类型 */
  md_type?: MDType;
  /** ISO 年-月-日 */
  begin_time?: string;
  end_time?: string;
  file_uris?: Array<string>;
  /** 文件细分类型, 如pdf、png等 */
  format?: string;
  Base?: base.Base;
}

export interface RecallFileMetaInfosResponse {
  file_list?: Array<FileMetaInfo>;
  /** 详情页链接 */
  detail_page_url?: string;
  /** 本次查询返回的图片个数 */
  file_num?: Int64;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

/** 当前请求会话的相关信息
 所有 plugin 的调用都需要包含该结构体 */
export interface ReqCommonBaseInfo {
  /** 用户ID */
  user_id: Int64;
  /** 会话ID */
  'X-AIPlugin-Conversation-ID': Int64;
  /** 业务方类型，人工分配给 coze、豆包、cici、Plugin 等上传业务方的标识 */
  biz_type: string;
  /** 业务方 ID，例如对 Plugin 来说是 PluginID */
  biz_id: string;
  /** 机器人ID */
  'X-AIPlugin-Bot-ID': Int64;
  /** 插件调用 FileBox API 时，由于 coze 平台要求，无法传递 UserID，但传递 SessionID，并提供通过 SessionID 解析 UserID 的接口 */
  'X-Temp-OpenID'?: string;
  /** 用户原始对话内容 */
  query: string;
  'X-AIPlugin-Connector-ID'?: Int64;
  /** 插件授权token */
  'Filebox-Auth-Token'?: string;
}

export interface SearchFileRequest {
  req_common_params: ReqCommonBaseInfo;
  /** 文件URI，如果传了，则对指定文件进行总结，如果没传，则按对符合下方条件的第一个文件进行总结 */
  file_uri_list?: Array<string>;
  /** 文件名 */
  file_name_list?: Array<string>;
  Base?: base.Base;
}

export interface SearchFileResponse {
  /** 文档内容检索结果 */
  file_search_item_list?: Array<FileSearchItem>;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

/** 视频云临时上传秘钥 */
export interface SecurityToken {
  AccessKeyID: string;
  SecretAccessKey: string;
  SessionToken: string;
  ExpiredTime: string;
  CurrentTime: string;
}

export interface ShowAlbumListRequest {
  /** 进行权限校验，如仅查询自己的相册 */
  req_common_params: ReqCommonBaseInfo;
  Base?: base.Base;
}

export interface ShowAlbumListResponse {
  album_name_list?: Array<string>;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface ShowAlbumRequest {
  /** 进行权限校验，如仅查询自己的相册 */
  req_common_params: ReqCommonBaseInfo;
  /** 先根据 AlbumID 精确查找 */
  album_id?: string;
  /** 若 AlbumID 为空，则根据 name 和 desc 模糊匹配 */
  album_name?: string;
  /** 分页查询参数 */
  start?: number;
  /** 分页查询参数 */
  limit?: number;
  Base?: base.Base;
}

export interface ShowAlbumResponse {
  album_info?: AlbumInfo;
  photos?: Array<FileMetaInfo>;
  /** 相册包含的图片总数 */
  album_length?: number;
  /** 详情页链接 */
  detail_page_url?: string;
  /** 用户自身创建的相册列表 */
  album_name_list?: Array<string>;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface SourceFileInfo {
  /** 源文件 URI */
  file_uri?: string;
  /** 源文件 URL */
  file_url?: string;
  /** 源文件 名称 */
  file_name?: string;
}

export interface SummaryFileRequest {
  req_common_params: ReqCommonBaseInfo;
  /** 文件URI，如果传了，则对指定文件进行总结，如果没传，则按对符合下方条件的第一个文件进行总结 */
  file_uri?: string;
  /** 文件名 */
  file_name?: string;
  /** 文件格式：jpeg、pdf、txt等等 */
  format?: string;
  /** 上传时间，ISO 年-月-日 */
  upload_date?: string;
  Base?: base.Base;
}

export interface SummaryFileResponse {
  /** 总结内容 */
  summary_info?: SummaryInfo;
  /** 如果不是总结指定的文件URI，返回则会带上建议的文件列表 */
  suggested_file_list?: Array<FileMetaInfo>;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface SummaryInfo {
  file_name: string;
  /** 总结内容 */
  content: string;
}

export interface SyncResultItem {
  /** 处理结果状态 */
  Status: string;
  /** 处理结果附加消息 */
  Message: string;
  FileID: string;
  Uri: string;
}

export interface UpdateAlbumRequest {
  /** 进行权限校验，如仅查询自己的相册 */
  req_common_params: ReqCommonBaseInfo;
  /** deprecated，通过 AlbumName 更新 */
  album_id: string;
  /** 有AlbumNmae时，根据AlbumName删除 */
  album_name?: string;
  /** 更新的内容 */
  new_album_name?: string;
  /** 更新的内容 */
  new_album_desc?: string;
  Base?: base.Base;
}

export interface UpdateAlbumResponse {
  /** 用户自身创建的相册列表 */
  album_name_list?: Array<string>;
  /** LLM 仅感知该字段内容 */
  response_for_model?: string;
  /** 首次使用时，需要返回合规文案 */
  compliance_statement?: string;
  base_resp: base.BaseResp;
}

export interface UpdateFileBoxUsageStatusRequest {
  req_common_params: ReqCommonBaseInfo;
  /** true 表示打开，false表示关闭 */
  switch_on: boolean;
  Base?: base.Base;
}

export interface UpdateFileBoxUsageStatusResponse {
  base_resp: base.BaseResp;
}

export interface UploadAuth {
  ServiceID: string;
  UploadPathPrefix: string;
  UploadHost: string;
  SecToken: SecurityToken;
}
/* eslint-enable */
