/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_common from './namespaces/bot_common';
import * as bot_open_api from './namespaces/bot_open_api';

export { bot_common, bot_open_api };
export * from './namespaces/bot_common';
export * from './namespaces/bot_open_api';

export type Int64 = string | number;

export default class BotOpenApiService<T> {
  private request: any = () => {
    throw new Error('BotOpenApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /api/oauth/authorization_code */
  OauthAuthorizationCode(
    req?: bot_open_api.OauthAuthorizationCodeReq,
    options?: T,
  ): Promise<bot_open_api.OauthAuthorizationCodeResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/oauth/authorization_code');
    const method = 'GET';
    const params = { code: _req['code'], state: _req['state'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/oauth/callback
   *
   * 飞书老接口 已弃用
   */
  OauthCallback(
    req?: bot_open_api.OauthCallbackReq,
    options?: T,
  ): Promise<bot_open_api.OauthCallbackResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/oauth/callback');
    const method = 'GET';
    const params = {
      oauth_token: _req['oauth_token'],
      oauth_token_secret: _req['oauth_token_secret'],
      oauth_callback_confirmed: _req['oauth_callback_confirmed'],
      state: _req['state'],
      oauth_verifier: _req['oauth_verifier'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/oauth/authorization_code_debug */
  OauthAuthorizationCodeDebug(
    req?: bot_open_api.OauthAuthorizationCodeReq,
    options?: T,
  ): Promise<bot_open_api.OauthAuthorizationCodeResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/oauth/authorization_code_debug');
    const method = 'GET';
    const params = { code: _req['code'], state: _req['state'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /open_api/v1/chat
   *
   * chat 接口
   */
  ChatV1(
    req: bot_open_api.ChatV1Req,
    options?: T,
  ): Promise<bot_open_api.ChatV1Resp> {
    const _req = req;
    const url = this.genBaseURL('/open_api/v1/chat');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      conversation_id: _req['conversation_id'],
      bot_version: _req['bot_version'],
      user: _req['user'],
      query: _req['query'],
      chat_history: _req['chat_history'],
      extra: _req['extra'],
      stream: _req['stream'],
      custom_variables: _req['custom_variables'],
      local_message_id: _req['local_message_id'],
      content_type: _req['content_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open_api/v1/web_chat
   *
   * web chat 接口
   */
  WebChatV1(
    req: bot_open_api.ChatV2Req,
    options?: T,
  ): Promise<bot_open_api.ChatV2NoneStreamResp> {
    const _req = req;
    const url = this.genBaseURL('/open_api/v1/web_chat');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      conversation_id: _req['conversation_id'],
      bot_version: _req['bot_version'],
      user: _req['user'],
      query: _req['query'],
      chat_history: _req['chat_history'],
      stream: _req['stream'],
      custom_variables: _req['custom_variables'],
      extra: _req['extra'],
      local_message_id: _req['local_message_id'],
      meta_data: _req['meta_data'],
      content_type: _req['content_type'],
      tools: _req['tools'],
      model_id: _req['model_id'],
      bot_name: _req['bot_name'],
      extra_params: _req['extra_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/oauth/exchange_token */
  OauthExchangeToken(
    req?: bot_open_api.OauthExchangeTokenReq,
    options?: T,
  ): Promise<bot_open_api.OauthExchangeTokenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/oauth/exchange_token');
    const method = 'GET';
    const params = { code: _req['code'], state: _req['state'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /open_api/v2/chat */
  ChatV2(
    req: bot_open_api.ChatV2Req,
    options?: T,
  ): Promise<bot_open_api.ChatV2NoneStreamResp> {
    const _req = req;
    const url = this.genBaseURL('/open_api/v2/chat');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      conversation_id: _req['conversation_id'],
      bot_version: _req['bot_version'],
      user: _req['user'],
      query: _req['query'],
      chat_history: _req['chat_history'],
      stream: _req['stream'],
      custom_variables: _req['custom_variables'],
      extra: _req['extra'],
      local_message_id: _req['local_message_id'],
      meta_data: _req['meta_data'],
      content_type: _req['content_type'],
      tools: _req['tools'],
      model_id: _req['model_id'],
      bot_name: _req['bot_name'],
      extra_params: _req['extra_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open_api/v1/file/upload
   *
   * 文件上传
   */
  UploadV1(
    req?: bot_open_api.UploadReq,
    options?: T,
  ): Promise<bot_open_api.UploadResp> {
    const _req = req || {};
    const url = this.genBaseURL('/open_api/v1/file/upload');
    const method = 'POST';
    const data = { source: _req['source'], bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /open_api/v1/bot/onboarding
   *
   * 开场白
   */
  BotOnboarding(
    req?: bot_open_api.BotOnboardingReq,
    options?: T,
  ): Promise<bot_open_api.BotOnboardingResp> {
    const _req = req || {};
    const url = this.genBaseURL('/open_api/v1/bot/onboarding');
    const method = 'GET';
    const params = { source: _req['source'], bot_id: _req['bot_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /open_api/connector/bot/get_bot_info
   *
   * Bot 相关 OpenAPI
   */
  GetBotInfo(
    req: bot_open_api.GetBotInfoReq,
    options?: T,
  ): Promise<bot_open_api.GetBotInfoResp> {
    const _req = req;
    const url = this.genBaseURL('/open_api/connector/bot/get_bot_info');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      version: _req['version'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v1/bot/get_online_info */
  GetBotOnlineInfo(
    req: bot_open_api.GetBotOnlineInfoReq,
    options?: T,
  ): Promise<bot_open_api.GetBotOnlineInfoResp> {
    const _req = req;
    const url = this.genBaseURL('/v1/bot/get_online_info');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      version: _req['version'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /v1/space/published_bots_list */
  GetSpacePublishedBotsList(
    req: bot_open_api.GetSpacePublishedBotsListReq,
    options?: T,
  ): Promise<bot_open_api.GetSpacePublishedBotsListResp> {
    const _req = req;
    const url = this.genBaseURL('/v1/space/published_bots_list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      connector_id: _req['connector_id'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v3/chat
   *
   * Chat 相关 OpenAPI
   */
  ChatV3(
    req: bot_open_api.ChatV3Request,
    options?: T,
  ): Promise<bot_open_api.ChatV3Response> {
    const _req = req;
    const url = this.genBaseURL('/v3/chat');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      user_id: _req['user_id'],
      stream: _req['stream'],
      additional_messages: _req['additional_messages'],
      custom_variables: _req['custom_variables'],
      auto_save_history: _req['auto_save_history'],
      meta_data: _req['meta_data'],
      tools: _req['tools'],
      custom_config: _req['custom_config'],
      extra_params: _req['extra_params'],
      connector_id: _req['connector_id'],
      shortcut_command: _req['shortcut_command'],
      parameters: _req['parameters'],
      enable_card: _req['enable_card'],
    };
    const params = { conversation_id: _req['conversation_id'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /v3/chat/submit_tool_outputs */
  SubmitToolOutputs(
    req: bot_open_api.SubmitToolOutputsRequest,
    options?: T,
  ): Promise<bot_open_api.ChatV3Response> {
    const _req = req;
    const url = this.genBaseURL('/v3/chat/submit_tool_outputs');
    const method = 'POST';
    const data = {
      stream: _req['stream'],
      tool_outputs: _req['tool_outputs'],
      connector_id: _req['connector_id'],
    };
    const params = {
      conversation_id: _req['conversation_id'],
      chat_id: _req['chat_id'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /v1/bot/update */
  UpdateDraftBot(
    req: bot_open_api.UpdateDraftBotRequest,
    options?: T,
  ): Promise<bot_open_api.UpdateDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/bot/update');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      name: _req['name'],
      description: _req['description'],
      icon_file_id: _req['icon_file_id'],
      prompt_info: _req['prompt_info'],
      plugin_id_list: _req['plugin_id_list'],
      onboarding_info: _req['onboarding_info'],
      voice_ids: _req['voice_ids'],
      knowledge: _req['knowledge'],
      workflow_id_list: _req['workflow_id_list'],
      model_info_config: _req['model_info_config'],
      suggest_reply_info: _req['suggest_reply_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/bot/get_voice_list
   *
   * 获取渠道资源、Bot管理相关信息
   */
  GetVoiceList(
    req?: bot_open_api.GetVoiceListReq,
    options?: T,
  ): Promise<bot_open_api.GetVoiceListResp> {
    const url = this.genBaseURL('/v1/bot/get_voice_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /v1/bot/create */
  CreateDraftBot(
    req: bot_open_api.CreateDraftBotRequest,
    options?: T,
  ): Promise<bot_open_api.CreateDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/bot/create');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      icon_file_id: _req['icon_file_id'],
      prompt_info: _req['prompt_info'],
      plugin_id_list: _req['plugin_id_list'],
      onboarding_info: _req['onboarding_info'],
      voice_ids: _req['voice_ids'],
      workflow_id_list: _req['workflow_id_list'],
      model_info_config: _req['model_info_config'],
      suggest_reply_info: _req['suggest_reply_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v1/bot/publish */
  PublishDraftBot(
    req: bot_open_api.PublishDraftBotRequest,
    options?: T,
  ): Promise<bot_open_api.PublishDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/bot/publish');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      connector_ids: _req['connector_ids'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
