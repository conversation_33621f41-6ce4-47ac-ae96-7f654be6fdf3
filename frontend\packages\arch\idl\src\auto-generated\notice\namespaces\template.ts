/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as common from './common';

export type Int64 = string | number;

export enum TemplateStatus {
  /** 有效 */
  Valid = 1,
  /** 被删除 */
  Deleted = 2,
  /** 草稿 */
  Draft = 3,
  /** 审核中 */
  Review = 4,
}

export enum TemplateType {
  /** 手动发通知 */
  Manual = 1,
  /** 对外开放 */
  Open = 2,
}

export interface CreateRecordData {
  /** 工单id */
  RecordID?: number;
}

export interface CreateRecordRequest {
  /** 模板id */
  id?: string;
  /** 模板名称 */
  name: string;
  /** 模板内容 */
  content: string;
  /** 模板场景 */
  template_scene: string;
  /** bot id */
  bot_id: string;
  Base?: base.Base;
}

export interface CreateRecordResponse {
  code?: number;
  msg?: string;
  data?: CreateRecordData;
  BaseResp?: base.BaseResp;
}

export interface CreateTemplateData {
  /** 模板ID */
  template_id?: string;
}

export interface CreateTemplateRequest {
  /** 模板名称 */
  name: string;
  /** 模板内容 */
  content: string;
  /** 模板类型 */
  template_type: TemplateType;
  /** 模板状态 */
  template_status: TemplateStatus;
  /** 模板场景 */
  template_scene: string;
  /** bot id */
  bot_id: string;
}

export interface CreateTemplateResponse {
  code?: number;
  msg?: string;
  data?: CreateTemplateData;
}

export interface DeleteTemplateRequest {
  /** 模板id */
  id: string;
}

export interface DeleteTemplateResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
}

export interface GetTemplateListData {
  data?: Array<TemplateData>;
  total?: number;
}

export interface GetTemplateListRequest {
  page?: number;
  size?: number;
  template_type?: TemplateType;
  template_status?: TemplateStatus;
}

export interface GetTemplateListResponse {
  code?: number;
  msg?: string;
  data?: GetTemplateListData;
}

export interface TemplateData {
  /** 模板id */
  id?: string;
  /** 模板名称 */
  name?: string;
  /** 模板内容 */
  content?: string;
  /** 最近更改时间 */
  last_modify_time?: string;
  /** 最近更新人 */
  last_modify_user?: string;
  /** 模板类型 */
  template_type?: TemplateType;
  /** 模板状态 */
  template_status?: TemplateStatus;
  /** 模板场景 */
  template_scene?: string;
  /** bot id */
  bot_id?: string;
  bot_icon_url?: string;
  bot_name?: string;
  /** bpm审批ID */
  record_id?: Int64;
  /** 当前用户是否允许编辑 */
  allow_edit?: boolean;
}

export interface UpdateTemplateRequest {
  /** 模板id */
  id: string;
  /** 模板名称 */
  name?: string;
  /** 模板内容 */
  content?: string;
  /** 模板类型 */
  template_type: TemplateType;
  /** 模板状态 */
  template_status: TemplateStatus;
  /** 模板场景 */
  template_scene: string;
  /** bot id */
  bot_id: string;
}

export interface UpdateTemplateResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
}
/* eslint-enable */
