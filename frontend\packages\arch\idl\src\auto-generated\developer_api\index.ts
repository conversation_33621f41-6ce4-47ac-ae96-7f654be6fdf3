/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as acrosite from './namespaces/acrosite';
import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as bot_connector_manage from './namespaces/bot_connector_manage';
import * as bot_task_common from './namespaces/bot_task_common';
import * as bot_user_auth from './namespaces/bot_user_auth';
import * as common from './namespaces/common';
import * as data_connector from './namespaces/data_connector';
import * as data_connector_plugin from './namespaces/data_connector_plugin';
import * as dataset from './namespaces/dataset';
import * as developer_api from './namespaces/developer_api';
import * as document from './namespaces/document';
import * as permission from './namespaces/permission';
import * as plugin_common from './namespaces/plugin_common';
import * as product_common from './namespaces/product_common';
import * as profile_memory from './namespaces/profile_memory';
import * as shortcut_command from './namespaces/shortcut_command';
import * as slice from './namespaces/slice';
import * as table from './namespaces/table';

export {
  acrosite,
  base,
  bot_common,
  bot_connector_manage,
  bot_task_common,
  bot_user_auth,
  common,
  data_connector,
  data_connector_plugin,
  dataset,
  developer_api,
  document,
  permission,
  plugin_common,
  product_common,
  profile_memory,
  shortcut_command,
  slice,
  table,
};
export * from './namespaces/acrosite';
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/bot_connector_manage';
export * from './namespaces/bot_task_common';
export * from './namespaces/bot_user_auth';
export * from './namespaces/common';
export * from './namespaces/data_connector';
export * from './namespaces/data_connector_plugin';
export * from './namespaces/dataset';
export * from './namespaces/developer_api';
export * from './namespaces/document';
export * from './namespaces/permission';
export * from './namespaces/plugin_common';
export * from './namespaces/product_common';
export * from './namespaces/profile_memory';
export * from './namespaces/shortcut_command';
export * from './namespaces/slice';
export * from './namespaces/table';

export type Int64 = string | number;

export default class DeveloperApiService<T> {
  private request: any = () => {
    throw new Error('DeveloperApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/playground/add_task */
  AddTask(
    req?: developer_api.AddTaskRequest,
    options?: T,
  ): Promise<developer_api.AddTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/add_task');
    const method = 'POST';
    const data = {
      name: _req['name'],
      target: _req['target'],
      task_type: _req['task_type'],
      bot_id: _req['bot_id'],
      bot_name: _req['bot_name'],
      bot_desc_to_human: _req['bot_desc_to_human'],
      bot_uri: _req['bot_uri'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/submit_task */
  SubmitTask(
    req?: developer_api.SubmitTaskRequest,
    options?: T,
  ): Promise<developer_api.SubmitTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/submit_task');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      model: _req['model'],
      temperature: _req['temperature'],
      max_tokens: _req['max_tokens'],
      top_p: _req['top_p'],
      frequency_penalty: _req['frequency_penalty'],
      presence_penalty: _req['presence_penalty'],
      messages: _req['messages'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/alter */
  AlterTable(
    req: table.AlterTableRequest,
    options?: T,
  ): Promise<table.AlterTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/table/alter');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      table_id: _req['table_id'],
      name: _req['name'],
      desc: _req['desc'],
      field_list: _req['field_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/info/query */
  TableInfoQuery(
    req?: table.TableInfoQueryRequest,
    options?: T,
  ): Promise<table.TableInfoQueryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/table/info/query');
    const method = 'POST';
    const data = {
      keyword: _req['keyword'],
      bot_id: _req['bot_id'],
      table_id: _req['table_id'],
      offset: _req['offset'],
      limit: _req['limit'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/info/update */
  TableInfoUpdate(
    req?: table.TableInfoUpdateRequest,
    options?: T,
  ): Promise<table.TableInfoUpdateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/table/info/update');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      table_id: _req['table_id'],
      data_list: _req['data_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/get_sso_user_info */
  GetSSOUserInfo(
    req?: developer_api.GetSSOUserInfoRequest,
    options?: T,
  ): Promise<developer_api.GetSSOUserInfoResponse> {
    const url = this.genBaseURL('/api/developer/get_sso_user_info');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/dataset/delete */
  DeleteDataSet(
    req?: developer_api.DeleteDataSetRequest,
    options?: T,
  ): Promise<developer_api.DeleteDataSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/delete');
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/info/delete */
  TableInfoDelete(
    req?: table.TableInfoDeleteRequest,
    options?: T,
  ): Promise<table.TableInfoDeleteResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/table/info/delete');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      table_id: _req['table_id'],
      data_ids: _req['data_ids'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/revert_playground_record */
  RevertPlaygroundRecord(
    req?: developer_api.RevertPlaygroundRecordRequest,
    options?: T,
  ): Promise<developer_api.RevertPlaygroundRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/revert_playground_record');
    const method = 'POST';
    const data = { version: _req['version'], task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/update_bot */
  UpdateBot(
    req: developer_api.UpdateBotRequest,
    options?: T,
  ): Promise<developer_api.UpdateBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/update_bot');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      name: _req['name'],
      description_for_human: _req['description_for_human'],
      icon_uri: _req['icon_uri'],
      delete: _req['delete'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/bind_card */
  BindCard(
    req?: developer_api.BindCardRequest,
    options?: T,
  ): Promise<developer_api.BindCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/bind_card');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      card_id: _req['card_id'],
      bot_id: _req['bot_id'],
      mapping_rule: _req['mapping_rule'],
      max_display_rows: _req['max_display_rows'],
      card_version_num: _req['card_version_num'],
      agent_id: _req['agent_id'],
      llm_text_card: _req['llm_text_card'],
      biz_type: _req['biz_type'],
      business_id: _req['business_id'],
      unique_id: _req['unique_id'],
      plugin_preset_card_selected: _req['plugin_preset_card_selected'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/save_plugin */
  SavePlugin(
    req?: developer_api.SavePluginRequest,
    options?: T,
  ): Promise<developer_api.SavePluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/save_plugin');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      item_infos: _req['item_infos'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/get_playground_plugins */
  GetPlaygroundPluginList(
    req: developer_api.GetPlaygroundPluginListRequest,
    options?: T,
  ): Promise<developer_api.GetPlaygroundPluginListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/playground/get_playground_plugins');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      name: _req['name'],
      plugin_ids: _req['plugin_ids'],
      plugin_tag: _req['plugin_tag'],
      self_created: _req['self_created'],
      plugin_type: _req['plugin_type'],
      space_id: _req['space_id'],
      order_by: _req['order_by'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/submit_bot_task */
  SubmitBotTask(
    req?: developer_api.SubmitBotTaskRequest,
    options?: T,
  ): Promise<developer_api.SubmitBotTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/submit_bot_task');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      model_info: _req['model_info'],
      plugin_apis: _req['plugin_apis'],
      bot_prompts: _req['bot_prompts'],
      messages: _req['messages'],
      device_id: _req['device_id'],
      push_uuid: _req['push_uuid'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/get_plugin_tags */
  GetPluginTags(
    req?: developer_api.GetPluginTagsRequest,
    options?: T,
  ): Promise<developer_api.GetPluginTagsResponse> {
    const url = this.genBaseURL('/api/playground/get_plugin_tags');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/bot/get_bot_list */
  GetBotList(
    req?: developer_api.GetBotListRequest,
    options?: T,
  ): Promise<developer_api.GetBotListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/get_bot_list');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      recommend: _req['recommend'],
      bot_ids: _req['bot_ids'],
      bot_types: _req['bot_types'],
      bot_name: _req['bot_name'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      bot_status: _req['bot_status'],
      order_by: _req['order_by'],
      GetBotListMode: _req['GetBotListMode'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/update_meta */
  UpdateDataSetMeta(
    req?: developer_api.UpdateDataSetMetaRequest,
    options?: T,
  ): Promise<developer_api.UpdateDataSetMetaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/update_meta');
    const method = 'POST';
    const data = {
      id: _req['id'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card/get_api_resp_struct
   *
   * ---------------------GUI card---------------------------------
   */
  GetApiRespStruct(
    req?: developer_api.GetAPIRespStructRequest,
    options?: T,
  ): Promise<developer_api.GetAPIRespStructResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_api_resp_struct');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/playground/list_playground_history
   *
   * ---------------------playground---------------------------------
   */
  ListPlaygroundHistoryInfo(
    req?: developer_api.ListPlaygroundHistoryInfoRequest,
    options?: T,
  ): Promise<developer_api.ListPlaygroundHistoryInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/list_playground_history');
    const method = 'POST';
    const data = { task_name: _req['task_name'], task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/developer/register
   *
   * -----------------------plugin-----------------------------------
   */
  RegisterPlugin(
    req?: developer_api.RegisterPluginRequest,
    options?: T,
  ): Promise<developer_api.RegisterPluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/register');
    const method = 'POST';
    const data = {
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      client_id: _req['client_id'],
      client_secret: _req['client_secret'],
      service_token: _req['service_token'],
      plugin_type: _req['plugin_type'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/update */
  UpdatePlugin(
    req?: developer_api.UpdatePluginRequest,
    options?: T,
  ): Promise<developer_api.UpdatePluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/update');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      ai_plugin: _req['ai_plugin'],
      openapi: _req['openapi'],
      client_id: _req['client_id'],
      client_secret: _req['client_secret'],
      service_token: _req['service_token'],
      source_code: _req['source_code'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/developer/get_profile_memory
   *
   * ---------------------memory---------------------------------
   */
  GetProfileMemory(
    req?: profile_memory.GetProfileMemoryRequest,
    options?: T,
  ): Promise<profile_memory.GetProfileMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/get_profile_memory');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      task_id: _req['task_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/query_web_info */
  QueryWebInfo(
    req?: developer_api.QueryWebInfoRequest,
    options?: T,
  ): Promise<developer_api.QueryWebInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/query_web_info');
    const method = 'POST';
    const data = {
      web_id: _req['web_id'],
      include_content: _req['include_content'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/submit_web_url */
  SubmitWebUrl(
    req?: developer_api.SubmitWebUrlRequest,
    options?: T,
  ): Promise<developer_api.SubmitWebUrlResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/submit_web_url');
    const method = 'POST';
    const data = {
      web_url: _req['web_url'],
      subpages_count: _req['subpages_count'],
      format_type: _req['format_type'],
      title: _req['title'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/list */
  ListTable(
    req: table.ListTableRequest,
    options?: T,
  ): Promise<table.ListTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/table/list');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], table_id: _req['table_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/doaction */
  DoAction(
    req: developer_api.DoActionRequest,
    options?: T,
  ): Promise<developer_api.DoActionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/developer/doaction');
    const method = 'POST';
    const data = {
      PluginID: _req['PluginID'],
      APIName: _req['APIName'],
      BotId: _req['BotId'],
      Parameters: _req['Parameters'],
      MessageID: _req['MessageID'],
      PluginName: _req['PluginName'],
      DeviceID: _req['DeviceID'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/get_bot_info */
  GetBotInfo(
    req?: developer_api.GetBotInfoRequest,
    options?: T,
  ): Promise<developer_api.GetBotInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/get_bot_info');
    const method = 'POST';
    const data = { Version: _req['Version'], BotID: _req['BotID'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/get_type_list */
  GetTypeList(
    req?: developer_api.GetTypeListRequest,
    options?: T,
  ): Promise<developer_api.GetTypeListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/get_type_list');
    const method = 'POST';
    const data = {
      model: _req['model'],
      voice: _req['voice'],
      raw_model: _req['raw_model'],
      space_id: _req['space_id'],
      cur_model_id: _req['cur_model_id'],
      cur_model_ids: _req['cur_model_ids'],
      model_scene: _req['model_scene'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_card_bind */
  GetCardBind(
    req?: developer_api.GetCardBindRequest,
    options?: T,
  ): Promise<developer_api.GetCardBindResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_card_bind');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_name: _req['api_name'],
      bot_id: _req['bot_id'],
      agent_id: _req['agent_id'],
      biz_type: _req['biz_type'],
      business_id: _req['business_id'],
      unique_id: _req['unique_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/get_playground_record */
  GetPlaygroundRecord(
    req?: developer_api.GetPlaygroundRecordRequest,
    options?: T,
  ): Promise<developer_api.GetPlaygroundRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/get_playground_record');
    const method = 'POST';
    const data = {
      version: _req['version'],
      task_id: _req['task_id'],
      task_name: _req['task_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/process_document */
  ProcessDocumentsTask(
    req?: developer_api.ProcessDocumentsTaskRequest,
    options?: T,
  ): Promise<developer_api.ProcessDocumentsTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/process_document');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_info: _req['document_info'],
      rule: _req['rule'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/submit_web_content */
  SubmitWebContent(
    req?: developer_api.SubmitWebContentRequest,
    options?: T,
  ): Promise<developer_api.SubmitWebContentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/submit_web_content');
    const method = 'POST';
    const data = { web_id: _req['web_id'], content: _req['content'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/playground/user/launch
   *
   * ---------------------frontier---------------------------------
   */
  Launch(
    req?: developer_api.LaunchRequest,
    options?: T,
  ): Promise<developer_api.LaunchResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/user/launch');
    const method = 'POST';
    const data = { device_id: _req['device_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/delete */
  DeleteTable(
    req: table.DeleteTableRequest,
    options?: T,
  ): Promise<table.DeleteTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/table/delete');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], table_id: _req['table_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/logout */
  Logout(
    req?: developer_api.LogoutRequest,
    options?: T,
  ): Promise<developer_api.LogoutResponse> {
    const url = this.genBaseURL('/api/developer/logout');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/developer/get_plugin_list */
  GetPluginList(
    req?: developer_api.GetPluginListRequest,
    options?: T,
  ): Promise<developer_api.GetPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/get_plugin_list');
    const method = 'POST';
    const data = {
      status: _req['status'],
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      scope_type: _req['scope_type'],
      order_by: _req['order_by'],
      publish_status: _req['publish_status'],
      name: _req['name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/del_plugin */
  DelPlugin(
    req?: developer_api.DelPluginRequest,
    options?: T,
  ): Promise<developer_api.DelPluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/del_plugin');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/upload_file
   *
   * ---------------------bot---------------------------------
   */
  UploadFile(
    req?: developer_api.UploadFileRequest,
    options?: T,
  ): Promise<developer_api.UploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/upload_file');
    const method = 'POST';
    const data = { file_head: _req['file_head'], data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/dataset/create_dataset
   *
   * ---------------------dataset---------------------------------
   */
  CreateDataSet(
    req?: developer_api.CreateDataSetRequest,
    options?: T,
  ): Promise<developer_api.CreateDataSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/create_dataset');
    const method = 'POST';
    const data = {
      name: _req['name'],
      space_id: _req['space_id'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/get_icon */
  GetIcon(
    req?: developer_api.GetIconRequest,
    options?: T,
  ): Promise<developer_api.GetIconResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/get_icon');
    const method = 'POST';
    const data = { icon_type: _req['icon_type'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/plugin_debug_run */
  PluginDebugRun(
    req: developer_api.PluginDebugRunRequest,
    options?: T,
  ): Promise<developer_api.PluginDebugRunResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/developer/plugin_debug_run');
    const method = 'POST';
    const data = {
      source_code: _req['source_code'],
      input_params: _req['input_params'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/duplicate_task */
  DuplicateTask(
    req?: developer_api.DuplicateTaskRequest,
    options?: T,
  ): Promise<developer_api.DuplicateTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/duplicate_task');
    const method = 'POST';
    const data = { task_id: _req['task_id'], task_name: _req['task_name'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/get_plugin_current_info */
  GetPluginCurrentInfo(
    req: developer_api.GetPluginCurrentRequest,
    options?: T,
  ): Promise<developer_api.GetPluginCurrentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/developer/get_plugin_current_info');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/del_profile_memory */
  DelProfileMemory(
    req?: profile_memory.DelProfileMemoryRequest,
    options?: T,
  ): Promise<profile_memory.DelProfileMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/del_profile_memory');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], keywords: _req['keywords'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/get_progress */
  GetTaskProgress(
    req?: developer_api.GetTaskProgressRequest,
    options?: T,
  ): Promise<developer_api.GetTaskProgressResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/get_progress');
    const method = 'POST';
    const data = { document_ids: _req['document_ids'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/save_playground */
  SavePlaygroundRecord(
    req?: developer_api.SavePlaygroundRecordRequest,
    options?: T,
  ): Promise<developer_api.SavePlaygroundRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/save_playground');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      item_infos: _req['item_infos'],
      playground_history_info: _req['playground_history_info'],
      task_name: _req['task_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/list_dataset */
  ListDateSet(
    req?: developer_api.ListDataSetRequest,
    options?: T,
  ): Promise<developer_api.ListDataSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/list_dataset');
    const method = 'POST';
    const data = {
      query: _req['query'],
      search_type: _req['search_type'],
      dataset_ids: _req['dataset_ids'],
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      scope_type: _req['scope_type'],
      source_type: _req['source_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/process_web_document */
  ProcessWebDocumentsTask(
    req?: developer_api.ProcessWebDocumentsTaskRequest,
    options?: T,
  ): Promise<developer_api.ProcessWebDocumentsTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/process_web_document');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_info: _req['document_info'],
      formatType: _req['formatType'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/share_bot */
  ShareBot(
    req: developer_api.ShareBotRequest,
    options?: T,
  ): Promise<developer_api.ShareBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/share_bot');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], operation: _req['operation'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/get_onboarding */
  GetOnboarding(
    req?: developer_api.GetOnboardingRequest,
    options?: T,
  ): Promise<developer_api.GetOnboardingResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/get_onboarding');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], bot_prompt: _req['bot_prompt'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/duplicate */
  DuplicateBot(
    req?: developer_api.DuplicateBotRequest,
    options?: T,
  ): Promise<developer_api.DuplicateBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/duplicate');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/table/add
   *
   * ---------------------table---------------------------------
   */
  AddTable(
    req: table.AddTableRequest,
    options?: T,
  ): Promise<table.AddTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/table/add');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      name: _req['name'],
      desc: _req['desc'],
      field_list: _req['field_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/table/info/add */
  TableInfoAdd(
    req?: table.TableInfoAddRequest,
    options?: T,
  ): Promise<table.TableInfoAddResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/table/info/add');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      table_id: _req['table_id'],
      data_list: _req['data_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/workflow/node_templage_list
   *
   * ---------------------workflow---------------------------------
   */
  NodeTemplateList(
    req?: developer_api.NodeTemplateListRequest,
    options?: T,
  ): Promise<developer_api.NodeTemplateListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflow/node_templage_list');
    const method = 'POST';
    const data = { need_types: _req['need_types'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/map_data */
  MapData(
    req: developer_api.MapDataRequest,
    options?: T,
  ): Promise<developer_api.MapDataResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/map_data');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      node_id: _req['node_id'],
      param_type: _req['param_type'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/workflow/get_process */
  GetWorkFlowProcess(
    req?: developer_api.GetWorkFlowProcessReq,
    options?: T,
  ): Promise<developer_api.GetWorkFlowProcessResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflow/get_process');
    const method = 'GET';
    const params = {
      executeId: _req['executeId'],
      workflowId: _req['workflowId'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/workflow/list */
  WorkFlowList(
    req?: developer_api.WorkFlowListRequest,
    options?: T,
  ): Promise<developer_api.WorkFlowListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflow/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      workflow_ids: _req['workflow_ids'],
      with_node: _req['with_node'],
      type: _req['type'],
      name: _req['name'],
      tags: _req['tags'],
      space_id: _req['space_id'],
      status: _req['status'],
      order_by: _req['order_by'],
      login_user_create: _req['login_user_create'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/run */
  RunWorkFlow(
    req?: developer_api.RunWorkFlowRequest,
    options?: T,
  ): Promise<developer_api.RunWorkFlowResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflow/run');
    const method = 'POST';
    const data = {
      workflowId: _req['workflowId'],
      input: _req['input'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/update_node */
  UpdateNode(
    req: developer_api.UpdateNodeRequest,
    options?: T,
  ): Promise<developer_api.UpdateNodeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/update_node');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      node_id: _req['node_id'],
      name: _req['name'],
      node_param: _req['node_param'],
      layout: _req['layout'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/update */
  UpdateWorkFlow(
    req: developer_api.UpdateWorkFlowRequest,
    options?: T,
  ): Promise<developer_api.UpdateWorkFlowResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/update');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      name: _req['name'],
      desc: _req['desc'],
      icon_uri: _req['icon_uri'],
      delete: _req['delete'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/node_list */
  NodeList(
    req: developer_api.NodeListRequest,
    options?: T,
  ): Promise<developer_api.NodeListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/node_list');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      node_ids: _req['node_ids'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/publish */
  WorkFlowPublish(
    req: developer_api.WorkFlowPublishRequest,
    options?: T,
  ): Promise<developer_api.WorkFlowPublishResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/publish');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/create */
  CreateWorkFlow(
    req: developer_api.CreateWorkFlowRequest,
    options?: T,
  ): Promise<developer_api.CreateWorkFlowResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      desc: _req['desc'],
      icon_uri: _req['icon_uri'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/create_node */
  CreateNode(
    req: developer_api.CreateNodeRequest,
    options?: T,
  ): Promise<developer_api.CreateNodeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/create_node');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      type: _req['type'],
      api_info: _req['api_info'],
      layout: _req['layout'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/delete_node */
  DeleteNode(
    req: developer_api.DeleteNodeRequest,
    options?: T,
  ): Promise<developer_api.DeleteNodeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/delete_node');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      node_id: _req['node_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/update_link */
  UpdateLink(
    req: developer_api.UpdateLinkRequest,
    options?: T,
  ): Promise<developer_api.UpdateLinkResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/update_link');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      from_node_id: _req['from_node_id'],
      to_node_id: _req['to_node_id'],
      type: _req['type'],
      if_node_branch: _req['if_node_branch'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/add_favorite_bot */
  AddFavoriteBot(
    req: developer_api.AddFavoriteBotRequest,
    options?: T,
  ): Promise<developer_api.AddFavoriteBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/add_favorite_bot');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], fav_status: _req['fav_status'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/get_user_bot_fav */
  GetUserBotFavorite(
    req: developer_api.GetUserBotFavoriteRequest,
    options?: T,
  ): Promise<developer_api.GetUserBotFavoriteResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/get_user_bot_fav');
    const method = 'POST';
    const data = { page: _req['page'], size: _req['size'], name: _req['name'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/cancel */
  CancelWorkFlow(
    req?: developer_api.CancelWorkFlowRequest,
    options?: T,
  ): Promise<developer_api.CancelWorkFlowResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflow/cancel');
    const method = 'POST';
    const data = { executeId: _req['executeId'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/delete_card */
  DeleteCard(
    req?: developer_api.DeleteCardRequest,
    options?: T,
  ): Promise<developer_api.DeleteCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/delete_card');
    const method = 'POST';
    const data = { card_id: _req['card_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/remove_task */
  RemoveTask(
    req: developer_api.RemoveTaskRequest,
    options?: T,
  ): Promise<developer_api.RemoveTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/remove_task');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/task/create_preset_task
   *
   * ---------------------task------------------------------
   */
  CreatePresetTask(
    req: developer_api.CreatePresetTaskRequest,
    options?: T,
  ): Promise<developer_api.CreatePresetTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/create_preset_task');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      cron_expr: _req['cron_expr'],
      content: _req['content'],
      user_question: _req['user_question'],
      time_zone: _req['time_zone'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/task_list */
  TaskList(
    req: developer_api.TaskListRequest,
    options?: T,
  ): Promise<developer_api.TaskListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/task_list');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      source: _req['source'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/get_task_intro */
  GetTaskIntro(
    req?: developer_api.GetTaskIntroRequest,
    options?: T,
  ): Promise<developer_api.GetTaskIntroResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/task/get_task_intro');
    const method = 'POST';
    const data = {
      system_prompt: _req['system_prompt'],
      suggested_questions: _req['suggested_questions'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/upload/auth_token */
  GetUploadAuthToken(
    req?: developer_api.GetUploadAuthTokenRequest,
    options?: T,
  ): Promise<developer_api.GetUploadAuthTokenResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/playground/upload/auth_token');
    const method = 'POST';
    const data = { scene: _req['scene'], data_type: _req['data_type'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/register_plugin_meta */
  RegisterPluginMeta(
    req: developer_api.RegisterPluginMetaRequest,
    options?: T,
  ): Promise<developer_api.RegisterPluginMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/register_plugin_meta');
    const method = 'POST';
    const data = {
      name: _req['name'],
      desc: _req['desc'],
      url: _req['url'],
      icon: _req['icon'],
      auth_type: _req['auth_type'],
      location: _req['location'],
      key: _req['key'],
      service_token: _req['service_token'],
      oauth_info: _req['oauth_info'],
      space_id: _req['space_id'],
      common_params: _req['common_params'],
      creation_method: _req['creation_method'],
      ide_code_runtime: _req['ide_code_runtime'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/debug_api */
  DebugAPI(
    req: developer_api.DebugAPIRequest,
    options?: T,
  ): Promise<developer_api.DebugAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/debug_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      parameters: _req['parameters'],
      operation: _req['operation'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/publish_plugin */
  PublishPlugin(
    req: developer_api.PublishPluginRequest,
    options?: T,
  ): Promise<developer_api.PublishPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/publish_plugin');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/update_plugin_meta */
  UpdatePluginMeta(
    req: developer_api.UpdatePluginMetaRequest,
    options?: T,
  ): Promise<developer_api.UpdatePluginMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/update_plugin_meta');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      name: _req['name'],
      desc: _req['desc'],
      url: _req['url'],
      icon: _req['icon'],
      auth_type: _req['auth_type'],
      location: _req['location'],
      key: _req['key'],
      service_token: _req['service_token'],
      oauth_info: _req['oauth_info'],
      common_params: _req['common_params'],
      creation_method: _req['creation_method'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/update_api */
  UpdateAPI(
    req: developer_api.UpdateAPIRequest,
    options?: T,
  ): Promise<developer_api.UpdateAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/update_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_id: _req['api_id'],
      name: _req['name'],
      desc: _req['desc'],
      path: _req['path'],
      method: _req['method'],
      request_params: _req['request_params'],
      response_params: _req['response_params'],
      disabled: _req['disabled'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/create_api */
  CreateAPI(
    req: developer_api.CreateAPIRequest,
    options?: T,
  ): Promise<developer_api.CreateAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/create_api');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      name: _req['name'],
      desc: _req['desc'],
      path: _req['path'],
      method: _req['method'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/get_plugin_info */
  GetPluginInfo(
    req: developer_api.GetPluginInfoRequest,
    options?: T,
  ): Promise<developer_api.GetPluginInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/get_plugin_info');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/copy_from_template */
  CopyFromTemplate(
    req: developer_api.CopyFromTemplateRequest,
    options?: T,
  ): Promise<developer_api.CopyFromTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/copy_from_template');
    const method = 'POST';
    const data = {
      template_workflow_id: _req['template_workflow_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/template_tag */
  WorkFlowTemplateTag(
    req?: developer_api.WorkFlowTemplateTagRequest,
    options?: T,
  ): Promise<developer_api.WorkFlowTemplateTagResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflow/template_tag');
    const method = 'POST';
    const data = { flow_mode: _req['flow_mode'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/get_oauth_schema */
  GetOAuthSchema(
    req?: developer_api.GetOAuthSchemaRequest,
    options?: T,
  ): Promise<developer_api.GetOAuthSchemaResponse> {
    const url = this.genBaseURL('/api/plugin/get_oauth_schema');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** GET /api/dataset/slice/list */
  GetSliceList(
    req?: slice.GetSliceListReq,
    options?: T,
  ): Promise<slice.GetSliceListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/slice/list');
    const method = 'GET';
    const params = {
      doc_id: _req['doc_id'],
      sequence: _req['sequence'],
      key_word: _req['key_word'],
      page_no: _req['page_no'],
      page_size: _req['page_size'],
      sort_field: _req['sort_field'],
      is_asc: _req['is_asc'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/dataset/get_bot */
  GetBotListByDataset(
    req: dataset.GetBotListByDatasetReq,
    options?: T,
  ): Promise<dataset.GetBotListByDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/dataset/get_bot');
    const method = 'GET';
    const params = {
      dataset_id: _req['dataset_id'],
      page_size: _req['page_size'],
      page_no: _req['page_no'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/dataset/slice/del */
  DelSlice(req: slice.DelSliceReq, options?: T): Promise<base.EmptyResp> {
    const _req = req;
    const url = this.genBaseURL('/api/dataset/slice/del');
    const method = 'POST';
    const data = { slice_id: _req['slice_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/slice/update */
  UpdateSliceContent(
    req: slice.UpdateSliceContentReq,
    options?: T,
  ): Promise<base.EmptyResp> {
    const _req = req;
    const url = this.genBaseURL('/api/dataset/slice/update');
    const method = 'POST';
    const data = { slice_id: _req['slice_id'], content: _req['content'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/update_document */
  UpdateDocument(
    req?: document.UpdateDocumentRequest,
    options?: T,
  ): Promise<document.UpdateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/update_document');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      status: _req['status'],
      document_name: _req['document_name'],
      table_meta: _req['table_meta'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/update_web_rule */
  UpdateWebRule(
    req?: document.UpdateWebRuleRequest,
    options?: T,
  ): Promise<document.UpdateWebRuleResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/update_web_rule');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      update_type: _req['update_type'],
      update_interval: _req['update_interval'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/list_document */
  ListDocument(
    req?: document.ListDocumentRequest,
    options?: T,
  ): Promise<document.ListDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/list_document');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      page: _req['page'],
      size: _req['size'],
      document_id: _req['document_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/member/add */
  AddBotSpaceMember(
    req: developer_api.AddSpaceMemberRequest,
    options?: T,
  ): Promise<developer_api.AddSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/member/add');
    const method = 'POST';
    const data = {
      member_info_list: _req['member_info_list'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/delete */
  DeleteSpace(
    req: developer_api.DeleteSpaceRequest,
    options?: T,
  ): Promise<developer_api.DeleteSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/delete');
    const method = 'POST';
    const data = { space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/list */
  SpaceList(
    req?: developer_api.SpaceListRequest,
    options?: T,
  ): Promise<developer_api.SpaceListResponse> {
    const url = this.genBaseURL('/api/space/list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/space/member/exit */
  ExitSpace(
    req?: developer_api.ExitSpaceRequest,
    options?: T,
  ): Promise<developer_api.ExitSpaceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/space/member/exit');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      transfer_user_id: _req['transfer_user_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/member/update */
  UpdateSpaceMember(
    req?: developer_api.UpdateSpaceMemberRequest,
    options?: T,
  ): Promise<developer_api.UpdateSpaceMemberResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/space/member/update');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      user_id: _req['user_id'],
      space_role_type: _req['space_role_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/member/remove */
  RemoveSpaceMember(
    req?: developer_api.RemoveSpaceMemberRequest,
    options?: T,
  ): Promise<developer_api.RemoveSpaceMemberResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/space/member/remove');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      remove_user_id: _req['remove_user_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/member/transfer */
  TransferSpace(
    req?: developer_api.TransferSpaceRequest,
    options?: T,
  ): Promise<developer_api.TransferSpaceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/space/member/transfer');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      transfer_user_id: _req['transfer_user_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/update */
  UpdateSpace(
    req: developer_api.UpdateSpaceRequest,
    options?: T,
  ): Promise<developer_api.UpdateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/update');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space/create
   *
   * ---------------------space------------------------------
   */
  CreateSpace(
    req: developer_api.CreateSpaceRequest,
    options?: T,
  ): Promise<developer_api.CreateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
      space_type: _req['space_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/member/search */
  SearchMember(
    req: developer_api.SearchMemberRequest,
    options?: T,
  ): Promise<developer_api.SearchMemberResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/member/search');
    const method = 'POST';
    const data = {
      search_list: _req['search_list'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/create */
  DraftBotCreate(
    req: developer_api.DraftBotCreateRequest,
    options?: T,
  ): Promise<developer_api.DraftBotCreateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/create');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
      visibility: _req['visibility'],
      monetization_conf: _req['monetization_conf'],
      create_from: _req['create_from'],
      app_id: _req['app_id'],
      business_type: _req['business_type'],
      folder_id: _req['folder_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/get_bot_info */
  GetDraftBotInfo(
    req: developer_api.GetDraftBotInfoRequest,
    options?: T,
  ): Promise<developer_api.GetDraftBotInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/get_bot_info');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      version: _req['version'],
      source: _req['source'],
      botMode: _req['botMode'],
      commit_version: _req['commit_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/update */
  UpdateDraftBot(
    req: developer_api.UpdateDraftBotRequest,
    options?: T,
  ): Promise<developer_api.UpdateDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/update');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      work_info: _req['work_info'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
      visibility: _req['visibility'],
      update_agents: _req['update_agents'],
      canvas_data: _req['canvas_data'],
      bot_mode: _req['bot_mode'],
      delete_agents: _req['delete_agents'],
      bot_tag_info: _req['bot_tag_info'],
      filebox_info: _req['filebox_info'],
      base_commit_version: _req['base_commit_version'],
      version_compat: _req['version_compat'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/delete */
  DeleteDraftBot(
    req: developer_api.DeleteDraftBotRequest,
    options?: T,
  ): Promise<developer_api.DeleteDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/delete');
    const method = 'POST';
    const data = { space_id: _req['space_id'], bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/duplicate */
  DuplicateDraftBot(
    req: developer_api.DuplicateDraftBotRequest,
    options?: T,
  ): Promise<developer_api.DuplicateDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/duplicate');
    const method = 'POST';
    const data = { space_id: _req['space_id'], bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/revert */
  RevertDraftBot(
    req: developer_api.RevertDraftBotRequest,
    options?: T,
  ): Promise<developer_api.RevertDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/revert');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      version: _req['version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/list_draft_history */
  ListDraftBotHistory(
    req: developer_api.ListDraftBotHistoryRequest,
    options?: T,
  ): Promise<developer_api.ListDraftBotHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/list_draft_history');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      history_type: _req['history_type'],
      connector_id: _req['connector_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/execute */
  ExecuteDraftBot(
    req: developer_api.ExecuteDraftBotRequest,
    options?: T,
  ): Promise<developer_api.ExecuteDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/execute');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      work_info: _req['work_info'],
      device_id: _req['device_id'],
      push_uuid: _req['push_uuid'],
      source: _req['source'],
      online_mode: _req['online_mode'],
      bot_version: _req['bot_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/publish */
  PublishDraftBot(
    req: developer_api.PublishDraftBotRequest,
    options?: T,
  ): Promise<developer_api.PublishDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/publish');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      work_info: _req['work_info'],
      connector_list: _req['connector_list'],
      connectors: _req['connectors'],
      botMode: _req['botMode'],
      agents: _req['agents'],
      canvas_data: _req['canvas_data'],
      bot_tag_info: _req['bot_tag_info'],
      submit_bot_market_config: _req['submit_bot_market_config'],
      publish_id: _req['publish_id'],
      commit_version: _req['commit_version'],
      publish_type: _req['publish_type'],
      pre_publish_ext: _req['pre_publish_ext'],
      history_info: _req['history_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/draftbot/get_draft_bot_list
   *
   * ---------------------bot draft---------------------------------
   */
  GetDraftBotList(
    req: developer_api.GetDraftBotListRequest,
    options?: T,
  ): Promise<developer_api.GetDraftBotListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/get_draft_bot_list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_name: _req['bot_name'],
      order_by: _req['order_by'],
      publish_platform: _req['publish_platform'],
      team_bot_type: _req['team_bot_type'],
      scope_type: _req['scope_type'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      is_publish: _req['is_publish'],
      cursor_id: _req['cursor_id'],
      is_fav: _req['is_fav'],
      draft_bot_status_list: _req['draft_bot_status_list'],
      recently_open: _req['recently_open'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/delete_api */
  DeleteAPI(
    req: developer_api.DeleteAPIRequest,
    options?: T,
  ): Promise<developer_api.DeleteAPIResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/delete_api');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'], api_id: _req['api_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/get_plugin_apis */
  GetPluginAPIs(
    req: developer_api.GetPluginAPIsRequest,
    options?: T,
  ): Promise<developer_api.GetPluginAPIsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/get_plugin_apis');
    const method = 'POST';
    const data = {
      plugin_id: _req['plugin_id'],
      api_ids: _req['api_ids'],
      page: _req['page'],
      size: _req['size'],
      order: _req['order'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/role/auth */
  SpaceRoleAuth(
    req?: developer_api.SpaceRoleAuthRequest,
    options?: T,
  ): Promise<developer_api.SpaceRoleAuthResponse> {
    const url = this.genBaseURL('/api/space/role/auth');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/dataset/slice/update_status */
  ChangeSliceStatus(
    req: slice.ChangeSliceStatusReq,
    options?: T,
  ): Promise<base.EmptyResp> {
    const _req = req;
    const url = this.genBaseURL('/api/dataset/slice/update_status');
    const method = 'POST';
    const data = { slice_id: _req['slice_id'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/get_updated_apis */
  GetUpdatedAPIs(
    req: developer_api.GetUpdatedAPIsRequest,
    options?: T,
  ): Promise<developer_api.GetUpdatedAPIsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/plugin/get_updated_apis');
    const method = 'POST';
    const data = { plugin_id: _req['plugin_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/member/detail */
  SpaceMemberDetail(
    req?: developer_api.SpaceMemberDetailRequest,
    options?: T,
  ): Promise<developer_api.SpaceMemberDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/space/member/detail');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      search_word: _req['search_word'],
      space_role_type: _req['space_role_type'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/wk_execute_pre_check */
  WKExecutePreCheck(
    req: developer_api.WKExecutePreCheckRequest,
    options?: T,
  ): Promise<developer_api.WKExecutePreCheckResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/wk_execute_pre_check');
    const method = 'POST';
    const data = { workflow_id: _req['workflow_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/get_workflow_plugins */
  GetWorkflowPluginList(
    req: developer_api.GetWorkflowPluginListRequest,
    options?: T,
  ): Promise<developer_api.GetWorkflowPluginListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/get_workflow_plugins');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      name: _req['name'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflow/copy_node */
  CopyNode(
    req: developer_api.CopyNodeRequest,
    options?: T,
  ): Promise<developer_api.CopyNodeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflow/copy_node');
    const method = 'POST';
    const data = { node_id: _req['node_id'], layout: _req['layout'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/explore/get_explore_list
   *
   * ---------------------explore---------------------------------
   */
  GetExploreBotList(
    req?: developer_api.GetExploreBotListRequest,
    options?: T,
  ): Promise<developer_api.GetExploreBotListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/explore/get_explore_list');
    const method = 'POST';
    const data = {
      publish_platform: _req['publish_platform'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      key_word: _req['key_word'],
      category_id: _req['category_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/explore/duplicate
   *
   * GetExploreBotInfoResponse GetExploreBotInfo(1:GetExploreBotInfoRequest request)(api.post='/api/explore/get_explore_bot', api.category="explore", api.gen_path="explore")
   */
  DuplicateBotToSpace(
    req: developer_api.DuplicateBotToSpaceRequest,
    options?: T,
  ): Promise<developer_api.DuplicateBotToSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/explore/duplicate');
    const method = 'POST';
    const data = {
      target_space_id: _req['target_space_id'],
      draft_bot_id: _req['draft_bot_id'],
      name: _req['name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/user/update_profile_check */
  UpdateUserProfileCheck(
    req?: developer_api.UpdateUserProfileCheckRequest,
    options?: T,
  ): Promise<developer_api.UpdateUserProfileCheckResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/update_profile_check');
    const method = 'POST';
    const data = { user_unique_name: _req['user_unique_name'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/update_profile
   *
   * 开发者profile
   */
  UpdateUserProfile(
    req?: developer_api.UpdateUserProfileRequest,
    options?: T,
  ): Promise<developer_api.UpdateUserProfileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/update_profile');
    const method = 'POST';
    const data = {
      user_unique_name: _req['user_unique_name'],
      name: _req['name'],
      avatar: _req['avatar'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/invite */
  InviteMemberLink(
    req: developer_api.InviteMemberLinkRequest,
    options?: T,
  ): Promise<developer_api.InviteMemberLinkResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/invite');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      team_invite_link_status: _req['team_invite_link_status'],
      func: _req['func'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/space/info */
  SpaceInfoForInvite(
    req: developer_api.SpaceInfoForInviteRequest,
    options?: T,
  ): Promise<developer_api.SpaceInfoForInviteResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/info');
    const method = 'POST';
    const data = { space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/wait_list/bots_op/wait_list_config */
  WaitListConfig(
    req?: developer_api.WaitListConfigRequest,
    options?: T,
  ): Promise<developer_api.WaitListConfigResponse> {
    const url = this.genBaseURL('/api/wait_list/bots_op/wait_list_config');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/space/join */
  JoinSpace(
    req: developer_api.JoinSpaceRequest,
    options?: T,
  ): Promise<developer_api.JoinSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space/join');
    const method = 'POST';
    const data = { space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/upsert_profile_memory */
  UpsertProfileMemory(
    req?: profile_memory.UpsertProfileMemoryRequest,
    options?: T,
  ): Promise<profile_memory.UpsertProfileMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/upsert_profile_memory');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], profile: _req['profile'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/store_cookie_banner */
  StoreCookieBanner(
    req?: developer_api.StoreCookieBannerRequest,
    options?: T,
  ): Promise<developer_api.StoreCookieBannerResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/developer/store_cookie_banner');
    const method = 'POST';
    const data = { cookie_banner_info: _req['cookie_banner_info'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/wait_list/user/submit_info
   *
   * ---------------wait list----------------------------------
   */
  SubmitUserWaitListInfo(
    req?: developer_api.SubmitUserWaitListInfoRequest,
    options?: T,
  ): Promise<developer_api.SubmitUserWaitListInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/wait_list/user/submit_info');
    const method = 'POST';
    const data = {
      using_for: _req['using_for'],
      hear_from: _req['hear_from'],
      ext_message: _req['ext_message'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/account_cancel */
  AccountCancel(
    req?: developer_api.AccountCancelRequest,
    options?: T,
  ): Promise<developer_api.AccountCancelResponse> {
    const url = this.genBaseURL('/api/developer/account_cancel');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** GET /api/fake/apply_login */
  ApplyFakeLogin(
    req?: developer_api.ApplyFakeLoginRequest,
    options?: T,
  ): Promise<developer_api.ApplyFakeLoginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/fake/apply_login');
    const method = 'GET';
    const params = { userId: _req['userId'], fakeUid: _req['fakeUid'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/workflowV2/query */
  QueryWorkflowV2(
    req: developer_api.QueryWorkflowV2Request,
    options?: T,
  ): Promise<developer_api.QueryWorkflowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/query');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/publish */
  PublishWorkflowV2(
    req: developer_api.PublishWorkflowV2Request,
    options?: T,
  ): Promise<developer_api.PublishWorkflowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/publish');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
      user_id: _req['user_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/save */
  SaveWorkflowV2(
    req: developer_api.SaveWorkflowV2Request,
    options?: T,
  ): Promise<developer_api.SaveWorkflowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/save');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      schema: _req['schema'],
      space_id: _req['space_id'],
      name: _req['name'],
      desc: _req['desc'],
      icon_uri: _req['icon_uri'],
      ignore_status_transfer: _req['ignore_status_transfer'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/list */
  WorkflowListV2(
    req?: developer_api.WorkflowListV2Request,
    options?: T,
  ): Promise<developer_api.WorkflowListV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      workflow_ids: _req['workflow_ids'],
      type: _req['type'],
      name: _req['name'],
      tags: _req['tags'],
      space_id: _req['space_id'],
      status: _req['status'],
      order_by: _req['order_by'],
      login_user_create: _req['login_user_create'],
      flow_mode: _req['flow_mode'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/workflowV2/create
   *
   * --------------------------------------------workflow V2-----------------------------------
   */
  CreateWorkflowV2(
    req: developer_api.CreateWorkflowV2Request,
    options?: T,
  ): Promise<developer_api.CreateWorkflowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      desc: _req['desc'],
      icon_uri: _req['icon_uri'],
      space_id: _req['space_id'],
      flow_mode: _req['flow_mode'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/bind/connector */
  BindConnector(
    req: developer_api.BindConnectorRequest,
    options?: T,
  ): Promise<developer_api.BindConnectorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/bind/connector');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      connector_info: _req['connector_info'],
      agent_type: _req['agent_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/unbind/connector */
  UnBindConnector(
    req: developer_api.UnBindConnectorRequest,
    options?: T,
  ): Promise<developer_api.UnBindConnectorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/unbind/connector');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      bind_id: _req['bind_id'],
      agent_type: _req['agent_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/publish/connector/list */
  PublishConnectorList(
    req: developer_api.PublishConnectorListRequest,
    options?: T,
  ): Promise<developer_api.PublishConnectorListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/publish/connector/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      commit_version: _req['commit_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/delete */
  DeleteWorkflowV2(
    req: developer_api.DeleteWorkflowV2Request,
    options?: T,
  ): Promise<developer_api.DeleteWorkflowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/delete');
    const method = 'POST';
    const data = { workflow_id: _req['workflow_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/testRun */
  WorkFlowTestRunV2(
    req?: developer_api.WorkFlowTestRunV2Request,
    options?: T,
  ): Promise<developer_api.WorkFlowTestRunV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/testRun');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      input: _req['input'],
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/copy */
  CopyWorkflowV2(
    req: developer_api.CopyWorkflowV2Request,
    options?: T,
  ): Promise<developer_api.CopyWorkflowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/copy');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/workflowV2/get_process */
  GetWorkflowProcessV2(
    req: developer_api.GetWorkflowProcessV2Request,
    options?: T,
  ): Promise<developer_api.GetWorkflowProcessV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/get_process');
    const method = 'GET';
    const params = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
      execute_id: _req['execute_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/playground/suggest_plugin
   *
   * NoUpdatedPromptResponse NoUpdatedPrompt(1: NoUpdatedPromptRequest request)(api.post='/api/plugin/no_updated_prompt', api.category="plugin", api.gen_path="plugin")
   */
  SuggestPlugin(
    req: developer_api.SuggestPluginRequest,
    options?: T,
  ): Promise<developer_api.SuggestPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/playground/suggest_plugin');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      bot_prompts: _req['bot_prompts'],
      plugin_apis: _req['plugin_apis'],
      workflow_ids: _req['workflow_ids'],
      bot_name: _req['bot_name'],
      bot_description: _req['bot_description'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/playground/optimize_prompt */
  PromptOptimize(
    req: developer_api.PromptOptimizeRequest,
    options?: T,
  ): Promise<developer_api.PromptOptimizeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/playground/optimize_prompt');
    const method = 'POST';
    const data = {
      device_id: _req['device_id'],
      push_uuid: _req['push_uuid'],
      original_prompt: _req['original_prompt'],
      optimize_type: _req['optimize_type'],
      bot_id: _req['bot_id'],
      sync: _req['sync'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/draftbot/agent/create
   *
   * chatflow
   */
  CreateChatflowAgent(
    req: developer_api.CreateAgentRequest,
    options?: T,
  ): Promise<developer_api.CreateAgentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/agent/create');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      agent_type: _req['agent_type'],
      position: _req['position'],
      references: _req['references'],
      base_commit_version: _req['base_commit_version'],
      version_compat: _req['version_compat'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/cancel */
  CancelWorkFlowV2(
    req: developer_api.CancelWorkFlowV2Request,
    options?: T,
  ): Promise<developer_api.CancelWorkFlowV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/cancel');
    const method = 'POST';
    const data = { execute_id: _req['execute_id'], space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/workflowV2/apiDetail */
  GetApiDetail(
    req?: developer_api.GetApiDetailRequest,
    options?: T,
  ): Promise<developer_api.GetApiDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/apiDetail');
    const method = 'GET';
    const params = {
      pluginID: _req['pluginID'],
      apiName: _req['apiName'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/draftbot/agent/copy */
  CopyChatflowAgent(
    req: developer_api.CopyAgentRequest,
    options?: T,
  ): Promise<developer_api.CopyAgentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/agent/copy');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      agent_id: _req['agent_id'],
      base_commit_version: _req['base_commit_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/user/get_user_auth_list */
  GetUserAuthList(
    req?: developer_api.GetUserAuthListRequest,
    options?: T,
  ): Promise<developer_api.GetUserAuthListResponse> {
    const url = this.genBaseURL('/api/user/get_user_auth_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/user/auth/code */
  UserAuthCode(
    req?: developer_api.UserAuthCodeRequest,
    options?: T,
  ): Promise<developer_api.UserAuthCodeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/auth/code');
    const method = 'POST';
    const data = {
      code: _req['code'],
      connector_id: _req['connector_id'],
      encrypt_state: _req['encrypt_state'],
      state: _req['state'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/user/auth/cancel */
  CancelUserAuth(
    req?: developer_api.CancelUserAuthRequest,
    options?: T,
  ): Promise<developer_api.CancelUserAuthResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/auth/cancel');
    const method = 'POST';
    const data = { connector_id: _req['connector_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/validate */
  ValidateSchema(
    req: developer_api.ValidateSchemaRequest,
    options?: T,
  ): Promise<developer_api.ValidateSchemaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/validate');
    const method = 'POST';
    const data = { schema: _req['schema'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/get_bot_module_info */
  GetBotModuleInfo(
    req?: developer_api.GetBotModuleInfoRequest,
    options?: T,
  ): Promise<developer_api.GetBotModuleInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/draftbot/get_bot_module_info');
    const method = 'POST';
    const data = { space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/isv/webhook/event */
  IsvWebhookEvent(
    req?: developer_api.IsvWebhookEventReq,
    options?: T,
  ): Promise<developer_api.IsvWebhookEventResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/isv/webhook/event');
    const method = 'POST';
    const data = { encrypt: _req['encrypt'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/generate_icon */
  GenerateIcon(
    req?: developer_api.GenerateIconRequest,
    options?: T,
  ): Promise<developer_api.GenerateIconResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/draftbot/generate_icon');
    const method = 'POST';
    const data = {
      bot_name: _req['bot_name'],
      description: _req['description'],
      timeout: _req['timeout'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/dataset/doc_table_info
   *
   * 获取表格元数据和预览数据
   */
  GetDocumentTableInfo(
    req?: document.GetDocumentTableInfoRequest,
    options?: T,
  ): Promise<document.GetDocumentTableInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/doc_table_info');
    const method = 'GET';
    const params = {
      submit_web_id: _req['submit_web_id'],
      tos_uri: _req['tos_uri'],
      document_id: _req['document_id'],
      source_file_id: _req['source_file_id'],
      source_type: _req['source_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/dataset/document/create
   *
   * 创建document
   */
  CreateDocument(
    req?: document.CreateDocumentRequest,
    options?: T,
  ): Promise<document.CreateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/document/create');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      space_id: _req['space_id'],
      document: _req['document'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/migrate */
  Migrate(
    req: developer_api.MigrateRequest,
    options?: T,
  ): Promise<developer_api.MigrateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/workflowV2/migrate');
    const method = 'POST';
    const data = {
      type: _req['type'],
      run_model: _req['run_model'],
      space_id: _req['space_id'],
      workflow_id: _req['workflow_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_builder_api
   *
   * 自定义平台自建卡片: card_builder
   */
  CardBuilderProxy(
    req?: developer_api.CardBuilderProxyRequest,
    options?: T,
  ): Promise<developer_api.CardBuilderProxyResponse> {
    const url = this.genBaseURL('/api/card_builder_api');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** GET /api/draftbot/get_generate_icon_info */
  GetGenerateIconInfo(
    req?: developer_api.GetGenerateIconInfoRequest,
    options?: T,
  ): Promise<developer_api.GetGenerateIconInfoResponse> {
    const url = this.genBaseURL('/api/draftbot/get_generate_icon_info');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/explore/get_category_list */
  GetExploreCategoryList(
    req?: developer_api.GetExploreCategoryListRequest,
    options?: T,
  ): Promise<developer_api.GetExploreCategoryListResponse> {
    const url = this.genBaseURL('/api/explore/get_category_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/card/get_plugin_cards */
  GetPluginCards(
    req?: developer_api.GetPluginCardsRequest,
    options?: T,
  ): Promise<developer_api.GetPluginCardsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_plugin_cards');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      plugins: _req['plugins'],
      agent_id: _req['agent_id'],
      using_master: _req['using_master'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/query_card_detail */
  QueryCardDetail(
    req?: developer_api.QueryCardDetailRequest,
    options?: T,
  ): Promise<developer_api.QueryCardDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/query_card_detail');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      card_id: _req['card_id'],
      card_version: _req['card_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card/create_card
   *
   * 自建卡片新接口
   */
  CreateCard(
    req?: developer_api.CreateCardRequest,
    options?: T,
  ): Promise<developer_api.CreateCardResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/create_card');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      card_id: _req['card_id'],
      version_num: _req['version_num'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/query_card_list */
  QueryCardList(
    req?: developer_api.QueryCardListRequest,
    options?: T,
  ): Promise<developer_api.QueryCardListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/query_card_list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      space_id: _req['space_id'],
      category: _req['category'],
      bind_card_id: _req['bind_card_id'],
      status: _req['status'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/publish
   *
   * 发布卡片
   */
  CardMetaPublish(
    req: developer_api.CardMetaPublishRequest,
    options?: T,
  ): Promise<developer_api.CardMetaPublishResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/publish');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      draft_card_id: _req['draft_card_id'],
      description: _req['description'],
      version_name: _req['version_name'],
      thumbnail_info: _req['thumbnail_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/get_card_info
   *
   * 查询卡片信息
   */
  GetCardInfo(
    req: developer_api.GetCardInfoRequest,
    options?: T,
  ): Promise<developer_api.GetCardInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/get_card_info');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      channel_type: _req['channel_type'],
      version_num: _req['version_num'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/upload_file
   *
   * 上传卡片相关资源
   */
  CardUploadFile(
    req?: developer_api.CardUploadFileRequest,
    options?: T,
  ): Promise<developer_api.CardUploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card_center/upload_file');
    const method = 'POST';
    const data = { file_type: _req['file_type'], data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/data_connector_plugin/get_notion_info
   *
   * --------------------------------------------data_connector_plugin--------------------------------------------
   */
  GetNotionDocumentInfo(
    req?: data_connector_plugin.GetNotionDocumentInfoRequest,
    options?: T,
  ): Promise<data_connector_plugin.GetNotionDocumentInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/data_connector_plugin/get_notion_info');
    const method = 'POST';
    const params = {
      document_url: _req['document_url'],
      block_search_size: _req['block_search_size'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/workflowV2/released_workflows */
  GetReleasedWorkflows(
    req?: developer_api.GetReleasedWorkflowsRequest,
    options?: T,
  ): Promise<developer_api.GetReleasedWorkflowsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/released_workflows');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      type: _req['type'],
      name: _req['name'],
      workflow_ids: _req['workflow_ids'],
      tags: _req['tags'],
      space_id: _req['space_id'],
      order_by: _req['order_by'],
      login_user_create: _req['login_user_create'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/update_display_info */
  UpdateDraftBotDisplayInfo(
    req: developer_api.UpdateDraftBotDisplayInfoRequest,
    options?: T,
  ): Promise<developer_api.UpdateDraftBotDisplayInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/update_display_info');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      display_info: _req['display_info'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/get_display_info */
  GetDraftBotDisplayInfo(
    req: developer_api.GetDraftBotDisplayInfoRequest,
    options?: T,
  ): Promise<developer_api.GetDraftBotDisplayInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/get_display_info');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/update
   *
   * 更新卡片信息
   */
  UpdateCardInfo(
    req: developer_api.UpdateCardInfoRequest,
    options?: T,
  ): Promise<developer_api.UpdateCardInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/update');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      name: _req['name'],
      card_status: _req['card_status'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/workflow_references */
  GetWorkflowReferences(
    req?: developer_api.GetWorkflowReferencesRequest,
    options?: T,
  ): Promise<developer_api.GetWorkflowReferencesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/workflow_references');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/connector/query_schemas
   *
   * ---------------------connector------------------------------
   */
  QuerySchemaList(
    req?: developer_api.QuerySchemaRequest,
    options?: T,
  ): Promise<developer_api.QuerySchemaConfig> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/query_schemas');
    const method = 'POST';
    const data = { connector_id: _req['connector_id'], scene: _req['scene'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/slice/create */
  CreateSlice(
    req: slice.CreateSliceReq,
    options?: T,
  ): Promise<slice.CreateSliceResp> {
    const _req = req;
    const url = this.genBaseURL('/api/dataset/slice/create');
    const method = 'POST';
    const data = { document_id: _req['document_id'], content: _req['content'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/dataset/del_web_data */
  DelWebData(
    req?: developer_api.DelWebDataRequest,
    options?: T,
  ): Promise<developer_api.DelWebDataResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/dataset/del_web_data');
    const method = 'POST';
    const data = { web_id: _req['web_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/dataset/resegment
   *
   * local / web doc 类型的重分片接口
   */
  Resegment(
    req: developer_api.ResegmentRequest,
    options?: T,
  ): Promise<developer_api.ResegmentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/dataset/resegment');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_id: _req['document_id'],
      rule: _req['rule'],
      format_type: _req['format_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/voice/get_voice_token */
  GetVoiceToken(
    req?: developer_api.GetVoiceTokenRequest,
    options?: T,
  ): Promise<developer_api.GetVoiceTokenResponse> {
    const url = this.genBaseURL('/api/voice/get_voice_token');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/voice/get_voice_config
   *
   * --------------------------------------------voice--------------------------------------------
   */
  GetVoiceConfig(
    req?: developer_api.GetVoiceConfigRequest,
    options?: T,
  ): Promise<developer_api.GetVoiceConfigResponse> {
    const url = this.genBaseURL('/api/voice/get_voice_config');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/data_connector/authorization */
  DataSourceAuthorize(
    req?: data_connector.DataSourceAuthorizeRequest,
    options?: T,
  ): Promise<data_connector.DataSourceAuthorizeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/data_connector/authorization');
    const method = 'GET';
    const params = { code: _req['code'], state: _req['state'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/workflowV2/bots_ide_token */
  GetBotsIDEToken(
    req?: developer_api.GetBotsIDETokenRequest,
    options?: T,
  ): Promise<developer_api.GetBotsIDETokenResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/bots_ide_token');
    const method = 'GET';
    const params = { space_id: _req['space_id'], can_write: _req['can_write'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/connector/update_api_key */
  UpdateApiKey(
    req?: developer_api.UpdateApiKeyRequest,
    options?: T,
  ): Promise<developer_api.UpdateApiKeyResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/update_api_key');
    const method = 'POST';
    const data = {
      api_key_id: _req['api_key_id'],
      space_id: _req['space_id'],
      key_name: _req['key_name'],
      operate_type: _req['operate_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/add_api_key */
  AddApiKey(
    req?: developer_api.AddApiKeyRequest,
    options?: T,
  ): Promise<developer_api.AddApiKeyResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/add_api_key');
    const method = 'POST';
    const data = { space_id: _req['space_id'], key_name: _req['key_name'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector/get_api_keys */
  GetApiKeyList(
    req?: developer_api.GetApiKeyListRequest,
    options?: T,
  ): Promise<developer_api.GetApiKeyListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/connector/get_api_keys');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      api_key_id: _req['api_key_id'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/developer/get_login_info */
  GetLoginInfo(
    req?: developer_api.GetLoginInfoRequest,
    options?: T,
  ): Promise<developer_api.GetLoginInfoResponse> {
    const url = this.genBaseURL('/api/developer/get_login_info');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/developer/timecapsule_list_items */
  TimeCapsuleListItems(
    req: developer_api.TimeCapsuleListItemsRequest,
    options?: T,
  ): Promise<developer_api.TimeCapsuleListItemsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/developer/timecapsule_list_items');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      start_event_time: _req['start_event_time'],
      end_event_time: _req['end_event_time'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/developer/feelgood_auth */
  FeelGoodAuth(
    req?: developer_api.FeelGoodAuthRequest,
    options?: T,
  ): Promise<developer_api.FeelGoodAuthResponse> {
    const url = this.genBaseURL('/api/developer/feelgood_auth');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/conversation/create_section */
  CreateSection(
    req: developer_api.CreateSectionRequest,
    options?: T,
  ): Promise<developer_api.CreateSectionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/create_section');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      scene: _req['scene'],
      insert_history_message_list: _req['insert_history_message_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_message_list */
  GetMessageList(
    req: developer_api.GetMessageListRequest,
    options?: T,
  ): Promise<developer_api.GetMessageListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/get_message_list');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      cursor: _req['cursor'],
      count: _req['count'],
      bot_id: _req['bot_id'],
      draft_mode: _req['draft_mode'],
      preset_bot: _req['preset_bot'],
      scene: _req['scene'],
      biz_kind: _req['biz_kind'],
      insert_history_message_list: _req['insert_history_message_list'],
      load_direction: _req['load_direction'],
      must_append: _req['must_append'],
      share_id: _req['share_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/clear_message */
  ClearMessage(
    req: developer_api.ClearMessageRequest,
    options?: T,
  ): Promise<developer_api.ClearMessageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/clear_message');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      scene: _req['scene'],
      bot_id: _req['bot_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/delete_message */
  DeleteMessage(
    req: developer_api.DeleteMessageRequest,
    options?: T,
  ): Promise<developer_api.DeleteMessageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/delete_message');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      message_id: _req['message_id'],
      scene: _req['scene'],
      bot_id: _req['bot_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/conversation/chat
   *
   * --------------------------------------------conversation--------------------------------------------
   */
  Chat(
    req: developer_api.ChatRequest,
    options?: T,
  ): Promise<developer_api.ChatResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/chat');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      conversation_id: _req['conversation_id'],
      bot_version: _req['bot_version'],
      user: _req['user'],
      query: _req['query'],
      chat_history: _req['chat_history'],
      extra: _req['extra'],
      stream: _req['stream'],
      custom_variables: _req['custom_variables'],
      draft_mode: _req['draft_mode'],
      scene: _req['scene'],
      content_type: _req['content_type'],
      regen_message_id: _req['regen_message_id'],
      local_message_id: _req['local_message_id'],
      preset_bot: _req['preset_bot'],
      insert_history_message_list: _req['insert_history_message_list'],
      device_id: _req['device_id'],
      space_id: _req['space_id'],
      mention_list: _req['mention_list'],
      toolList: _req['toolList'],
      commit_version: _req['commit_version'],
      sub_scene: _req['sub_scene'],
      diff_mode_identifier: _req['diff_mode_identifier'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_conversation */
  GetConversation(
    req?: developer_api.GetConversationRequest,
    options?: T,
  ): Promise<developer_api.GetConversationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/conversation/get_conversation');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      draft_mode: _req['draft_mode'],
      preset_bot: _req['preset_bot'],
      scene: _req['scene'],
      biz_kind: _req['biz_kind'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/break_message */
  BreakMessage(
    req: developer_api.BreakMessageRequest,
    options?: T,
  ): Promise<developer_api.BreakMessageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/break_message');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      query_message_id: _req['query_message_id'],
      answer_message_id: _req['answer_message_id'],
      broken_pos: _req['broken_pos'],
      scene: _req['scene'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/node_type */
  QueryWorkflowNodeTypes(
    req?: developer_api.QueryWorkflowNodeTypeRequest,
    options?: T,
  ): Promise<developer_api.QueryWorkflowNodeTypeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/node_type');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      workflow_id: _req['workflow_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/gray_feature */
  GetWorkflowGrayFeature(
    req?: developer_api.GetWorkflowGrayFeatureRequest,
    options?: T,
  ): Promise<developer_api.GetWorkflowGrayFeatureResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/gray_feature');
    const method = 'POST';
    const data = { space_id: _req['space_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/get_published_list */
  GetPublishedPluginList(
    req?: developer_api.GetPublishedPluginListRequest,
    options?: T,
  ): Promise<developer_api.GetPublishedPluginListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin/get_published_list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      size: _req['size'],
      user_space_ids: _req['user_space_ids'],
      plugin_ids: _req['plugin_ids'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/commit */
  CommitDraftBot(
    req: developer_api.CommitDraftBotRequest,
    options?: T,
  ): Promise<developer_api.CommitDraftBotResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/commit');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      work_info: _req['work_info'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
      visibility: _req['visibility'],
      update_agents: _req['update_agents'],
      canvas_data: _req['canvas_data'],
      bot_mode: _req['bot_mode'],
      delete_agents: _req['delete_agents'],
      remark: _req['remark'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/commit_check */
  CheckDraftBotCommit(
    req: developer_api.CheckDraftBotCommitRequest,
    options?: T,
  ): Promise<developer_api.CheckDraftBotCommitResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/commit_check');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      commit_version: _req['commit_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_onboarding */
  ChatGetOnboarding(
    req?: developer_api.ChatGetOnboardingRequest,
    options?: T,
  ): Promise<developer_api.ChatGetOnboardingResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/conversation/get_onboarding');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      draft_mode: _req['draft_mode'],
      bot_version: _req['bot_version'],
      preset_bot: _req['preset_bot'],
      scene: _req['scene'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/auto_gen_changelog */
  AutoGenChangelog(
    req: developer_api.AutoGenChangelogRequest,
    options?: T,
  ): Promise<developer_api.AutoGenChangelogResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/auto_gen_changelog');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      publish_id: _req['publish_id'],
      connectors: _req['connectors'],
      draft_version: _req['draft_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/bind/get_connector_config */
  GetBindConnectorConfig(
    req: developer_api.GetBindConnectorConfigRequest,
    options?: T,
  ): Promise<developer_api.GetBindConnectorConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/bind/get_connector_config');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      app_id: _req['app_id'],
      detail: _req['detail'],
      agent_type: _req['agent_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/bind/save_connector_config */
  SaveBindConnectorConfig(
    req: developer_api.SaveBindConnectorConfigRequest,
    options?: T,
  ): Promise<developer_api.SaveBindConnectorConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/bind/save_connector_config');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      detail: _req['detail'],
      agent_type: _req['agent_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/save_template
   *
   * 保存卡片模版
   */
  SaveCardTemplate(
    req: developer_api.SaveCardTemplateRequest,
    options?: T,
  ): Promise<developer_api.SaveCardTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/save_template');
    const method = 'POST';
    const data = {
      channel_type: _req['channel_type'],
      thumbnail: _req['thumbnail'],
      name: _req['name'],
      dsl_content: _req['dsl_content'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/query_history
   *
   * 查询卡片发布历史
   */
  QueryCardHistory(
    req: developer_api.QueryCardHistoryRequest,
    options?: T,
  ): Promise<developer_api.QueryCardHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/query_history');
    const method = 'POST';
    const data = {
      card_id: _req['card_id'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/mget_template
   *
   * 获取卡片模版
   */
  MGetCardTemplate(
    req: developer_api.MGetCardTemplateRequest,
    options?: T,
  ): Promise<developer_api.MGetCardTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/mget_template');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      size: _req['size'],
      page: _req['page'],
      channel_type: _req['channel_type'],
      category: _req['category'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/card_center/delete_template
   *
   * 删除卡片模版
   */
  DeleteCardTemplate(
    req: developer_api.DeleteCardTemplateRequest,
    options?: T,
  ): Promise<developer_api.DeleteCardTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/card_center/delete_template');
    const method = 'POST';
    const data = { template_id: _req['template_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/acrosite/get_bus */
  GetBusiness(
    req?: acrosite.GetBusinessRequest,
    options?: T,
  ): Promise<acrosite.GetBusinessResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/acrosite/get_bus');
    const method = 'POST';
    const data = { app_id: _req['app_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/acrosite/get_doc_detail */
  GetDocDetailById(
    req: acrosite.GetDocDetailByIdRequest,
    options?: T,
  ): Promise<acrosite.GetDocDetailByIdResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/acrosite/get_doc_detail');
    const method = 'POST';
    const data = { id: _req['id'], app_id: _req['app_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/acrosite/openapi
   *
   * Acrosite后端打通接口
   */
  AcrositeAPI(
    req?: acrosite.AcrositeAPIRequest,
    options?: T,
  ): Promise<acrosite.AcrositeAPIResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/acrosite/openapi');
    const method = 'POST';
    const data = { action: _req['action'], data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/acrosite/get_doc */
  GetDocById(
    req: acrosite.GetDocByIdRequest,
    options?: T,
  ): Promise<acrosite.GetDocByIdResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/acrosite/get_doc');
    const method = 'POST';
    const data = { id: _req['id'], app_id: _req['app_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/acrosite/search */
  Search(
    req?: acrosite.SearchRequest,
    options?: T,
  ): Promise<acrosite.SearchResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/acrosite/search');
    const method = 'POST';
    const data = {
      app_id: _req['app_id'],
      app_ids: _req['app_ids'],
      q: _req['q'],
      lang: _req['lang'],
      type: _req['type'],
      business_id: _req['business_id'],
      skip: _req['skip'],
      limit: _req['limit'],
      number_of_fragments: _req['number_of_fragments'],
      fragment_size: _req['fragment_size'],
      version_scope: _req['version_scope'],
      update_time_start: _req['update_time_start'],
      update_time_end: _req['update_time_end'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/acrosite/get_bus_struct */
  GetBusStructureById(
    req?: acrosite.GetBusStructureByIdRequest,
    options?: T,
  ): Promise<acrosite.GetBusStructureByIdResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/acrosite/get_bus_struct');
    const method = 'POST';
    const data = {
      business_id: _req['business_id'],
      lang: _req['lang'],
      source: _req['source'],
      req_source: _req['req_source'],
      business_version_id: _req['business_version_id'],
      app_id: _req['app_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/card/get_bind_card_status */
  GetBindCardsStatus(
    req?: developer_api.GetBindCardsStatusRequest,
    options?: T,
  ): Promise<developer_api.GetBindCardsStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/card/get_bind_card_status');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      business_list: _req['business_list'],
      agent_id: _req['agent_id'],
      using_master: _req['using_master'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/workflowV2/nodeDebug */
  WorkFlowNodeDebugV2(
    req?: developer_api.WorkFlowNodeDebugV2Request,
    options?: T,
  ): Promise<developer_api.WorkFlowNodeDebugV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/nodeDebug');
    const method = 'POST';
    const data = {
      workflow_id: _req['workflow_id'],
      node_id: _req['node_id'],
      input: _req['input'],
      batch: _req['batch'],
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_conversation_detail */
  GetConversationDetail(
    req: developer_api.GetConversationDetailRequest,
    options?: T,
  ): Promise<developer_api.GetConversationDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/get_conversation_detail');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      draft_mode: _req['draft_mode'],
      bot_version: _req['bot_version'],
      preset_bot: _req['preset_bot'],
      scene: _req['scene'],
      conversation_id: _req['conversation_id'],
      cursor: _req['cursor'],
      count: _req['count'],
      load_direction: _req['load_direction'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/search_mention */
  SearchMention(
    req: developer_api.SearchMentionRequest,
    options?: T,
  ): Promise<developer_api.SearchMentionResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/search_mention');
    const method = 'POST';
    const data = {
      preset_bot: _req['preset_bot'],
      keyword: _req['keyword'],
      cursor_id: _req['cursor_id'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/plugin/get_card_resp_struct */
  GetCardRespStruct(
    req?: developer_api.GetCardRespStructRequest,
    options?: T,
  ): Promise<developer_api.GetAPIRespStructResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/plugin/get_card_resp_struct');
    const method = 'POST';
    const data = {
      biz_type: _req['biz_type'],
      plugin_id: _req['plugin_id'],
      unique_id: _req['unique_id'],
      space_id: _req['space_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/workflowV2/message_nodes */
  GetWorkflowMessageNodes(
    req?: developer_api.GetWorkflowMessageNodesRequest,
    options?: T,
  ): Promise<developer_api.GetWorkflowMessageNodesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/workflowV2/message_nodes');
    const method = 'GET';
    const params = { space_id: _req['space_id'], plugin_id: _req['plugin_id'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/task/create_webhook */
  CreateBotTaskWebhook(
    req: developer_api.CreateBotTaskWebhookRequest,
    options?: T,
  ): Promise<developer_api.CreateBotTaskWebhookResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/create_webhook');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/remove_bot_task */
  RemoveBotTask(
    req: developer_api.RemoveBotTaskRequest,
    options?: T,
  ): Promise<developer_api.RemoveBotTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/remove_bot_task');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/create_bot_task */
  CreateBotTask(
    req: developer_api.CreateBotTaskRequest,
    options?: T,
  ): Promise<developer_api.CreateBotTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/create_bot_task');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      task_id: _req['task_id'],
      name: _req['name'],
      trigger_type: _req['trigger_type'],
      mode: _req['mode'],
      time_data: _req['time_data'],
      webhook_data: _req['webhook_data'],
      action: _req['action'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/bot_task_list */
  GetBotTaskList(
    req: developer_api.GetBotTaskListRequest,
    options?: T,
  ): Promise<developer_api.GetBotTaskListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/bot_task_list');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      conversation_id: _req['conversation_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/task/trigger_task */
  TriggerBotTask(
    req: developer_api.TriggerBotTaskRequest,
    options?: T,
  ): Promise<developer_api.TriggerBotTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/task/trigger_task');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      task_id: _req['task_id'],
      webhook_url: _req['webhook_url'],
      bearer_token: _req['bearer_token'],
      params: _req['params'],
      conversation_id: _req['conversation_id'],
      extra: _req['extra'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/mark_read */
  MarkRead(
    req: developer_api.MarkReadRequest,
    options?: T,
  ): Promise<developer_api.MarkReadResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/mark_read');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      read_message_index: _req['read_message_index'],
      mark_time: _req['mark_time'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/draftbot/switch_develop_mode */
  SwitchDevelopMode(
    req: developer_api.SwitchDevelopModeRequest,
    options?: T,
  ): Promise<developer_api.SwitchDevelopModeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/draftbot/switch_develop_mode');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      target_develop_mode: _req['target_develop_mode'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_message_index */
  GetConversationParticipantsReadIndex(
    req?: developer_api.GetConversationParticipantsReadIndexRequest,
    options?: T,
  ): Promise<developer_api.GetConversationParticipantsReadIndexResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/conversation/get_message_index');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      bot_id: _req['bot_id'],
      draft_mode: _req['draft_mode'],
      preset_bot: _req['preset_bot'],
      scene: _req['scene'],
      biz_kind: _req['biz_kind'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/message/report */
  ReportMessage(
    req: developer_api.ReportMessageRequest,
    options?: T,
  ): Promise<developer_api.ReportMessageResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/message/report');
    const method = 'POST';
    const data = {
      biz_conversation_id: _req['biz_conversation_id'],
      message_id: _req['message_id'],
      scene: _req['scene'],
      action: _req['action'],
      message_feedback: _req['message_feedback'],
      attributes: _req['attributes'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_pe_rules */
  GetPERules(
    req: developer_api.GetPERulesRequest,
    options?: T,
  ): Promise<developer_api.GetPERulesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/get_pe_rules');
    const method = 'POST';
    const data = { scene: _req['scene'], action: _req['action'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_bot_init_info */
  GetBotInitInfo(
    req?: developer_api.GetBotInitInfoRequest,
    options?: T,
  ): Promise<developer_api.GetBotInitInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/conversation/get_bot_init_info');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      draft_mode: _req['draft_mode'],
      bot_version: _req['bot_version'],
      preset_bot: _req['preset_bot'],
      scene: _req['scene'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_bot_info */
  GetConversationBotInfo(
    req: developer_api.GetConversationBotInfoRequest,
    options?: T,
  ): Promise<developer_api.GetConversationBotInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/get_bot_info');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/delete_mention */
  DeleteMention(
    req?: developer_api.DeleteMentionRequest,
    options?: T,
  ): Promise<developer_api.DeleteMentionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/conversation/delete_mention');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/home_trigger */
  UpdateHomeTriggerUserConfig(
    req: developer_api.UpdateHomeTriggerConfigRequest,
    options?: T,
  ): Promise<developer_api.UpdateHomeTriggerUserConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/home_trigger');
    const method = 'POST';
    const data = { bot_id: _req['bot_id'], action: _req['action'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/conversation/get_bot_participant_info */
  GetBotParticipantInfoByBotIds(
    req: developer_api.GetBotParticipantInfoByBotIdsRequest,
    options?: T,
  ): Promise<developer_api.GetBotParticipantInfoByBotIdsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/conversation/get_bot_participant_info');
    const method = 'POST';
    const data = { bot_ids: _req['bot_ids'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/user/auth/connector_state */
  GetConnectorAuthState(
    req?: developer_api.GetConnectorAuthStateRequest,
    options?: T,
  ): Promise<developer_api.GetConnectorAuthStateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/auth/connector_state');
    const method = 'GET';
    const params = { connector_id: _req['connector_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/connector_user/bind
   *
   * 三方渠道用户绑定
   */
  BindConnectorUser(
    req: developer_api.BindConnectorUserRequest,
    options?: T,
  ): Promise<developer_api.BindConnectorUserResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/connector_user/bind');
    const method = 'POST';
    const data = { connector_id: _req['connector_id'], param: _req['param'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/connector_user/unbind */
  UnbindConnectorUser(
    req: developer_api.UnbindConnectorUserRequest,
    options?: T,
  ): Promise<developer_api.UnbindConnectorUserResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/connector_user/unbind');
    const method = 'POST';
    const data = {
      connector_id: _req['connector_id'],
      connector_uid: _req['connector_uid'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/connector_user/connections */
  GetBindConnections(
    req?: developer_api.GetBindConnectionsRequest,
    options?: T,
  ): Promise<developer_api.GetBindConnectionsResponse> {
    const url = this.genBaseURL('/api/connector_user/connections');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/connector_user/config */
  GetConnectorUserBindConfig(
    req: developer_api.GetConnectorUserBindConfigRequest,
    options?: T,
  ): Promise<developer_api.GetConnectorUserBindConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/connector_user/config');
    const method = 'GET';
    const params = {
      connector_id: _req['connector_id'],
      redirect_uri: _req['redirect_uri'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/conversation/resume_chat */
  ResumeChat(
    req?: developer_api.ResumeChatRequest,
    options?: T,
  ): Promise<developer_api.ResumeChatResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/conversation/resume_chat');
    const method = 'POST';
    const data = {
      interrupt_message_id: _req['interrupt_message_id'],
      conversation_id: _req['conversation_id'],
      tool_outputs: _req['tool_outputs'],
      scene: _req['scene'],
      resume_message_id: _req['resume_message_id'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
