// Code generated by thriftgo (0.4.1). DO NOT EDIT.

package dataset

import (
	"context"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type DatasetService interface {
	// 知识库相关
	GetIconForDataset(ctx context.Context, req *GetIconRequest) (r *GetIconResponse, err error)

	CreateDataset(ctx context.Context, req *CreateDatasetRequest) (r *CreateDatasetResponse, err error)

	DatasetDetail(ctx context.Context, req *DatasetDetailRequest) (r *DatasetDetailResponse, err error)

	ListDataset(ctx context.Context, req *ListDatasetRequest) (r *ListDatasetResponse, err error)

	DeleteDataset(ctx context.Context, req *DeleteDatasetRequest) (r *DeleteDatasetResponse, err error)

	UpdateDataset(ctx context.Context, req *UpdateDatasetRequest) (r *UpdateDatasetResponse, err error)
	// Document相关
	CreateDocument(ctx context.Context, req *CreateDocumentRequest) (r *CreateDocumentResponse, err error)

	ListDocument(ctx context.Context, req *ListDocumentRequest) (r *ListDocumentResponse, err error)

	DeleteDocument(ctx context.Context, req *DeleteDocumentRequest) (r *DeleteDocumentResponse, err error)

	UpdateDocument(ctx context.Context, req *UpdateDocumentRequest) (r *UpdateDocumentResponse, err error)

	GetDocumentProgress(ctx context.Context, req *GetDocumentProgressRequest) (r *GetDocumentProgressResponse, err error)

	Resegment(ctx context.Context, req *ResegmentRequest) (r *ResegmentResponse, err error)

	UpdatePhotoCaption(ctx context.Context, req *UpdatePhotoCaptionRequest) (r *UpdatePhotoCaptionResponse, err error)

	ListPhoto(ctx context.Context, req *ListPhotoRequest) (r *ListPhotoResponse, err error)

	PhotoDetail(ctx context.Context, req *PhotoDetailRequest) (r *PhotoDetailResponse, err error)

	ExtractPhotoCaption(ctx context.Context, req *ExtractPhotoCaptionRequest) (r *ExtractPhotoCaptionResponse, err error)

	GetTableSchema(ctx context.Context, req *GetTableSchemaRequest) (r *GetTableSchemaResponse, err error)

	ValidateTableSchema(ctx context.Context, req *ValidateTableSchemaRequest) (r *ValidateTableSchemaResponse, err error)
	// slice相关
	DeleteSlice(ctx context.Context, req *DeleteSliceRequest) (r *DeleteSliceResponse, err error)

	CreateSlice(ctx context.Context, req *CreateSliceRequest) (r *CreateSliceResponse, err error)

	UpdateSlice(ctx context.Context, req *UpdateSliceRequest) (r *UpdateSliceResponse, err error)

	ListSlice(ctx context.Context, req *ListSliceRequest) (r *ListSliceResponse, err error)
	/** 预分片相关 **/
	CreateDocumentReview(ctx context.Context, req *CreateDocumentReviewRequest) (r *CreateDocumentReviewResponse, err error)

	MGetDocumentReview(ctx context.Context, req *MGetDocumentReviewRequest) (r *MGetDocumentReviewResponse, err error)

	SaveDocumentReview(ctx context.Context, req *SaveDocumentReviewRequest) (r *SaveDocumentReviewResponse, err error)
}

type DatasetServiceClient struct {
	c thrift.TClient
}

func NewDatasetServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DatasetServiceClient {
	return &DatasetServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewDatasetServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DatasetServiceClient {
	return &DatasetServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewDatasetServiceClient(c thrift.TClient) *DatasetServiceClient {
	return &DatasetServiceClient{
		c: c,
	}
}

func (p *DatasetServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *DatasetServiceClient) GetIconForDataset(ctx context.Context, req *GetIconRequest) (r *GetIconResponse, err error) {
	var _args DatasetServiceGetIconForDatasetArgs
	_args.Req = req
	var _result DatasetServiceGetIconForDatasetResult
	if err = p.Client_().Call(ctx, "GetIconForDataset", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) CreateDataset(ctx context.Context, req *CreateDatasetRequest) (r *CreateDatasetResponse, err error) {
	var _args DatasetServiceCreateDatasetArgs
	_args.Req = req
	var _result DatasetServiceCreateDatasetResult
	if err = p.Client_().Call(ctx, "CreateDataset", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) DatasetDetail(ctx context.Context, req *DatasetDetailRequest) (r *DatasetDetailResponse, err error) {
	var _args DatasetServiceDatasetDetailArgs
	_args.Req = req
	var _result DatasetServiceDatasetDetailResult
	if err = p.Client_().Call(ctx, "DatasetDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) ListDataset(ctx context.Context, req *ListDatasetRequest) (r *ListDatasetResponse, err error) {
	var _args DatasetServiceListDatasetArgs
	_args.Req = req
	var _result DatasetServiceListDatasetResult
	if err = p.Client_().Call(ctx, "ListDataset", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) DeleteDataset(ctx context.Context, req *DeleteDatasetRequest) (r *DeleteDatasetResponse, err error) {
	var _args DatasetServiceDeleteDatasetArgs
	_args.Req = req
	var _result DatasetServiceDeleteDatasetResult
	if err = p.Client_().Call(ctx, "DeleteDataset", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) UpdateDataset(ctx context.Context, req *UpdateDatasetRequest) (r *UpdateDatasetResponse, err error) {
	var _args DatasetServiceUpdateDatasetArgs
	_args.Req = req
	var _result DatasetServiceUpdateDatasetResult
	if err = p.Client_().Call(ctx, "UpdateDataset", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) CreateDocument(ctx context.Context, req *CreateDocumentRequest) (r *CreateDocumentResponse, err error) {
	var _args DatasetServiceCreateDocumentArgs
	_args.Req = req
	var _result DatasetServiceCreateDocumentResult
	if err = p.Client_().Call(ctx, "CreateDocument", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) ListDocument(ctx context.Context, req *ListDocumentRequest) (r *ListDocumentResponse, err error) {
	var _args DatasetServiceListDocumentArgs
	_args.Req = req
	var _result DatasetServiceListDocumentResult
	if err = p.Client_().Call(ctx, "ListDocument", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) DeleteDocument(ctx context.Context, req *DeleteDocumentRequest) (r *DeleteDocumentResponse, err error) {
	var _args DatasetServiceDeleteDocumentArgs
	_args.Req = req
	var _result DatasetServiceDeleteDocumentResult
	if err = p.Client_().Call(ctx, "DeleteDocument", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) UpdateDocument(ctx context.Context, req *UpdateDocumentRequest) (r *UpdateDocumentResponse, err error) {
	var _args DatasetServiceUpdateDocumentArgs
	_args.Req = req
	var _result DatasetServiceUpdateDocumentResult
	if err = p.Client_().Call(ctx, "UpdateDocument", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) GetDocumentProgress(ctx context.Context, req *GetDocumentProgressRequest) (r *GetDocumentProgressResponse, err error) {
	var _args DatasetServiceGetDocumentProgressArgs
	_args.Req = req
	var _result DatasetServiceGetDocumentProgressResult
	if err = p.Client_().Call(ctx, "GetDocumentProgress", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) Resegment(ctx context.Context, req *ResegmentRequest) (r *ResegmentResponse, err error) {
	var _args DatasetServiceResegmentArgs
	_args.Req = req
	var _result DatasetServiceResegmentResult
	if err = p.Client_().Call(ctx, "Resegment", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) UpdatePhotoCaption(ctx context.Context, req *UpdatePhotoCaptionRequest) (r *UpdatePhotoCaptionResponse, err error) {
	var _args DatasetServiceUpdatePhotoCaptionArgs
	_args.Req = req
	var _result DatasetServiceUpdatePhotoCaptionResult
	if err = p.Client_().Call(ctx, "UpdatePhotoCaption", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) ListPhoto(ctx context.Context, req *ListPhotoRequest) (r *ListPhotoResponse, err error) {
	var _args DatasetServiceListPhotoArgs
	_args.Req = req
	var _result DatasetServiceListPhotoResult
	if err = p.Client_().Call(ctx, "ListPhoto", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) PhotoDetail(ctx context.Context, req *PhotoDetailRequest) (r *PhotoDetailResponse, err error) {
	var _args DatasetServicePhotoDetailArgs
	_args.Req = req
	var _result DatasetServicePhotoDetailResult
	if err = p.Client_().Call(ctx, "PhotoDetail", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) ExtractPhotoCaption(ctx context.Context, req *ExtractPhotoCaptionRequest) (r *ExtractPhotoCaptionResponse, err error) {
	var _args DatasetServiceExtractPhotoCaptionArgs
	_args.Req = req
	var _result DatasetServiceExtractPhotoCaptionResult
	if err = p.Client_().Call(ctx, "ExtractPhotoCaption", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) GetTableSchema(ctx context.Context, req *GetTableSchemaRequest) (r *GetTableSchemaResponse, err error) {
	var _args DatasetServiceGetTableSchemaArgs
	_args.Req = req
	var _result DatasetServiceGetTableSchemaResult
	if err = p.Client_().Call(ctx, "GetTableSchema", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) ValidateTableSchema(ctx context.Context, req *ValidateTableSchemaRequest) (r *ValidateTableSchemaResponse, err error) {
	var _args DatasetServiceValidateTableSchemaArgs
	_args.Req = req
	var _result DatasetServiceValidateTableSchemaResult
	if err = p.Client_().Call(ctx, "ValidateTableSchema", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) DeleteSlice(ctx context.Context, req *DeleteSliceRequest) (r *DeleteSliceResponse, err error) {
	var _args DatasetServiceDeleteSliceArgs
	_args.Req = req
	var _result DatasetServiceDeleteSliceResult
	if err = p.Client_().Call(ctx, "DeleteSlice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) CreateSlice(ctx context.Context, req *CreateSliceRequest) (r *CreateSliceResponse, err error) {
	var _args DatasetServiceCreateSliceArgs
	_args.Req = req
	var _result DatasetServiceCreateSliceResult
	if err = p.Client_().Call(ctx, "CreateSlice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) UpdateSlice(ctx context.Context, req *UpdateSliceRequest) (r *UpdateSliceResponse, err error) {
	var _args DatasetServiceUpdateSliceArgs
	_args.Req = req
	var _result DatasetServiceUpdateSliceResult
	if err = p.Client_().Call(ctx, "UpdateSlice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) ListSlice(ctx context.Context, req *ListSliceRequest) (r *ListSliceResponse, err error) {
	var _args DatasetServiceListSliceArgs
	_args.Req = req
	var _result DatasetServiceListSliceResult
	if err = p.Client_().Call(ctx, "ListSlice", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) CreateDocumentReview(ctx context.Context, req *CreateDocumentReviewRequest) (r *CreateDocumentReviewResponse, err error) {
	var _args DatasetServiceCreateDocumentReviewArgs
	_args.Req = req
	var _result DatasetServiceCreateDocumentReviewResult
	if err = p.Client_().Call(ctx, "CreateDocumentReview", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) MGetDocumentReview(ctx context.Context, req *MGetDocumentReviewRequest) (r *MGetDocumentReviewResponse, err error) {
	var _args DatasetServiceMGetDocumentReviewArgs
	_args.Req = req
	var _result DatasetServiceMGetDocumentReviewResult
	if err = p.Client_().Call(ctx, "MGetDocumentReview", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *DatasetServiceClient) SaveDocumentReview(ctx context.Context, req *SaveDocumentReviewRequest) (r *SaveDocumentReviewResponse, err error) {
	var _args DatasetServiceSaveDocumentReviewArgs
	_args.Req = req
	var _result DatasetServiceSaveDocumentReviewResult
	if err = p.Client_().Call(ctx, "SaveDocumentReview", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type DatasetServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DatasetService
}

func (p *DatasetServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DatasetServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DatasetServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDatasetServiceProcessor(handler DatasetService) *DatasetServiceProcessor {
	self := &DatasetServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("GetIconForDataset", &datasetServiceProcessorGetIconForDataset{handler: handler})
	self.AddToProcessorMap("CreateDataset", &datasetServiceProcessorCreateDataset{handler: handler})
	self.AddToProcessorMap("DatasetDetail", &datasetServiceProcessorDatasetDetail{handler: handler})
	self.AddToProcessorMap("ListDataset", &datasetServiceProcessorListDataset{handler: handler})
	self.AddToProcessorMap("DeleteDataset", &datasetServiceProcessorDeleteDataset{handler: handler})
	self.AddToProcessorMap("UpdateDataset", &datasetServiceProcessorUpdateDataset{handler: handler})
	self.AddToProcessorMap("CreateDocument", &datasetServiceProcessorCreateDocument{handler: handler})
	self.AddToProcessorMap("ListDocument", &datasetServiceProcessorListDocument{handler: handler})
	self.AddToProcessorMap("DeleteDocument", &datasetServiceProcessorDeleteDocument{handler: handler})
	self.AddToProcessorMap("UpdateDocument", &datasetServiceProcessorUpdateDocument{handler: handler})
	self.AddToProcessorMap("GetDocumentProgress", &datasetServiceProcessorGetDocumentProgress{handler: handler})
	self.AddToProcessorMap("Resegment", &datasetServiceProcessorResegment{handler: handler})
	self.AddToProcessorMap("UpdatePhotoCaption", &datasetServiceProcessorUpdatePhotoCaption{handler: handler})
	self.AddToProcessorMap("ListPhoto", &datasetServiceProcessorListPhoto{handler: handler})
	self.AddToProcessorMap("PhotoDetail", &datasetServiceProcessorPhotoDetail{handler: handler})
	self.AddToProcessorMap("ExtractPhotoCaption", &datasetServiceProcessorExtractPhotoCaption{handler: handler})
	self.AddToProcessorMap("GetTableSchema", &datasetServiceProcessorGetTableSchema{handler: handler})
	self.AddToProcessorMap("ValidateTableSchema", &datasetServiceProcessorValidateTableSchema{handler: handler})
	self.AddToProcessorMap("DeleteSlice", &datasetServiceProcessorDeleteSlice{handler: handler})
	self.AddToProcessorMap("CreateSlice", &datasetServiceProcessorCreateSlice{handler: handler})
	self.AddToProcessorMap("UpdateSlice", &datasetServiceProcessorUpdateSlice{handler: handler})
	self.AddToProcessorMap("ListSlice", &datasetServiceProcessorListSlice{handler: handler})
	self.AddToProcessorMap("CreateDocumentReview", &datasetServiceProcessorCreateDocumentReview{handler: handler})
	self.AddToProcessorMap("MGetDocumentReview", &datasetServiceProcessorMGetDocumentReview{handler: handler})
	self.AddToProcessorMap("SaveDocumentReview", &datasetServiceProcessorSaveDocumentReview{handler: handler})
	return self
}
func (p *DatasetServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type datasetServiceProcessorGetIconForDataset struct {
	handler DatasetService
}

func (p *datasetServiceProcessorGetIconForDataset) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceGetIconForDatasetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetIconForDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceGetIconForDatasetResult{}
	var retval *GetIconResponse
	if retval, err2 = p.handler.GetIconForDataset(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetIconForDataset: "+err2.Error())
		oprot.WriteMessageBegin("GetIconForDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetIconForDataset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorCreateDataset struct {
	handler DatasetService
}

func (p *datasetServiceProcessorCreateDataset) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceCreateDatasetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CreateDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceCreateDatasetResult{}
	var retval *CreateDatasetResponse
	if retval, err2 = p.handler.CreateDataset(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateDataset: "+err2.Error())
		oprot.WriteMessageBegin("CreateDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CreateDataset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorDatasetDetail struct {
	handler DatasetService
}

func (p *datasetServiceProcessorDatasetDetail) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceDatasetDetailArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DatasetDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceDatasetDetailResult{}
	var retval *DatasetDetailResponse
	if retval, err2 = p.handler.DatasetDetail(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DatasetDetail: "+err2.Error())
		oprot.WriteMessageBegin("DatasetDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DatasetDetail", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorListDataset struct {
	handler DatasetService
}

func (p *datasetServiceProcessorListDataset) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceListDatasetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceListDatasetResult{}
	var retval *ListDatasetResponse
	if retval, err2 = p.handler.ListDataset(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListDataset: "+err2.Error())
		oprot.WriteMessageBegin("ListDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListDataset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorDeleteDataset struct {
	handler DatasetService
}

func (p *datasetServiceProcessorDeleteDataset) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceDeleteDatasetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DeleteDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceDeleteDatasetResult{}
	var retval *DeleteDatasetResponse
	if retval, err2 = p.handler.DeleteDataset(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteDataset: "+err2.Error())
		oprot.WriteMessageBegin("DeleteDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DeleteDataset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorUpdateDataset struct {
	handler DatasetService
}

func (p *datasetServiceProcessorUpdateDataset) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceUpdateDatasetArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceUpdateDatasetResult{}
	var retval *UpdateDatasetResponse
	if retval, err2 = p.handler.UpdateDataset(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateDataset: "+err2.Error())
		oprot.WriteMessageBegin("UpdateDataset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateDataset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorCreateDocument struct {
	handler DatasetService
}

func (p *datasetServiceProcessorCreateDocument) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceCreateDocumentArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CreateDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceCreateDocumentResult{}
	var retval *CreateDocumentResponse
	if retval, err2 = p.handler.CreateDocument(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateDocument: "+err2.Error())
		oprot.WriteMessageBegin("CreateDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CreateDocument", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorListDocument struct {
	handler DatasetService
}

func (p *datasetServiceProcessorListDocument) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceListDocumentArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceListDocumentResult{}
	var retval *ListDocumentResponse
	if retval, err2 = p.handler.ListDocument(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListDocument: "+err2.Error())
		oprot.WriteMessageBegin("ListDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListDocument", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorDeleteDocument struct {
	handler DatasetService
}

func (p *datasetServiceProcessorDeleteDocument) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceDeleteDocumentArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DeleteDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceDeleteDocumentResult{}
	var retval *DeleteDocumentResponse
	if retval, err2 = p.handler.DeleteDocument(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteDocument: "+err2.Error())
		oprot.WriteMessageBegin("DeleteDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DeleteDocument", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorUpdateDocument struct {
	handler DatasetService
}

func (p *datasetServiceProcessorUpdateDocument) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceUpdateDocumentArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceUpdateDocumentResult{}
	var retval *UpdateDocumentResponse
	if retval, err2 = p.handler.UpdateDocument(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateDocument: "+err2.Error())
		oprot.WriteMessageBegin("UpdateDocument", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateDocument", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorGetDocumentProgress struct {
	handler DatasetService
}

func (p *datasetServiceProcessorGetDocumentProgress) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceGetDocumentProgressArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetDocumentProgress", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceGetDocumentProgressResult{}
	var retval *GetDocumentProgressResponse
	if retval, err2 = p.handler.GetDocumentProgress(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetDocumentProgress: "+err2.Error())
		oprot.WriteMessageBegin("GetDocumentProgress", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetDocumentProgress", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorResegment struct {
	handler DatasetService
}

func (p *datasetServiceProcessorResegment) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceResegmentArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("Resegment", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceResegmentResult{}
	var retval *ResegmentResponse
	if retval, err2 = p.handler.Resegment(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing Resegment: "+err2.Error())
		oprot.WriteMessageBegin("Resegment", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("Resegment", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorUpdatePhotoCaption struct {
	handler DatasetService
}

func (p *datasetServiceProcessorUpdatePhotoCaption) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceUpdatePhotoCaptionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdatePhotoCaption", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceUpdatePhotoCaptionResult{}
	var retval *UpdatePhotoCaptionResponse
	if retval, err2 = p.handler.UpdatePhotoCaption(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdatePhotoCaption: "+err2.Error())
		oprot.WriteMessageBegin("UpdatePhotoCaption", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdatePhotoCaption", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorListPhoto struct {
	handler DatasetService
}

func (p *datasetServiceProcessorListPhoto) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceListPhotoArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListPhoto", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceListPhotoResult{}
	var retval *ListPhotoResponse
	if retval, err2 = p.handler.ListPhoto(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListPhoto: "+err2.Error())
		oprot.WriteMessageBegin("ListPhoto", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListPhoto", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorPhotoDetail struct {
	handler DatasetService
}

func (p *datasetServiceProcessorPhotoDetail) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServicePhotoDetailArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("PhotoDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServicePhotoDetailResult{}
	var retval *PhotoDetailResponse
	if retval, err2 = p.handler.PhotoDetail(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PhotoDetail: "+err2.Error())
		oprot.WriteMessageBegin("PhotoDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("PhotoDetail", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorExtractPhotoCaption struct {
	handler DatasetService
}

func (p *datasetServiceProcessorExtractPhotoCaption) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceExtractPhotoCaptionArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ExtractPhotoCaption", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceExtractPhotoCaptionResult{}
	var retval *ExtractPhotoCaptionResponse
	if retval, err2 = p.handler.ExtractPhotoCaption(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ExtractPhotoCaption: "+err2.Error())
		oprot.WriteMessageBegin("ExtractPhotoCaption", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ExtractPhotoCaption", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorGetTableSchema struct {
	handler DatasetService
}

func (p *datasetServiceProcessorGetTableSchema) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceGetTableSchemaArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("GetTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceGetTableSchemaResult{}
	var retval *GetTableSchemaResponse
	if retval, err2 = p.handler.GetTableSchema(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetTableSchema: "+err2.Error())
		oprot.WriteMessageBegin("GetTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("GetTableSchema", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorValidateTableSchema struct {
	handler DatasetService
}

func (p *datasetServiceProcessorValidateTableSchema) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceValidateTableSchemaArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ValidateTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceValidateTableSchemaResult{}
	var retval *ValidateTableSchemaResponse
	if retval, err2 = p.handler.ValidateTableSchema(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ValidateTableSchema: "+err2.Error())
		oprot.WriteMessageBegin("ValidateTableSchema", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ValidateTableSchema", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorDeleteSlice struct {
	handler DatasetService
}

func (p *datasetServiceProcessorDeleteSlice) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceDeleteSliceArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("DeleteSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceDeleteSliceResult{}
	var retval *DeleteSliceResponse
	if retval, err2 = p.handler.DeleteSlice(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteSlice: "+err2.Error())
		oprot.WriteMessageBegin("DeleteSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("DeleteSlice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorCreateSlice struct {
	handler DatasetService
}

func (p *datasetServiceProcessorCreateSlice) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceCreateSliceArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CreateSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceCreateSliceResult{}
	var retval *CreateSliceResponse
	if retval, err2 = p.handler.CreateSlice(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateSlice: "+err2.Error())
		oprot.WriteMessageBegin("CreateSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CreateSlice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorUpdateSlice struct {
	handler DatasetService
}

func (p *datasetServiceProcessorUpdateSlice) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceUpdateSliceArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("UpdateSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceUpdateSliceResult{}
	var retval *UpdateSliceResponse
	if retval, err2 = p.handler.UpdateSlice(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateSlice: "+err2.Error())
		oprot.WriteMessageBegin("UpdateSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("UpdateSlice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorListSlice struct {
	handler DatasetService
}

func (p *datasetServiceProcessorListSlice) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceListSliceArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("ListSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceListSliceResult{}
	var retval *ListSliceResponse
	if retval, err2 = p.handler.ListSlice(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListSlice: "+err2.Error())
		oprot.WriteMessageBegin("ListSlice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("ListSlice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorCreateDocumentReview struct {
	handler DatasetService
}

func (p *datasetServiceProcessorCreateDocumentReview) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceCreateDocumentReviewArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("CreateDocumentReview", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceCreateDocumentReviewResult{}
	var retval *CreateDocumentReviewResponse
	if retval, err2 = p.handler.CreateDocumentReview(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateDocumentReview: "+err2.Error())
		oprot.WriteMessageBegin("CreateDocumentReview", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("CreateDocumentReview", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorMGetDocumentReview struct {
	handler DatasetService
}

func (p *datasetServiceProcessorMGetDocumentReview) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceMGetDocumentReviewArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("MGetDocumentReview", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceMGetDocumentReviewResult{}
	var retval *MGetDocumentReviewResponse
	if retval, err2 = p.handler.MGetDocumentReview(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing MGetDocumentReview: "+err2.Error())
		oprot.WriteMessageBegin("MGetDocumentReview", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("MGetDocumentReview", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type datasetServiceProcessorSaveDocumentReview struct {
	handler DatasetService
}

func (p *datasetServiceProcessorSaveDocumentReview) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := DatasetServiceSaveDocumentReviewArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("SaveDocumentReview", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := DatasetServiceSaveDocumentReviewResult{}
	var retval *SaveDocumentReviewResponse
	if retval, err2 = p.handler.SaveDocumentReview(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing SaveDocumentReview: "+err2.Error())
		oprot.WriteMessageBegin("SaveDocumentReview", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("SaveDocumentReview", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type DatasetServiceGetIconForDatasetArgs struct {
	Req *GetIconRequest `thrift:"req,1"`
}

func NewDatasetServiceGetIconForDatasetArgs() *DatasetServiceGetIconForDatasetArgs {
	return &DatasetServiceGetIconForDatasetArgs{}
}

func (p *DatasetServiceGetIconForDatasetArgs) InitDefault() {
}

var DatasetServiceGetIconForDatasetArgs_Req_DEFAULT *GetIconRequest

func (p *DatasetServiceGetIconForDatasetArgs) GetReq() (v *GetIconRequest) {
	if !p.IsSetReq() {
		return DatasetServiceGetIconForDatasetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceGetIconForDatasetArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceGetIconForDatasetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceGetIconForDatasetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceGetIconForDatasetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceGetIconForDatasetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetIconRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceGetIconForDatasetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetIconForDataset_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceGetIconForDatasetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceGetIconForDatasetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceGetIconForDatasetArgs(%+v)", *p)

}

type DatasetServiceGetIconForDatasetResult struct {
	Success *GetIconResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceGetIconForDatasetResult() *DatasetServiceGetIconForDatasetResult {
	return &DatasetServiceGetIconForDatasetResult{}
}

func (p *DatasetServiceGetIconForDatasetResult) InitDefault() {
}

var DatasetServiceGetIconForDatasetResult_Success_DEFAULT *GetIconResponse

func (p *DatasetServiceGetIconForDatasetResult) GetSuccess() (v *GetIconResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceGetIconForDatasetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceGetIconForDatasetResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceGetIconForDatasetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceGetIconForDatasetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceGetIconForDatasetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceGetIconForDatasetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetIconResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceGetIconForDatasetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetIconForDataset_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceGetIconForDatasetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceGetIconForDatasetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceGetIconForDatasetResult(%+v)", *p)

}

type DatasetServiceCreateDatasetArgs struct {
	Req *CreateDatasetRequest `thrift:"req,1"`
}

func NewDatasetServiceCreateDatasetArgs() *DatasetServiceCreateDatasetArgs {
	return &DatasetServiceCreateDatasetArgs{}
}

func (p *DatasetServiceCreateDatasetArgs) InitDefault() {
}

var DatasetServiceCreateDatasetArgs_Req_DEFAULT *CreateDatasetRequest

func (p *DatasetServiceCreateDatasetArgs) GetReq() (v *CreateDatasetRequest) {
	if !p.IsSetReq() {
		return DatasetServiceCreateDatasetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceCreateDatasetArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceCreateDatasetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceCreateDatasetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateDatasetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateDatasetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateDatasetRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceCreateDatasetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDataset_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateDatasetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceCreateDatasetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateDatasetArgs(%+v)", *p)

}

type DatasetServiceCreateDatasetResult struct {
	Success *CreateDatasetResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceCreateDatasetResult() *DatasetServiceCreateDatasetResult {
	return &DatasetServiceCreateDatasetResult{}
}

func (p *DatasetServiceCreateDatasetResult) InitDefault() {
}

var DatasetServiceCreateDatasetResult_Success_DEFAULT *CreateDatasetResponse

func (p *DatasetServiceCreateDatasetResult) GetSuccess() (v *CreateDatasetResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceCreateDatasetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceCreateDatasetResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceCreateDatasetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceCreateDatasetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateDatasetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateDatasetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateDatasetResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceCreateDatasetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDataset_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateDatasetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceCreateDatasetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateDatasetResult(%+v)", *p)

}

type DatasetServiceDatasetDetailArgs struct {
	Req *DatasetDetailRequest `thrift:"req,1"`
}

func NewDatasetServiceDatasetDetailArgs() *DatasetServiceDatasetDetailArgs {
	return &DatasetServiceDatasetDetailArgs{}
}

func (p *DatasetServiceDatasetDetailArgs) InitDefault() {
}

var DatasetServiceDatasetDetailArgs_Req_DEFAULT *DatasetDetailRequest

func (p *DatasetServiceDatasetDetailArgs) GetReq() (v *DatasetDetailRequest) {
	if !p.IsSetReq() {
		return DatasetServiceDatasetDetailArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceDatasetDetailArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceDatasetDetailArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceDatasetDetailArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDatasetDetailArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDatasetDetailArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDatasetDetailRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceDatasetDetailArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DatasetDetail_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDatasetDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceDatasetDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDatasetDetailArgs(%+v)", *p)

}

type DatasetServiceDatasetDetailResult struct {
	Success *DatasetDetailResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceDatasetDetailResult() *DatasetServiceDatasetDetailResult {
	return &DatasetServiceDatasetDetailResult{}
}

func (p *DatasetServiceDatasetDetailResult) InitDefault() {
}

var DatasetServiceDatasetDetailResult_Success_DEFAULT *DatasetDetailResponse

func (p *DatasetServiceDatasetDetailResult) GetSuccess() (v *DatasetDetailResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceDatasetDetailResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceDatasetDetailResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceDatasetDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceDatasetDetailResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDatasetDetailResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDatasetDetailResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDatasetDetailResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceDatasetDetailResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DatasetDetail_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDatasetDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceDatasetDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDatasetDetailResult(%+v)", *p)

}

type DatasetServiceListDatasetArgs struct {
	Req *ListDatasetRequest `thrift:"req,1"`
}

func NewDatasetServiceListDatasetArgs() *DatasetServiceListDatasetArgs {
	return &DatasetServiceListDatasetArgs{}
}

func (p *DatasetServiceListDatasetArgs) InitDefault() {
}

var DatasetServiceListDatasetArgs_Req_DEFAULT *ListDatasetRequest

func (p *DatasetServiceListDatasetArgs) GetReq() (v *ListDatasetRequest) {
	if !p.IsSetReq() {
		return DatasetServiceListDatasetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceListDatasetArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceListDatasetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceListDatasetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListDatasetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListDatasetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListDatasetRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceListDatasetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDataset_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListDatasetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceListDatasetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListDatasetArgs(%+v)", *p)

}

type DatasetServiceListDatasetResult struct {
	Success *ListDatasetResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceListDatasetResult() *DatasetServiceListDatasetResult {
	return &DatasetServiceListDatasetResult{}
}

func (p *DatasetServiceListDatasetResult) InitDefault() {
}

var DatasetServiceListDatasetResult_Success_DEFAULT *ListDatasetResponse

func (p *DatasetServiceListDatasetResult) GetSuccess() (v *ListDatasetResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceListDatasetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceListDatasetResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceListDatasetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceListDatasetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListDatasetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListDatasetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListDatasetResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceListDatasetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDataset_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListDatasetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceListDatasetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListDatasetResult(%+v)", *p)

}

type DatasetServiceDeleteDatasetArgs struct {
	Req *DeleteDatasetRequest `thrift:"req,1"`
}

func NewDatasetServiceDeleteDatasetArgs() *DatasetServiceDeleteDatasetArgs {
	return &DatasetServiceDeleteDatasetArgs{}
}

func (p *DatasetServiceDeleteDatasetArgs) InitDefault() {
}

var DatasetServiceDeleteDatasetArgs_Req_DEFAULT *DeleteDatasetRequest

func (p *DatasetServiceDeleteDatasetArgs) GetReq() (v *DeleteDatasetRequest) {
	if !p.IsSetReq() {
		return DatasetServiceDeleteDatasetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceDeleteDatasetArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceDeleteDatasetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceDeleteDatasetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDeleteDatasetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDatasetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteDatasetRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceDeleteDatasetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDataset_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDatasetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceDeleteDatasetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDeleteDatasetArgs(%+v)", *p)

}

type DatasetServiceDeleteDatasetResult struct {
	Success *DeleteDatasetResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceDeleteDatasetResult() *DatasetServiceDeleteDatasetResult {
	return &DatasetServiceDeleteDatasetResult{}
}

func (p *DatasetServiceDeleteDatasetResult) InitDefault() {
}

var DatasetServiceDeleteDatasetResult_Success_DEFAULT *DeleteDatasetResponse

func (p *DatasetServiceDeleteDatasetResult) GetSuccess() (v *DeleteDatasetResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceDeleteDatasetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceDeleteDatasetResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceDeleteDatasetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceDeleteDatasetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDeleteDatasetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDatasetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteDatasetResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceDeleteDatasetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDataset_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDatasetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceDeleteDatasetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDeleteDatasetResult(%+v)", *p)

}

type DatasetServiceUpdateDatasetArgs struct {
	Req *UpdateDatasetRequest `thrift:"req,1"`
}

func NewDatasetServiceUpdateDatasetArgs() *DatasetServiceUpdateDatasetArgs {
	return &DatasetServiceUpdateDatasetArgs{}
}

func (p *DatasetServiceUpdateDatasetArgs) InitDefault() {
}

var DatasetServiceUpdateDatasetArgs_Req_DEFAULT *UpdateDatasetRequest

func (p *DatasetServiceUpdateDatasetArgs) GetReq() (v *UpdateDatasetRequest) {
	if !p.IsSetReq() {
		return DatasetServiceUpdateDatasetArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceUpdateDatasetArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceUpdateDatasetArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceUpdateDatasetArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdateDatasetArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDatasetArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdateDatasetRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceUpdateDatasetArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDataset_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDatasetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceUpdateDatasetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdateDatasetArgs(%+v)", *p)

}

type DatasetServiceUpdateDatasetResult struct {
	Success *UpdateDatasetResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceUpdateDatasetResult() *DatasetServiceUpdateDatasetResult {
	return &DatasetServiceUpdateDatasetResult{}
}

func (p *DatasetServiceUpdateDatasetResult) InitDefault() {
}

var DatasetServiceUpdateDatasetResult_Success_DEFAULT *UpdateDatasetResponse

func (p *DatasetServiceUpdateDatasetResult) GetSuccess() (v *UpdateDatasetResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceUpdateDatasetResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceUpdateDatasetResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceUpdateDatasetResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceUpdateDatasetResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdateDatasetResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDatasetResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdateDatasetResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceUpdateDatasetResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDataset_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDatasetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceUpdateDatasetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdateDatasetResult(%+v)", *p)

}

type DatasetServiceCreateDocumentArgs struct {
	Req *CreateDocumentRequest `thrift:"req,1"`
}

func NewDatasetServiceCreateDocumentArgs() *DatasetServiceCreateDocumentArgs {
	return &DatasetServiceCreateDocumentArgs{}
}

func (p *DatasetServiceCreateDocumentArgs) InitDefault() {
}

var DatasetServiceCreateDocumentArgs_Req_DEFAULT *CreateDocumentRequest

func (p *DatasetServiceCreateDocumentArgs) GetReq() (v *CreateDocumentRequest) {
	if !p.IsSetReq() {
		return DatasetServiceCreateDocumentArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceCreateDocumentArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceCreateDocumentArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceCreateDocumentArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateDocumentArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateDocumentRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceCreateDocumentArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDocument_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateDocumentArgs(%+v)", *p)

}

type DatasetServiceCreateDocumentResult struct {
	Success *CreateDocumentResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceCreateDocumentResult() *DatasetServiceCreateDocumentResult {
	return &DatasetServiceCreateDocumentResult{}
}

func (p *DatasetServiceCreateDocumentResult) InitDefault() {
}

var DatasetServiceCreateDocumentResult_Success_DEFAULT *CreateDocumentResponse

func (p *DatasetServiceCreateDocumentResult) GetSuccess() (v *CreateDocumentResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceCreateDocumentResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceCreateDocumentResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceCreateDocumentResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceCreateDocumentResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateDocumentResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateDocumentResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceCreateDocumentResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDocument_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateDocumentResult(%+v)", *p)

}

type DatasetServiceListDocumentArgs struct {
	Req *ListDocumentRequest `thrift:"req,1"`
}

func NewDatasetServiceListDocumentArgs() *DatasetServiceListDocumentArgs {
	return &DatasetServiceListDocumentArgs{}
}

func (p *DatasetServiceListDocumentArgs) InitDefault() {
}

var DatasetServiceListDocumentArgs_Req_DEFAULT *ListDocumentRequest

func (p *DatasetServiceListDocumentArgs) GetReq() (v *ListDocumentRequest) {
	if !p.IsSetReq() {
		return DatasetServiceListDocumentArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceListDocumentArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceListDocumentArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceListDocumentArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListDocumentArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListDocumentArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListDocumentRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceListDocumentArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDocument_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListDocumentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceListDocumentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListDocumentArgs(%+v)", *p)

}

type DatasetServiceListDocumentResult struct {
	Success *ListDocumentResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceListDocumentResult() *DatasetServiceListDocumentResult {
	return &DatasetServiceListDocumentResult{}
}

func (p *DatasetServiceListDocumentResult) InitDefault() {
}

var DatasetServiceListDocumentResult_Success_DEFAULT *ListDocumentResponse

func (p *DatasetServiceListDocumentResult) GetSuccess() (v *ListDocumentResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceListDocumentResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceListDocumentResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceListDocumentResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceListDocumentResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListDocumentResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListDocumentResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListDocumentResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceListDocumentResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListDocument_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListDocumentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceListDocumentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListDocumentResult(%+v)", *p)

}

type DatasetServiceDeleteDocumentArgs struct {
	Req *DeleteDocumentRequest `thrift:"req,1"`
}

func NewDatasetServiceDeleteDocumentArgs() *DatasetServiceDeleteDocumentArgs {
	return &DatasetServiceDeleteDocumentArgs{}
}

func (p *DatasetServiceDeleteDocumentArgs) InitDefault() {
}

var DatasetServiceDeleteDocumentArgs_Req_DEFAULT *DeleteDocumentRequest

func (p *DatasetServiceDeleteDocumentArgs) GetReq() (v *DeleteDocumentRequest) {
	if !p.IsSetReq() {
		return DatasetServiceDeleteDocumentArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceDeleteDocumentArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceDeleteDocumentArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceDeleteDocumentArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDeleteDocumentArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDocumentArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteDocumentRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceDeleteDocumentArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDocument_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDocumentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceDeleteDocumentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDeleteDocumentArgs(%+v)", *p)

}

type DatasetServiceDeleteDocumentResult struct {
	Success *DeleteDocumentResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceDeleteDocumentResult() *DatasetServiceDeleteDocumentResult {
	return &DatasetServiceDeleteDocumentResult{}
}

func (p *DatasetServiceDeleteDocumentResult) InitDefault() {
}

var DatasetServiceDeleteDocumentResult_Success_DEFAULT *DeleteDocumentResponse

func (p *DatasetServiceDeleteDocumentResult) GetSuccess() (v *DeleteDocumentResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceDeleteDocumentResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceDeleteDocumentResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceDeleteDocumentResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceDeleteDocumentResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDeleteDocumentResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDocumentResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteDocumentResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceDeleteDocumentResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteDocument_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDeleteDocumentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceDeleteDocumentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDeleteDocumentResult(%+v)", *p)

}

type DatasetServiceUpdateDocumentArgs struct {
	Req *UpdateDocumentRequest `thrift:"req,1"`
}

func NewDatasetServiceUpdateDocumentArgs() *DatasetServiceUpdateDocumentArgs {
	return &DatasetServiceUpdateDocumentArgs{}
}

func (p *DatasetServiceUpdateDocumentArgs) InitDefault() {
}

var DatasetServiceUpdateDocumentArgs_Req_DEFAULT *UpdateDocumentRequest

func (p *DatasetServiceUpdateDocumentArgs) GetReq() (v *UpdateDocumentRequest) {
	if !p.IsSetReq() {
		return DatasetServiceUpdateDocumentArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceUpdateDocumentArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceUpdateDocumentArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceUpdateDocumentArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdateDocumentArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDocumentArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdateDocumentRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceUpdateDocumentArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDocument_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDocumentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceUpdateDocumentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdateDocumentArgs(%+v)", *p)

}

type DatasetServiceUpdateDocumentResult struct {
	Success *UpdateDocumentResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceUpdateDocumentResult() *DatasetServiceUpdateDocumentResult {
	return &DatasetServiceUpdateDocumentResult{}
}

func (p *DatasetServiceUpdateDocumentResult) InitDefault() {
}

var DatasetServiceUpdateDocumentResult_Success_DEFAULT *UpdateDocumentResponse

func (p *DatasetServiceUpdateDocumentResult) GetSuccess() (v *UpdateDocumentResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceUpdateDocumentResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceUpdateDocumentResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceUpdateDocumentResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceUpdateDocumentResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdateDocumentResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDocumentResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdateDocumentResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceUpdateDocumentResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateDocument_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdateDocumentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceUpdateDocumentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdateDocumentResult(%+v)", *p)

}

type DatasetServiceGetDocumentProgressArgs struct {
	Req *GetDocumentProgressRequest `thrift:"req,1"`
}

func NewDatasetServiceGetDocumentProgressArgs() *DatasetServiceGetDocumentProgressArgs {
	return &DatasetServiceGetDocumentProgressArgs{}
}

func (p *DatasetServiceGetDocumentProgressArgs) InitDefault() {
}

var DatasetServiceGetDocumentProgressArgs_Req_DEFAULT *GetDocumentProgressRequest

func (p *DatasetServiceGetDocumentProgressArgs) GetReq() (v *GetDocumentProgressRequest) {
	if !p.IsSetReq() {
		return DatasetServiceGetDocumentProgressArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceGetDocumentProgressArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceGetDocumentProgressArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceGetDocumentProgressArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceGetDocumentProgressArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceGetDocumentProgressArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetDocumentProgressRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceGetDocumentProgressArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDocumentProgress_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceGetDocumentProgressArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceGetDocumentProgressArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceGetDocumentProgressArgs(%+v)", *p)

}

type DatasetServiceGetDocumentProgressResult struct {
	Success *GetDocumentProgressResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceGetDocumentProgressResult() *DatasetServiceGetDocumentProgressResult {
	return &DatasetServiceGetDocumentProgressResult{}
}

func (p *DatasetServiceGetDocumentProgressResult) InitDefault() {
}

var DatasetServiceGetDocumentProgressResult_Success_DEFAULT *GetDocumentProgressResponse

func (p *DatasetServiceGetDocumentProgressResult) GetSuccess() (v *GetDocumentProgressResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceGetDocumentProgressResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceGetDocumentProgressResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceGetDocumentProgressResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceGetDocumentProgressResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceGetDocumentProgressResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceGetDocumentProgressResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetDocumentProgressResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceGetDocumentProgressResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetDocumentProgress_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceGetDocumentProgressResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceGetDocumentProgressResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceGetDocumentProgressResult(%+v)", *p)

}

type DatasetServiceResegmentArgs struct {
	Req *ResegmentRequest `thrift:"req,1"`
}

func NewDatasetServiceResegmentArgs() *DatasetServiceResegmentArgs {
	return &DatasetServiceResegmentArgs{}
}

func (p *DatasetServiceResegmentArgs) InitDefault() {
}

var DatasetServiceResegmentArgs_Req_DEFAULT *ResegmentRequest

func (p *DatasetServiceResegmentArgs) GetReq() (v *ResegmentRequest) {
	if !p.IsSetReq() {
		return DatasetServiceResegmentArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceResegmentArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceResegmentArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceResegmentArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceResegmentArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceResegmentArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewResegmentRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceResegmentArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Resegment_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceResegmentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceResegmentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceResegmentArgs(%+v)", *p)

}

type DatasetServiceResegmentResult struct {
	Success *ResegmentResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceResegmentResult() *DatasetServiceResegmentResult {
	return &DatasetServiceResegmentResult{}
}

func (p *DatasetServiceResegmentResult) InitDefault() {
}

var DatasetServiceResegmentResult_Success_DEFAULT *ResegmentResponse

func (p *DatasetServiceResegmentResult) GetSuccess() (v *ResegmentResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceResegmentResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceResegmentResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceResegmentResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceResegmentResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceResegmentResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceResegmentResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewResegmentResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceResegmentResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("Resegment_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceResegmentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceResegmentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceResegmentResult(%+v)", *p)

}

type DatasetServiceUpdatePhotoCaptionArgs struct {
	Req *UpdatePhotoCaptionRequest `thrift:"req,1"`
}

func NewDatasetServiceUpdatePhotoCaptionArgs() *DatasetServiceUpdatePhotoCaptionArgs {
	return &DatasetServiceUpdatePhotoCaptionArgs{}
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) InitDefault() {
}

var DatasetServiceUpdatePhotoCaptionArgs_Req_DEFAULT *UpdatePhotoCaptionRequest

func (p *DatasetServiceUpdatePhotoCaptionArgs) GetReq() (v *UpdatePhotoCaptionRequest) {
	if !p.IsSetReq() {
		return DatasetServiceUpdatePhotoCaptionArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceUpdatePhotoCaptionArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdatePhotoCaptionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdatePhotoCaptionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePhotoCaption_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceUpdatePhotoCaptionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdatePhotoCaptionArgs(%+v)", *p)

}

type DatasetServiceUpdatePhotoCaptionResult struct {
	Success *UpdatePhotoCaptionResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceUpdatePhotoCaptionResult() *DatasetServiceUpdatePhotoCaptionResult {
	return &DatasetServiceUpdatePhotoCaptionResult{}
}

func (p *DatasetServiceUpdatePhotoCaptionResult) InitDefault() {
}

var DatasetServiceUpdatePhotoCaptionResult_Success_DEFAULT *UpdatePhotoCaptionResponse

func (p *DatasetServiceUpdatePhotoCaptionResult) GetSuccess() (v *UpdatePhotoCaptionResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceUpdatePhotoCaptionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceUpdatePhotoCaptionResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceUpdatePhotoCaptionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceUpdatePhotoCaptionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdatePhotoCaptionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdatePhotoCaptionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdatePhotoCaptionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceUpdatePhotoCaptionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdatePhotoCaption_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdatePhotoCaptionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceUpdatePhotoCaptionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdatePhotoCaptionResult(%+v)", *p)

}

type DatasetServiceListPhotoArgs struct {
	Req *ListPhotoRequest `thrift:"req,1"`
}

func NewDatasetServiceListPhotoArgs() *DatasetServiceListPhotoArgs {
	return &DatasetServiceListPhotoArgs{}
}

func (p *DatasetServiceListPhotoArgs) InitDefault() {
}

var DatasetServiceListPhotoArgs_Req_DEFAULT *ListPhotoRequest

func (p *DatasetServiceListPhotoArgs) GetReq() (v *ListPhotoRequest) {
	if !p.IsSetReq() {
		return DatasetServiceListPhotoArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceListPhotoArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceListPhotoArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceListPhotoArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListPhotoArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListPhotoArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListPhotoRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceListPhotoArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPhoto_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListPhotoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceListPhotoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListPhotoArgs(%+v)", *p)

}

type DatasetServiceListPhotoResult struct {
	Success *ListPhotoResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceListPhotoResult() *DatasetServiceListPhotoResult {
	return &DatasetServiceListPhotoResult{}
}

func (p *DatasetServiceListPhotoResult) InitDefault() {
}

var DatasetServiceListPhotoResult_Success_DEFAULT *ListPhotoResponse

func (p *DatasetServiceListPhotoResult) GetSuccess() (v *ListPhotoResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceListPhotoResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceListPhotoResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceListPhotoResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceListPhotoResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListPhotoResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListPhotoResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListPhotoResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceListPhotoResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListPhoto_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListPhotoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceListPhotoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListPhotoResult(%+v)", *p)

}

type DatasetServicePhotoDetailArgs struct {
	Req *PhotoDetailRequest `thrift:"req,1"`
}

func NewDatasetServicePhotoDetailArgs() *DatasetServicePhotoDetailArgs {
	return &DatasetServicePhotoDetailArgs{}
}

func (p *DatasetServicePhotoDetailArgs) InitDefault() {
}

var DatasetServicePhotoDetailArgs_Req_DEFAULT *PhotoDetailRequest

func (p *DatasetServicePhotoDetailArgs) GetReq() (v *PhotoDetailRequest) {
	if !p.IsSetReq() {
		return DatasetServicePhotoDetailArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServicePhotoDetailArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServicePhotoDetailArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServicePhotoDetailArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServicePhotoDetailArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServicePhotoDetailArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewPhotoDetailRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServicePhotoDetailArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PhotoDetail_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServicePhotoDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServicePhotoDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServicePhotoDetailArgs(%+v)", *p)

}

type DatasetServicePhotoDetailResult struct {
	Success *PhotoDetailResponse `thrift:"success,0,optional"`
}

func NewDatasetServicePhotoDetailResult() *DatasetServicePhotoDetailResult {
	return &DatasetServicePhotoDetailResult{}
}

func (p *DatasetServicePhotoDetailResult) InitDefault() {
}

var DatasetServicePhotoDetailResult_Success_DEFAULT *PhotoDetailResponse

func (p *DatasetServicePhotoDetailResult) GetSuccess() (v *PhotoDetailResponse) {
	if !p.IsSetSuccess() {
		return DatasetServicePhotoDetailResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServicePhotoDetailResult = map[int16]string{
	0: "success",
}

func (p *DatasetServicePhotoDetailResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServicePhotoDetailResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServicePhotoDetailResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServicePhotoDetailResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewPhotoDetailResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServicePhotoDetailResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("PhotoDetail_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServicePhotoDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServicePhotoDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServicePhotoDetailResult(%+v)", *p)

}

type DatasetServiceExtractPhotoCaptionArgs struct {
	Req *ExtractPhotoCaptionRequest `thrift:"req,1"`
}

func NewDatasetServiceExtractPhotoCaptionArgs() *DatasetServiceExtractPhotoCaptionArgs {
	return &DatasetServiceExtractPhotoCaptionArgs{}
}

func (p *DatasetServiceExtractPhotoCaptionArgs) InitDefault() {
}

var DatasetServiceExtractPhotoCaptionArgs_Req_DEFAULT *ExtractPhotoCaptionRequest

func (p *DatasetServiceExtractPhotoCaptionArgs) GetReq() (v *ExtractPhotoCaptionRequest) {
	if !p.IsSetReq() {
		return DatasetServiceExtractPhotoCaptionArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceExtractPhotoCaptionArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceExtractPhotoCaptionArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceExtractPhotoCaptionArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceExtractPhotoCaptionArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceExtractPhotoCaptionArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewExtractPhotoCaptionRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceExtractPhotoCaptionArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExtractPhotoCaption_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceExtractPhotoCaptionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceExtractPhotoCaptionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceExtractPhotoCaptionArgs(%+v)", *p)

}

type DatasetServiceExtractPhotoCaptionResult struct {
	Success *ExtractPhotoCaptionResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceExtractPhotoCaptionResult() *DatasetServiceExtractPhotoCaptionResult {
	return &DatasetServiceExtractPhotoCaptionResult{}
}

func (p *DatasetServiceExtractPhotoCaptionResult) InitDefault() {
}

var DatasetServiceExtractPhotoCaptionResult_Success_DEFAULT *ExtractPhotoCaptionResponse

func (p *DatasetServiceExtractPhotoCaptionResult) GetSuccess() (v *ExtractPhotoCaptionResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceExtractPhotoCaptionResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceExtractPhotoCaptionResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceExtractPhotoCaptionResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceExtractPhotoCaptionResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceExtractPhotoCaptionResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceExtractPhotoCaptionResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewExtractPhotoCaptionResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceExtractPhotoCaptionResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ExtractPhotoCaption_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceExtractPhotoCaptionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceExtractPhotoCaptionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceExtractPhotoCaptionResult(%+v)", *p)

}

type DatasetServiceGetTableSchemaArgs struct {
	Req *GetTableSchemaRequest `thrift:"req,1"`
}

func NewDatasetServiceGetTableSchemaArgs() *DatasetServiceGetTableSchemaArgs {
	return &DatasetServiceGetTableSchemaArgs{}
}

func (p *DatasetServiceGetTableSchemaArgs) InitDefault() {
}

var DatasetServiceGetTableSchemaArgs_Req_DEFAULT *GetTableSchemaRequest

func (p *DatasetServiceGetTableSchemaArgs) GetReq() (v *GetTableSchemaRequest) {
	if !p.IsSetReq() {
		return DatasetServiceGetTableSchemaArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceGetTableSchemaArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceGetTableSchemaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceGetTableSchemaArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceGetTableSchemaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceGetTableSchemaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewGetTableSchemaRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceGetTableSchemaArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetTableSchema_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceGetTableSchemaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceGetTableSchemaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceGetTableSchemaArgs(%+v)", *p)

}

type DatasetServiceGetTableSchemaResult struct {
	Success *GetTableSchemaResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceGetTableSchemaResult() *DatasetServiceGetTableSchemaResult {
	return &DatasetServiceGetTableSchemaResult{}
}

func (p *DatasetServiceGetTableSchemaResult) InitDefault() {
}

var DatasetServiceGetTableSchemaResult_Success_DEFAULT *GetTableSchemaResponse

func (p *DatasetServiceGetTableSchemaResult) GetSuccess() (v *GetTableSchemaResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceGetTableSchemaResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceGetTableSchemaResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceGetTableSchemaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceGetTableSchemaResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceGetTableSchemaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceGetTableSchemaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewGetTableSchemaResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceGetTableSchemaResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("GetTableSchema_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceGetTableSchemaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceGetTableSchemaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceGetTableSchemaResult(%+v)", *p)

}

type DatasetServiceValidateTableSchemaArgs struct {
	Req *ValidateTableSchemaRequest `thrift:"req,1"`
}

func NewDatasetServiceValidateTableSchemaArgs() *DatasetServiceValidateTableSchemaArgs {
	return &DatasetServiceValidateTableSchemaArgs{}
}

func (p *DatasetServiceValidateTableSchemaArgs) InitDefault() {
}

var DatasetServiceValidateTableSchemaArgs_Req_DEFAULT *ValidateTableSchemaRequest

func (p *DatasetServiceValidateTableSchemaArgs) GetReq() (v *ValidateTableSchemaRequest) {
	if !p.IsSetReq() {
		return DatasetServiceValidateTableSchemaArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceValidateTableSchemaArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceValidateTableSchemaArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceValidateTableSchemaArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceValidateTableSchemaArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceValidateTableSchemaArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewValidateTableSchemaRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceValidateTableSchemaArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ValidateTableSchema_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceValidateTableSchemaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceValidateTableSchemaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceValidateTableSchemaArgs(%+v)", *p)

}

type DatasetServiceValidateTableSchemaResult struct {
	Success *ValidateTableSchemaResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceValidateTableSchemaResult() *DatasetServiceValidateTableSchemaResult {
	return &DatasetServiceValidateTableSchemaResult{}
}

func (p *DatasetServiceValidateTableSchemaResult) InitDefault() {
}

var DatasetServiceValidateTableSchemaResult_Success_DEFAULT *ValidateTableSchemaResponse

func (p *DatasetServiceValidateTableSchemaResult) GetSuccess() (v *ValidateTableSchemaResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceValidateTableSchemaResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceValidateTableSchemaResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceValidateTableSchemaResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceValidateTableSchemaResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceValidateTableSchemaResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceValidateTableSchemaResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewValidateTableSchemaResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceValidateTableSchemaResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ValidateTableSchema_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceValidateTableSchemaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceValidateTableSchemaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceValidateTableSchemaResult(%+v)", *p)

}

type DatasetServiceDeleteSliceArgs struct {
	Req *DeleteSliceRequest `thrift:"req,1"`
}

func NewDatasetServiceDeleteSliceArgs() *DatasetServiceDeleteSliceArgs {
	return &DatasetServiceDeleteSliceArgs{}
}

func (p *DatasetServiceDeleteSliceArgs) InitDefault() {
}

var DatasetServiceDeleteSliceArgs_Req_DEFAULT *DeleteSliceRequest

func (p *DatasetServiceDeleteSliceArgs) GetReq() (v *DeleteSliceRequest) {
	if !p.IsSetReq() {
		return DatasetServiceDeleteSliceArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceDeleteSliceArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceDeleteSliceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceDeleteSliceArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDeleteSliceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDeleteSliceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteSliceRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceDeleteSliceArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteSlice_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDeleteSliceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceDeleteSliceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDeleteSliceArgs(%+v)", *p)

}

type DatasetServiceDeleteSliceResult struct {
	Success *DeleteSliceResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceDeleteSliceResult() *DatasetServiceDeleteSliceResult {
	return &DatasetServiceDeleteSliceResult{}
}

func (p *DatasetServiceDeleteSliceResult) InitDefault() {
}

var DatasetServiceDeleteSliceResult_Success_DEFAULT *DeleteSliceResponse

func (p *DatasetServiceDeleteSliceResult) GetSuccess() (v *DeleteSliceResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceDeleteSliceResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceDeleteSliceResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceDeleteSliceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceDeleteSliceResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceDeleteSliceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceDeleteSliceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteSliceResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceDeleteSliceResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteSlice_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceDeleteSliceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceDeleteSliceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceDeleteSliceResult(%+v)", *p)

}

type DatasetServiceCreateSliceArgs struct {
	Req *CreateSliceRequest `thrift:"req,1"`
}

func NewDatasetServiceCreateSliceArgs() *DatasetServiceCreateSliceArgs {
	return &DatasetServiceCreateSliceArgs{}
}

func (p *DatasetServiceCreateSliceArgs) InitDefault() {
}

var DatasetServiceCreateSliceArgs_Req_DEFAULT *CreateSliceRequest

func (p *DatasetServiceCreateSliceArgs) GetReq() (v *CreateSliceRequest) {
	if !p.IsSetReq() {
		return DatasetServiceCreateSliceArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceCreateSliceArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceCreateSliceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceCreateSliceArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateSliceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateSliceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateSliceRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceCreateSliceArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSlice_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateSliceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceCreateSliceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateSliceArgs(%+v)", *p)

}

type DatasetServiceCreateSliceResult struct {
	Success *CreateSliceResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceCreateSliceResult() *DatasetServiceCreateSliceResult {
	return &DatasetServiceCreateSliceResult{}
}

func (p *DatasetServiceCreateSliceResult) InitDefault() {
}

var DatasetServiceCreateSliceResult_Success_DEFAULT *CreateSliceResponse

func (p *DatasetServiceCreateSliceResult) GetSuccess() (v *CreateSliceResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceCreateSliceResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceCreateSliceResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceCreateSliceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceCreateSliceResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateSliceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateSliceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateSliceResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceCreateSliceResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateSlice_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateSliceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceCreateSliceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateSliceResult(%+v)", *p)

}

type DatasetServiceUpdateSliceArgs struct {
	Req *UpdateSliceRequest `thrift:"req,1"`
}

func NewDatasetServiceUpdateSliceArgs() *DatasetServiceUpdateSliceArgs {
	return &DatasetServiceUpdateSliceArgs{}
}

func (p *DatasetServiceUpdateSliceArgs) InitDefault() {
}

var DatasetServiceUpdateSliceArgs_Req_DEFAULT *UpdateSliceRequest

func (p *DatasetServiceUpdateSliceArgs) GetReq() (v *UpdateSliceRequest) {
	if !p.IsSetReq() {
		return DatasetServiceUpdateSliceArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceUpdateSliceArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceUpdateSliceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceUpdateSliceArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdateSliceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdateSliceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdateSliceRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceUpdateSliceArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateSlice_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdateSliceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceUpdateSliceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdateSliceArgs(%+v)", *p)

}

type DatasetServiceUpdateSliceResult struct {
	Success *UpdateSliceResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceUpdateSliceResult() *DatasetServiceUpdateSliceResult {
	return &DatasetServiceUpdateSliceResult{}
}

func (p *DatasetServiceUpdateSliceResult) InitDefault() {
}

var DatasetServiceUpdateSliceResult_Success_DEFAULT *UpdateSliceResponse

func (p *DatasetServiceUpdateSliceResult) GetSuccess() (v *UpdateSliceResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceUpdateSliceResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceUpdateSliceResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceUpdateSliceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceUpdateSliceResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceUpdateSliceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceUpdateSliceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdateSliceResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceUpdateSliceResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateSlice_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceUpdateSliceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceUpdateSliceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceUpdateSliceResult(%+v)", *p)

}

type DatasetServiceListSliceArgs struct {
	Req *ListSliceRequest `thrift:"req,1"`
}

func NewDatasetServiceListSliceArgs() *DatasetServiceListSliceArgs {
	return &DatasetServiceListSliceArgs{}
}

func (p *DatasetServiceListSliceArgs) InitDefault() {
}

var DatasetServiceListSliceArgs_Req_DEFAULT *ListSliceRequest

func (p *DatasetServiceListSliceArgs) GetReq() (v *ListSliceRequest) {
	if !p.IsSetReq() {
		return DatasetServiceListSliceArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceListSliceArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceListSliceArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceListSliceArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListSliceArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListSliceArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListSliceRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceListSliceArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlice_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListSliceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceListSliceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListSliceArgs(%+v)", *p)

}

type DatasetServiceListSliceResult struct {
	Success *ListSliceResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceListSliceResult() *DatasetServiceListSliceResult {
	return &DatasetServiceListSliceResult{}
}

func (p *DatasetServiceListSliceResult) InitDefault() {
}

var DatasetServiceListSliceResult_Success_DEFAULT *ListSliceResponse

func (p *DatasetServiceListSliceResult) GetSuccess() (v *ListSliceResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceListSliceResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceListSliceResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceListSliceResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceListSliceResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceListSliceResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceListSliceResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListSliceResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceListSliceResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListSlice_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceListSliceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceListSliceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceListSliceResult(%+v)", *p)

}

type DatasetServiceCreateDocumentReviewArgs struct {
	Req *CreateDocumentReviewRequest `thrift:"req,1"`
}

func NewDatasetServiceCreateDocumentReviewArgs() *DatasetServiceCreateDocumentReviewArgs {
	return &DatasetServiceCreateDocumentReviewArgs{}
}

func (p *DatasetServiceCreateDocumentReviewArgs) InitDefault() {
}

var DatasetServiceCreateDocumentReviewArgs_Req_DEFAULT *CreateDocumentReviewRequest

func (p *DatasetServiceCreateDocumentReviewArgs) GetReq() (v *CreateDocumentReviewRequest) {
	if !p.IsSetReq() {
		return DatasetServiceCreateDocumentReviewArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceCreateDocumentReviewArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceCreateDocumentReviewArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceCreateDocumentReviewArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateDocumentReviewArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentReviewArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateDocumentReviewRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceCreateDocumentReviewArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDocumentReview_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentReviewArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentReviewArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateDocumentReviewArgs(%+v)", *p)

}

type DatasetServiceCreateDocumentReviewResult struct {
	Success *CreateDocumentReviewResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceCreateDocumentReviewResult() *DatasetServiceCreateDocumentReviewResult {
	return &DatasetServiceCreateDocumentReviewResult{}
}

func (p *DatasetServiceCreateDocumentReviewResult) InitDefault() {
}

var DatasetServiceCreateDocumentReviewResult_Success_DEFAULT *CreateDocumentReviewResponse

func (p *DatasetServiceCreateDocumentReviewResult) GetSuccess() (v *CreateDocumentReviewResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceCreateDocumentReviewResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceCreateDocumentReviewResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceCreateDocumentReviewResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceCreateDocumentReviewResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceCreateDocumentReviewResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentReviewResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateDocumentReviewResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceCreateDocumentReviewResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateDocumentReview_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentReviewResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceCreateDocumentReviewResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceCreateDocumentReviewResult(%+v)", *p)

}

type DatasetServiceMGetDocumentReviewArgs struct {
	Req *MGetDocumentReviewRequest `thrift:"req,1"`
}

func NewDatasetServiceMGetDocumentReviewArgs() *DatasetServiceMGetDocumentReviewArgs {
	return &DatasetServiceMGetDocumentReviewArgs{}
}

func (p *DatasetServiceMGetDocumentReviewArgs) InitDefault() {
}

var DatasetServiceMGetDocumentReviewArgs_Req_DEFAULT *MGetDocumentReviewRequest

func (p *DatasetServiceMGetDocumentReviewArgs) GetReq() (v *MGetDocumentReviewRequest) {
	if !p.IsSetReq() {
		return DatasetServiceMGetDocumentReviewArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceMGetDocumentReviewArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceMGetDocumentReviewArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceMGetDocumentReviewArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceMGetDocumentReviewArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceMGetDocumentReviewArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewMGetDocumentReviewRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceMGetDocumentReviewArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MGetDocumentReview_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceMGetDocumentReviewArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceMGetDocumentReviewArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceMGetDocumentReviewArgs(%+v)", *p)

}

type DatasetServiceMGetDocumentReviewResult struct {
	Success *MGetDocumentReviewResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceMGetDocumentReviewResult() *DatasetServiceMGetDocumentReviewResult {
	return &DatasetServiceMGetDocumentReviewResult{}
}

func (p *DatasetServiceMGetDocumentReviewResult) InitDefault() {
}

var DatasetServiceMGetDocumentReviewResult_Success_DEFAULT *MGetDocumentReviewResponse

func (p *DatasetServiceMGetDocumentReviewResult) GetSuccess() (v *MGetDocumentReviewResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceMGetDocumentReviewResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceMGetDocumentReviewResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceMGetDocumentReviewResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceMGetDocumentReviewResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceMGetDocumentReviewResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceMGetDocumentReviewResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewMGetDocumentReviewResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceMGetDocumentReviewResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("MGetDocumentReview_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceMGetDocumentReviewResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceMGetDocumentReviewResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceMGetDocumentReviewResult(%+v)", *p)

}

type DatasetServiceSaveDocumentReviewArgs struct {
	Req *SaveDocumentReviewRequest `thrift:"req,1"`
}

func NewDatasetServiceSaveDocumentReviewArgs() *DatasetServiceSaveDocumentReviewArgs {
	return &DatasetServiceSaveDocumentReviewArgs{}
}

func (p *DatasetServiceSaveDocumentReviewArgs) InitDefault() {
}

var DatasetServiceSaveDocumentReviewArgs_Req_DEFAULT *SaveDocumentReviewRequest

func (p *DatasetServiceSaveDocumentReviewArgs) GetReq() (v *SaveDocumentReviewRequest) {
	if !p.IsSetReq() {
		return DatasetServiceSaveDocumentReviewArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_DatasetServiceSaveDocumentReviewArgs = map[int16]string{
	1: "req",
}

func (p *DatasetServiceSaveDocumentReviewArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *DatasetServiceSaveDocumentReviewArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceSaveDocumentReviewArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceSaveDocumentReviewArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewSaveDocumentReviewRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *DatasetServiceSaveDocumentReviewArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SaveDocumentReview_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceSaveDocumentReviewArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DatasetServiceSaveDocumentReviewArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceSaveDocumentReviewArgs(%+v)", *p)

}

type DatasetServiceSaveDocumentReviewResult struct {
	Success *SaveDocumentReviewResponse `thrift:"success,0,optional"`
}

func NewDatasetServiceSaveDocumentReviewResult() *DatasetServiceSaveDocumentReviewResult {
	return &DatasetServiceSaveDocumentReviewResult{}
}

func (p *DatasetServiceSaveDocumentReviewResult) InitDefault() {
}

var DatasetServiceSaveDocumentReviewResult_Success_DEFAULT *SaveDocumentReviewResponse

func (p *DatasetServiceSaveDocumentReviewResult) GetSuccess() (v *SaveDocumentReviewResponse) {
	if !p.IsSetSuccess() {
		return DatasetServiceSaveDocumentReviewResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_DatasetServiceSaveDocumentReviewResult = map[int16]string{
	0: "success",
}

func (p *DatasetServiceSaveDocumentReviewResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *DatasetServiceSaveDocumentReviewResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DatasetServiceSaveDocumentReviewResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *DatasetServiceSaveDocumentReviewResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewSaveDocumentReviewResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *DatasetServiceSaveDocumentReviewResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("SaveDocumentReview_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DatasetServiceSaveDocumentReviewResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *DatasetServiceSaveDocumentReviewResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DatasetServiceSaveDocumentReviewResult(%+v)", *p)

}
