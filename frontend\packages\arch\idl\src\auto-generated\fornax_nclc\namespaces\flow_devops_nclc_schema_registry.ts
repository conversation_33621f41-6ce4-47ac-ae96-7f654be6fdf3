/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum FactoryType {
  /** func(config) impl */
  Simple = 0,
  /** func(ctx, config) (impl, err) */
  Common = 1,
}

export enum Kind {
  /** 与 golang 的 reflect.Kind 并不是完全对应的，不能依赖 value 直接转换 */
  Invalid = 0,
  String = 1,
  Bool = 2,
  Array = 3,
  Struct = 4,
  Map = 5,
  Interface = 6,
  Int = 7,
  Int64 = 8,
  Int32 = 9,
  Int16 = 10,
  Int8 = 11,
  UInt = 12,
  Uint64 = 13,
  Uint32 = 14,
  Uint16 = 15,
  Uint8 = 16,
  Float64 = 17,
  Float32 = 18,
  Ptr = 19,
}

export enum Source {
  FirstParty = 0,
  Custom = 1,
}
/* eslint-enable */
