/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as automation from './namespaces/automation';
import * as base from './namespaces/base';
import * as flow_devops_evaluation_automation from './namespaces/flow_devops_evaluation_automation';
import * as flow_devops_evaluation_callback_common from './namespaces/flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_dataset from './namespaces/flow_devops_evaluation_dataset';
import * as flow_devops_evaluation_dataset_openapi from './namespaces/flow_devops_evaluation_dataset_openapi';
import * as flow_devops_evaluation_entity from './namespaces/flow_devops_evaluation_entity';
import * as flow_devops_evaluation_evaluator from './namespaces/flow_devops_evaluation_evaluator';
import * as flow_devops_evaluation_evaluator_callback from './namespaces/flow_devops_evaluation_evaluator_callback';
import * as flow_devops_evaluation_manual_annotation from './namespaces/flow_devops_evaluation_manual_annotation';
import * as flow_devops_evaluation_object from './namespaces/flow_devops_evaluation_object';
import * as flow_devops_evaluation_object_callback from './namespaces/flow_devops_evaluation_object_callback';
import * as flow_devops_evaluation_openapi_common from './namespaces/flow_devops_evaluation_openapi_common';
import * as flow_devops_evaluation_ping from './namespaces/flow_devops_evaluation_ping';
import * as flow_devops_evaluation_proxy from './namespaces/flow_devops_evaluation_proxy';
import * as flow_devops_evaluation_task from './namespaces/flow_devops_evaluation_task';
import * as flow_devops_evaluation_task_openapi from './namespaces/flow_devops_evaluation_task_openapi';
import * as flow_devops_prompt_common from './namespaces/flow_devops_prompt_common';
import * as model from './namespaces/model';

export {
  automation,
  base,
  flow_devops_evaluation_automation,
  flow_devops_evaluation_callback_common,
  flow_devops_evaluation_dataset,
  flow_devops_evaluation_dataset_openapi,
  flow_devops_evaluation_entity,
  flow_devops_evaluation_evaluator,
  flow_devops_evaluation_evaluator_callback,
  flow_devops_evaluation_manual_annotation,
  flow_devops_evaluation_object,
  flow_devops_evaluation_object_callback,
  flow_devops_evaluation_openapi_common,
  flow_devops_evaluation_ping,
  flow_devops_evaluation_proxy,
  flow_devops_evaluation_task,
  flow_devops_evaluation_task_openapi,
  flow_devops_prompt_common,
  model,
};
export * from './namespaces/automation';
export * from './namespaces/base';
export * from './namespaces/flow_devops_evaluation_automation';
export * from './namespaces/flow_devops_evaluation_callback_common';
export * from './namespaces/flow_devops_evaluation_dataset';
export * from './namespaces/flow_devops_evaluation_dataset_openapi';
export * from './namespaces/flow_devops_evaluation_entity';
export * from './namespaces/flow_devops_evaluation_evaluator';
export * from './namespaces/flow_devops_evaluation_evaluator_callback';
export * from './namespaces/flow_devops_evaluation_manual_annotation';
export * from './namespaces/flow_devops_evaluation_object';
export * from './namespaces/flow_devops_evaluation_object_callback';
export * from './namespaces/flow_devops_evaluation_openapi_common';
export * from './namespaces/flow_devops_evaluation_ping';
export * from './namespaces/flow_devops_evaluation_proxy';
export * from './namespaces/flow_devops_evaluation_task';
export * from './namespaces/flow_devops_evaluation_task_openapi';
export * from './namespaces/flow_devops_prompt_common';
export * from './namespaces/model';

export type Int64 = string | number;

export default class DevopsEvaluationService<T> {
  private request: any = () => {
    throw new Error('DevopsEvaluationService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * @deprecated
   *
   * PUT /api/evaluation/v1/case/dataset/associate
   *
   * case 关联评测资源
   *
   * (废弃)关联case的数据集
   */
  AssociateDataset(
    req: flow_devops_evaluation_task.AssociateDatasetRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.AssociateDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/dataset/associate');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /api/evaluation/v1/case */
  DeleteCase(
    req: flow_devops_evaluation_task.DeleteCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DeleteCaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case');
    const method = 'DELETE';
    const data = { case_id: _req['case_id'], space_id: _req['space_id'] };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/evalobject/associate
   *
   * 关联case的评测对象
   */
  AssociateEvalObject(
    req: flow_devops_evaluation_task.AssociateEvalObjectRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.AssociateEvalObjectResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/evalobject/associate');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      eval_object: _req['eval_object'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case
   *
   * 更新case， 更新case元信息描述
   */
  UpdateCase(
    req: flow_devops_evaluation_task.UpdateCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.UpdateCaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      name: _req['name'],
      desc: _req['desc'],
      runtime_parameter: _req['runtime_parameter'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/cases/list
   *
   * 分页拉当前空间下的case列表
   */
  PullCase(
    req: flow_devops_evaluation_task.PullCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.PullCaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/cases/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      tag_name: _req['tag_name'],
      tag_value: _req['tag_value'],
      fuzzy_name: _req['fuzzy_name'],
      creator_id: _req['creator_id'],
      region: _req['region'],
      object_type: _req['object_type'],
      dataset_ids: _req['dataset_ids'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/evaluator/associate
   *
   * 关联case评估方法
   */
  AssociateEvaluator(
    req: flow_devops_evaluation_task.AssociateEvaluatorRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.AssociateEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/evaluator/associate');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      mode: _req['mode'],
      scope: _req['scope'],
      row_id: _req['row_id'],
      row_group_id: _req['row_group_id'],
      cid: _req['cid'],
      threshold: _req['threshold'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case
   *
   * case curd
   *
   * 创建case，版本为draft版本
   */
  CreateCase(
    req: flow_devops_evaluation_task.CreateCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CreateCaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case');
    const method = 'POST';
    const data = {
      name: _req['name'],
      space_id: _req['space_id'],
      desc: _req['desc'],
      cid: _req['cid'],
      tag_list: _req['tag_list'],
      region: _req['region'],
      UserChangeMode: _req['UserChangeMode'],
      eval_object: _req['eval_object'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/exec
   *
   * 单次执行case
   */
  ExecCase(
    req: flow_devops_evaluation_task.ExecCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ExecCaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/exec');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      cid: _req['cid'],
      runtime_parameter: _req['runtime_parameter'],
      exec_times: _req['exec_times'],
      task_description: _req['task_description'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/case/task
   *
   * 删除一次历史执行记录
   */
  DeleteTask(
    req: flow_devops_evaluation_task.DeleteTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DeleteTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/task');
    const method = 'DELETE';
    const data = {
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      space_id: _req['space_id'],
    };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/task/retry
   *
   * 重试执行，尽在task error状态下可以触发成功
   */
  RetryExecTask(
    req: flow_devops_evaluation_task.RetryExecTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.RetryExecTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/task/retry');
    const method = 'PUT';
    const data = {
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      mode: _req['mode'],
      row_group_id: _req['row_group_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/:dataset_id/row_group/scan */
  ScanRowGroups(
    req: flow_devops_evaluation_dataset.ScanRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.ScanRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/scan`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      filter_rows: _req['filter_rows'],
      tags: _req['tags'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/dataset/:dataset_id/row_group/batch_delete
   *
   * 移动行顺序
   *
   * MoveRowGroupsResp MoveRowGroups(1: MoveRowGroupsReq req) (api.post = "/api/evaluation/v1/dataset/:dataset_id/row_group/move"),
   *
   * 删除一行
   */
  BatchDeleteRowGroups(
    req: flow_devops_evaluation_dataset.BatchDeleteRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.BatchDeleteRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/batch_delete`,
    );
    const method = 'DELETE';
    const data = {
      space_id: _req['space_id'],
      row_group_ids: _req['row_group_ids'],
    };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /api/evaluation/v1/dataset/:dataset_id/clone */
  CloneDataset(
    req: flow_devops_evaluation_dataset.CloneDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.CloneDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/clone`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      to_space_id: _req['to_space_id'],
      clone_name: _req['clone_name'],
      row_group_ids: _req['row_group_ids'],
      publish_option: _req['publish_option'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /api/evaluation/v1/dataset/:dataset_id */
  DeleteDataset(
    req: flow_devops_evaluation_dataset.DeleteDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.DeleteDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}`,
    );
    const method = 'DELETE';
    const data = { space_id: _req['space_id'] };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /api/evaluation/v1/dataset/:dataset_id/column/insert */
  InsertColumn(
    req: flow_devops_evaluation_dataset.InsertColumnReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.InsertColumnResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/column/insert`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      column_info: _req['column_info'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** PUT /api/evaluation/v1/dataset/:dataset_id/column/:column_id */
  UpdateColumn(
    req: flow_devops_evaluation_dataset.UpdateColumnReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.UpdateColumnResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/column/${_req['column_id']}`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      column_info: _req['column_info'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/row_group/batch_get
   *
   * RowGroup
   */
  BatchGetRowGroups(
    req: flow_devops_evaluation_dataset.BatchGetRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.BatchGetRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/batch_get`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      row_group_ids: _req['row_group_ids'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/evaluation/v1/dataset/:dataset_id/row_group/:row_group_id/replace
   *
   * 替换一行/修改一行
   */
  ReplaceRowGroups(
    req: flow_devops_evaluation_dataset.ReplaceRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.ReplaceRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/${_req['row_group_id']}/replace`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      row_group: _req['row_group'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/row_group/insert
   *
   * 对表的数据操作
   *
   * 新增多行
   */
  InsertRowGroups(
    req: flow_devops_evaluation_dataset.InsertRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.InsertRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/insert`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      row_groups: _req['row_groups'],
      before_row_group_id: _req['before_row_group_id'],
      skip_limit_check: _req['skip_limit_check'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/search */
  SearchDataset(
    req: flow_devops_evaluation_dataset.SearchDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.SearchDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/search');
    const method = 'GET';
    const params = {
      name: _req['name'],
      space_id: _req['space_id'],
      creator: _req['creator'],
      publish_option: _req['publish_option'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/dataset/:dataset_id/column
   *
   * Column info
   */
  GetColumnSchema(
    req: flow_devops_evaluation_dataset.GetColumnSchemaReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.GetColumnSchemaResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/column`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** PUT /api/evaluation/v1/dataset/:dataset_id */
  UpdateDataset(
    req: flow_devops_evaluation_dataset.UpdateDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.UpdateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      desc: _req['desc'],
      tag_ids: _req['tag_ids'],
      update_fields: _req['update_fields'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset
   *
   * Dataset
   */
  CreateDataset(
    req: flow_devops_evaluation_dataset.CreateDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.CreateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset');
    const method = 'POST';
    const data = {
      name: _req['name'],
      space_id: _req['space_id'],
      desc: _req['desc'],
      column_schema: _req['column_schema'],
      row_groups: _req['row_groups'],
      publish_option: _req['publish_option'],
      tag_list: _req['tag_list'],
      dataset_type: _req['dataset_type'],
      tag_ids: _req['tag_ids'],
      security_level: _req['security_level'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/:dataset_id */
  GetDataset(
    req: flow_devops_evaluation_dataset.GetDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.GetDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** PUT /api/evaluation/v1/dataset/:dataset_id/overwrite */
  OverWriteDataset(
    req: flow_devops_evaluation_dataset.OverWriteDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.OverWriteDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/overwrite`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      column_schema: _req['column_schema'],
      row_groups: _req['row_groups'],
      source_dataset_id: _req['source_dataset_id'],
      source_space_id: _req['source_space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/list */
  ListDataset(
    req: flow_devops_evaluation_dataset.ListDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.ListDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      tag_name: _req['tag_name'],
      tag_value: _req['tag_value'],
      publish_option: _req['publish_option'],
      dataset_types: _req['dataset_types'],
      page: _req['page'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      fuzzy_name: _req['fuzzy_name'],
      creator_id: _req['creator_id'],
      tag_ids: _req['tag_ids'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** DELETE /api/evaluation/v1/dataset/:dataset_id/column/:column_id */
  DeleteColumn(
    req: flow_devops_evaluation_dataset.DeleteColumnReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.DeleteColumnResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/column/${_req['column_id']}`,
    );
    const method = 'DELETE';
    const data = { space_id: _req['space_id'] };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/:dataset_id/row_group/list */
  ListRowGroups(
    req: flow_devops_evaluation_dataset.ListRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.ListRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/list`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      tags: _req['tags'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/evaluation/v1/case/:case_id/task/:task_id/rows/get
   *
   * row eval
   *
   * 查询当前row的评测结果
   */
  BatchGetRowEvalRes(
    req: flow_devops_evaluation_task.BatchGetRowEvalResRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.BatchGetRowEvalResResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/rows/get`,
    );
    const method = 'POST';
    const data = {
      row_ids: _req['row_ids'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id
   *
   * 拉去当前case全部信息，包括关联的评测信息
   */
  GetCase(
    req: flow_devops_evaluation_task.GetCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetCaseResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/evaluation/v1/case/${_req['case_id']}`);
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/tasks/list
   *
   * case 一次执行 task 记录
   *
   * 拉case历史执行记录
   */
  PullCaseExecHistory(
    req: flow_devops_evaluation_task.PullCaseExecHistoryRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.PullCaseExecHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/tasks/list`,
    );
    const method = 'GET';
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      DatasetIDs: _req['DatasetIDs'],
      task_description: _req['task_description'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/task/rows/manual_score
   *
   * 人工打分
   */
  ManualScoreFowRows(
    req: flow_devops_evaluation_task.ManualScoreFowRowsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ManualScoreFowRowsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/case/task/rows/manual_score',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      manual_scores: _req['manual_scores'],
      space_id: _req['space_id'],
      group_manual_scores: _req['group_manual_scores'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/task/:task_id
   *
   * 拉单次历史执行记录
   */
  GetTask(
    req: flow_devops_evaluation_task.GetTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/check
   *
   * 检测Case关联Evaluator合法性
   */
  CheckCaseEvaluator(
    req: flow_devops_evaluation_task.CheckCaseEvaluatorRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CheckCaseEvaluatorResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/check`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/task/kill
   *
   * 终止Processing的任务
   */
  KillRunningTask(
    req: flow_devops_evaluation_task.KillRunningTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.KillRunningTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/task/kill');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/object_meta/list
   *
   * 二级查询页面，根据一级查询到的id拉取入参meta信息
   */
  ListObjectMetaByType(
    req: flow_devops_evaluation_object.ListObjectMetaByTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.ListObjectMetaByTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object_meta/list');
    const method = 'POST';
    const data = {
      object_type: _req['object_type'],
      search_key: _req['search_key'],
      region: _req['region'],
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/rule_group/:rule_group_id/rule/:rule_id
   *
   * 从 group 中移除一个 rule
   */
  RemoveRule(
    req: flow_devops_evaluation_evaluator.RemoveRuleRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.RemoveRuleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/rule/${_req['rule_id']}`,
    );
    const method = 'DELETE';
    const data = { space_id: _req['space_id'], cid: _req['cid'] };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/rule_groups/list
   *
   * 查询 rule_group 列表
   */
  ListRuleGroupMeta(
    req: flow_devops_evaluation_evaluator.ListRuleGroupMetaRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.ListRuleGroupMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_groups/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      need_rules_count: _req['need_rules_count'],
      tag_name: _req['tag_name'],
      tag_value: _req['tag_value'],
      fuzzy_name: _req['fuzzy_name'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/rule_group/types/list
   *
   * 查询支持的 rule types
   */
  ListSupportRuleType(
    req: flow_devops_evaluation_evaluator.ListSupportRuleTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.ListSupportRuleTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group/types/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/rule_group/:rule_group_id/rules/list
   *
   * 查询 rule_group 中的 rules
   */
  GetRuleGroupRules(
    req: flow_devops_evaluation_evaluator.GetRuleGroupRulesRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.GetRuleGroupRulesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/rules/list`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/rules
   *
   * 查询一批 rule
   */
  GetRules(
    req: flow_devops_evaluation_evaluator.GetRulesRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.GetRulesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group/rules');
    const method = 'POST';
    const data = {
      rule_ids: _req['rule_ids'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group
   *
   * 创建一个 rule_group
   */
  CreateRuleGroup(
    req: flow_devops_evaluation_evaluator.CreateRuleGroupRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.CreateRuleGroupResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group');
    const method = 'POST';
    const data = {
      name: _req['name'],
      desc: _req['desc'],
      publish_to_rule_group: _req['publish_to_rule_group'],
      space_id: _req['space_id'],
      tag_list: _req['tag_list'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/:rule_group_id/rule
   *
   * 创建 rule
   */
  CreateRule(
    req: flow_devops_evaluation_evaluator.CreateRuleRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.CreateRuleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/rule`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      rule: _req['rule'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/rule_group/:rule_group_id/meta
   *
   * 查询一个 rule_group meta
   */
  GetRuleGroupMeta(
    req: flow_devops_evaluation_evaluator.GetRuleGroupMetaRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.GetRuleGroupMetaResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/meta`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/rule_group/:rule_group_id/rule/:rule_id
   *
   * 修改 rule
   */
  UpdateRule(
    req: flow_devops_evaluation_evaluator.UpdateRuleRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.UpdateRuleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/rule/${_req['rule_id']}`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      rule: _req['rule'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/clone
   *
   * 复制一个 rule_group
   */
  CopyRuleGroup(
    req: flow_devops_evaluation_evaluator.CopyRuleGroupRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.CopyRuleGroupResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group/clone');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_rule_group_id: _req['source_rule_group_id'],
      target_rule_group_id: _req['target_rule_group_id'],
      to_space_id: _req['to_space_id'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/rule_group
   *
   * 更新一个 rule_group
   */
  UpdateRuleGroup(
    req: flow_devops_evaluation_evaluator.UpdateRuleGroupRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.UpdateRuleGroupResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      space_id: _req['space_id'],
      update_fields: _req['update_fields'],
      name: _req['name'],
      desc: _req['desc'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/rule_group
   *
   * 删除一个 rule_group
   */
  DeleteRuleGroup(
    req: flow_devops_evaluation_evaluator.DeleteRuleGroupRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.DeleteRuleGroupResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group');
    const method = 'DELETE';
    const data = {
      rule_group_id: _req['rule_group_id'],
      space_id: _req['space_id'],
      cid: _req['cid'],
    };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/task/:task_id/dashboard
   *
   * dashboard 展示一次执行任务
   */
  DashboardTask(
    req: flow_devops_evaluation_task.DashboardTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DashboardTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/dashboard`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      row_group_run_state: _req['row_group_run_state'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/ping
   *
   * KitexThrift
   */
  Ping(
    req: flow_devops_evaluation_ping.PingReq,
    options?: T,
  ): Promise<flow_devops_evaluation_ping.PingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/ping');
    const method = 'GET';
    const params = { ping_message: _req['ping_message'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/evaluation/v1/ping_server */
  PingServer(
    req: flow_devops_evaluation_ping.PingReq,
    options?: T,
  ): Promise<flow_devops_evaluation_ping.PingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/ping_server');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const params = { ping_message: _req['ping_message'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/object_type
   *
   * 注册评测对象
   */
  RegisterObjectType(
    req: flow_devops_evaluation_object.RegisterObjectTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.RegisterObjectTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object_type');
    const method = 'PUT';
    const data = {
      object_type_detail_info: _req['object_type_detail_info'],
      cid: _req['cid'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/object_type_detail/list
   *
   * 查询评测对象列表
   */
  ListObjectTypeDetail(
    req: flow_devops_evaluation_object.ListObjectTypeDetailRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.ListObjectTypeDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object_type_detail/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      list_object_type_filter: _req['list_object_type_filter'],
    };
    const params = { cursor: _req['cursor'], limit: _req['limit'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/object_type
   *
   * 更改评测对象
   */
  UpdateObjectType(
    req: flow_devops_evaluation_object.UpdateObjectTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.UpdateObjectTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object_type');
    const method = 'POST';
    const data = { object_type_detail_info: _req['object_type_detail_info'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/object_type/:object_type
   *
   * 查询评测对象
   */
  GetObjectType(
    req: flow_devops_evaluation_object.GetObjectTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.GetObjectTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/object_type/${_req['object_type']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/object_type
   *
   * 删除评测对象
   */
  DeleteObjectType(
    req: flow_devops_evaluation_object.DeleteObjectTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.DeleteObjectTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object_type');
    const method = 'DELETE';
    const data = {
      object_type: _req['object_type'],
      space_id: _req['space_id'],
      cid: _req['cid'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/object/object_type/list
   *
   * 一级查询页面，查一批评测对象
   */
  ListObjectType(
    req: flow_devops_evaluation_object.ListObjectTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.ListObjectTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object/object_type/list');
    const method = 'POST';
    const data = { list_object_type_filter: _req['list_object_type_filter'] };
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      space_id: _req['space_id'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/:case_id/tasks/get
   *
   * 批量拉取case的历史执行记录
   */
  GetTasks(
    req: flow_devops_evaluation_task.GetTasksRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetTasksResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/tasks/get`,
    );
    const method = 'POST';
    const data = { task_ids: _req['task_ids'], Base: _req['Base'] };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/security_token */
  GetSecurityToken(
    req?: flow_devops_evaluation_dataset.GetSecurityTokenReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.GetSecurityTokenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/evaluation/v1/dataset/security_token');
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/rule_group/custom_types/list
   *
   * 查询支持的自定义的rule types
   */
  ListCustomSupportRuleType(
    req: flow_devops_evaluation_evaluator.ListCustomSupportRuleTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.ListCustomSupportRuleTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/rule_group/custom_types/list',
    );
    const method = 'GET';
    const data = {
      list_custom_support_rule_type_filter:
        _req['list_custom_support_rule_type_filter'],
    };
    const params = {
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/evaluator_type
   *
   * 删除自定义评估器
   */
  DeleteEvaluatorType(
    req: flow_devops_evaluation_evaluator.DeleteEvaluatorTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.DeleteEvaluatorTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/evaluator_type');
    const method = 'DELETE';
    const data = {
      evaluator_type: _req['evaluator_type'],
      space_id: _req['space_id'],
      cid: _req['cid'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/evaluator_type
   *
   * 注册自定义评估器
   */
  RegisterEvaluatorType(
    req: flow_devops_evaluation_evaluator.RegisterEvaluatorTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.RegisterEvaluatorTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/evaluator_type');
    const method = 'PUT';
    const data = {
      evaluator_type_detail_info: _req['evaluator_type_detail_info'],
      cid: _req['cid'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/evaluator_type
   *
   * 更改自定义评估器
   */
  UpdateEvaluatorType(
    req: flow_devops_evaluation_evaluator.UpdateEvaluatorTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.UpdateEvaluatorTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/evaluator_type');
    const method = 'POST';
    const data = {
      evaluator_type_detail_info: _req['evaluator_type_detail_info'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/evaluator_type_detail/list
   *
   * 查询自定义评估器列表
   */
  ListEvaluatorTypeDetail(
    req: flow_devops_evaluation_evaluator.ListEvaluatorTypeDetailRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.ListEvaluatorTypeDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/evaluator_type_detail/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      list_evaluator_type_filter: _req['list_evaluator_type_filter'],
    };
    const params = { cursor: _req['cursor'], limit: _req['limit'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/evaluator_type/:evaluator_type
   *
   * 查询自定义评估器
   */
  GetEvaluatorType(
    req: flow_devops_evaluation_evaluator.GetEvaluatorTypeRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.GetEvaluatorTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/evaluator_type/${_req['evaluator_type']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/dataset/:dataset_id/row_group/:row_group_id
   *
   * 更新指定一行数据
   */
  UpdateRowGroups(
    req: flow_devops_evaluation_dataset.UpdateRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.UpdateRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/row_group/${_req['row_group_id']}`,
    );
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      row_group: _req['row_group'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/batch_get
   *
   * 查询一批 rule_group meta
   */
  BatchGetRuleGroupMeta(
    req: flow_devops_evaluation_evaluator.BatchGetRuleGroupMetaRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.BatchGetRuleGroupMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group/batch_get');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      rule_group_ids: _req['rule_group_ids'],
      need_rules_count: _req['need_rules_count'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /proxy/v1/evaluation */
  AgentExecuteProxy(
    req?: flow_devops_evaluation_proxy.AgentExecuteProxyReq,
    options?: T,
  ): Promise<flow_devops_evaluation_proxy.AgentExecuteProxyResp> {
    const _req = req || {};
    const url = this.genBaseURL('/proxy/v1/evaluation');
    const method = 'POST';
    const data = {
      agent_execute_proxy_content: _req['agent_execute_proxy_content'],
      extra: _req['extra'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/evaluation/v1/object/object_type/v2/list
   *
   * 一级查询页面，查一批评测对象
   */
  ListObjectTypeV2(
    req: flow_devops_evaluation_object.ListObjectTypeRequestV2,
    options?: T,
  ): Promise<flow_devops_evaluation_object.ListObjectTypeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/object/object_type/v2/list',
    );
    const method = 'POST';
    const data = {
      region: _req['region'],
      list_object_type_filter: _req['list_object_type_filter'],
    };
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      space_id: _req['space_id'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/chain_task/version/list
   *
   * 查询ChainTask版本列表
   */
  ListChainTaskVersions(
    req: flow_devops_evaluation_object.ListChainTaskVersionsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.ListChainTaskVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/chain_task/version/list');
    const method = 'POST';
    const data = {
      task_name: _req['task_name'],
      task_id: _req['task_id'],
      space_id: _req['space_id'],
      region: _req['region'],
      app_id: _req['app_id'],
    };
    const params = { cursor: _req['cursor'], limit: _req['limit'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/debug
   *
   * rule debug
   */
  DebugRule(
    req: flow_devops_evaluation_evaluator.DebugRuleRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.DebugRuleResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/rule_group/debug');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      rule: _req['rule'],
      data: _req['data'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/dataset/:dataset_id/tag_tree */
  GetDatasetTagTree(
    req: flow_devops_evaluation_dataset.GetDatasetTagTreeReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.GetDatasetTagTreeResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/tag_tree`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/task/:task_id/aggregate_report
   *
   * 获取聚合报告
   */
  GetAggregateReport(
    req: flow_devops_evaluation_task.GetAggregateReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetAggregateReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/aggregate_report`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/evaluation/v1/object/search/fornax_app
   *
   * FornaxAppSearchObject FornaxApp SearchObject 透传接口，端上发起请求后服务端转发至 FornaxApp
   */
  FornaxAppSearchObject(
    req: flow_devops_evaluation_object.FornaxAppSearchObjectRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.FornaxAppSearchObjectResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/object/search/fornax_app');
    const method = 'POST';
    const data = {
      object_type: _req['object_type'],
      search_key: _req['search_key'],
      app_client_id: _req['app_client_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/:case_id/task/:task_id/aggregate_report/gen
   *
   * 生成聚合报告
   */
  GenAggregateReport(
    req: flow_devops_evaluation_task.GenAggregateReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GenAggregateReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/aggregate_report/gen`,
    );
    const method = 'PUT';
    const data = { Base: _req['Base'] };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/coze_bot/version/list
   *
   * 查询CozeBot预发布的版本列表
   */
  ListCozeBotVersions(
    req: flow_devops_evaluation_object.ListCozeBotVersionsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_object.ListCozeBotVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/coze_bot/version/list');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      space_id: _req['space_id'],
      region: _req['region'],
    };
    const params = { cursor: _req['cursor'], limit: _req['limit'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /open-api/evaluation/v1/cases/:case_id/tasks/:task_id/custom_metrics */
  CreateCustomMetricsDataPoints(
    req: flow_devops_evaluation_task_openapi.CreateCustomMetricsDataPointsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task_openapi.CreateCustomMetricsDataPointsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/cases/${_req['case_id']}/tasks/${_req['task_id']}/custom_metrics`,
    );
    const method = 'POST';
    const data = { data_points: _req['data_points'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** PUT /open-api/evaluation/v1/cases/:case_id/tasks/:task_id/row_group_results */
  UpdateAgentOutput(
    req: flow_devops_evaluation_task_openapi.UpdateAgentOutputRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task_openapi.UpdateAgentOutputResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/cases/${_req['case_id']}/tasks/${_req['task_id']}/row_group_results`,
    );
    const method = 'PUT';
    const data = {
      row_group_results: _req['row_group_results'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /open-api/evaluation/v1/cases/:case_id/tasks/:task_id/dataset/list */
  ListTaskRowGroups(
    req: flow_devops_evaluation_task_openapi.ListTaskRowGroupsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task_openapi.ListTaskRowGroupsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/cases/${_req['case_id']}/tasks/${_req['task_id']}/dataset/list`,
    );
    const method = 'GET';
    const params = {
      page_token: _req['page_token'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** PUT /open-api/evaluation/v1/cases/:case_id/tasks/cancel */
  CancelTasks(
    req: flow_devops_evaluation_task_openapi.CancelTasksRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task_openapi.CancelTasksResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/cases/${_req['case_id']}/tasks/cancel`,
    );
    const method = 'PUT';
    const data = { task_ids: _req['task_ids'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-api/evaluation/v1/cases/:case_id/tasks */
  CreateEvalTask(
    req: flow_devops_evaluation_task_openapi.CreateEvalTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task_openapi.CreateEvalTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/cases/${_req['case_id']}/tasks`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/analysis_chart_report/associate
   *
   * 分析报告列表关联case
   */
  AssociateAnalysisChartReport(
    req: flow_devops_evaluation_task.AssociateAnalysisChartReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.AssociateAnalysisChartReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/case/analysis_chart_report/associate',
    );
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      analysis_chart_report_ids: _req['analysis_chart_report_ids'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/:case_id/task/:task_id/analysis_chart_report/update
   *
   * 修改分析报告配置
   */
  UpdateAnalysisChartReport(
    req: flow_devops_evaluation_task.UpdateAnalysisChartReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.UpdateAnalysisChartReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/analysis_chart_report/update`,
    );
    const method = 'POST';
    const data = {
      analysis_chart_report_id: _req['analysis_chart_report_id'],
      analysis_chart_report_config: _req['analysis_chart_report_config'],
      Base: _req['Base'],
    };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/task/:task_id/rule_group_id
   *
   * 拉取一个task下的rule_group_id
   */
  GetTaskRuleGroupId(
    req: flow_devops_evaluation_task.GetTaskRuleGroupIdRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetTaskRuleGroupIdResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/rule_group_id`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/case/task/analysis_chart_report
   *
   * 删除一个task下的某个分析报告
   */
  DeleteAnalysisChartReport(
    req: flow_devops_evaluation_task.DeleteAnalysisChartReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DeleteAnalysisChartReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/case/task/analysis_chart_report',
    );
    const method = 'DELETE';
    const data = {
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      analysis_chart_report_id: _req['analysis_chart_report_id'],
      space_id: _req['space_id'],
    };
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/evaluator_type/intersection
   *
   * 查询多个报告的对比维度的共同交集
   */
  GetCommonEvaluatorDimensions(
    req: flow_devops_evaluation_task.GetIntersectionEvaluatorDimensionsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetIntersectionEvaluatorDimensionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/evaluator_type/intersection',
    );
    const method = 'POST';
    const data = {
      task_infos: _req['task_infos'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/task/compare_aggregate_report/gen
   *
   * 生成多个task的分析对比报告
   */
  GenAggregateComparisonReport(
    req: flow_devops_evaluation_task.GenAggregateComparisonReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GenAggregateComparisonReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/case/task/compare_aggregate_report/gen',
    );
    const method = 'POST';
    const data = {
      aggregator_configs: _req['aggregator_configs'],
      Base: _req['Base'],
    };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/task/:task_id/analysis_chart_report/list
   *
   * 拉取task的所有的分析报告
   */
  PullTaskAnalysisChartReports(
    req: flow_devops_evaluation_task.PullTaskAnalysisChartReportsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.PullTaskAnalysisChartReportsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/analysis_chart_report/list`,
    );
    const method = 'GET';
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/:case_id/task/:task_id/analysis_chart_report/gen
   *
   * 生成某个任务报告的分析报告
   */
  GenAnalysisChartReport(
    req: flow_devops_evaluation_task.GenAnalysisChartReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GenAnalysisChartReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/analysis_chart_report/gen`,
    );
    const method = 'POST';
    const data = {
      analysis_chart_report_config: _req['analysis_chart_report_config'],
      Base: _req['Base'],
    };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/:rule_group_id/rules
   *
   * 创建一批 rule
   */
  CreateRules(
    req: flow_devops_evaluation_evaluator.CreateRulesRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.CreateRulesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/rules`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      rules: _req['rules'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /open-api/evaluation/v1/account */
  GetAccountInfo(
    req: flow_devops_evaluation_task_openapi.GetAccountInfoRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task_openapi.GetAccountInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/open-api/evaluation/v1/account');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/dataset_row_groups/submit
   *
   * 用户挑选数据集自动生成数据集的结果上报用来确认任务的状态。
   */
  SubmitGeneratedDatasetRowGroups(
    req: flow_devops_evaluation_dataset.SubmitGeneratedDatasetRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.SubmitGeneratedDatasetRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/dataset_row_groups/submit`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      task_id: _req['task_id'],
      submit_row_groups: _req['submit_row_groups'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/dataset_generate/publish
   *
   * 发布生产数据集接口
   */
  PublishGenerateDataset(
    req: flow_devops_evaluation_dataset.PublishGenerateDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.PublishGenerateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/dataset_generate/publish`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      dataset_describe: _req['dataset_describe'],
      generate_column_info_list: _req['generate_column_info_list'],
      total: _req['total'],
      row_group_examples: _req['row_group_examples'],
      reference_dataset: _req['reference_dataset'],
      timeliness: _req['timeliness'],
      professional_knowledges: _req['professional_knowledges'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/semantic/optimize
   *
   * 优化字段语义
   */
  StreamOptimizeRichSemantic(
    req: flow_devops_evaluation_dataset.OptimizeRichSemanticReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.OptimizeRichSemanticResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/semantic/optimize`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      semantic_describe: _req['semantic_describe'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/dataset_describe/optimize
   *
   * 优化数据集生成描述
   */
  StreamOptimizeRichDatasetDescribe(
    req: flow_devops_evaluation_dataset.OptimizeRichDatasetDescribeReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.OptimizeRichDatasetDescribeResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/dataset_describe/optimize`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      dataset_describe: _req['dataset_describe'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/dataset/:dataset_id/dataset_row_groups/dataset_generate
   *
   * 查询namespace dataset id 下自动生成数据集的任务状态
   */
  GetGeneratedDatasetRowGroups(
    req: flow_devops_evaluation_dataset.GetGeneratedDatasetRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.GetGeneratedDatasetRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/dataset_row_groups/dataset_generate`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      task_id: _req['task_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/:dataset_id/dataset_generate/kill
   *
   * 终止生产数据集任务
   */
  KillGenerateDatasetTask(
    req: flow_devops_evaluation_dataset.KillGenerateDatasetTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.KillGenerateDatasetTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/dataset/${_req['dataset_id']}/dataset_generate/kill`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      task_id: _req['task_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/case/:case_id/clone */
  CloneCase(
    req: flow_devops_evaluation_task.CloneCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CloneCaseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/clone`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      desc: _req['desc'],
      eval_object: _req['eval_object'],
      region: _req['region'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/batch_task */
  CreateBatchTask(
    req: flow_devops_evaluation_task.CreateBatchTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CreateBatchTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/batch_task');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_case_tasks: _req['batch_case_tasks'],
      default_exec_runtime_parameter: _req['default_exec_runtime_parameter'],
      execution_policy: _req['execution_policy'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/batch_task/:batch_task_id/retry */
  RetryExecBatchTask(
    req: flow_devops_evaluation_task.RetryExecBatchTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.RetryExecBatchTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/batch_task/${_req['batch_task_id']}/retry`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      mode: _req['mode'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/batch_task/:batch_task_id/kill */
  KillBatchTask(
    req: flow_devops_evaluation_task.KillBatchTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.KillBatchTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/batch_task/${_req['batch_task_id']}/kill`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/batch_task/:batch_task_id */
  GetBatchTaskRet(
    req: flow_devops_evaluation_task.GetBatchTaskRetRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetBatchTaskRetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/batch_task/${_req['batch_task_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/evaluation/v1/batch_task/history */
  GetBatchTaskHistory(
    req: flow_devops_evaluation_task.GetBatchTaskHistoryRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetBatchTaskHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/batch_task/history');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      object_id: _req['object_id'],
      object_type: _req['object_type'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /open-api/evaluation/v1/dataset/:dataset_id/row_groups */
  BatchInsertRowGroups(
    req: flow_devops_evaluation_dataset_openapi.BatchInsertRowGroupsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset_openapi.BatchInsertRowGroupsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/dataset/${_req['dataset_id']}/row_groups`,
    );
    const method = 'POST';
    const data = { row_groups: _req['row_groups'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/lock
   *
   * add dataset supprot sft
   *
   * 锁定指定数据集,将数据集置为可读状态
   */
  LockDataset(
    req: flow_devops_evaluation_dataset.LockDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.LockDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/lock');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      space_id: _req['space_id'],
      expiration: _req['expiration'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/release
   *
   * 解锁指定数据集,将数据集置为正常状态
   */
  ReleaseDataset(
    req: flow_devops_evaluation_dataset.ReleaseDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.ReleaseDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/release');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/dataset_row_group/trim_right
   *
   * 从指定的row_group_id 开始删除后面的所有row_group
   */
  DatasetRowGroupTrimRight(
    req: flow_devops_evaluation_dataset.DatasetRowGroupTrimRightReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.DatasetRowGroupTrimRightResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/dataset/dataset_row_group/trim_right',
    );
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      space_id: _req['space_id'],
      row_group_id: _req['row_group_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/batch_update_row_group
   *
   * 批量更新指定数据集的row_group
   */
  BatchUpdateRowGroups(
    req: flow_devops_evaluation_dataset.BatchUpdateRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.BatchUpdateRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/dataset/batch_update_row_group',
    );
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      space_id: _req['space_id'],
      row_groups: _req['row_groups'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/rule_group/:rule_group_id/rule_hub
   *
   * 创建rule并保存到hub中
   */
  CreateRuleAndSaveToHub(
    req: flow_devops_evaluation_evaluator.CreateRuleAndSaveToHubRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.CreateRuleAndSaveToHubResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/rule_group/${_req['rule_group_id']}/rule_hub`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      rule: _req['rule'],
      tags: _req['tags'],
      modal_tags: _req['modal_tags'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * DELETE /api/evaluation/v1/rule_group/:rule_group_id/rule_hub
   *
   * 删除hub
   */
  DeleteEvaluatorHub(
    req: flow_devops_evaluation_evaluator.DeleteEvaluatorHubRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_evaluator.DeleteEvaluatorHubResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/rule_group/:rule_group_id/rule_hub',
    );
    const method = 'DELETE';
    const data = {
      evaluator_hub_id: _req['evaluator_hub_id'],
      space_id: _req['space_id'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/dataset/list_v2
   *
   * ListDatasetV2 ListDataset为Get接口,无法通过body传递tagID list进行筛选, 因此新增ListDatasetV2接口逻辑与ListDataset一致
   */
  ListDatasetV2(
    req: flow_devops_evaluation_dataset.ListDatasetV2Req,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.ListDatasetV2Resp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/list_v2');
    const method = 'POST';
    const data = {
      dataset_types: _req['dataset_types'],
      tag_ids: _req['tag_ids'],
      Base: _req['Base'],
    };
    const params = {
      space_id: _req['space_id'],
      tag_name: _req['tag_name'],
      tag_value: _req['tag_value'],
      publish_option: _req['publish_option'],
      page: _req['page'],
      page_size: _req['page_size'],
      cursor: _req['cursor'],
      fuzzy_name: _req['fuzzy_name'],
      creator_id: _req['creator_id'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /api/evaluation/v1/case/:case_id/task/:task_id/export_bitable */
  ExportBitableReport(
    req: flow_devops_evaluation_task.ExportBitableReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ExportBitableReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/export_bitable`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_scoring_user_num: _req['manual_scoring_user_num'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/dataset/unbind
   *
   * 解绑已有数据集
   */
  UnbindDataset(
    req: flow_devops_evaluation_task.UnbindDatasetRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.UnbindDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/dataset/unbind');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/dataset/associate_v2
   *
   * 关联已有数据集
   */
  AssociateDatasetV2(
    req: flow_devops_evaluation_task.AssociateDatasetV2Request,
    options?: T,
  ): Promise<flow_devops_evaluation_task.AssociateDatasetV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/dataset/associate_v2');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/dataset/batch_get */
  BatchGetDataset(
    req: flow_devops_evaluation_dataset.BatchGetDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.BatchGetDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/batch_get');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const params = {
      space_id: _req['space_id'],
      dataset_ids: _req['dataset_ids'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/evaluation/v1/case/:case_id/task/:task_id/dashboard_rowgroup
   *
   * dashboard 展示一次执行任务
   */
  DashboardTaskRowGroup(
    req: flow_devops_evaluation_task.DashboardTaskRowGroupRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DashboardTaskRowGroupResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/dashboard_rowgroup`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      row_group_id: _req['row_group_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/cases/list_v2
   *
   * PullCaseV2 PullCase为Get接口,无法通过body传递datasetIDs进行筛选, 因此新增PullCaseV2接口逻辑与PullCase一致
   */
  PullCaseV2(
    req: flow_devops_evaluation_task.PullCaseV2Request,
    options?: T,
  ): Promise<flow_devops_evaluation_task.PullCaseV2Response> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/cases/list_v2');
    const method = 'POST';
    const data = {
      fuzzy_name: _req['fuzzy_name'],
      creator_id: _req['creator_id'],
      region: _req['region'],
      object_type: _req['object_type'],
      dataset_ids: _req['dataset_ids'],
      Base: _req['Base'],
    };
    const params = {
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      tag_name: _req['tag_name'],
      tag_value: _req['tag_value'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /api/evaluation/v1/case/:case_id/tasks/list_v2 */
  PullCaseExecHistoryV2(
    req: flow_devops_evaluation_task.PullCaseExecHistoryV2Request,
    options?: T,
  ): Promise<flow_devops_evaluation_task.PullCaseExecHistoryV2Response> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/tasks/list_v2`,
    );
    const method = 'POST';
    const data = {
      dataset_ids: _req['dataset_ids'],
      creator_ids: _req['creator_ids'],
      Base: _req['Base'],
    };
    const params = {
      cursor: _req['cursor'],
      limit: _req['limit'],
      space_id: _req['space_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      task_description: _req['task_description'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** GET /api/evaluation/v1/case/:case_id/task/:task_id/bitable */
  GetBitableReport(
    req: flow_devops_evaluation_task.GetBitableReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetBitableReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/bitable`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/evaluation/v1/case/:case_id/task/:task_id/import_bitable */
  ImportBitableReport(
    req: flow_devops_evaluation_task.ImportBitableReportRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ImportBitableReportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/import_bitable`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /api/evaluation/v1/contrast_report/:contrast_report_id */
  DeleteContrastReport(
    req: flow_devops_evaluation_task.DeleteContrastReportReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DeleteContrastReportResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/contrast_report/${_req['contrast_report_id']}`,
    );
    const method = 'DELETE';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/manual_annotation_task
   *
   * 标注任务
   */
  CreateManualAnnotationTask(
    req: flow_devops_evaluation_manual_annotation.CreateManualAnnotationTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.CreateManualAnnotationTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/manual_annotation_task');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_id: _req['source_id'],
      source_type: _req['source_type'],
      manual_annotation_items: _req['manual_annotation_items'],
      invisible: _req['invisible'],
      name: _req['name'],
      description: _req['description'],
      source_range: _req['source_range'],
      blind_count: _req['blind_count'],
      visible_area: _req['visible_area'],
      extra: _req['extra'],
      create_status: _req['create_status'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_label_task/:manual_annotation_label_task_id/submit */
  SubmitManualAnnotationLabelTask(
    req: flow_devops_evaluation_manual_annotation.SubmitManualAnnotationLabelTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.SubmitManualAnnotationLabelTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_label_task/${_req['manual_annotation_label_task_id']}/submit`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_label_results: _req['manual_annotation_label_results'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/contrast_report/:contrast_report_id */
  GetContrastReport(
    req: flow_devops_evaluation_task.GetContrastReportReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetContrastReportResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/contrast_report/${_req['contrast_report_id']}`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id/batch_create_manual_annotation_label_task
   *
   * 标注打标任务
   */
  BatchCreateManualAnnotationLabelTask(
    req: flow_devops_evaluation_manual_annotation.BatchCreateManualAnnotationLabelTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.BatchCreateManualAnnotationLabelTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}/batch_create_manual_annotation_label_task`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_label_tasks: _req['manual_annotation_label_tasks'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/contrast_report */
  CreateContrastReport(
    req: flow_devops_evaluation_task.CreateContrastReportReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CreateContrastReportResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/contrast_report');
    const method = 'POST';
    const data = {
      name: _req['name'],
      contrast_tasks: _req['contrast_tasks'],
      Base: _req['Base'],
    };
    const params = {
      space_id: _req['space_id'],
      case_id: _req['case_id'],
      task_id: _req['task_id'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_label_task/list */
  ListManualAnnotationLabelTask(
    req: flow_devops_evaluation_manual_annotation.ListManualAnnotationLabelTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.ListManualAnnotationLabelTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_label_task/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_task_id: _req['manual_annotation_task_id'],
      object_ids: _req['object_ids'],
      object_type: _req['object_type'],
      status: _req['status'],
      assign_annotator_ids: _req['assign_annotator_ids'],
      actual_annotator_ids: _req['actual_annotator_ids'],
      blind_nums: _req['blind_nums'],
      serial_nums: _req['serial_nums'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_task/list */
  ListManualAnnotationTask(
    req: flow_devops_evaluation_manual_annotation.ListManualAnnotationTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.ListManualAnnotationTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_task/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_id: _req['source_id'],
      source_types: _req['source_types'],
      visible_area: _req['visible_area'],
      creator_id: _req['creator_id'],
      name: _req['name'],
      assign_annotator_ids: _req['assign_annotator_ids'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/contrast_report/:contrast_report_id */
  UpdateContrastReport(
    req: flow_devops_evaluation_task.UpdateContrastReportReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.UpdateContrastReportResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/contrast_report/${_req['contrast_report_id']}`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const params = { space_id: _req['space_id'], name: _req['name'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/evaluation/v1/contrast_report/:contrast_report_id/create_post_columns
   *
   * 创建后置列
   */
  CreatePostColumns(
    req: flow_devops_evaluation_task.CreatePostColumnsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CreatePostColumnsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/contrast_report/${_req['contrast_report_id']}/create_post_columns`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_items: _req['manual_annotation_items'],
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/cases */
  MGetCases(
    req: flow_devops_evaluation_task.MGetCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.MGetCaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/cases');
    const method = 'POST';
    const data = {
      case_ids: _req['case_ids'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id */
  DeleteManualAnnotationTask(
    req: flow_devops_evaluation_manual_annotation.DeleteManualAnnotationTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.DeleteManualAnnotationTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}`,
    );
    const method = 'DELETE';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/evaluation/v1/contrast_report/list_task_contrast_report */
  ListTaskContrastReport(
    req: flow_devops_evaluation_task.ListTaskContrastReportReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ListTaskContrastReportResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/contrast_report/list_task_contrast_report',
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      task_id: _req['task_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/evaluation/v1/cases/batch_create */
  BatchCreateCase(
    req?: flow_devops_evaluation_task.BatchCreateCaseRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.BatchCreateCaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/evaluation/v1/cases/batch_create');
    const method = 'POST';
    const data = {
      name_eval_object: _req['name_eval_object'],
      space_id: _req['space_id'],
      region: _req['region'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id/update_visible
   */
  UpdateManualAnnotationTaskVisible(
    req: flow_devops_evaluation_manual_annotation.UpdateManualAnnotationTaskVisibleReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.UpdateManualAnnotationTaskVisibleResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}/update_visible`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id/manual_annotation_item/:manual_annotation_item_id */
  DeleteManualAnnotationTaskItem(
    req: flow_devops_evaluation_manual_annotation.DeleteManualAnnotationTaskItemReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.DeleteManualAnnotationTaskItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}/manual_annotation_item/${_req['manual_annotation_item_id']}`,
    );
    const method = 'DELETE';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_label_task/batch_assign */
  BatchAssignManualAnnotationLabelTask(
    req: flow_devops_evaluation_manual_annotation.BatchAssignManualAnnotationLabelTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.BatchAssignManualAnnotationLabelTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_label_task/batch_assign',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_task_id: _req['manual_annotation_task_id'],
      manual_annotation_label_task_ids:
        _req['manual_annotation_label_task_ids'],
      assign_annotator_id: _req['assign_annotator_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/task/batch_create_manual_annotation_item
   *
   * 标注项
   */
  BatchCreateManualAnnotationItem(
    req: flow_devops_evaluation_manual_annotation.BatchCreateManualAnnotationItemReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.BatchCreateManualAnnotationItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/task/batch_create_manual_annotation_item',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_items: _req['manual_annotation_items'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_item/list */
  ListManualAnnotationItem(
    req: flow_devops_evaluation_manual_annotation.ListManualAnnotationItemReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.ListManualAnnotationItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_item/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_id: _req['source_id'],
      source_type: _req['source_type'],
      tag_ids: _req['tag_ids'],
      annotation_item_data_types: _req['annotation_item_data_types'],
      Base: _req['Base'],
    };
    const params = {
      fuzzy_name: _req['fuzzy_name'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /** GET /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id */
  GetManualAnnotationTask(
    req: flow_devops_evaluation_manual_annotation.GetManualAnnotationTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.GetManualAnnotationTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/evaluation/v1/contrast_report/:contrast_report_id/meta */
  GetTaskContrastReportMeta(
    req: flow_devops_evaluation_task.GetTaskContrastReportMetaReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetTaskContrastReportMetaResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/contrast_report/${_req['contrast_report_id']}/meta`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id/dashboard */
  DashboardAnnotationTask(
    req: flow_devops_evaluation_manual_annotation.DashboardAnnotationTaskRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.DashboardAnnotationTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}/dashboard`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/task/initiate_manual_annotation_task
   *
   * 发起人工标注任务
   */
  InitiateManualAnnotationTask(
    req: flow_devops_evaluation_task.InitiateManualAnnotationTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_task.InitiateManualAnnotationTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/task/initiate_manual_annotation_task',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      contrast_report_id: _req['contrast_report_id'],
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      item_ids: _req['item_ids'],
      name: _req['name'],
      description: _req['description'],
      source_range: _req['source_range'],
      blind_count: _req['blind_count'],
      source_type: _req['source_type'],
      custom_filter: _req['custom_filter'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/evaluation/v1/manual_annotation_label_task/:manual_annotation_label_task_id */
  GetManualAnnotationLabelTask(
    req: flow_devops_evaluation_manual_annotation.GetManualAnnotationLabelTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.GetManualAnnotationLabelTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_label_task/${_req['manual_annotation_label_task_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id/update */
  UpdateManualAnnotationTask(
    req: flow_devops_evaluation_manual_annotation.UpdateManualAnnotationTaskReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.UpdateManualAnnotationTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}/update`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_task: _req['manual_annotation_task'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/contrast_report/create_post_columns_v2
   *
   * 支持评测任务创建后置列
   */
  CreatePostColumnsV2(
    req: flow_devops_evaluation_task.CreatePostColumnsV2Req,
    options?: T,
  ): Promise<flow_devops_evaluation_task.CreatePostColumnsV2Resp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/contrast_report/create_post_columns_v2',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      contrast_report_id: _req['contrast_report_id'],
      task_id: _req['task_id'],
      case_id: _req['case_id'],
      manual_annotation_items: _req['manual_annotation_items'],
      source_type: _req['source_type'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_item/batch_get */
  BatchGetManualAnnotationItem(
    req: flow_devops_evaluation_manual_annotation.BatchGetManualAnnotationItemReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.BatchGetManualAnnotationItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_item/batch_get',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      item_ids: _req['item_ids'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-api/evaluation/v1/dataset/:dataset_id/pull_row_groups */
  PullRowGroups(
    req: flow_devops_evaluation_dataset_openapi.PullRowGroupsRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset_openapi.PullRowGroupsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/dataset/${_req['dataset_id']}/pull_row_groups`,
    );
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-api/evaluation/v1/dataset */
  CreateEvalDataset(
    req: flow_devops_evaluation_dataset_openapi.CreateEvalDatasetRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset_openapi.CreateEvalDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL('/open-api/evaluation/v1/dataset');
    const method = 'POST';
    const data = {
      name: _req['name'],
      desc: _req['desc'],
      column_schema: _req['column_schema'],
      publish_option: _req['publish_option'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-api/evaluation/v1/dataset/:dataset_id/clear */
  ClearEvalDataset(
    req: flow_devops_evaluation_dataset_openapi.ClearEvalDatasetRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset_openapi.ClearEvalDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/evaluation/v1/dataset/${_req['dataset_id']}/clear`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/dataset/batch_delete */
  BatchDeleteDataset(
    req: flow_devops_evaluation_dataset.BatchDeleteDatasetReq,
    options?: T,
  ): Promise<flow_devops_evaluation_dataset.BatchDeleteDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/dataset/batch_delete');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      dataset_ids: _req['dataset_ids'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/annotation_task/export_csv */
  ExportAnnotationTaskToCsv(
    req: flow_devops_evaluation_manual_annotation.ExportAnnotationTaskToCsvRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.ExportAnnotationTaskToCsvResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/annotation_task/export_csv',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      annotation_task_id: _req['annotation_task_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/case/:case_id/task/export_csv */
  ExportReportToCsv(
    req: flow_devops_evaluation_task.ExportReportToCsvRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ExportReportToCsvResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/case/:case_id/task/export_csv',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      case_id: _req['case_id'],
      source_id: _req['source_id'],
      source_type: _req['source_type'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_task/:manual_annotation_task_id/write_back_annotation_results */
  WriteBackAnnotationResults(
    req: flow_devops_evaluation_manual_annotation.WriteBackAnnotationResultsReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.WriteBackAnnotationResultsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_task/${_req['manual_annotation_task_id']}/write_back_annotation_results`,
    );
    const method = 'POST';
    const data = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_item/:manual_annotation_item_id/update */
  UpdateManualAnnotationItem(
    req: flow_devops_evaluation_manual_annotation.UpdateManualAnnotationItemReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.UpdateManualAnnotationItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_item/${_req['manual_annotation_item_id']}/update`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_item: _req['manual_annotation_item'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** DELETE /api/evaluation/v1/manual_annotation_item/:manual_annotation_item_id */
  DeleteManualAnnotationItem(
    req: flow_devops_evaluation_manual_annotation.DeleteManualAnnotationItemReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.DeleteManualAnnotationItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/manual_annotation_item/${_req['manual_annotation_item_id']}`,
    );
    const method = 'DELETE';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_task/cancel_select_for_write_back */
  CancelSelectAnnotationLabelResultForWriteBack(
    req: flow_devops_evaluation_manual_annotation.CancelSelectAnnotationLabelResultForWriteBackReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.CancelSelectAnnotationLabelResultForWriteBackResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_task/cancel_select_for_write_back',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_label_task_id: _req['manual_annotation_label_task_id'],
      item_id: _req['item_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/manual_annotation_task/select_for_write_back */
  SelectAnnotationLabelResultForWriteBack(
    req: flow_devops_evaluation_manual_annotation.SelectAnnotationLabelResultForWriteBackReq,
    options?: T,
  ): Promise<flow_devops_evaluation_manual_annotation.SelectAnnotationLabelResultForWriteBackResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/evaluation/v1/manual_annotation_task/select_for_write_back',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      manual_annotation_label_task_id: _req['manual_annotation_label_task_id'],
      item_id: _req['item_id'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/evaluation/v1/case/:case_id/task/:task_id/dashboard/v2
   *
   * dashboard 展示一次执行任务
   */
  DashboardTaskV2(
    req: flow_devops_evaluation_task.DashboardTaskV2Request,
    options?: T,
  ): Promise<flow_devops_evaluation_task.DashboardTaskV2Response> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/case/${_req['case_id']}/task/${_req['task_id']}/dashboard/v2`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      row_group_run_state: _req['row_group_run_state'],
      custom_filter: _req['custom_filter'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/evaluation/v1/get_contrast_report/:contrast_report_id/v2 */
  GetContrastReportV2(
    req: flow_devops_evaluation_task.GetContrastReportV2Req,
    options?: T,
  ): Promise<flow_devops_evaluation_task.GetContrastReportV2Resp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/evaluation/v1/get_contrast_report/${_req['contrast_report_id']}/v2`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      custom_filter: _req['custom_filter'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/evaluation/v1/case/exec_dynamic
   *
   * 单次执行case，动态传入参数
   */
  ExecCaseDynamic(
    req: flow_devops_evaluation_task.ExecCaseDynamicRequest,
    options?: T,
  ): Promise<flow_devops_evaluation_task.ExecCaseDynamicResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/evaluation/v1/case/exec_dynamic');
    const method = 'PUT';
    const data = {
      case_id: _req['case_id'],
      space_id: _req['space_id'],
      cid: _req['cid'],
      runtime_parameter: _req['runtime_parameter'],
      exec_times: _req['exec_times'],
      dataset_source: _req['dataset_source'],
      evaluator_source: _req['evaluator_source'],
      eval_object: _req['eval_object'],
      Base: _req['Base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'FlowDevops-Agw-AppId': _req['FlowDevops-Agw-AppId'],
    };
    return this.request({ url, method, data, headers }, options);
  }
}
/* eslint-enable */
