/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 通知渠道 */
export enum ChannelType {
  SMS = 1,
  Email = 2,
  Inbox = 3,
  HomeBot = 4,
}

/** 通知场景 */
export enum NotifyScene {
  /** 审核通过 (插件或者Bot等通过 params 参数注入)  params 需要 check 下 是否有模板必须字段 */
  ProductAuditSuccess = 1,
  /** 审核未通过 (插件或者Bot等通过 params 参数注入) */
  ProductAuditFailed = 2,
  /** 商品下架，给Owner发送通知 (插件或者Bot等通过 params 参数注入) */
  ProductOfflineOwner = 3,
  /** 商品下架，给使用者发送通知 */
  ProductOfflineUser = 4,
  /** 开发者认证 */
  DeveloperCertification = 5,
  /** 自动充值成功 */
  TokenAutoChargeSuccess = 6,
  /** 自动充值失败 */
  TokenAutoChargeFailed = 7,
  /** 互动通知 */
  InteractionDailyNotify = 8,
  /** 用户信息审核通过 */
  UserInfoAuditSuccess = 9,
  /** 用户信息审核不通过 */
  UserInfoAuditFailed = 10,
  /** 单个用户关注了"我" */
  InteractionFollowSingle = 11,
  /** 多个用户关注了"我" */
  InteractionFollowMulti = 12,
  /** 关注的单个用户有bot更新 */
  InteractionBotUpdateSingle = 13,
  /** 关注的多个用户有bot更新 */
  InteractionBotUpdateMulti = 14,
  /** 发票待申请 */
  TradeInvoiceWaitApply = 15,
  /** 发票可下载 */
  TradeInvoiceApplyReady = 16,
  /** Premium购买失败 */
  PremiumPurchasePayFailed = 17,
  /** Premium续费支付失败 */
  PremiumRenewalPayFailed = 18,
  /** 拒付通知 */
  TradeChargeback = 19,
  /** 举报处理完成，通知举报人 */
  ReportProcessedToWhistleblower = 20,
  /** 举报处理完成，通知帖子的作者 */
  ReportProcessedToPostOwner = 21,
  /** 举报处理完成，通知评论的作者 */
  ReportProcessedToCommentOwner = 22,
  /** 举报处理完成，通知商品的作者 */
  ReportProcessedToProductOwner = 23,
}

/** 异步通知任务状态 */
export enum NotifyTaskStatus {
  Waiting = 0,
  InProcess = 1,
  Finish = 2,
}

/** 票据验证状态 */
export enum TicketCheckStatus {
  TicketCorrect = 0,
  TicketError = 1,
  TicketExpiry = 2,
}

/** 验证码状态 */
export enum VerifyStatus {
  Correct = 0,
  Error = 1,
  Expiry = 2,
}

/** channelType 是邮件的时候，可以传UserID或者EmailAddress
 channelType 是SMS的时候，可以传UserID或者MobileNumber */
export interface NotifyTarget {
  UserID?: Int64;
  EmailAddress?: string;
  MobileNum?: string;
  BizParams?: Record<string, string>;
}

export interface NotifyTask {
  NotifyTaskID?: string;
  /** 通知渠道 */
  ChannelType?: ChannelType;
  /** 当前任务状态 */
  Status?: NotifyTaskStatus;
  /** 通知成功的目标 */
  Success?: Array<NotifyTarget>;
  /** 通知失败的目标 */
  Failed?: Array<NotifyTarget>;
  /** 等待通知的目标 */
  InProcess?: Array<NotifyTarget>;
}
/* eslint-enable */
