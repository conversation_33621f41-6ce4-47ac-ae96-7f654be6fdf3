/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as openapi from './namespaces/openapi';

export { openapi };
export * from './namespaces/openapi';

export type Int64 = string | number;

export interface AddCollaboratorRequest2 {
  collaborator_types?: Array<openapi.CollaboratorType>;
  principal: openapi.PrincipalIdentifier;
  resource: openapi.ResourceIdentifier;
}

export interface AddCollaboratorResponse {
  code: number;
  msg: string;
}

export interface AppInstallationConsentRequest2 {
  appid: string;
  installation_account_hint: string;
}

export interface AppInstallationConsentResponse {
  code: number;
  msg: string;
}

export interface AuthorizeAppsAndPersonalaccesstokenListWithPersonalAccountPermissionRequest {
  /** crossaccount authinfo list */
  crossaccount_authinfo_list?: Array<openapi.AppAndPATAuthInfoForPersonalAccountAuthorization>;
}

export interface AuthorizeAppsAndPersonalaccesstokenListWithPersonalAccountPermissionResponse {
  code: number;
  msg: string;
}

export interface AuthorizeAppWithDeclaredPermissionRequest2 {
  appid: string;
  organization_id?: string;
}

export interface AuthorizeAppWithDeclaredPermissionResponse {
  code: number;
  msg: string;
}

export interface AuthorizeAppWithSpecifiedWorkspaceRequest2 {
  appid: string;
  workspace_list: Array<string>;
  organization_id?: string;
}

export interface AuthorizeAppWithSpecifiedWorkspaceResponse {
  code: number;
  msg: string;
}

export interface BatchAddCollaboratorRequest2 {
  /** 1-User，2-Service */
  principal_type: number;
  resource: openapi.ResourceIdentifier;
  principal_ids: Array<string>;
  collaborator_types?: Array<openapi.CollaboratorType>;
}

export interface BatchAddCollaboratorResponse2 {
  code: number;
  msg: string;
  data: openapi.BatchAddCollaboratorResponseData;
}

export interface BatchAddEnterprisePeopleRequest2 {
  enterprise_id: string;
  enterprise_people: Array<openapi.EnterprisePeopleAddData>;
  need_check_people_valid?: boolean;
}

export interface BatchAddEnterprisePeopleResponse {
  code: number;
  msg: string;
}

export interface BatchMigrateAuthorizationRequest2 {
  authorization_list: Array<openapi.MigrateAuthorizationItem>;
}

export interface BatchMigrateAuthorizationResponse {
  code: number;
  msg: string;
}

export interface BindVolcanoRequest {}

export interface BindVolcanoResponse2 {
  code: number;
  msg: string;
  /** 1-success */
  bind_result?: number;
}

export interface CheckEnterpriseExistRequest {}

export interface CheckEnterpriseExistResponse2 {
  code: number;
  msg: string;
  data: openapi.CheckEnterpriseExistResponseData;
}

export interface CheckPersonalAccessTokenInWorkspaceRequest {
  /** workspace id */
  workspace_id: string;
}

export interface CheckPersonalAccessTokenInWorkspaceResponse2 {
  code: number;
  msg: string;
  data: openapi.CheckPersonalAccessTokenInWorkspaceResponseData;
}

export interface CreateApplicationForEnterpriseMemberRequest2 {
  enterprise_id: string;
  remark?: string;
}

export interface CreateApplicationForEnterpriseMemberResponse {
  code: number;
  msg: string;
}

export interface CreateAppMetaRequest2 {
  app_type: openapi.AppType;
  client_type?: openapi.ClientType;
  name: string;
  description?: string;
  organization_id?: string;
}

export interface CreateAppMetaResponse2 {
  code: number;
  msg: string;
  data: openapi.CreateAppMetaResponseData;
}

export interface CreateClientSecretRequest2 {
  appid: string;
}

export interface CreateClientSecretResponse2 {
  code: number;
  msg: string;
  data: openapi.CreateClientSecretResponseData;
}

export interface CreateEnterpriseInviteLinkRequest2 {
  enterprise_id: string;
}

export interface CreateEnterpriseInviteLinkResponse2 {
  code: number;
  msg: string;
  data: openapi.CreateEnterpriseInviteLinkResponseData;
}

export interface CreateEnterpriseRequest2 {
  name: string;
  icon_uri: string;
}

export interface CreateEnterpriseResponse2 {
  code: number;
  msg: string;
  data: openapi.CreateEnterpriseResponseData;
}

export interface CreateJoinApplicationRequest2 {
  key: string;
  remark?: string;
}

export interface CreateJoinApplicationResponse {
  code: number;
  msg: string;
}

export interface CreatePersonalAccessTokenAndPermissionRequest2 {
  /** PAT名称 */
  name: string;
  /** PAT自定义过期时间 */
  expire_at?: Int64;
  /** PAT用户枚举过期时间 1、30、60、90、180、365、permanent */
  duration_day?: string;
  /** organization id */
  organization_id?: string;
  workspace_permission?: openapi.WorkspacePermission;
  account_permission?: openapi.AccountPermission;
  workspace_permission_v2?: openapi.WorkspacePermissionV2;
  enterprise_permission?: openapi.EnterprisePermission;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface CreatePersonalAccessTokenAndPermissionResponse2 {
  code: number;
  msg: string;
  data: openapi.CreatePersonalAccessTokenAndPermissionResponseData;
}

export interface CreateServiceIdentityRequest2 {
  /** 服务身份名称 */
  name: string;
  /** 所属组织id */
  organization_id?: string;
  /** PAT自定义过期时间 */
  expire_at?: Int64;
  /** 可枚举过期时间 */
  duration_day?: openapi.DurationDay;
  permission: openapi.ServicePermission;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface CreateServiceIdentityResponse2 {
  code: number;
  msg: string;
  data: openapi.CreateServiceIdentityResponseData;
}

export interface DeleteAppRequest2 {
  appid: string;
}

export interface DeleteAppResponse {
  code: number;
  msg: string;
}

export interface DeleteClientSecretRequest2 {
  appid: string;
  client_secret_id: string;
}

export interface DeleteClientSecretResponse {
  code: number;
  msg: string;
}

export interface DeletePersonalAccessTokenAndPermissionRequest2 {
  /** PAT Id */
  id: string;
}

export interface DeletePersonalAccessTokenAndPermissionResponse {
  code: number;
  msg: string;
}

export interface DeletePublicKeyRequest2 {
  fingerprint: string;
}

export interface DeletePublicKeyResponse {
  code: number;
  msg: string;
}

export interface DeleteServiceIdentityRequest {
  /** service identity id */
  id: string;
}

export interface DeleteServiceIdentityResponse {
  code: number;
  msg: string;
}

export interface GetAppAuthorizationRequestInfoRequest {
  /** authorize key */
  authorize_key: string;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface GetAppAuthorizationRequestInfoResponse2 {
  code: number;
  msg: string;
  data: openapi.GetAppAuthorizationRequestInfoResponseData;
}

export interface GetAppInstallationRequestInfoRequest {
  /** app to be installed */
  appid: string;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface GetAppInstallationRequestInfoResponse2 {
  code: number;
  msg: string;
  data: openapi.GetAppInstallationRequestInfoResponseData;
}

export interface GetApplicationForEnterpriseMemberRequest2 {
  enterprise_id: string;
}

export interface GetApplicationForEnterpriseMemberResponse2 {
  code: number;
  msg: string;
  data: openapi.GetApplicationForEnterpriseMemberResponseData;
}

export interface GetAppMetaRequest {
  /** appid */
  appid: string;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface GetAppMetaResponse2 {
  code: number;
  msg: string;
  data: openapi.GetAppMetaResponseData;
}

export interface GetAppsAndPersonalAccessTokenListForPersonalAccountPermissionAuthorizationRequest {
  /** page number */
  page?: Int64;
  /** page size */
  page_size?: Int64;
}

export interface GetAppsAndPersonalAccessTokenListForPersonalAccountPermissionAuthorizationResponse {
  code: number;
  msg: string;
  data?: openapi.GetAppsAndPATListForPersonalAccountPermissionAuthorizationResponseData;
}

export interface GetCertificationInfoRequest {}

export interface GetCertificationInfoResponse2 {
  code: number;
  msg: string;
  data: openapi.GetCertificationInfoResponseData;
}

export interface GetChecklistForWorkspaceMigrationRequest2 {
  workspace_id_list: Array<string>;
}

export interface GetChecklistForWorkspaceMigrationResponse2 {
  code: number;
  msg: string;
  data: openapi.GetChecklistForWorkspaceMigrationResponseData;
}

export interface GetEnterpriseRequest {
  /** Enterprise Id */
  enterprise_id: string;
}

export interface GetEnterpriseResponse2 {
  code: number;
  msg: string;
  data: openapi.GetEnterpriseResponseData;
}

export interface GetEnterpriseSettingsRequest2 {
  enterprise_id: string;
  enterprise_setting_key_list?: Array<openapi.EnterpriseSettingKey>;
}

export interface GetEnterpriseSettingsResponse2 {
  code: number;
  msg: string;
  data: openapi.GetEnterpriseSettingsResponseData;
}

export interface GetInviteInfoRequest {
  invite_key: string;
}

export interface GetInviteInfoResponse2 {
  code: number;
  msg: string;
  data: openapi.GetInviteInfoResponseData;
}

export interface GetJoinInvitationRequest2 {
  join_invitation_id: string;
}

export interface GetJoinInvitationResponse2 {
  code: number;
  msg: string;
  data: openapi.GetJoinInvitationResponseData;
}

export interface GetPersonalAccessTokenAndPermissionRequest {
  /** PAT Id */
  id: string;
}

export interface GetPersonalAccessTokenAndPermissionResponse2 {
  code: number;
  msg: string;
  data: openapi.GetPersonalAccessTokenAndPermissionResponseData;
}

export interface GetServiceIdentityRequest {
  /** service identity id */
  id: string;
}

export interface GetServiceIdentityResponse2 {
  code: number;
  msg: string;
  data: openapi.GetServiceIdentityResponseData;
}

export interface GetSSOSettingRequest2 {
  enterprise_id: string;
}

export interface GetSSOSettingResponse2 {
  code: number;
  msg: string;
  data: openapi.GetSSOSettingResponseData;
}

export interface GetUserProfileRequest {}

export interface GetUserProfileResponse2 {
  code: number;
  msg: string;
  detail: openapi.OpenApiRespDetailDetail;
  data: openapi.UserProfile;
}

export interface GetVolcanoConnectInfoWithInsNameRequest {}

export interface GetVolcanoConnectInfoWithInsNameResponse2 {
  code: number;
  msg: string;
  volcano_connect_info_with_ins_name?: openapi.VolcanoConnectInfoWithInsName;
}

export interface GetVolcanoMaskedMobileRequest {}

export interface GetVolcanoMaskedMobileResponse2 {
  code: number;
  msg: string;
  /** 是否有火山账号信息 */
  have_volcano: boolean;
  /** 掩码手机号 */
  mobile?: string;
}

export interface ImpersonateCozeUserRequest2 {
  duration_seconds?: Int64;
  scope?: openapi.Scope;
}

export interface ImpersonateCozeUserResponse2 {
  code: number;
  msg: string;
  data?: openapi.ImpersonateCozeUserResponseData;
}

export interface InstallAppOboRequest2 {
  appid: string;
  enterprise_id?: string;
}

export interface InstallAppOboResponse {
  code: number;
  msg: string;
}

export interface ListAppAuthorizationsRequest {
  /** appid */
  appid: string;
  /** page num */
  page_num?: Int64;
  /** page size */
  page_size?: Int64;
  /** JWT signed by OAuth App private key */
  authorization: string;
}

export interface ListAppAuthorizationsResponse2 {
  code: number;
  msg: string;
  detail: openapi.OpenApiRespDetailDetail;
  data: openapi.ListAppAuthorizationsResponseData;
}

export interface ListAppInstallationsRequest {
  /** enterprise id */
  enterprise_id?: string;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface ListAppInstallationsResponse2 {
  code: number;
  msg: string;
  data: openapi.ListAppInstallationsResponseData;
}

export interface ListAppMetaRequest {
  /** organization id */
  organization_id?: string;
}

export interface ListAppMetaResponse2 {
  code: number;
  msg: string;
  data: openapi.ListAppMetaResponseData;
}

export interface ListAuthorizedAppsRequest {
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
  /** page */
  page: Int64;
  /** page size */
  size: Int64;
}

export interface ListAuthorizedAppsResponse2 {
  code: number;
  msg: string;
  data: openapi.ListAuthorizedAppsResponseData;
}

export interface ListEnterpriseRequest {
  /** Contain enterprise of user */
  contain_enterprise_of_user?: boolean;
}

export interface ListEnterpriseResponse2 {
  code: number;
  msg: string;
  data: openapi.ListEnterpriseResponseData;
}

export interface ListJoinApplicationRequest2 {
  application_status?: openapi.ApplicationStatus;
  enterprise_id: string;
  search_key?: string;
  page: number;
  page_size: number;
}

export interface ListJoinApplicationResponse2 {
  code: number;
  msg: string;
  data: openapi.ListJoinApplicationResponseData;
}

export interface ListJoinInvitationRequest2 {
  invitation_status?: openapi.InvitationStatus;
  enterprise_id: string;
  search_key?: string;
  page: number;
  page_size: number;
}

export interface ListJoinInvitationResponse2 {
  code: number;
  msg: string;
  data: openapi.ListJoinInvitationResponseData;
}

export interface ListPersonalAccessTokensByCreatorRequest {
  /** organization id */
  organization_id: string;
}

export interface ListPersonalAccessTokensByCreatorResponse2 {
  code: number;
  msg: string;
  data: openapi.ListPersonalAccessTokensByCreatorResponseData;
}

export interface ListPersonalAccessTokensRequest {
  /** organization id */
  organization_id?: string;
  /** zero-indexed */
  page?: Int64;
  /** page size */
  size?: Int64;
  /** search option */
  search_option?: openapi.PatSearchOption;
}

export interface ListPersonalAccessTokensResponse2 {
  code: number;
  msg: string;
  data: openapi.ListPersonalAccessTokensResponseData;
}

export interface ListPersonalAccessTokenSupportPermissionsRequest {}

export interface ListPersonalAccessTokenSupportPermissionsResponse2 {
  code: number;
  msg: string;
  data: openapi.ListPersonalAccessTokenSupportPermissionsResponseData;
}

export interface ListServiceIdentitiesRequest {
  /** organization id */
  organization_id?: string;
}

export interface ListServiceIdentitiesResponse2 {
  code: number;
  msg: string;
  data: openapi.ListServiceIdentitiesResponseData;
}

export interface ModifyCollaboratorRequest2 {
  principal: openapi.PrincipalIdentifier;
  resource: openapi.ResourceIdentifier;
  collaborator_types?: Array<openapi.CollaboratorType>;
}

export interface ModifyCollaboratorResponse {
  code: number;
  msg: string;
}

export interface NeedCreateEnterpriseRequest {}

export interface NeedCreateEnterpriseResponse2 {
  code: number;
  msg: string;
  data: openapi.NeedCreateEnterpriseResponseData;
}

export interface PostAdapterMpV1Code2SessionRequest {
  bot_id?: string;
  app_id?: string;
  connector_id?: string;
  code?: string;
  with_userinfo?: boolean;
  execute_mode?: string;
  agent_type?: Int64;
  agent_version?: string;
}

export interface PostAdapterMpV1Code2SessionResponse {
  code: Int64;
  msg: string;
  data?: openapi.Code2SessionResponseData;
}

export interface PutOAuth2ConfigurationRequest2 {
  oauth2_configuration: openapi.OAuth2Configuration;
  appid: string;
}

export interface PutOAuth2ConfigurationResponse {
  code: number;
  msg: string;
}

export interface RemoveCollaboratorRequest2 {
  principal: openapi.PrincipalIdentifier;
  resource: openapi.ResourceIdentifier;
}

export interface RemoveCollaboratorResponse {
  code: number;
  msg: string;
}

export interface RemoveEnterprisePeopleRequest2 {
  enterprise_id: string;
  user_id: string;
  receiver: string;
}

export interface RemoveEnterprisePeopleResponse {
  code: number;
  msg: string;
}

export interface RevokeAppAndPersonalaccesstokenPersonalAccountPermissionRequest {
  crossaccount_authinfo?: openapi.AppAndPATAuthInfoForPersonalAccountAuthorization;
}

export interface RevokeAppAndPersonalaccesstokenPersonalAccountPermissionResponse {
  code: number;
  msg: string;
}

export interface RevokeAppAuthorizedPermissionRequest2 {
  authorization_type: openapi.AuthorizationType;
  appid: string;
  organization_id?: string;
}

export interface RevokeAppAuthorizedPermissionResponse {
  code: number;
  msg: string;
}

export interface RevokeJoinInvitationRequest2 {
  enterprise_id: string;
  join_invitation_id: string;
}

export interface RevokeJoinInvitationResponse {
  code: number;
  msg: string;
}

export interface SearchCanAddEnterprisePeopleRequest2 {
  enterprise_id: string;
  search_key?: string;
}

export interface SearchCanAddEnterprisePeopleResponse2 {
  code: number;
  msg: string;
  data: openapi.SearchCanAddEnterprisePeopleResponseData;
}

export interface SearchEnterprisePeopleRequest2 {
  people_type?: openapi.PeopleType;
  enterprise_id: string;
  search_key?: string;
  enterprise_role_type_list?: Array<openapi.EnterpriseRoleType>;
  need_volcano_user_info?: boolean;
  need_people_number?: boolean;
  page: number;
  page_size: number;
}

export interface SearchEnterprisePeopleResponse2 {
  code: number;
  msg: string;
  data: openapi.SearchEnterprisePeopleResponseData;
}

export interface SearchPeopleInOtherEnterpriseRequest2 {
  enterprise_id: string;
  search_key?: string;
  page: number;
  page_size: number;
}

export interface SearchPeopleInOtherEnterpriseResponse2 {
  code: number;
  msg: string;
  data: openapi.SearchPeopleInOtherEnterpriseResponseData;
}

export interface SubmitAppOboInstallationReviewRequest2 {
  appid: string;
  enterprise_id?: string;
}

export interface SubmitAppOboInstallationReviewResponse {
  code: number;
  msg: string;
}

export interface UninstallAppOboRequest2 {
  appid: string;
  enterprise_id?: string;
}

export interface UninstallAppOboResponse {
  code: number;
  msg: string;
}

export interface UpdateAppMetaRequest2 {
  status?: openapi.Status;
  oauth2_configuration?: openapi.OAuth2Configuration;
  appid: string;
  name?: string;
  description?: string;
  declared_permission?: Array<openapi.DeclaredPermission>;
  declared_permission_v2?: Array<string>;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface UpdateAppMetaResponse {
  code: number;
  msg: string;
}

export interface UpdateEnterprisePeopleRequest2 {
  enterprise_id: string;
  user_id: string;
  enterprise_role_type_list: Array<openapi.EnterpriseRoleType>;
}

export interface UpdateEnterprisePeopleResponse {
  code: number;
  msg: string;
}

export interface UpdateEnterpriseRequest2 {
  enterprise_id: string;
  name?: string;
  icon_uri?: string;
  replace_logo?: boolean;
}

export interface UpdateEnterpriseResponse {
  code: number;
  msg: string;
}

export interface UpdateEnterpriseSettingsRequest2 {
  enterprise_id: string;
  enterprise_settings: Array<openapi.EnterpriseSetting>;
}

export interface UpdateEnterpriseSettingsResponse {
  code: number;
  msg: string;
}

export interface UpdateJoinApplicationRequest2 {
  application_status: openapi.ApplicationStatus;
  enterprise_id: string;
  join_application_id_list: Array<string>;
}

export interface UpdateJoinApplicationResponse {
  code: number;
  msg: string;
}

export interface UpdateJoinInvitationRequest2 {
  enterprise_id: string;
  join_invitation_id: string;
  joined: boolean;
}

export interface UpdateJoinInvitationResponse {
  code: number;
  msg: string;
}

export interface UpdatePersonalAccessTokenAndPermissionRequest2 {
  workspace_permission?: openapi.WorkspacePermission;
  account_permission?: openapi.AccountPermission;
  workspace_permission_v2?: openapi.WorkspacePermissionV2;
  enterprise_permission?: openapi.EnterprisePermission;
  /** PAT Id */
  id: string;
  /** PAT 名称 */
  name?: string;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface UpdatePersonalAccessTokenAndPermissionResponse {
  code: number;
  msg: string;
}

export interface UpdateServiceIdentityRequest2 {
  permission?: openapi.ServicePermission;
  /** 服务身份id */
  id: string;
  /** 服务身份名称 */
  name?: string;
  /** PAT自定义过期时间 */
  expire_at?: Int64;
  /** 可枚举过期时间 */
  duration_day?: openapi.DurationDay2;
  /** x-tt-env bytedance env tag */
  'x-tt-env'?: string;
}

export interface UpdateServiceIdentityResponse {
  code: number;
  msg: string;
}

export interface UploadPublicKeyRequest2 {
  appid: string;
  public_key_pem: string;
}

export interface UploadPublicKeyResponse2 {
  code: number;
  msg: string;
  data: openapi.UploadPublicKeyResponseData;
}

export default class PatPermissionApiService<T> {
  private request: any = () => {
    throw new Error('PatPermissionApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/permission_api/pat/create_personal_access_token_and_permission
   *
   * create pat with permission initialized
   *
   * create pat with permission initialized
   */
  CreatePersonalAccessTokenAndPermission(
    req: CreatePersonalAccessTokenAndPermissionRequest2,
    options?: T,
  ): Promise<CreatePersonalAccessTokenAndPermissionResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/pat/create_personal_access_token_and_permission',
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      expire_at: _req['expire_at'],
      duration_day: _req['duration_day'],
      organization_id: _req['organization_id'],
      workspace_permission: _req['workspace_permission'],
      account_permission: _req['account_permission'],
      workspace_permission_v2: _req['workspace_permission_v2'],
      enterprise_permission: _req['enterprise_permission'],
    };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/permission_api/pat/update_personal_access_token_and_permission
   *
   * update pat with permission updated
   *
   * update pat with permission updated
   */
  UpdatePersonalAccessTokenAndPermission(
    req: UpdatePersonalAccessTokenAndPermissionRequest2,
    options?: T,
  ): Promise<UpdatePersonalAccessTokenAndPermissionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/pat/update_personal_access_token_and_permission',
    );
    const method = 'POST';
    const data = {
      workspace_permission: _req['workspace_permission'],
      account_permission: _req['account_permission'],
      workspace_permission_v2: _req['workspace_permission_v2'],
      enterprise_permission: _req['enterprise_permission'],
      id: _req['id'],
      name: _req['name'],
    };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/permission_api/pat/delete_personal_access_token_and_permission
   *
   * delete pat with permission deleted
   *
   * delete pat with permission deleted
   */
  DeletePersonalAccessTokenAndPermission(
    req: DeletePersonalAccessTokenAndPermissionRequest2,
    options?: T,
  ): Promise<DeletePersonalAccessTokenAndPermissionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/pat/delete_personal_access_token_and_permission',
    );
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/pat/list_personal_access_tokens
   *
   * list pats
   *
   * list pats in account
   */
  ListPersonalAccessTokens(
    req?: ListPersonalAccessTokensRequest,
    options?: T,
  ): Promise<ListPersonalAccessTokensResponse2> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/pat/list_personal_access_tokens',
    );
    const method = 'GET';
    const params = {
      organization_id: _req['organization_id'],
      page: _req['page'],
      size: _req['size'],
      search_option: _req['search_option'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/permission_api/pat/get_personal_access_token_and_permission
   *
   * get pat and permission
   *
   * get pat and permission
   */
  GetPersonalAccessTokenAndPermission(
    req: GetPersonalAccessTokenAndPermissionRequest,
    options?: T,
  ): Promise<GetPersonalAccessTokenAndPermissionResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/pat/get_personal_access_token_and_permission',
    );
    const method = 'GET';
    const params = { id: _req['id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/permission_api/pat/list_personal_access_token_support_permissions
   *
   * list pat support permissions
   *
   * list pat support permissions
   */
  ListPersonalAccessTokenSupportPermissions(
    req?: ListPersonalAccessTokenSupportPermissionsRequest,
    options?: T,
  ): Promise<ListPersonalAccessTokenSupportPermissionsResponse2> {
    const url = this.genBaseURL(
      '/api/permission_api/pat/list_personal_access_token_support_permissions',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/permission_api/pat/check_personal_access_token_in_workspace
   *
   * check pat under the certain workspace
   *
   * check pat under the certain workspace
   */
  CheckPersonalAccessTokenInWorkspace(
    req: CheckPersonalAccessTokenInWorkspaceRequest,
    options?: T,
  ): Promise<CheckPersonalAccessTokenInWorkspaceResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/pat/check_personal_access_token_in_workspace',
    );
    const method = 'GET';
    const params = { workspace_id: _req['workspace_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/permission/authz/add_collaborator
   *
   * add collaborator to a resource
   *
   * add collaborator to a resource
   */
  AddCollaborator(
    req: AddCollaboratorRequest2,
    options?: T,
  ): Promise<AddCollaboratorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission/authz/add_collaborator');
    const method = 'POST';
    const data = {
      collaborator_types: _req['collaborator_types'],
      principal: _req['principal'],
      resource: _req['resource'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission/authz/remove_collaborator
   *
   * remove collaborator from a resource
   *
   * remove collaborator from a resource
   */
  RemoveCollaborator(
    req: RemoveCollaboratorRequest2,
    options?: T,
  ): Promise<RemoveCollaboratorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission/authz/remove_collaborator');
    const method = 'POST';
    const data = { principal: _req['principal'], resource: _req['resource'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/create_app_meta
   *
   * create coze app meta data
   *
   * create coze app meta data
   */
  CreateAppMeta(
    req: CreateAppMetaRequest2,
    options?: T,
  ): Promise<CreateAppMetaResponse2> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/create_app_meta');
    const method = 'POST';
    const data = {
      app_type: _req['app_type'],
      client_type: _req['client_type'],
      name: _req['name'],
      description: _req['description'],
      organization_id: _req['organization_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/app/list_app_meta
   *
   * list coze app meta data under account
   *
   * list coze app meta data under account
   */
  ListAppMeta(
    req?: ListAppMetaRequest,
    options?: T,
  ): Promise<ListAppMetaResponse2> {
    const _req = req || {};
    const url = this.genBaseURL('/api/permission_api/app/list_app_meta');
    const method = 'GET';
    const params = { organization_id: _req['organization_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/permission_api/app/get_app_meta
   *
   * get coze app meta data of a certain coze app
   *
   * get coze app meta data of a certain coze app
   */
  GetAppMeta(
    req: GetAppMetaRequest,
    options?: T,
  ): Promise<GetAppMetaResponse2> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/get_app_meta');
    const method = 'GET';
    const params = { appid: _req['appid'] };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/permission_api/app/update_app_meta
   *
   * update coze app meta data
   *
   * update coze app meta data
   */
  UpdateAppMeta(
    req: UpdateAppMetaRequest2,
    options?: T,
  ): Promise<UpdateAppMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/update_app_meta');
    const method = 'POST';
    const data = {
      status: _req['status'],
      oauth2_configuration: _req['oauth2_configuration'],
      appid: _req['appid'],
      name: _req['name'],
      description: _req['description'],
      declared_permission: _req['declared_permission'],
      declared_permission_v2: _req['declared_permission_v2'],
    };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/permission_api/app/upload_public_key
   *
   * upload public key for coze app
   *
   * upload public key for coze app
   */
  UploadPublicKey(
    req: UploadPublicKeyRequest2,
    options?: T,
  ): Promise<UploadPublicKeyResponse2> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/upload_public_key');
    const method = 'POST';
    const data = {
      appid: _req['appid'],
      public_key_pem: _req['public_key_pem'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/delete_public_key
   *
   * delete public key for coze app
   *
   * delete public key for coze app
   */
  DeletePublicKey(
    req: DeletePublicKeyRequest2,
    options?: T,
  ): Promise<DeletePublicKeyResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/delete_public_key');
    const method = 'POST';
    const data = { fingerprint: _req['fingerprint'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/create_client_secret
   *
   * create client secret for coze app
   *
   * create client secret for coze app
   */
  CreateClientSecret(
    req: CreateClientSecretRequest2,
    options?: T,
  ): Promise<CreateClientSecretResponse2> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/create_client_secret');
    const method = 'POST';
    const data = { appid: _req['appid'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/delete_client_secret
   *
   * delete client secret for coze app
   *
   * delete client secret for coze app
   */
  DeleteClientSecret(
    req: DeleteClientSecretRequest2,
    options?: T,
  ): Promise<DeleteClientSecretResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/delete_client_secret');
    const method = 'POST';
    const data = {
      appid: _req['appid'],
      client_secret_id: _req['client_secret_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/put_oauth2_configuration
   *
   * put oauth2 configuration for coze app
   *
   * put oauth2 configuration for coze app
   */
  PutOAuth2Configuration(
    req: PutOAuth2ConfigurationRequest2,
    options?: T,
  ): Promise<PutOAuth2ConfigurationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/put_oauth2_configuration',
    );
    const method = 'POST';
    const data = {
      oauth2_configuration: _req['oauth2_configuration'],
      appid: _req['appid'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/authorize_app_with_declared_permission
   *
   * authorize app with declared permission under account
   *
   * authorize app with declared permission under account
   */
  AuthorizeAppWithDeclaredPermission(
    req: AuthorizeAppWithDeclaredPermissionRequest2,
    options?: T,
  ): Promise<AuthorizeAppWithDeclaredPermissionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/authorize_app_with_declared_permission',
    );
    const method = 'POST';
    const data = {
      appid: _req['appid'],
      organization_id: _req['organization_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/app/list_authorized_apps
   *
   * list authorized coze apps under account
   *
   * list authorized coze apps under account
   */
  ListAuthorizedApps(
    req: ListAuthorizedAppsRequest,
    options?: T,
  ): Promise<ListAuthorizedAppsResponse2> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/list_authorized_apps');
    const method = 'GET';
    const params = { page: _req['page'], size: _req['size'] };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/permission_api/app/get_app_authorization_request_info
   *
   * get app authorization request page infomation
   *
   * get app authorization request page infomation
   */
  GetAppAuthorizationRequestInfo(
    req: GetAppAuthorizationRequestInfoRequest,
    options?: T,
  ): Promise<GetAppAuthorizationRequestInfoResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/get_app_authorization_request_info',
    );
    const method = 'GET';
    const params = { authorize_key: _req['authorize_key'] };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/permission_api/app/revoke_app_authorized_permission
   *
   * revoke app authorized permission
   *
   * revoke app authorized permission
   */
  RevokeAppAuthorizedPermission(
    req: RevokeAppAuthorizedPermissionRequest2,
    options?: T,
  ): Promise<RevokeAppAuthorizedPermissionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/revoke_app_authorized_permission',
    );
    const method = 'POST';
    const data = {
      authorization_type: _req['authorization_type'],
      appid: _req['appid'],
      organization_id: _req['organization_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/delete_app
   *
   * delete coze app
   *
   * delete coze
   */
  DeleteApp(req: DeleteAppRequest2, options?: T): Promise<DeleteAppResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/delete_app');
    const method = 'POST';
    const data = { appid: _req['appid'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission/authz/modify_collaborator
   *
   * modify collaborator from a resource
   *
   * modify collaborator from a resource
   */
  ModifyCollaborator(
    req: ModifyCollaboratorRequest2,
    options?: T,
  ): Promise<ModifyCollaboratorResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission/authz/modify_collaborator');
    const method = 'POST';
    const data = {
      principal: _req['principal'],
      resource: _req['resource'],
      collaborator_types: _req['collaborator_types'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission/authz/batch_add_collaborator
   *
   * batch add collaborator to a resource
   *
   * batch add collaborator to a resource
   */
  BatchAddCollaborator(
    req: BatchAddCollaboratorRequest2,
    options?: T,
  ): Promise<BatchAddCollaboratorResponse2> {
    const _req = req;
    const url = this.genBaseURL('/api/permission/authz/batch_add_collaborator');
    const method = 'POST';
    const data = {
      principal_type: _req['principal_type'],
      resource: _req['resource'],
      principal_ids: _req['principal_ids'],
      collaborator_types: _req['collaborator_types'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/app/get_app_installation_request_info
   *
   * get app installation request page infomation
   *
   * get app installation request page infomation
   */
  GetAppInstallationRequestInfo(
    req: GetAppInstallationRequestInfoRequest,
    options?: T,
  ): Promise<GetAppInstallationRequestInfoResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/get_app_installation_request_info',
    );
    const method = 'GET';
    const params = { appid: _req['appid'] };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/permission_api/app/app_installation_consent
   *
   * consent app installation
   *
   * consent app installation
   */
  AppInstallationConsent(
    req: AppInstallationConsentRequest2,
    options?: T,
  ): Promise<AppInstallationConsentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/app_installation_consent',
    );
    const method = 'POST';
    const data = {
      appid: _req['appid'],
      installation_account_hint: _req['installation_account_hint'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/coze_web_app/impersonate_coze_user
   *
   * create access token with login session
   *
   * create access token with login session
   */
  ImpersonateCozeUser(
    req?: ImpersonateCozeUserRequest2,
    options?: T,
  ): Promise<ImpersonateCozeUserResponse2> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/coze_web_app/impersonate_coze_user',
    );
    const method = 'POST';
    const data = {
      duration_seconds: _req['duration_seconds'],
      scope: _req['scope'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/volcano/get_volcano_connect_info_with_ins_name
   *
   * get volcano connect info with instance name
   *
   * get volcano connect info with instance name
   */
  GetVolcanoConnectInfoWithInsName(
    req?: GetVolcanoConnectInfoWithInsNameRequest,
    options?: T,
  ): Promise<GetVolcanoConnectInfoWithInsNameResponse2> {
    const url = this.genBaseURL(
      '/api/permission_api/volcano/get_volcano_connect_info_with_ins_name',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/permission_api/volcano/get_volcano_masked_mobile
   *
   * get volcano masked mobile
   *
   * get volcano masked mobile
   */
  GetVolcanoMaskedMobile(
    req?: GetVolcanoMaskedMobileRequest,
    options?: T,
  ): Promise<GetVolcanoMaskedMobileResponse2> {
    const url = this.genBaseURL(
      '/api/permission_api/volcano/get_volcano_masked_mobile',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/permission_api/volcano/bind_volcano
   *
   * bind volcano
   *
   * bind volcano
   */
  BindVolcano(
    req?: BindVolcanoRequest,
    options?: T,
  ): Promise<BindVolcanoResponse2> {
    const url = this.genBaseURL('/api/permission_api/volcano/bind_volcano');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/permission_api/app/authorize_app_with_specified_permission
   *
   * authorize app with specified workspace list
   *
   * authorize app with declared permission, but under specified workspace list
   */
  AuthorizeAppWithSpecifiedWorkspace(
    req: AuthorizeAppWithSpecifiedWorkspaceRequest2,
    options?: T,
  ): Promise<AuthorizeAppWithSpecifiedWorkspaceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/authorize_app_with_specified_permission',
    );
    const method = 'POST';
    const data = {
      appid: _req['appid'],
      workspace_list: _req['workspace_list'],
      organization_id: _req['organization_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/users/me
   *
   * get user profile for user access token
   *
   * get user profile for user access token
   */
  GetUserProfile(
    req?: GetUserProfileRequest,
    options?: T,
  ): Promise<GetUserProfileResponse2> {
    const url = this.genBaseURL('/v1/users/me');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /v1/oauth-apps/:appid/authorizations
   *
   * list app authorizations
   *
   * list app authorizations
   */
  ListAppAuthorizations(
    req: ListAppAuthorizationsRequest,
    options?: T,
  ): Promise<ListAppAuthorizationsResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      `/v1/oauth-apps/${_req['appid']}/authorizations`,
    );
    const method = 'GET';
    const params = { page_num: _req['page_num'], page_size: _req['page_size'] };
    const headers = { authorization: _req['authorization'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/permission_api/enterprise/get_certification_info
   *
   * get certification info from volcano
   *
   * get certification info from volcano
   */
  GetCertificationInfo(
    req?: GetCertificationInfoRequest,
    options?: T,
  ): Promise<GetCertificationInfoResponse2> {
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_certification_info',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/permission_api/enterprise/need_create_enterprise
   *
   * check if need create enterprise
   *
   * check if need create enterprise
   */
  NeedCreateEnterprise(
    req?: NeedCreateEnterpriseRequest,
    options?: T,
  ): Promise<NeedCreateEnterpriseResponse2> {
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/need_create_enterprise',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/permission_api/enterprise/create_enterprise
   *
   * create enterprise
   *
   * create enterprise
   */
  CreateEnterprise(
    req: CreateEnterpriseRequest2,
    options?: T,
  ): Promise<CreateEnterpriseResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/create_enterprise',
    );
    const method = 'POST';
    const data = { name: _req['name'], icon_uri: _req['icon_uri'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/enterprise/list_enterprises
   *
   * list enterprise
   *
   * list enterprise
   */
  ListEnterprise(
    req?: ListEnterpriseRequest,
    options?: T,
  ): Promise<ListEnterpriseResponse2> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/list_enterprises',
    );
    const method = 'GET';
    const params = {
      contain_enterprise_of_user: _req['contain_enterprise_of_user'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/permission_api/enterprise/get_enterprise
   *
   * get enterprise info
   *
   * get enterprise info
   */
  GetEnterprise(
    req: GetEnterpriseRequest,
    options?: T,
  ): Promise<GetEnterpriseResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_enterprise',
    );
    const method = 'GET';
    const params = { enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/permission_api/enterprise/update_enterprise
   *
   * update enterprise info
   *
   * update enterprise info
   */
  UpdateEnterprise(
    req: UpdateEnterpriseRequest2,
    options?: T,
  ): Promise<UpdateEnterpriseResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/update_enterprise',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      name: _req['name'],
      icon_uri: _req['icon_uri'],
      replace_logo: _req['replace_logo'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/update_enterprise_settings
   *
   * update enterprise settings
   *
   * update enterprise settings
   */
  UpdateEnterpriseSettings(
    req: UpdateEnterpriseSettingsRequest2,
    options?: T,
  ): Promise<UpdateEnterpriseSettingsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/update_enterprise_settings',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      enterprise_settings: _req['enterprise_settings'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/batch_add_enterprise_people
   *
   * batch add enterprise people
   *
   * batch add enterprise people
   */
  BatchAddEnterprisePeople(
    req: BatchAddEnterprisePeopleRequest2,
    options?: T,
  ): Promise<BatchAddEnterprisePeopleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/batch_add_enterprise_people',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      enterprise_people: _req['enterprise_people'],
      need_check_people_valid: _req['need_check_people_valid'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/remove_enterprise_people
   *
   * remove enterprise people
   *
   * remove enterprise people
   */
  RemoveEnterprisePeople(
    req: RemoveEnterprisePeopleRequest2,
    options?: T,
  ): Promise<RemoveEnterprisePeopleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/remove_enterprise_people',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      user_id: _req['user_id'],
      receiver: _req['receiver'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/update_enterprise_people
   *
   * update enterprise people
   *
   * update enterprise people
   */
  UpdateEnterprisePeople(
    req: UpdateEnterprisePeopleRequest2,
    options?: T,
  ): Promise<UpdateEnterprisePeopleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/update_enterprise_people',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      user_id: _req['user_id'],
      enterprise_role_type_list: _req['enterprise_role_type_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/create_enterprise_invite_link
   *
   * create enterprise invite link
   *
   * create enterprise invite link
   */
  CreateEnterpriseInviteLink(
    req: CreateEnterpriseInviteLinkRequest2,
    options?: T,
  ): Promise<CreateEnterpriseInviteLinkResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/create_enterprise_invite_link',
    );
    const method = 'POST';
    const data = { enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/create_join_application
   *
   * create join application
   *
   * create join application
   */
  CreateJoinApplication(
    req: CreateJoinApplicationRequest2,
    options?: T,
  ): Promise<CreateJoinApplicationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/create_join_application',
    );
    const method = 'POST';
    const data = { key: _req['key'], remark: _req['remark'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/enterprise/get_invite_info
   *
   * get invite info
   *
   * get invite info
   */
  GetInviteInfo(
    req: GetInviteInfoRequest,
    options?: T,
  ): Promise<GetInviteInfoResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_invite_info',
    );
    const method = 'GET';
    const params = { invite_key: _req['invite_key'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/permission_api/enterprise/update_join_application
   *
   * update join application
   *
   * update join application
   */
  UpdateJoinApplication(
    req: UpdateJoinApplicationRequest2,
    options?: T,
  ): Promise<UpdateJoinApplicationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/update_join_application',
    );
    const method = 'POST';
    const data = {
      application_status: _req['application_status'],
      enterprise_id: _req['enterprise_id'],
      join_application_id_list: _req['join_application_id_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/permission/list_personal_access_tokens_by_creator
   *
   * list pats by creator
   *
   * list pats by creator
   */
  ListPersonalAccessTokensByCreator(
    req: ListPersonalAccessTokensByCreatorRequest,
    options?: T,
  ): Promise<ListPersonalAccessTokensByCreatorResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/permission/list_personal_access_tokens_by_creator',
    );
    const method = 'GET';
    const params = { organization_id: _req['organization_id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/permission_api/app/list_app_installations
   *
   * list app installations
   *
   * list app installations
   */
  ListAppInstallations(
    req?: ListAppInstallationsRequest,
    options?: T,
  ): Promise<ListAppInstallationsResponse2> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/app/list_app_installations',
    );
    const method = 'GET';
    const params = { enterprise_id: _req['enterprise_id'] };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/permission_api/enterprise/get_enterprise_settings
   *
   * get enterprise settings
   *
   * get enterprise settings
   */
  GetEnterpriseSettings(
    req: GetEnterpriseSettingsRequest2,
    options?: T,
  ): Promise<GetEnterpriseSettingsResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_enterprise_settings',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      enterprise_setting_key_list: _req['enterprise_setting_key_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/search_can_add_enterprise_people
   *
   * search can add enterprise people
   *
   * search can add enterprise people
   */
  SearchCanAddEnterprisePeople(
    req: SearchCanAddEnterprisePeopleRequest2,
    options?: T,
  ): Promise<SearchCanAddEnterprisePeopleResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/search_can_add_enterprise_people',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      search_key: _req['search_key'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/search_enterprise_people
   *
   * search enterprise people
   *
   * search enterprise people
   */
  SearchEnterprisePeople(
    req: SearchEnterprisePeopleRequest2,
    options?: T,
  ): Promise<SearchEnterprisePeopleResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/search_enterprise_people',
    );
    const method = 'POST';
    const data = {
      people_type: _req['people_type'],
      enterprise_id: _req['enterprise_id'],
      search_key: _req['search_key'],
      enterprise_role_type_list: _req['enterprise_role_type_list'],
      need_volcano_user_info: _req['need_volcano_user_info'],
      need_people_number: _req['need_people_number'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/list_join_application
   *
   * list join application
   *
   * list join application
   */
  ListJoinApplication(
    req: ListJoinApplicationRequest2,
    options?: T,
  ): Promise<ListJoinApplicationResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/list_join_application',
    );
    const method = 'POST';
    const data = {
      application_status: _req['application_status'],
      enterprise_id: _req['enterprise_id'],
      search_key: _req['search_key'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /adapter/mp/v1/code2session
   *
   * code2Session
   *
   * code2Session
   */
  PostAdapterMpV1Code2Session(
    req?: PostAdapterMpV1Code2SessionRequest,
    options?: T,
  ): Promise<PostAdapterMpV1Code2SessionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/adapter/mp/v1/code2session');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      app_id: _req['app_id'],
      connector_id: _req['connector_id'],
      code: _req['code'],
      with_userinfo: _req['with_userinfo'],
      execute_mode: _req['execute_mode'],
      agent_type: _req['agent_type'],
      agent_version: _req['agent_version'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/submit_app_obo_installation_review
   *
   * submit app obo installation review
   *
   * submit app obo installation review
   */
  SubmitAppOboInstallationReview(
    req: SubmitAppOboInstallationReviewRequest2,
    options?: T,
  ): Promise<SubmitAppOboInstallationReviewResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/submit_app_obo_installation_review',
    );
    const method = 'POST';
    const data = { appid: _req['appid'], enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/install_app_obo
   *
   * install app obo
   *
   * install app obo
   */
  InstallAppObo(
    req: InstallAppOboRequest2,
    options?: T,
  ): Promise<InstallAppOboResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/install_app_obo');
    const method = 'POST';
    const data = { appid: _req['appid'], enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/uninstall_app_obo
   *
   * uninstall app obo
   *
   * uninstall app obo
   */
  UninstallAppObo(
    req: UninstallAppOboRequest2,
    options?: T,
  ): Promise<UninstallAppOboResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/permission_api/app/uninstall_app_obo');
    const method = 'POST';
    const data = { appid: _req['appid'], enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/get_checklist_for_workspace_migration
   *
   * get checklist for workspace migration
   *
   * get checklist for workspace migration
   */
  GetChecklistForWorkspaceMigration(
    req: GetChecklistForWorkspaceMigrationRequest2,
    options?: T,
  ): Promise<GetChecklistForWorkspaceMigrationResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/get_checklist_for_workspace_migration',
    );
    const method = 'POST';
    const data = { workspace_id_list: _req['workspace_id_list'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/permission_api/enterprise/check_enterprise_exist
   *
   * check enterprise exist
   *
   * check enterprise exist
   */
  CheckEnterpriseExist(
    req?: CheckEnterpriseExistRequest,
    options?: T,
  ): Promise<CheckEnterpriseExistResponse2> {
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/check_enterprise_exist',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/permission_api/enterprise/get_sso_setting
   *
   * get sso setting
   *
   * get sso setting
   */
  GetSSOSetting(
    req: GetSSOSettingRequest2,
    options?: T,
  ): Promise<GetSSOSettingResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_sso_setting',
    );
    const method = 'POST';
    const data = { enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/batch_migrate_authorization
   *
   * migrate authorization
   *
   * migrate authorization
   */
  BatchMigrateAuthorization(
    req: BatchMigrateAuthorizationRequest2,
    options?: T,
  ): Promise<BatchMigrateAuthorizationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/app/batch_migrate_authorization',
    );
    const method = 'POST';
    const data = { authorization_list: _req['authorization_list'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/list_join_invitation
   *
   * list join invitation
   *
   * list join invitation
   */
  ListJoinInvitation(
    req: ListJoinInvitationRequest2,
    options?: T,
  ): Promise<ListJoinInvitationResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/list_join_invitation',
    );
    const method = 'POST';
    const data = {
      invitation_status: _req['invitation_status'],
      enterprise_id: _req['enterprise_id'],
      search_key: _req['search_key'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/revoke_join_invitation
   *
   * revoke join invitation
   *
   * revoke join invitation
   */
  RevokeJoinInvitation(
    req: RevokeJoinInvitationRequest2,
    options?: T,
  ): Promise<RevokeJoinInvitationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/revoke_join_invitation',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      join_invitation_id: _req['join_invitation_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/update_join_invitation
   *
   * update join invitation
   *
   * update join invitation
   */
  UpdateJoinInvitation(
    req: UpdateJoinInvitationRequest2,
    options?: T,
  ): Promise<UpdateJoinInvitationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/update_join_invitation',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      join_invitation_id: _req['join_invitation_id'],
      joined: _req['joined'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/get_join_invitation
   *
   * get join invitation
   *
   * get join invitation
   */
  GetJoinInvitation(
    req: GetJoinInvitationRequest2,
    options?: T,
  ): Promise<GetJoinInvitationResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_join_invitation',
    );
    const method = 'POST';
    const data = { join_invitation_id: _req['join_invitation_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/search_people_in_other_enterprise
   *
   * search people in other enterprise
   *
   * search people in other enterprise
   */
  SearchPeopleInOtherEnterprise(
    req: SearchPeopleInOtherEnterpriseRequest2,
    options?: T,
  ): Promise<SearchPeopleInOtherEnterpriseResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/search_people_in_other_enterprise',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      search_key: _req['search_key'],
      page: _req['page'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/create_application_for_enterprise_member
   *
   * create application for enterprise member
   *
   * create application for enterprise member
   */
  CreateApplicationForEnterpriseMember(
    req: CreateApplicationForEnterpriseMemberRequest2,
    options?: T,
  ): Promise<CreateApplicationForEnterpriseMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/create_application_for_enterprise_member',
    );
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      remark: _req['remark'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/enterprise/get_application_for_enterprise_member
   *
   * get application for enterprise member
   *
   * get application for enterprise member
   */
  GetApplicationForEnterpriseMember(
    req: GetApplicationForEnterpriseMemberRequest2,
    options?: T,
  ): Promise<GetApplicationForEnterpriseMemberResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/enterprise/get_application_for_enterprise_member',
    );
    const method = 'POST';
    const data = { enterprise_id: _req['enterprise_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/authorize_apps_and_patlist_with_personalaccountpermission
   *
   * authorize apps and personalaccesstoken list with personal account permission
   *
   * authorize apps and personalaccesstoken list with personal account permission
   */
  AuthorizeAppsAndPersonalaccesstokenListWithPersonalAccountPermission(
    req?: AuthorizeAppsAndPersonalaccesstokenListWithPersonalAccountPermissionRequest,
    options?: T,
  ): Promise<AuthorizeAppsAndPersonalaccesstokenListWithPersonalAccountPermissionResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/app/authorize_apps_and_patlist_with_personalaccountpermission',
    );
    const method = 'POST';
    const data = {
      crossaccount_authinfo_list: _req['crossaccount_authinfo_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/revoke_app_and_pat_personalaccountpermission
   *
   * revoke app's and personalaccesstoken's personal account permission
   *
   * revoke app's and personalaccesstoken's personal account permission
   */
  RevokeAppAndPersonalaccesstokenPersonalAccountPermission(
    req?: RevokeAppAndPersonalaccesstokenPersonalAccountPermissionRequest,
    options?: T,
  ): Promise<RevokeAppAndPersonalaccesstokenPersonalAccountPermissionResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/app/revoke_app_and_pat_personalaccountpermission',
    );
    const method = 'POST';
    const data = { crossaccount_authinfo: _req['crossaccount_authinfo'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/app/get_apps_and_patlist_for_personalaccountpermission_authorization
   *
   * get apps and personalaccesstoken list for personal account permission authorization
   *
   * get apps and personalaccesstoken list for personal account permission authorization
   */
  GetAppsAndPersonalAccessTokenListForPersonalAccountPermissionAuthorization(
    req?: GetAppsAndPersonalAccessTokenListForPersonalAccountPermissionAuthorizationRequest,
    options?: T,
  ): Promise<GetAppsAndPersonalAccessTokenListForPersonalAccountPermissionAuthorizationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/app/get_apps_and_patlist_for_personalaccountpermission_authorization',
    );
    const method = 'POST';
    const data = { page: _req['page'], page_size: _req['page_size'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/permission_api/service/create_service_identity
   *
   * 创建服务身份
   *
   * 创建服务身份
   */
  create_service_identity(
    req: CreateServiceIdentityRequest2,
    options?: T,
  ): Promise<CreateServiceIdentityResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/service/create_service_identity',
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      organization_id: _req['organization_id'],
      expire_at: _req['expire_at'],
      duration_day: _req['duration_day'],
      permission: _req['permission'],
    };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/permission_api/service/get_service_identity
   *
   * 获取服务身份
   *
   * 获取服务身份
   */
  get_service_identity(
    req: GetServiceIdentityRequest,
    options?: T,
  ): Promise<GetServiceIdentityResponse2> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/service/get_service_identity',
    );
    const method = 'GET';
    const params = { id: _req['id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/permission_api/service/update_service_identity
   *
   * 更新服务身份
   *
   * 更新服务身份
   */
  update_service_identity(
    req: UpdateServiceIdentityRequest2,
    options?: T,
  ): Promise<UpdateServiceIdentityResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/service/update_service_identity',
    );
    const method = 'POST';
    const data = {
      permission: _req['permission'],
      id: _req['id'],
      name: _req['name'],
      expire_at: _req['expire_at'],
      duration_day: _req['duration_day'],
    };
    const headers = { 'x-tt-env': _req['x-tt-env'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/permission_api/service/delete_service_identity
   *
   * 删除服务身份
   *
   * 删除服务身份
   */
  delete_service_identity(
    req: DeleteServiceIdentityRequest,
    options?: T,
  ): Promise<DeleteServiceIdentityResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/permission_api/service/delete_service_identity',
    );
    const method = 'POST';
    const params = { id: _req['id'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/permission_api/service/list_service_identities
   *
   * 列出服务身份
   *
   * 列出服务身份
   */
  list_service_identities(
    req?: ListServiceIdentitiesRequest,
    options?: T,
  ): Promise<ListServiceIdentitiesResponse2> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/permission_api/service/list_service_identities',
    );
    const method = 'GET';
    const params = { organization_id: _req['organization_id'] };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
