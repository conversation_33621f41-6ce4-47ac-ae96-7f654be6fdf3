/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum FieldFilterType {
  Unknown = 1,
  /** Equals (=) */
  Eq = 2,
  /** Greater Than or Equal (>=) */
  Gte = 3,
  /** Less Than or Equal (<=) */
  Lte = 4,
  /** Contains All */
  Containsall = 5,
  /** In */
  In = 6,
  /** Not In */
  NotIn = 7,
}

/** Syntax: 
  <fieldName>: {
      "i64": {
          "gte": <value>, 
          "lte": <value>
      }
  } */
export interface FieldFilter {
  bool?: FieldFilterBool;
  i32?: FieldFilterI32;
  i64?: FieldFilterI64;
  f64?: FieldFilterF64;
  string?: FieldFilterString;
}

/** applied on a bool field 
 
 Syntax: 
  "bool": {
      "eq": <value>
  }

  Example:

 "bool": {"eq": true} */
export interface FieldFilterBool {
  /** Equals (=)
The eq operator matches records where the value of a field equals the specified value. */
  eq?: boolean;
}

/** applied on a float64 field 
 
 Syntax: 
  "f64": {
      "gte": <value>, 
      "lte": <value>
  }

  Example:

 "f64": {"gte": 100.0, "lte": 500.0} */
export interface FieldFilterF64 {
  /** Greater Than or Equal (>=)
It selects those records where the value of the field is greater than or equal to (i.e. >=) the specified value. */
  gte?: number;
  /** Less Than or Equal (<=)
It selects those records where the value of the field is less than or equal to (i.e. <=) the specified value. */
  lte?: number;
  /** In
It selects those records where the value of the field is within the specified list of values.
The "In" operator is used to compare the field's value against an array of values, and if the field's value
is found within that array, the record is included in the result set. */
  in?: Array<number>;
  /** Not In
It selects those records where the value of the field is not within the specified list of values.
The "Not In" operator is used to compare the field's value against an array of values, and if the field's value
is not found within that array, the record is included in the result set. */
  not_in?: Array<number>;
}

/** applied on a int32 field

 Syntax:
  "i64": {
      "gte": <value>,
      "lte": <value>
  }

  Example:

 "i32": {"gte": 100, "lte": 1000} */
export interface FieldFilterI32 {
  /** Greater Than or Equal (>=)
It selects those records where the value of the field is greater than or equal to (i.e. >=) the specified value. */
  gte?: number;
  /** Less Than or Equal (<=)
It selects those records where the value of the field is less than or equal to (i.e. <=) the specified value. */
  lte?: number;
  /** In
It selects those records where the value of the field is within the specified list of values.
The "In" operator is used to compare the field's value against an array of values, and if the field's value
is found within that array, the record is included in the result set. */
  in?: Array<number>;
  /** Not In
It selects those records where the value of the field is not within the specified list of values.
The "Not In" operator is used to compare the field's value against an array of values, and if the field's value
is not found within that array, the record is included in the result set. */
  not_in?: Array<number>;
}

/** applied on a int64 field 
 
 Syntax: 
  "i64": {
      "gte": <value>, 
      "lte": <value>
  }

  Example:

 "i64": {"gte": 100, "lte": 1000} */
export interface FieldFilterI64 {
  /** Greater Than or Equal (>=)
It selects those records where the value of the field is greater than or equal to (i.e. >=) the specified value. */
  gte?: string;
  /** Less Than or Equal (<=)
It selects those records where the value of the field is less than or equal to (i.e. <=) the specified value. */
  lte?: string;
  /** In
It selects those records where the value of the field is within the specified list of values.
The "In" operator is used to compare the field's value against an array of values, and if the field's value
is found within that array, the record is included in the result set. */
  in?: Array<string>;
  /** Not In
It selects those records where the value of the field is not within the specified list of values.
The "Not In" operator is used to compare the field's value against an array of values, and if the field's value
is not found within that array, the record is included in the result set. */
  not_in?: Array<string>;
}

/** applied on a string field 
 
 Syntax: 
  "string": {
      "containsall": [<value>]
  }

  Example:

 "string": {"containsall": ["kid"]}
 Only one of the following filtering operators can be selected */
export interface FieldFilterString {
  /** Contains All
It selects those records where the value of the field contains all of the characters in the specified array. */
  containsall?: Array<string>;
  /** In
It selects those records where the value of the field is within the specified list of values.
The "In" operator is used to compare the field's value against an array of values, and if the field's value
is found within that array, the record is included in the result set.
If you want to match an empty string, pass in "" in the array and cannot pass in other strings at the same time */
  in?: Array<string>;
  /** Not In
It selects those records where the value of the field is not within the specified list of values.
The "Not In" operator is used to compare the field's value against an array of values, and if the field's value
is not found within that array, the record is included in the result set.
If you want to match a non empty string, pass in "" in the array and cannot pass in other strings at the same time */
  not_in?: Array<string>;
}

export interface FieldOptions {
  i32?: Array<number>;
  i64?: Array<string>;
  f64?: Array<number>;
  string?: Array<string>;
}
/* eslint-enable */
