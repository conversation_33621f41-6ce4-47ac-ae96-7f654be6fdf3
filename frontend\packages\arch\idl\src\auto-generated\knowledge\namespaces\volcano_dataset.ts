/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface BatchCreateVolcanoDatasetRequest {
  /** 空间id */
  space_id: string;
  /** project ID project内创建 */
  project_id?: string;
  /** 火山侧知识库id */
  volcano_dataset_id_list?: Array<string>;
}

export interface BatchCreateVolcanoDatasetResponse {
  /** 返回创建的知识库id列表 */
  dataset_id_list?: Array<string>;
  code?: Int64;
  msg?: string;
}

export interface GetVolcanoDatasetListData {
  volcano_dataset_list?: Array<common.VolcanoDataset>;
  /** 跳转火山对应项目的新建知识库页面 */
  create_volcano_dataset_link?: string;
}

export interface GetVolcanoDatasetListRequest {
  /** 空间id */
  space_id: string;
  /** 火山知识库项目空间名称 */
  project_name: string;
}

export interface GetVolcanoDatasetListResponse {
  data?: GetVolcanoDatasetListData;
  code?: Int64;
  msg?: string;
}

export interface GetVolcanoDatasetProjectListData {
  volcano_dataset_project_list?: Array<common.VolcanoDatasetProject>;
  /** 跳转火山创建项目页面链接 */
  create_volcano_dataset_project_link?: string;
}

export interface GetVolcanoDatasetProjectListRequest {
  /** 空间id */
  space_id: string;
}

export interface GetVolcanoDatasetProjectListResponse {
  data?: GetVolcanoDatasetProjectListData;
  code?: Int64;
  msg?: string;
}

export interface GetVolcanoDatasetServiceListData {
  volcano_dataset_service_list?: Array<common.VolcanoDatasetService>;
  /** 跳转火山知识库下知识服务创建页面 */
  create_volcano_dataset_service_link?: string;
  /** 标签信息（标签名和标签信息） */
  tab_info?: Record<string, common.VolcanoDatasetTabInfo>;
}

export interface GetVolcanoDatasetServiceListRequest {
  /** 知识库id */
  dataset_id?: string;
  volcano_dataset_service_ids?: Array<string>;
  /** 传volcano_dataset_service_ids时需要提供对应的space id */
  space_id?: string;
}

export interface GetVolcanoDatasetServiceListResponse {
  data?: GetVolcanoDatasetServiceListData;
  code?: Int64;
  msg?: string;
}
/* eslint-enable */
