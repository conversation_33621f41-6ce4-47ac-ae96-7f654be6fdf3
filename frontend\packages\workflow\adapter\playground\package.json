{"name": "@coze-workflow/playground-adapter", "version": "0.0.1", "description": "workflow playground adapter", "license": "Apache-2.0", "author": "zeng<PERSON>oh<PERSON>@bytedance.com", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/playground": "workspace:*", "ahooks": "^3.7.8", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/json-schema": "~7.0.15", "@types/lodash-es": "^4.17.10", "@types/md5": "^2.3.2", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-helmet": "^6.1.11", "@types/semver": "^7.3.4", "@vitest/coverage-v8": "~3.0.5", "prop-types": "^15.5.7", "react": "~18.2.0", "react-dom": "~18.2.0", "react-helmet": "^6.1.0", "react-is": ">= 16.8.0", "react-router-dom": "^6.22.0", "scheduler": ">=0.19.0", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "typescript": "~5.8.2", "utility-types": "^3.10.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}