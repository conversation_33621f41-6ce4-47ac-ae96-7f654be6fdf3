/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as waimai_ExchangeTokenResponse from './waimai_ExchangeTokenResponse';
import * as waimai_FoodListResponse from './waimai_FoodListResponse';
import * as waimai_ShopcartOperationResponse from './waimai_ShopcartOperationResponse';

export type Int64 = string | number;

export interface AddressInfo {
  address_id?: string;
  name?: string;
  gender?: number;
  phone?: string;
  address?: string;
  house_number?: string;
  latitude?: string;
  longitude?: string;
  can_shipping?: number;
  address_range_tip?: string;
  /** 0 非默认 1 默认地址 */
  address_type?: number;
  /** 备选地址 ；默认地址 */
  address_type_desc?: string;
}

export interface AddressListRequest {}

export interface AddressListResp {
  address_info_list?: Array<AddressInfo>;
}

export interface AddressListResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: AddressListResp;
  type_for_model?: number;
}

export interface Attr {
  name: string;
  values?: Array<string>;
}

export interface AuthorizeRequest {
  /** 默认值code */
  response_type: string;
  /** oauth客户端id */
  client_id: string;
  /** 重定向地址，eg.https://chat.openai.com/aip/{plugin_id}/oauth/callback */
  redirect_uri: string;
}

export interface AuthorizeResponse {
  /** 错误码; 0 成功 */
  code: number;
  /** 错误信息 */
  msg: string;
}

export interface ErrorModel {
  message?: string;
  code: number;
  /** todo */
  errorInfo?: string;
}

export interface ExchangeTokenRequest {
  code: string;
}

export interface ExchangeTokenResponse {
  code?: number;
  msg?: string;
  data?: waimai_ExchangeTokenResponse.ExchangeTokenResRes;
}

export interface FoodListItem {
  food_sku_id?: string;
  count?: number;
}

export interface FoodListRequest {
  restaurant_id: string;
  longitude?: string;
  latitude?: string;
  query_spu_ids?: string;
}

export interface FoodListResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: waimai_FoodListResponse.FoodListRes;
  type_for_model?: number;
}

export interface FoodOperationItem {
  food_sku_id?: string;
  count?: number;
  operation_type?: string;
}

export interface OrderAgainRequest {
  order_id?: string;
}

export interface OrderAgainResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: ShoppingCartRespObj;
  type_for_model?: number;
}

export interface OrderConfirmation {
  code?: number;
  order_id?: string;
  /** optional string pay_url = 3;
  optional string wx_pay_params = 4; */
  order_total_price?: string;
  restaurant_name?: string;
  food_list?: Array<OrderFoodInfo>;
  delivery_time_tip?: string;
}

export interface OrderDetail {
  order_id?: string;
  order_time?: number;
  pay_status?: number;
  total_price?: string;
  original_price?: string;
  shipping_fee?: string;
  box_total_price?: string;
  night_shipping_fee?: string;
  status?: number;
  status_desc?: string;
  estimate_arrival_time?: number;
  restaurant_name?: string;
  restaurant_id?: string;
  recipient_phone?: string;
  recipient_address?: string;
  recipient_name?: string;
  logistics_status?: number;
  food_list?: Array<OrderFoodInfo>;
  logistics_status_desc?: string;
  remark?: string;
  pay_status_desc?: string;
  /** 店铺图片 */
  restaurant_pic_url?: string;
  delivery_time_tip?: string;
  /** 距离 */
  distance?: string;
  /** 商家评分 */
  restaurant_score?: string;
  user_caution?: string;
}

export interface OrderFoodInfo {
  food_sku_id?: string;
  food_price?: string;
  unit?: string;
  count?: number;
  /** optional int32 box_num = 5;
  optional string box_price = 6; */
  food_name?: string;
  origin_food_price?: string;
  picture?: string;
  attrs?: Array<Attr>;
  /** optional string spec = 10; */
  food_spu_id?: string;
  attr_selection_list?: Array<string>;
  food_selection_id?: string;
  food_total_price?: string;
}

export interface OrderListRequest {
  cursor?: string;
}

export interface OrderListResp {
  has_more?: boolean;
  cursor?: string;
  order_list?: Array<OrderDetail>;
}

export interface OrderListResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: OrderListResp;
  type_for_model?: number;
}

export interface PreviewOrderInfo {
  recipient_address?: string;
  recipient_name?: string;
  recipient_phone?: string;
  /** 配送费 */
  shipping_fee?: string;
  /** 预计送达时间 */
  estimate_arrival_time?: number;
  /** 用户备注 */
  caution?: string;
  restaurant_id?: string;
  restaurant_name?: string;
  restaurant_min_fee?: string;
  /** 总价 */
  total_price?: string;
  original_price?: string;
  /** 餐盒费 */
  box_total_price?: string;
  user_phone?: string;
  is_pre_order?: number;
  delivery_type?: number;
  /** 店铺图片 */
  restaurant_pic_url?: string;
  delivery_time_tip?: string;
  /** 距离 */
  distance?: string;
  /** 商家评分 */
  restaurant_score?: string;
}

export interface RestaurantInfo {
  /** 商家ID */
  restaurant_id?: string;
  /** 营业状态 1:可配送 2:忙碌中 3:休息中 */
  status?: number;
  /** 营业状态描述 */
  status_desc?: string;
  /** 商家名称 */
  name?: string;
  /** 商家图片url */
  pic_url?: string;
  /** 配送费 */
  shipping_fee?: string;
  /** 起送价 */
  min_price?: string;
  /** 商家评分 */
  restaurant_score?: string;
  /** 平均配送时间 */
  avg_delivery_time?: number;
  /** 我和商家的距离 */
  distance?: string;
  /** optional string address = 11; // 商家地址
  optional int32 delivery_type = 12; // 配送类型，1:美团专送，0:非美团专送
人均价格展示文案 */
  average_price_tip?: string;
  product_list?: Array<Spu>;
}

export interface RestaurantListRequest {
  query?: string;
  longitude?: string;
  latitude?: string;
  sortKey?: number;
  food?: string;
  restaurant_types?: string;
}

export interface RestaurantListResp {
  /** 符合搜索的商家总数 */
  restaurant_total_num?: number;
  /** 是否有下一页 */
  have_next_page?: number;
  /** 当前页号 */
  current_page_index?: number;
  /** 每页数量 */
  page_size?: number;
  restaurant_info_list?: Array<RestaurantInfo>;
}

export interface RestaurantListResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: RestaurantListResp;
  type_for_model?: number;
}

export interface SaveAddressRequest {
  address_id?: string;
  name: string;
  phone: string;
  address: string;
  longitude?: string;
  latitude?: string;
}

export interface SaveAddressResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: AddressListResp;
  type_for_model?: number;
}

export interface ScriptRequest {
  job_name: string;
}

export interface ScriptResponse {
  code?: number;
  msg?: string;
}

export interface ShopcartOperationRequest {
  restaurant_id?: string;
  shopcart_operation?: string;
  food_list?: Array<FoodOperationItem>;
  user_caution?: string;
  address_id?: string;
}

export interface ShopcartOperationResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: waimai_ShopcartOperationResponse.PreviewOrderRes;
  type_for_model?: number;
}

export interface ShoppingCartFoodItem {
  food_spu_id?: string;
  /** optional string food_sku_id = 2; */
  attr_selection_list?: Array<string>;
  count?: number;
  food_selection_id?: string;
}

export interface ShoppingCartFoodRequest {
  restaurant_id?: string;
  food_list?: Array<ShoppingCartFoodItem>;
}

export interface ShoppingCartRequest {
  restaurant_id?: string;
  shopcart_operation?: string;
  user_caution?: string;
  address_id?: string;
}

export interface ShoppingCartRespObj {
  preview_order_info?: PreviewOrderInfo;
  preview_food_list?: Array<OrderFoodInfo>;
  address_info?: AddressInfo;
  token?: string;
}

export interface ShoppingCartResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: ShoppingCartRespObj;
  type_for_model?: number;
}

export interface Sku {
  food_sku_id?: string;
  /** optional string spec = 2;
  optional string description = 3; */
  picture?: string;
  price?: string;
  /** optional string origin_price = 6;
  optional int32 box_num = 7;
  optional string box_price = 8; */
  min_order_count?: number;
  /** optional int32 stock = 11; */
  status?: number;
}

export interface Spu {
  food_spu_id?: string;
  name?: string;
  min_price?: string;
  /** optional string unit = 4;
  optional string description = 5; */
  picture?: string;
  /** optional int32 month_saled = 7; */
  status?: number;
  skus?: Array<Sku>;
  attrs?: Array<Attr>;
  price?: string;
}

export interface SubmitOrderRequest {
  restaurant_id?: string;
  /** repeated FoodListItem food_list = 2; */
  user_caution?: string;
  /** optional string user_longitude = 4;
  optional string user_latitude = 5;
  optional string user_name = 6;
  optional string user_phone = 7;
  optional string user_address = 8; */
  address_id?: string;
  /** optional string wx_pay_open_id = 10;
  optional string wx_pay_app_id = 11; */
  token?: string;
}

export interface SubmitOrderResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  data?: OrderConfirmation;
  type_for_model?: number;
}
/* eslint-enable */
