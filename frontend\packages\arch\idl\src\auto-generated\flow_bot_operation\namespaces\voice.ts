/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum VoiceSource {
  Sami = 1,
  /** 火山 */
  Volcano = 2,
}

export enum VoiceType {
  /** 过滤系统音色 */
  SystemVoice = 1,
  /** 过滤用户音色 */
  UserVoice = 2,
}

export interface AudioInfo {
  audio_uri: string;
  audio_url: string;
}

export interface FetchVoiceListRequest {
  voice_id?: string;
  name?: string;
  style_id?: string;
  language_code?: string;
  page_index?: number;
  page_size?: number;
  /** 音色来源, 默认获取 sami 的 */
  source?: VoiceSource;
  /** 获取音色类型 1 系统 2 用户 默认所有 */
  voice_type?: VoiceType;
  /** 火山账号 */
  volcano_account_id?: string;
}

export interface FetchVoiceListResponse {
  data?: VoiceInfo;
  code: Int64;
  msg: string;
}

export interface GenerateVoiceRequest {
  voice_id?: string;
  preview_text?: string;
  source?: VoiceSource;
}

export interface GenerateVoiceResponse {
  data?: AudioInfo;
  code: Int64;
  msg: string;
}

export interface GetSupportLanguageRequest {
  source?: VoiceSource;
}

export interface GetSupportLanguageResponse {
  data?: Array<LanguageConfig>;
  code: Int64;
  msg: string;
}

export interface LanguageConfig {
  language_code: string;
  language_name: string;
  language_loc: string;
}

export interface UpdateVoiceConfigRequest {
  voice_id: string;
  /** 音色状态，0-草稿，1-上架，2-下架 */
  status?: number;
  /** 预览音频文本 */
  preview_text?: string;
  /** 预览音频的uri */
  preview_audio?: string;
  /** 是否为默认音色，0-非默认，1-默认 */
  is_default?: number;
  /** 音色名称 */
  name?: string;
  /** 语言 */
  language_code?: string;
  source?: VoiceSource;
}

export interface UpdateVoiceConfigResponse {
  code: Int64;
  msg: string;
}

export interface VoiceConfig {
  id: string;
  language_code: string;
  language_name: string;
  name: string;
  style_id: string;
  update_time: Int64;
  preview_text: string;
  /** 此处返回url */
  preview_audio_url: string;
  /** 是否为默认音色，0-非默认，1-默认 */
  is_default: number;
  /** 音色状态，0-草稿，1-上架，2-下架 */
  status: number;
  /** 音色来源 */
  source?: VoiceSource;
  /** 火山账号 */
  volcano_account_id?: string;
}

export interface VoiceInfo {
  voice_list?: Array<VoiceConfig>;
  total?: number;
}
/* eslint-enable */
