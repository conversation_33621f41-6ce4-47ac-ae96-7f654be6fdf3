/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AutoChargeConfig {
  is_enabled?: boolean;
  threshold_amount?: number;
  charge_amount?: number;
  max_charge_amount_per_day?: number;
}

export interface AutoChargeConfigData {
  /** 自动充值相关配置 */
  auto_charge_config?: AutoChargeConfig;
  /** 自动充值状态 */
  auto_charge_state?: AutoChargeState;
}

export interface AutoChargeState {
  today_charge_amount?: number;
}

export interface CancelAutoChargeRequest {
  UserID?: Int64;
}

export interface CancelAutoChargeResponse {
  code?: number;
  message?: string;
}

export interface GetAutoChargeConfigRequest {
  UserID?: Int64;
}

export interface GetAutoChargeConfigResponse {
  code?: number;
  message?: string;
  data?: AutoChargeConfigData;
}

export interface SignAutoChargeRequest {
  UserID?: Int64;
  threshold_amount?: number;
  charge_amount?: number;
  max_charge_amount_per_day?: number;
}

export interface SignAutoChargeResponse {
  code?: number;
  message?: string;
}
/* eslint-enable */
