/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as copilot_common from './copilot_common';

export type Int64 = string | number;

export enum PromptTemplateFormat {
  FString = 1,
  Jinja2 = 2,
  GoTemplate = 3,
}

export enum ReferenceType {
  DocumentReference = 1,
}

export enum ResultType {
  PluginResponse = 1,
  PluginIntent = 2,
  Variables = 3,
  None = 4,
  BotSchema = 5,
  ReferenceVariable = 6,
}

export enum RetrieverType {
  Plugin = 1,
  PluginAsService = 2,
  Service = 3,
}

export interface Message {
  conversation_id?: Int64;
  section_id?: Int64;
  message_id?: Int64;
  content?: string;
  role?: copilot_common.CopilotRole;
  location?: copilot_common.LocationInfo;
  files?: Array<copilot_common.FileInfo>;
  images?: Array<copilot_common.ImageInfo>;
}

export interface RetrieveResult {
  result_type?: ResultType;
  /** json string */
  content?: string;
}
/* eslint-enable */
