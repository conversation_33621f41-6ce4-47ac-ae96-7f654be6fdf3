/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as exper_agent from './exper_agent';

export type Int64 = string | number;

export enum CozeSpaceTaskStatus {
  /** 运行中，Chat和任务执行都算在运行中 */
  Running = 1,
  /** 暂停 */
  Pause = 2,
  /** 一轮任务完成 */
  TaskFinish = 3,
  /** 初始化 */
  Init = 4,
  /** 终止 */
  Stop = 5,
  /** 中断 */
  Interrupt = 6,
  /** 存在非法内容 */
  IllegalContent = 7,
  /** 异常中断 */
  AbnormalInterrupt = 8,
  /** 休眠 */
  Sleep = 9,
}

export enum CozeSpaceTaskType {
  /** 通用任务 */
  General = 1,
  /** 用研专家任务 */
  UserResearch = 2,
  /** 股票任务 */
  Stock = 3,
  /** 舆情专家 */
  AnalyzePublicOpinion = 5,
  /** PPT Agent */
  PPTAgent = 6,
  /** 同程专家agent */
  TongCheng = 9,
}

export enum FeelType {
  Good = 1,
  Bad = 2,
}

export enum MessageType {
  Query = 1,
  Answer = 2,
  Resume = 3,
}

export enum MilvTaskType {
  /** 审查，起草，法律问答 */
  Review = 1,
  Draft = 2,
  LawQA = 3,
}

export enum OperateType {
  Pause = 1,
  Resume = 2,
  Pin = 3,
  Unpin = 4,
  DoNotDisturb = 5,
  CancelDoNotDisturb = 6,
  Stop = 7,
}

/** 定时任务状态 */
export enum ScheduledTaskStatus {
  /** 初始化 */
  Init = 1,
  /** 启用中 */
  Enabled = 2,
  /** 弃用 */
  Disabled = 3,
}

/** 定时任务调度状态 */
export enum ScheduledTaskTriggerStatus {
  /** 初始化状态 */
  Pending = 1,
  /** 调度成功 */
  Success = 2,
  /** 调度回避 */
  Avoid = 3,
  /** 调度失败 */
  Failed = 4,
}

export enum StockSearchType {
  /** 初始化传 */
  Init = 1,
  /** 搜股票传 */
  Stock = 2,
  /** 搜板块传 */
  Sector = 3,
}

export enum StockTaskType {
  /** 普通咨询任务 */
  GeneralChat = 1,
  /** 定时任务 */
  Scheduled = 2,
}

export enum TaskExampleStatus {
  Init = 0,
  Enable = 1,
  Disable = 2,
}

export enum TaskReplayOperateType {
  Open = 1,
  Close = 2,
}

export enum TaskReplayStatus {
  Open = 1,
  Close = 2,
}

export enum TaskRunMode {
  HandsOff = 0,
  Cooperative = 1,
  Auto = 2,
}

export enum TaskSourceFrom {
  UserCreate = 0,
  Example = 1,
}

export enum UploadUserResearchFileAction {
  FieldAnalysis = 1,
  Upload = 2,
}

export interface Action {
  /** 主键ID */
  action_id?: string;
  action_sort_id?: string;
  /** 文本 */
  content?: string;
  /** 容器输出的文本 */
  computer_content?: string;
  create_time?: Int64;
  /** 产物文件 */
  file_list?: Array<File>;
  /** 动作类型 */
  action_type?: string;
  /** 工具操作类型 */
  tool_operation_type?: string;
  /** 缩进 */
  parent_step_ids?: Array<string>;
}

export interface AnalyzePublicOpinionConfig {
  /** 关键词 */
  key_word?: string;
  /** 描述 */
  description?: string;
  /** 关联词 */
  related_words?: Array<RelatedWord>;
  /** 已授权的渠道 */
  authorized_channels?: Array<Channel>;
  /** 是否开启联网搜索 */
  is_open_search?: boolean;
}

export interface BrowserResumeData {
  /** 是否跳过接管 */
  skip_takeover?: boolean;
}

export interface Channel {
  url_code?: string;
  name?: string;
  is_authorization?: boolean;
  icon?: string;
  /** 不在白名单里面的URL */
  extra_web_url?: string;
  is_open?: boolean;
}

export interface CheckUserScheduledTaskQuotaData {
  remained_quota?: number;
}

export interface CheckUserScheduledTaskQuotaRequest {}

export interface CheckUserScheduledTaskQuotaResponse {
  code?: Int64;
  msg?: string;
  data?: CheckUserScheduledTaskQuotaData;
}

export interface CozeSpaceChatRequest {
  task_id?: string;
  query?: string;
  files?: Array<File>;
  mcp_list?: Array<Mcp>;
  chat_type?: string;
  /** pause - resume 时需传入 */
  pause_reason?: string;
  task_run_mode?: TaskRunMode;
  expert_agent_run_config?: ExpertTaskRunConfig;
  scheduled_task_config?: ScheduledTaskConfig;
  /** 禁用团队模式 */
  disable_team_mode?: boolean;
  /** resume 时相关表单数据 */
  resume_data?: ResumeData;
}

export interface CozeSpaceChatResponse {
  code?: Int64;
  msg?: string;
  data?: CozeSpaceChatResponseData;
}

export interface CozeSpaceChatResponseData {
  answer_id?: string;
  query_id?: string;
}

export interface CozeSpaceTask {
  task_id?: string;
  task_name?: string;
  task_type?: Int64;
  task_status?: CozeSpaceTaskStatus;
  task_create_time?: string;
  task_update_time?: string;
  task_display_info?: CozeSpaceTaskDisplayInfo;
  mcp_tool_list?: Array<Mcp>;
  expert_agent_config?: ExpertAgentConfig;
  parent_id?: string;
  /** 是否是定时任务 */
  is_scheduled_task?: boolean;
  /** task 关联的定时任务相关信息 */
  scheduled_task?: TaskScheduledInfo;
  /** 是否开启团队模式 */
  team_mode?: boolean;
}

export interface CozeSpaceTaskDisplayInfo {
  /** 是否置顶 */
  is_pin?: boolean;
  /** 是否免打扰 */
  is_dnd?: boolean;
}

export interface CreateCozeScheduledTaskData {
  scheduled_task?: ScheduledTask;
  answer_id?: string;
  query_id?: string;
}

export interface CreateCozeScheduledTaskRequest {
  /** 是否需要试运行 */
  is_need_try_run?: boolean;
  scheduled_task_name?: string;
  trigger?: string;
  /** 源任务 id */
  source_task_id?: string;
  /** 任务计划 */
  task_plan?: string;
  description?: string;
  /** 执行的时间点，格式为 10:00 */
  daily_executed_at?: string;
  /** 新版的plan，一个普通的string，优先取这个，没有就取 task_plan */
  task_plan_v2?: string;
}

export interface CreateCozeScheduledTaskResponse {
  code?: Int64;
  msg?: string;
  data?: CreateCozeScheduledTaskData;
}

export interface CreateCozeSpaceTaskData {
  task: CozeSpaceTask;
}

export interface CreateCozeSpaceTaskRequest {
  task_name: string;
  task_type: Int64;
  file_uri_list?: Array<string>;
  mcp_tool_list?: Array<Mcp>;
  agent_ids?: Array<string>;
  scheduled_task_setting?: ScheduledTaskSetting;
  source_from?: TaskSourceFrom;
  expert_agent_config?: ExpertAgentConfig;
}

export interface CreateCozeSpaceTaskResponse {
  code?: Int64;
  msg?: string;
  data: CreateCozeSpaceTaskData;
}

export interface CreateTaskReplayRequest {
  task_id?: string;
  need_qr_code?: boolean;
}

export interface CreateTaskReplayResponse {
  code?: Int64;
  msg?: string;
  data?: CreateTaskReplayResponseData;
}

export interface CreateTaskReplayResponseData {
  task_share_url?: string;
  qr_code_url?: string;
}

export interface DeleteCozeScheduledTaskRequest {
  scheduled_task_id: string;
}

export interface DeleteCozeScheduledTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface DeleteCozeSpaceTaskRequest {
  task_id: string;
}

export interface DeleteCozeSpaceTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface DiscountTravelConfig {
  /** 出发地 */
  departure?: string;
  /** 目的地 */
  destination?: string;
  /** 出发时间 */
  departure_time?: string;
  /** 出行人数 */
  return_time?: string;
  /** 出行方式 */
  travel_mode?: Array<string>;
  /** 个性化需求 */
  personal_demand?: string;
}

export interface ExpertAgentConfig {
  user_research_config?: UserResearchConfig;
  stock_config?: StockConfig;
  analyze_public_opinion_config?: AnalyzePublicOpinionConfig;
  /** callback时透传 */
  external_config?: Record<string, string>;
  milv_config?: MilvConfig;
  tongcheng_agent_config?: TongChengTravelAgentConfig;
}

export interface ExpertTaskRunConfig {
  user_research_run_config?: UserResearchRunConfig;
  stock_task_run_config?: StockTaskRunConfig;
  analyze_public_opinion_config?: AnalyzePublicOpinionConfig;
  milv_config?: MilvConfig;
}

export interface FeelContent {
  bad_reason?: string;
}

export interface FeelGoodData {
  task_id?: string;
  answer_id?: string;
  feel?: FeelType;
}

export interface FeelGoodRequest {
  task_id?: string;
  answer_id?: string;
  feel?: FeelType;
  content?: FeelContent;
}

export interface FeelGoodResponse {
  code?: Int64;
  msg?: string;
}

export interface File {
  file_name?: string;
  file_uri?: string;
  file_url?: string;
}

export interface GetCozeSpaceTaskListData {
  task_list?: Array<CozeSpaceTask>;
  next_cursor?: string;
  has_more?: boolean;
}

export interface GetCozeSpaceTaskListRequest {
  cursor?: string;
  size?: Int64;
}

export interface GetCozeSpaceTaskListResponse {
  code?: Int64;
  msg?: string;
  data: GetCozeSpaceTaskListData;
}

export interface GetMessageListRequest {
  task_id?: string;
  /** 游标,如果为空 从头开始 */
  cursor?: string;
  /** 获取几个 */
  size?: Int64;
}

export interface GetMessageListResponse {
  code?: Int64;
  msg?: string;
  data?: GetMessageListResponseData;
}

export interface GetMessageListResponseData {
  messages?: Array<Message>;
  /** 游标 */
  cursor?: string;
  task_status?: CozeSpaceTaskStatus;
  task_run_mode?: TaskRunMode;
  /** poll传入的next_key */
  next_key?: string;
  run_time?: Int64;
}

export interface GetSandboxTokenRequest {
  task_id: string;
  action_url_code?: string;
}

export interface GetSandboxTokenResponse {
  code?: Int64;
  msg?: string;
  data?: GetSandboxTokenResponseData;
}

export interface GetSandboxTokenResponseData {
  token?: string;
  url?: string;
}

export interface GetSingleScheduledTaskData {
  taskInfo?: ScheduledTask;
}

export interface GetSingleScheduledTaskRequest {
  scheduled_task_id: string;
}

export interface GetSingleScheduledTaskResponse {
  code?: Int64;
  msg?: string;
  data?: GetSingleScheduledTaskData;
}

export interface GetTaskExampleCategoryListData {
  example_categories?: Array<TaskExampleCategory>;
}

export interface GetTaskExampleCategoryListRequest {}

export interface GetTaskExampleCategoryListResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskExampleCategoryListData;
}

export interface GetTaskExampleData {
  task_example?: TaskExample;
}

export interface GetTaskExampleListData {
  task_examples?: Array<TaskExample>;
  total?: number;
}

export interface GetTaskExampleListRequest {
  category_id?: string;
  page_size?: number;
  page?: number;
}

export interface GetTaskExampleListResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskExampleListData;
}

export interface GetTaskExampleRequest {
  task_example_id: string;
}

export interface GetTaskExampleResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskExampleData;
}

export interface GetTaskInfoData {
  task_info?: CozeSpaceTask;
}

export interface GetTaskInfoRequest {
  task_id: string;
}

export interface GetTaskInfoResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskInfoData;
}

export interface GetTaskReplayByIdRequest {
  task_share_id?: string;
  secret?: string;
}

export interface GetTaskReplayByIdResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskReplayByIdResponseData;
}

export interface GetTaskReplayByIdResponseData {
  replay_file?: File;
  task_example_id?: string;
}

export interface GetTaskReplayRequest {
  task_id?: string;
}

export interface GetTaskReplayResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskReplayResponseData;
}

export interface GetTaskReplayResponseData {
  replay_task_list?: Array<TaskReplay>;
}

export interface GetUserScheduledTaskData {
  scheduled_tasks?: Array<ScheduledTask>;
}

export interface GetUserScheduledTaskRequest {}

export interface GetUserScheduledTaskResponse {
  code?: Int64;
  msg?: string;
  data?: GetUserScheduledTaskData;
}

export interface GetUserScheduledTasksData {
  task_num_map?: Record<Int64, Int64>;
}

export interface GetUserScheduledTasksRequest {}

export interface GetUserScheduledTasksResponse {
  code?: Int64;
  msg?: string;
  data?: GetUserScheduledTasksData;
}

export interface Mcp {
  id?: string;
}

export interface Message {
  message_id?: string;
  type?: MessageType;
  steps?: Array<Step>;
  content?: string;
  file_list?: Array<File>;
  create_time?: Int64;
  /** resume 时相关表单数据 */
  resume_data?: ResumeData;
}

export interface MilvConfig {
  /** 类型 */
  type?: MilvTaskType;
  /** 文件id */
  fileId?: string;
  review_config?: exper_agent.ReviewConfig;
  draft_config?: exper_agent.DraftConfig;
  law_qa_config?: exper_agent.LawQAConfig;
  contract_files?: Array<exper_agent.ContractFileInfo>;
}

export interface NameDesc {
  name?: string;
  desc?: string;
  /** analyze csv时返回展示使用，后续无需携带 */
  ori_name?: string;
}

export interface OperatePausedTaskRequest {
  task_id?: string;
  /** confirm_plan or ack_outline */
  pause_reason?: string;
}

export interface OperatePausedTaskResponse {
  code?: Int64;
  msg?: string;
}

export interface OperateTaskData {
  /** OperateType=Resume时返回 */
  answer_id?: string;
  /** OperateType=Resume时返回 */
  message_id?: string;
}

export interface OperateTaskReplayRequest {
  task_id?: string;
  task_share_id?: string;
  operate_type?: TaskReplayOperateType;
}

export interface OperateTaskReplayResponse {
  code?: Int64;
  msg?: string;
}

export interface OperateTaskRequest {
  task_id?: string;
  operate_type?: OperateType;
  /** pause - resume 时需传入 */
  pause_reason?: string;
  /** browser pause - resume 时需传入 */
  browser?: BrowserResumeData;
}

export interface OperateTaskResponse {
  code?: Int64;
  msg?: string;
  data?: OperateTaskData;
}

export interface PollStepListRequest {
  task_id?: string;
  answer_id?: string;
  next_key?: string;
}

export interface PollStepListResponse {
  code?: Int64;
  msg?: string;
  data?: PollStepListResponseData;
}

export interface PollStepListResponseData {
  steps?: Array<Step>;
  run_time?: Int64;
  task_name?: string;
  is_end?: boolean;
  next_key?: string;
  next_answer_id?: string;
}

export interface RelatedWord {
  intent?: string;
  keywords?: Array<string>;
  no_choice_keywords?: Array<string>;
}

export interface RestoreExampleFileData {
  files?: Array<File>;
}

export interface RestoreExampleFileRequest {
  task_id: string;
  example_id: string;
}

export interface RestoreExampleFileResponse {
  code?: Int64;
  msg?: string;
  data?: RestoreExampleFileData;
}

export interface ResumeData {
  /** 幻灯片 */
  slide_resume_data?: SlideResumeData;
}

export interface ScheduledRecord {
  /** 调度状态 */
  trigger_status?: ScheduledTaskTriggerStatus;
  /** 调度时间 */
  trigger_time?: string;
}

export interface ScheduledTask {
  scheduled_task_id?: string;
  scheduled_task_name?: string;
  trigger?: string;
  /** 定时任务状态 */
  status?: ScheduledTaskStatus;
  task_type?: CozeSpaceTaskType;
  mcp_tool_list?: Array<Mcp>;
  expert_agent_config?: ExpertAgentConfig;
  /** 任务步骤，对应 agent 中的 PlanUpdateData 结构 */
  task_plan?: string;
  description?: string;
  /** 执行的时间点，格式为 10:00 */
  daily_executed_at?: string;
  /** 新版的plan，一个普通的string，优先取这个，没有就取 task_plan */
  task_plan_v2?: string;
}

export interface ScheduledTaskConfig {
  plan_update_config?: string;
  /** 如果用户通过时间选择窗口修改了执行时间就传 true */
  save_with_new_time?: boolean;
  /** 用户保存计划时的 cron 表达式 */
  cron_exp?: string;
  /** 用于间隔 xx 天场景，具体的执行时刻 */
  execute_time?: string;
  /** plan卡片，用户希望通过query修改定时任务时传true */
  save_and_modify_with_query?: boolean;
}

export interface ScheduledTaskSetting {
  trigger?: string;
  related_scheduled_task_id?: string;
}

export interface SearchFeelGoodRequest {
  task_id?: string;
  answer_id?: string;
}

export interface SearchFeelGoodResponse {
  code?: Int64;
  msg?: string;
  data?: SearchFeelGoodResponseData;
}

export interface SearchFeelGoodResponseData {
  feel_good_data?: FeelGoodData;
}

export interface SearchStockData {
  stock_list?: Array<StockInfo>;
  sector_list?: Array<string>;
  hot_sector_list?: Array<string>;
}

export interface SearchStockRequest {
  search_type: StockSearchType;
  /** 股票代码，前缀匹配 */
  stock_search_word?: string;
  /** 股票名称，前缀匹配 */
  sector_search_word?: string;
}

export interface SearchStockResponse {
  code?: Int64;
  msg?: string;
  data?: SearchStockData;
}

export interface Slide {
  title?: string;
  content?: string;
}

export interface SlideResumeData {
  /** 总页数 */
  total_page_number?: Int64;
  /** PPT场景 */
  scene_key?: string;
  /** 大纲 */
  slides?: Array<Slide>;
}

export interface Step {
  /** 主键ID */
  step_id?: string;
  answer_id?: string;
  step_sort_id?: string;
  /** 动作列表 */
  action_list?: Array<Action>;
  create_time?: Int64;
  is_finish?: boolean;
}

export interface StockConfig {
  /** 股票任务细分类型 */
  stock_task_type?: StockTaskType;
  /** 是否需要定时任务 */
  sheduled_task_switch: boolean;
  /** 用户选定的股票 */
  stock_info_list?: Array<StockInfo>;
  /** 用户选定的板块 */
  sector_list?: Array<string>;
  /** 保存设置后需要发送的query（不支持接口修改） */
  user_send_query?: string;
  /** 保存设置后直接展示的开场白（不支持接口修改） */
  onboarding?: string;
  /** 是否有定时任务在运行（不支持接口修改） */
  is_scheduled_task_running?: boolean;
  /** 是否为早报准备时间（不支持修改） */
  is_morning_report_preparing?: boolean;
}

export interface StockInfo {
  stock_code?: string;
  stock_name?: string;
}

export interface StockTaskRunConfig {
  is_onboarding_run?: boolean;
}

export interface TaskExample {
  /** 案例的 id */
  id?: string;
  name?: string;
  description?: string;
  icon_url?: string;
  category_info?: TaskExampleCategory;
  user_query?: string;
  task_replay_url?: string;
  task_type?: CozeSpaceTaskType;
  mcp_tool_list?: Array<Mcp>;
  expert_agent_config?: ExpertAgentConfig;
  task_run_mode?: TaskRunMode;
  status?: TaskExampleStatus;
  files?: Array<File>;
  is_team_mode?: boolean;
}

export interface TaskExampleCategory {
  id?: string;
  name?: string;
  is_job_recommend?: boolean;
  recommend_content?: string;
}

export interface TaskReplay {
  secret?: string;
  task_share_id?: string;
  taskReplayStatus?: TaskReplayStatus;
}

export interface TaskScheduledInfo {
  /** 源定时任务信息 */
  source_scheduled_task?: ScheduledTask;
  /** 定时任务调度记录 */
  scheduled_record?: ScheduledRecord;
}

export interface TongChengTravelAgentConfig {
  /** 旅游攻略配置 */
  travel_guide_config?: TravelGuideConfig;
  /** 优惠出行配置 */
  discount_travel_config?: DiscountTravelConfig;
  /** 保存设置后需要发送的query（不支持接口修改) */
  user_send_query?: string;
}

export interface TravelGuideConfig {
  /** 出发地 */
  departure?: string;
  /** 目的地 */
  destination?: string;
  /** 出发时间 */
  departure_time?: string;
  /** 返程时间 */
  return_time?: string;
  /** 预算 */
  budget?: string;
  /** 个性化需求 */
  personal_demand?: string;
  /** 出行人数 */
  travel_people?: TravelPeople;
}

export interface TravelPeople {
  /** 成人人数 */
  adult_num?: Int64;
  /** 儿童人数 */
  child_num?: Int64;
  /** 婴儿人数 */
  infant_num?: Int64;
  /** 宠物人数 */
  pet_num?: Int64;
}

export interface TryRunCozeScheduledTaskData {
  answer_id?: string;
  query_id?: string;
  user_query?: string;
  new_task_id?: string;
}

export interface TryRunCozeScheduledTaskRequest {
  scheduled_task_id: string;
}

export interface TryRunCozeScheduledTaskResponse {
  code?: Int64;
  msg?: string;
  data?: TryRunCozeScheduledTaskData;
}

export interface UpdateCozeScheduledTaskData {
  answer_id?: string;
  query_id?: string;
}

export interface UpdateCozeScheduledTaskRequest {
  scheduled_task_id: string;
  task_type: CozeSpaceTaskType;
  /** 定时任务状态 */
  status?: ScheduledTaskStatus;
  scheduled_task_name?: string;
  trigger?: string;
  mcp_tool_list?: Array<Mcp>;
  expert_agent_config?: ExpertAgentConfig;
  /** 任务计划, 对应 agent 的PlanUpdateData 结构 */
  task_plan?: string;
  /** 是否需要试运行 */
  is_need_try_run?: boolean;
  description?: string;
  /** 请求来自于哪个 task_id，如果是来自管理页面就不用传 */
  task_id?: string;
  /** 执行的时间点，格式为 10:00 */
  daily_executed_at?: string;
  /** 新版的plan，一个普通的string，优先取这个，没有就取 task_plan */
  task_plan_v2?: string;
}

export interface UpdateCozeScheduledTaskResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateCozeScheduledTaskData;
}

export interface UpdateCozeSpaceTaskData {
  task_name?: string;
  /** 保存设置后需要发送的query */
  user_send_query?: string;
  /** 保存设置后直接展示的开场白 */
  onboarding?: string;
}

export interface UpdateCozeSpaceTaskRequest {
  task_id: string;
  task_name?: string;
  mcp_tool_list?: Array<Mcp>;
  expert_agent_config?: ExpertAgentConfig;
  scheduled_task_setting?: ScheduledTaskSetting;
}

export interface UpdateCozeSpaceTaskResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateCozeSpaceTaskData;
}

export interface UpdateFileContentRequest {
  task_id?: string;
  file_uri?: string;
  file_content?: Blob;
}

export interface UpdateFileContentResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateTaskPlanData {
  answer_id?: string;
}

export interface UpdateTaskPlanRequest {
  task_id: string;
  action_id: string;
  task_plan?: string;
}

export interface UpdateTaskPlanResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateTaskPlanData;
}

export interface UploadFileData {
  file_uri?: string;
  task_id?: string;
}

export interface UploadTaskFileRequest {
  task_id?: string;
  file_name?: string;
  file_content?: Blob;
}

export interface UploadTaskFileResponse {
  code?: Int64;
  msg?: string;
  data?: UploadFileData;
}

export interface UploadUserResearchFileData {
  /** csv/xlsx 字段名称+描述 */
  fields?: Array<NameDesc>;
  /** other file */
  uri?: string;
}

export interface UploadUserResearchFileRequest {
  task_id: string;
  action: UploadUserResearchFileAction;
  /** 文件类型，csv/xlsx */
  file_type: string;
  /** 文件名，对应data_table */
  file_name: string;
  file_content?: Blob;
  /** 后续信息为提交字段，触发上传文件到DB，否则只解析文件字段名称和描述
表描述 */
  desc?: string;
  /** 字段名称+描述 */
  fields?: Array<NameDesc>;
}

export interface UploadUserResearchFileResponse {
  code?: Int64;
  msg?: string;
  data?: UploadUserResearchFileData;
}

export interface UserResearchConfig {
  /** create/update可修改 */
  product_intro: string;
  /** 不支持create，update可传最新的列表(name、type必传)仅做删除使用 */
  user_research_file_list?: Array<UserResearchFile>;
}

export interface UserResearchFile {
  file_name?: string;
  file_type?: string;
  file_uri?: string;
}

export interface UserResearchRunConfig {
  /** 传name即可 */
  cited_documents?: Array<UserResearchFile>;
}
/* eslint-enable */
