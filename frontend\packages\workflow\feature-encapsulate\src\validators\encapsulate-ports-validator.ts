/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { injectable } from 'inversify';
import { StandardNodeType } from '@coze-workflow/base';
import { I18n } from '@coze-arch/i18n';
import { FlowNodeBaseType } from '@flowgram-adapter/free-layout-editor';
import {
  type WorkflowNodeEntity,
  WorkflowNodePortsData,
} from '@flowgram-adapter/free-layout-editor';

import {
  EncapsulateValidateErrorCode,
  type EncapsulateNodesValidator,
} from '../validate';
import { getNodesWithSubCanvas } from '../utils/get-nodes-with-sub-canvas';
import { EncapsulateBaseValidator } from './encapsulate-base-validator';

@injectable()
export class EncapsulatePortsValidator
  extends EncapsulateBaseValidator
  implements EncapsulateNodesValidator
{
  validate(nodes: WorkflowNodeEntity[], result) {
    getNodesWithSubCanvas(nodes).forEach(node => {
      const ignoreNodes = [
        StandardNodeType.Comment,
        FlowNodeBaseType.SUB_CANVAS,
      ];
      if (ignoreNodes.includes(node.flowNodeType as StandardNodeType)) {
        return;
      }

      const portsData = node.getData<WorkflowNodePortsData>(
        WorkflowNodePortsData,
      );

      const hasNotConnectPort = portsData.allPorts.some(
        port => port.lines.length === 0,
      );
      if (hasNotConnectPort) {
        const sourceName = this.getNodeName(node);
        const sourceIcon = this.getNodeIcon(node);

        result.addError({
          code: EncapsulateValidateErrorCode.INVALID_PORTS,
          message: I18n.t(
            'workflow_encapsulate_button_unable_uncomplete',
            undefined,
            '封装不应该包含没有输入输出的节点',
          ),
          source: node.id,
          sourceName,
          sourceIcon,
        });
      }
    });
  }
  includeStartEnd: true;
}
